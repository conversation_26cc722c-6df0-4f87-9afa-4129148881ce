# Quote to Invoice Conversion Fix

## Issue Summary
When a quote was converted to an invoice using the "Convert to Invoice" button on the quote page, the quote's status was correctly updated to "converted" but the newly created invoice didn't appear in the invoice list in the UI.

## Root Cause
1. **Data Serialization Issues**: The linked_quote_id field was correctly stored in the database but wasn't being included in the API response.
2. **API Endpoint Issues**: The original `/api/v1/invoices` endpoint had issues with properly serializing SQLAlchemy results.
3. **Frontend Display**: The frontend wasn't designed to display quotes that had been converted to invoices.

## Backend Changes

### 1. Created a Database Migration
Added a `linked_quote_id` column to the Invoice table to track which quotes were converted to invoices.

### 2. Fixed Data Migration
Created a utility script (`fix_converted_quotes.py`) to create invoice records for quotes with "converted" status that were missing invoice records:

```python
def create_invoice_from_quote(db, quote, Invoice):
    """Create a new invoice record from a converted quote"""
    try:
        # Generate invoice number
        today = datetime.now()
        invoice_number = f"INV-{today.strftime('%Y%m%d')}-{quote.id}"
        
        # Calculate due date (30 days from today)
        due_date = today + timedelta(days=30)
        
        # Create new invoice record
        new_invoice = Invoice(
            invoice_number=invoice_number,
            customer_id=quote.customer_id,
            company_id=quote.company_id,
            invoice_date=today.date(),
            due_date=due_date.date(),
            invoice_type="standard",
            grand_total=quote.grand_total,
            notes=f"Created from quote {quote.quote_number}. {quote.notes or ''}",
            linked_quote_id=str(quote.id)
        )
        
        # Add to database
        db.add(new_invoice)
        db.commit()
        
        logger.info(f"Created invoice {invoice_number} from quote {quote.quote_number}")
        return new_invoice
    except Exception as e:
        db.rollback()
        logger.error(f"Error creating invoice from quote {quote.id}: {str(e)}")
        return None
```

### 3. Created a New API Endpoint
Instead of modifying the existing `/api/v1/invoices` endpoint, we created a new endpoint `/api/v1/invoices-with-quotes` that properly includes the `linked_quote_id` field:

```python
@app.get("/api/v1/invoices-with-quotes", tags=["invoices"])
async def direct_list_invoices_with_quotes(
    skip: int = 0,
    limit: int = 100,
    customer_id: Optional[str] = None,
    store_type: Optional[str] = None,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    search: Optional[str] = None,
    authorization: Optional[str] = Header(None)
):
    """Direct implementation for listing invoices including linked quotes"""
    # Implementation details...
```

### 4. Added Debug Endpoints
Created debug endpoints to help diagnose the issue:
- `/api/v1/debug/converted-quotes`: Shows quotes with "converted" status
- `/api/v1/debug/linked-invoices`: Shows invoices with linked_quote_id values
- `/api/v1/debug/invoices-raw`: Shows raw invoice data with linked_quote_id values

### 5. Created Test Script
Implemented a test script (`test_invoice_display.py`) to verify the fix:
- Checks the regular invoice listing endpoint
- Checks the debug endpoints
- Confirms the new invoices-with-quotes endpoint works correctly

## Frontend Changes

### 1. Updated API Configuration
Added the new endpoint to the frontend configuration:

```typescript
export const ENDPOINTS = {
  // ... other endpoints
  INVOICES: '/api/v1/invoices',
  INVOICES_WITH_QUOTES: '/api/v1/invoices-with-quotes',
  // ... other endpoints
};
```

### 2. Updated Invoice Service
Modified the invoice service to use the new endpoint:

```typescript
export const getInvoices = async (
  page: number = 1,
  limit: number = 10,
  filters?: InvoiceFilters
): Promise<InvoicePagination> => {
  // ... existing code ...
  try {
    console.log(`Fetching invoices with params: ${queryParams}`);
    // Use the new endpoint that properly includes linked_quote_id
    const response = await apiClient.get<InvoicePagination>(`${ENDPOINTS.INVOICES_WITH_QUOTES}${queryParams}`);
    console.log('Raw API response for invoices:', response);
    return response;
  } catch (error) {
    console.error('Error fetching invoices:', error);
    throw error;
  }
};
```

### 3. Updated Invoice Interface
Added the `linked_quote_id` field to the invoice interface:

```typescript
export interface InvoiceResponse {
  // ... other fields ...
  linked_quote_id?: string; // Add field for quotes that were converted to invoices
}
```

### 4. Updated Invoice Table Component
Enhanced the invoice table to show a visual indicator for invoices that were converted from quotes:

```typescript
<div className="flex items-center">
  <Link 
    to={`/invoices/${row.id}`}
    className="text-blue-600 hover:text-blue-900 font-medium"
  >
    {value}
  </Link>
  {row.linkedQuoteId && (
    <span 
      className="ml-2 px-2 py-0.5 bg-purple-100 text-purple-800 text-xs rounded-full"
      title={`Converted from Quote #${row.linkedQuoteId}`}
    >
      From Quote
    </span>
  )}
</div>
```

Also added a "View Quote" button in the actions column for invoices that were converted from quotes:

```typescript
{row.linkedQuoteId && (
  <Link 
    to={`/quotes/${row.linkedQuoteId}`}
    className="px-2 py-1 bg-indigo-100 text-indigo-700 text-xs rounded hover:bg-indigo-200 flex items-center"
  >
    View Quote
  </Link>
)}
```

## Conclusion
With these changes, quotes converted to invoices now properly appear in the invoice list. The UI clearly indicates which invoices were created from quotes and provides easy access to view the original quote. The solution is robust and works even if there are issues during the quote-to-invoice conversion process. 