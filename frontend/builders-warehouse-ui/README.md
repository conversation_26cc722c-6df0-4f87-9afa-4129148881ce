# Builders Warehouse Australia

A React application for Builders Warehouse Australia, featuring a login page, home dashboard, and packages page.

## Features

- User authentication (login page)
- Dashboard with statistics
- Packages listing
- Responsive design
- Mobile-friendly navigation with hamburger menu

## Prerequisites

- Node.js (v14 or higher)
- npm or yarn
- PM2 (optional, for production deployment)

## Installation

1. Clone the repository:
```
git clone <repository-url>
cd builders-warehouse
```

2. Install dependencies:
```
npm install
```

3. Start the development server:
```
npm start
```

The application will be available at http://localhost:4200.

## Production Deployment with PM2

PM2 is a production process manager for Node.js applications that helps you keep your app running 24/7.

1. Install PM2 globally (if not already installed):
```
npm install -g pm2
```

2. Build the production version of the app:
```
npm run build
```

3. Install a static server (if not already installed):
```
npm install -g serve
```

4. Start the application with PM2:
```
pm2 start serve --name "builders-warehouse" -- -s build -l 4200
```

This will start the application on port 4200.

5. Additional PM2 commands:

   - View running applications:
   ```
   pm2 list
   ```

   - Monitor application:
   ```
   pm2 monit
   ```

   - View logs:
   ```
   pm2 logs builders-warehouse
   ```

   - Restart application:
   ```
   pm2 restart builders-warehouse
   ```

   - Stop application:
   ```
   pm2 stop builders-warehouse
   ```

   - Setup PM2 to start on system boot:
   ```
   pm2 startup
   pm2 save
   ```

## Docker Deployment

To build and run the application using Docker:

1. Build the Docker image:
```
docker build -t builders-warehouse .
```

2. Run the container:
```
docker run -p 4200:4200 builders-warehouse
```

The application will be available at http://localhost:4200.

## Project Structure

```
builders-warehouse/
├── public/                 # Static files
├── src/                    # Source files
│   ├── assets/             # Images and SVGs
│   ├── components/         # Reusable components
│   ├── pages/              # Page components
│   ├── App.tsx             # Main App component with routing
│   └── index.tsx           # Entry point
├── Dockerfile              # Docker configuration
├── nginx.conf              # Nginx configuration for Docker
└── package.json            # Dependencies and scripts
```

## Technologies Used

- React
- TypeScript
- React Router
- Styled Components
- Docker
- Nginx
- PM2 (for production deployment)

## Troubleshooting

### API Authentication Issues

If you encounter "401 Unauthorized" errors when making API requests, follow these steps:

1. **Check Authentication Token**:
   - Open browser developer tools (F12)
   - Go to Application tab → Local Storage → Check if `authToken` exists and is valid
   - You can use the auth testing utility by pasting this in the console:
     ```js
     const script = document.createElement('script');
     script.src = '/utils/authTest.js';
     document.head.appendChild(script);
     ```
   - Then use `AuthTest.showInfo()` to check your token status

2. **Set Test Token for Development**:
   - If no token exists, you can set a test token:
     ```js
     localStorage.setItem('authToken', 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************.l5GArb-LzLXqkYeD4--o3vik0t36QmmS_j3iE5zwoFs');
     ```
   - Or use `AuthTest.setToken()` if you loaded the auth testing utility

3. **Start Backend with CORS Enabled**:
   - The frontend at `localhost:4200` may not be able to access the backend at `127.0.0.1:8000` due to CORS restrictions
   - Start the backend with the CORS-enabled script:
     ```bash
     cd backend/builders-warehouse-api
     python run_with_cors.py
     ```

4. **Inspect Network Requests**:
   - In browser dev tools, go to Network tab
   - Check if the `Authorization` header is being sent with your requests
   - The header should look like: `Bearer eyJhbGciOiJIUzI1NiIsIn...`

5. **Clear Cache and Cookies**:
   - Sometimes clearing browser cache and cookies can resolve authentication issues
   - Try using incognito/private browsing mode as well

### CORS Configuration

This frontend application is configured to handle CORS (Cross-Origin Resource Sharing) issues that can occur when the frontend and backend are running on different ports during development. Multiple CORS handling strategies have been implemented:

#### 1. Backend CORS Configuration (Primary Solution)
The backend FastAPI server has been configured with permissive CORS settings for development:
- Allows all origins (`allow_origins=["*"]`)
- Allows all HTTP methods
- Allows all headers
- Credentials disabled (`allow_credentials=False`)

#### 2. Frontend CORS Configuration
Several frontend configurations work together to ensure smooth API communication:

**API Client Settings** (`src/services/apiClient.ts`):
- `credentials: "omit"` - Consistent with backend configuration
- `mode: "cors"` - Explicitly enables CORS requests
- `cache: "no-cache"` - Prevents caching issues

**Fetch Interceptor** (`src/services/apiInterceptor.ts`):
- Global fetch interceptor with CORS-friendly settings
- Automatic authentication header injection
- Enhanced error handling for CORS issues

#### 3. Development Proxy Options
Three proxy options are available for additional CORS protection:

**Option A: Package.json Proxy** (Simple):
```json
"proxy": "http://localhost:8000"
```

**Option B: SetupProxy.js** (Advanced):
- Uses `http-proxy-middleware` for detailed proxy configuration
- Located at `src/setupProxy.js`
- Provides logging and error handling

**Option C: Environment Configuration**:
- Set `REACT_APP_API_URL` to your backend URL
- Configured in `src/config.ts`

#### 4. Troubleshooting CORS Issues

If you still encounter CORS errors:

1. **Check Backend Status**:
   ```bash
   curl -X OPTIONS http://localhost:8000/api/v1/users/me \
     -H "Origin: http://localhost:4200" \
     -H "Access-Control-Request-Method: GET" \
     -v
   ```

2. **Verify Frontend Configuration**:
   - Check browser developer tools → Network tab
   - Look for preflight OPTIONS requests
   - Verify response headers include CORS headers

3. **Common Solutions**:
   - Restart both frontend and backend servers
   - Clear browser cache and localStorage
   - Try incognito/private browsing mode
   - Disable browser extensions temporarily

4. **Development vs Production**:
   - These CORS settings are for development only
   - For production, configure specific allowed origins in the backend
   - Update frontend API URLs for production deployment

#### 5. Environment Variables
Create a `.env.local` file (not committed to git) for local development:
```env
REACT_APP_API_URL=http://localhost:8000
REACT_APP_VERSION=0.1.0
```

### Testing Price List Dropdown Functionality

To test the price list dropdown functionality in the Add Customer modal:

1. **Prerequisites**:
   - Ensure the backend API is running at `http://127.0.0.1:8000`
   - Make sure you're logged in (valid auth token in localStorage)

2. **Test Using Console**:
   - Open your browser developer tools (F12)
   - Navigate to the Console tab
   - Import and run the test function:
     ```js
     import { testPriceListFetch } from './services/priceListService';
     testPriceListFetch();
     ```
   - This will test the API connection and show the results in the console

3. **Visual Testing**:
   - Open the Add Customer modal
   - The dropdown should be populated with price list names from the API
   - If the API fails, it will fall back to default price lists
   - Check browser console for any error messages

4. **Expected Behavior**:
   - The dropdown should show all price lists retrieved from the backend
   - Each option should display the price list name
   - When a price list is selected, its ID is stored internally for the form submission
