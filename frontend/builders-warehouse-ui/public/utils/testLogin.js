/**
 * Test Login Utility
 * 
 * This utility helps test the login functionality directly from the browser console.
 * It uses the original fetch function to avoid interceptor issues.
 */

(function() {
  // Store the original fetch
  const originalFetch = window.fetch;
  const API_URL = 'http://127.0.0.1:8000';
  const AUTH_TOKEN_KEY = 'authToken';
  
  // Test login function
  async function testLogin(username, password) {
    console.log(`Testing login for: ${username}`);
    
    try {
      const loginUrl = `${API_URL}/auth/login`;
      
      // Use URL encoded form data
      const formData = new URLSearchParams();
      formData.append('username', username);
      formData.append('password', password);
      
      // Add marker to avoid recursion
      const response = await originalFetch(loginUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'X-Intercepted-Request': 'true'
        },
        body: formData.toString()
      });
      
      if (!response.ok) {
        const errorText = await response.text();
        console.error(`Login failed: ${response.status} ${response.statusText}`);
        console.error(`Error details: ${errorText}`);
        return false;
      }
      
      const data = await response.json();
      console.log('Login successful!', data);
      
      // Store token if needed
      if (data.access_token) {
        localStorage.setItem(AUTH_TOKEN_KEY, data.access_token);
        console.log('Token stored in localStorage');
      }
      
      return true;
    } catch (error) {
      console.error('Login test failed:', error);
      return false;
    }
  }
  
  // Test direct API call with token
  async function testAuthenticatedCall() {
    const token = localStorage.getItem(AUTH_TOKEN_KEY);
    if (!token) {
      console.warn('No auth token found in localStorage');
      return false;
    }
    
    try {
      const url = `${API_URL}/api/v1/users/me`;
      const response = await originalFetch(url, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'X-Intercepted-Request': 'true'
        }
      });
      
      if (!response.ok) {
        console.error(`API call failed: ${response.status} ${response.statusText}`);
        return false;
      }
      
      const data = await response.json();
      console.log('API call successful!', data);
      return true;
    } catch (error) {
      console.error('API call failed:', error);
      return false;
    }
  }
  
  // Expose functions to window
  window.TestLogin = {
    login: testLogin,
    testApi: testAuthenticatedCall,
    help: function() {
      console.log(`
=== Test Login Utility ===
Available methods:
- TestLogin.login(username, password) - Test login with credentials
- TestLogin.testApi() - Test API call with stored token
`);
    }
  };
  
  // Show help message
  console.log('✅ Test Login Utility loaded successfully!');
  window.TestLogin.help();
})(); 