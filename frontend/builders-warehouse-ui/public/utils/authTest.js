/**
 * Authentication Test Utility
 * 
 * This utility can be loaded into the browser console to test authentication.
 * Usage: 
 * 1. In the browser console, run: const script = document.createElement('script'); script.src = '/utils/authTest.js'; document.head.appendChild(script);
 * 2. Then use the AuthTest object to test authentication: AuthTest.help()
 */

(function() {
  // Store the auth token key in a variable
  const AUTH_TOKEN_KEY = 'authToken';
  
  // Get the token from localStorage
  function getAuthToken() {
    return localStorage.getItem(AUTH_TOKEN_KEY);
  }
  
  // Set a test token in localStorage
  function setTestToken() {
    const token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************.l5GArb-LzLXqkYeD4--o3vik0t36QmmS_j3iE5zwoFs';
    localStorage.setItem(AUTH_TOKEN_KEY, token);
    console.log('Auth token set successfully');
  }
  
  // Remove the token from localStorage
  function clearAuthToken() {
    localStorage.removeItem(AUTH_TOKEN_KEY);
    console.log('Auth token removed');
  }
  
  // Test the API with authentication
  async function testAuthAPI() {
    const token = getAuthToken();
    if (!token) {
      console.warn('No auth token found. Please set a token first with setTestToken()');
      return;
    }
    
    const headers = {
      'Content-Type': 'application/json',
      'Authorization': `Bearer ${token}`
    };
    
    try {
      console.log('Testing API with auth token...');
      const response = await fetch('http://127.0.0.1:8000/api/v1/inventory/?skip=0&limit=10', {
        headers
      });
      
      if (response.ok) {
        const data = await response.json();
        console.log('✅ API call successful!');
        console.log('Response:', data);
      } else {
        console.error('❌ API call failed with status:', response.status);
        const text = await response.text();
        console.error('Error response:', text);
      }
    } catch (error) {
      console.error('❌ API call error:', error);
    }
  }
  
  // Display token info
  function showTokenInfo() {
    const token = getAuthToken();
    if (!token) {
      console.log('No auth token found in localStorage');
      return;
    }
    
    console.log('Auth token:', token);
    
    try {
      // Split token into parts
      const parts = token.split('.');
      if (parts.length === 3) {
        // Decode payload (base64)
        const payload = JSON.parse(atob(parts[1]));
        console.log('Token payload:', payload);
        
        // Check expiration
        if (payload.exp) {
          const expTimestamp = payload.exp * 1000; // Convert to milliseconds
          const expDate = new Date(expTimestamp);
          const now = new Date();
          
          console.log('Token expires:', expDate.toLocaleString());
          console.log('Current time:', now.toLocaleString());
          console.log('Token is expired:', expDate < now);
          
          if (expDate > now) {
            const timeLeft = Math.floor((expTimestamp - Date.now()) / (1000 * 60)); // Minutes
            console.log(`Token expires in: ${timeLeft} minutes`);
          }
        }
      }
    } catch (e) {
      console.error('Error parsing token:', e);
    }
  }
  
  // Expose functions to the global scope
  window.AuthTest = {
    getToken: getAuthToken,
    setToken: setTestToken,
    clearToken: clearAuthToken,
    testAPI: testAuthAPI,
    showInfo: showTokenInfo,
    help: function() {
      console.log(`
=== Auth Test Utility ===
Available methods:
- AuthTest.setToken() - Set a test token
- AuthTest.getToken() - Get the current token
- AuthTest.clearToken() - Remove the token
- AuthTest.testAPI() - Test API with the token
- AuthTest.showInfo() - Show information about the token
`);
    }
  };
  
  // Show help message
  console.log("✅ Auth Test Utility loaded successfully!");
  window.AuthTest.help();
})(); 