# Auth Testing Utilities

This directory contains utilities to help test and debug authentication-related issues.

## authTest.js

This is a utility script that can be loaded into the browser console to test authentication.

### How to Use

1. Open your browser's developer console (F12 or right-click → Inspect → Console)

2. Load the script by running:
   ```js
   const script = document.createElement('script');
   script.src = '/utils/authTest.js';
   document.head.appendChild(script);
   ```

3. Once loaded, you'll have access to the `AuthTest` object with these methods:

   - `AuthTest.help()` - Show available commands
   - `AuthTest.setToken()` - Set a test authentication token
   - `AuthTest.getToken()` - Get the current token
   - `AuthTest.clearToken()` - Remove the token
   - `AuthTest.testAPI()` - Test API with the token
   - `AuthTest.showInfo()` - Show information about the token

### Example Workflow

1. Check if you have a valid token:
   ```js
   AuthTest.showInfo();
   ```

2. If no token exists or it's expired, set a test token:
   ```js
   AuthTest.setToken();
   ```

3. Test if you can access the API with the token:
   ```js
   AuthTest.testAPI();
   ```

## Troubleshooting Common Issues

### 401 Unauthorized Errors

If you're getting 401 errors:

1. Check if your token exists and is valid with `AuthTest.showInfo()`
2. Set a new token with `AuthTest.setToken()`
3. Clear your browser cache and cookies
4. Make sure the backend is running with CORS enabled

### CORS Errors

If you're seeing CORS errors, make sure:

1. The backend is running with CORS enabled:
   ```bash
   cd backend/builders-warehouse-api
   python run_with_cors.py
   ```

2. The frontend and backend domains match the allowed CORS settings 