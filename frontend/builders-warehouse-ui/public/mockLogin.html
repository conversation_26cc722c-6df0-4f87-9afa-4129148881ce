<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mock Login Tool</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 2rem;
            background-color: #f5f5f5;
        }
        .card {
            background-color: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            padding: 2rem;
            margin-bottom: 2rem;
        }
        button {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s;
        }
        button:hover {
            background-color: #45a049;
        }
        code {
            background-color: #f1f1f1;
            padding: 2px 6px;
            border-radius: 4px;
            font-family: monospace;
        }
        .token-display {
            background-color: #f1f1f1;
            padding: 1rem;
            border-radius: 4px;
            font-family: monospace;
            word-break: break-all;
            max-height: 100px;
            overflow-y: auto;
            margin-top: 1rem;
        }
        .success {
            color: #4CAF50;
            font-weight: bold;
        }
        h1 {
            color: #333;
        }
    </style>
</head>
<body>
    <h1>Mock Login Tool</h1>
    
    <div class="card">
        <h2>Authentication Status</h2>
        <div id="auth-status">Checking...</div>
        
        <h3>Current Token</h3>
        <div class="token-display" id="current-token">None</div>
        
        <div style="margin-top: 1rem;">
            <button id="login-btn">Generate New Auth Token</button>
            <button id="logout-btn" style="background-color: #f44336; margin-left: 10px;">Clear Token</button>
        </div>
    </div>
    
    <div class="card">
        <h2>Instructions</h2>
        <p>This tool helps you test the application by creating a mock authentication token. The backend API requires authentication for most endpoints.</p>
        <ol>
            <li>Click <b>Generate New Auth Token</b> to create and store a new mock JWT token</li>
            <li>The token will be stored in localStorage as <code>authToken</code></li>
            <li>Return to the main app and refresh the page</li>
            <li>Your API requests should now include this token automatically</li>
        </ol>
        <p><b>Note:</b> This is for development purposes only. In a real application, you would authenticate through a proper login form.</p>
    </div>

    <script>
        // Function to generate a mock JWT token
        function generateMockToken() {
            // Create a simple mock header
            const header = {
                alg: 'HS256',
                typ: 'JWT'
            };
            
            // Create a mock payload with user data and expiration
            const payload = {
                sub: 'admin',
                name: 'Admin User',
                email: '<EMAIL>',
                role: 'admin',
                exp: Math.floor(Date.now() / 1000) + (60 * 60), // 1 hour from now
                iat: Math.floor(Date.now() / 1000)
            };
            
            // Convert header and payload to base64
            const headerBase64 = btoa(JSON.stringify(header));
            const payloadBase64 = btoa(JSON.stringify(payload));
            
            // Create a signature (this is not cryptographically secure, just for testing)
            const signature = btoa('fake_signature');
            
            // Combine all parts to form the JWT token
            return `${headerBase64}.${payloadBase64}.${signature}`;
        }

        // Function to set the auth token
        function setAuthToken() {
            const token = generateMockToken();
            localStorage.setItem('authToken', token);
            updateUI();
            return token;
        }

        // Function to clear the auth token
        function clearAuthToken() {
            localStorage.removeItem('authToken');
            updateUI();
        }

        // Function to update the UI
        function updateUI() {
            const token = localStorage.getItem('authToken');
            const statusElement = document.getElementById('auth-status');
            const tokenElement = document.getElementById('current-token');
            
            if (token) {
                statusElement.innerHTML = '<span class="success">✓ Authenticated</span>';
                tokenElement.textContent = token;
            } else {
                statusElement.textContent = '❌ Not authenticated';
                tokenElement.textContent = 'None';
            }
        }

        // Add event listeners
        document.getElementById('login-btn').addEventListener('click', setAuthToken);
        document.getElementById('logout-btn').addEventListener('click', clearAuthToken);

        // Update UI on page load
        updateUI();
    </script>
</body>
</html> 