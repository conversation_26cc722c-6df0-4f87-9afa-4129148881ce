import { useAuth } from '../context/AuthContext';

export interface StoreFilterOptions {
  shouldFilterByStore: boolean;
  userStoreType: string | null;
  userStoreTypeId: number | null;
  isStoreRestricted: boolean;
}

/**
 * Custom hook to determine if the current user should have store-based filtering applied
 * and provide store information for filtering
 */
export const useStoreFilter = (): StoreFilterOptions => {
  const { user } = useAuth();

  // Check if user should be restricted to their store
  const isStoreRestricted = user?.role === 'staff' || user?.role === 'manager';
  
  // Check if we should filter by store (user has a store and is restricted)
  const shouldFilterByStore = isStoreRestricted && !!user?.store_type;
  
  // Get user's store information
  const userStoreType = user?.store_type?.name || null;
  const userStoreTypeId = user?.store_type?.id || null;

  return {
    shouldFilterByStore,
    userStoreType,
    userStoreTypeId,
    isStoreRestricted
  };
};

/**
 * Helper function to add store filter to API query parameters
 */
export const addStoreFilterToParams = (
  params: URLSearchParams, 
  storeFilter: StoreFilterOptions
): URLSearchParams => {
  if (storeFilter.shouldFilterByStore && storeFilter.userStoreType) {
    params.append('store_type', storeFilter.userStoreType);
  }
  return params;
};

/**
 * Helper function to get default store type for dropdowns
 */
export const getDefaultStoreType = (storeFilter: StoreFilterOptions) => {
  if (storeFilter.isStoreRestricted && storeFilter.userStoreTypeId) {
    return {
      id: storeFilter.userStoreTypeId,
      name: storeFilter.userStoreType || '',
      disabled: true // Should be disabled for staff/manager
    };
  }
  return null;
}; 