import { useState, useCallback } from 'react';

interface ToastOptions {
  duration?: number;
  type?: 'success' | 'error' | 'info' | 'warning';
}

export const useToast = () => {
  const [isVisible, setIsVisible] = useState(false);
  const [message, setMessage] = useState('');
  const [type, setType] = useState<'success' | 'error' | 'info' | 'warning'>('info');

  const showToast = useCallback((newMessage: string, options: ToastOptions = {}) => {
    const { duration = 3000, type: newType = 'info' } = options;
    
    setMessage(newMessage);
    setType(newType);
    setIsVisible(true);

    setTimeout(() => {
      setIsVisible(false);
    }, duration);
  }, []);

  return {
    showToast,
    isVisible,
    message,
    type
  };
};

export default useToast; 