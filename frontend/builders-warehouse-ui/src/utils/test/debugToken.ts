import { setAuthToken } from '../auth';

/**
 * Debug function to set the authentication token directly.
 * Only use this during development.
 */
export const setDebugToken = (): void => {
  const token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************.l5GArb-LzLXqkYeD4--o3vik0t36QmmS_j3iE5zwoFs';
  setAuthToken(token);
  console.log('Debug token set:', token);
};

// You can manually call this function in the browser console:
// import { setDebugToken } from './utils/debugToken';
// setDebugToken(); 