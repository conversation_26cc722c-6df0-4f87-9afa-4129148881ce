// This is a simple script that you can copy into your browser console 
// to manually set the JWT token for testing

function setAuthToken() {
  const token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************.l5GArb-LzLXqkYeD4--o3vik0t36QmmS_j3iE5zwoFs';
  localStorage.setItem('authToken', token);
  console.log('Auth token set successfully');
  console.log('You can now try your API calls');
}

// Call the function - you can run this in your browser console
// by copying the entire file content and pasting it there
setAuthToken(); 