/**
 * Debug Auth Utilities
 * 
 * This file contains utilities to help debug authentication issues
 * Don't use in production, this is for development debugging only
 */

import { AUTH_TOKEN_KEY } from '../config';

/**
 * Logs the current auth state to the console
 */
export function logAuthState() {
  const token = localStorage.getItem(AUTH_TOKEN_KEY);
  console.log('==== Auth Debug Info ====');
  console.log('Auth token exists:', !!token);
  
  if (token) {
    try {
      // Get token parts
      const parts = token.split('.');
      if (parts.length === 3) {
        // Decode the payload (middle part)
        const payload = JSON.parse(atob(parts[1]));
        console.log('Token payload:', payload);
        
        // Check expiration
        if (payload.exp) {
          const expDate = new Date(payload.exp * 1000);
          const now = new Date();
          console.log('Token expires:', expDate.toLocaleString());
          console.log('Current time:', now.toLocaleString());
          console.log('Token is expired:', expDate < now);
        }
      }
    } catch (e) {
      console.error('Error decoding token:', e);
    }
  }
  console.log('========================');
}

/**
 * Sets a test token for development purposes
 */
export function setTestToken() {
  // This is a sample token with a long expiration for testing only
  const token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************.l5GArb-LzLXqkYeD4--o3vik0t36QmmS_j3iE5zwoFs';
  localStorage.setItem(AUTH_TOKEN_KEY, token);
  console.log('Test auth token set successfully');
  logAuthState();
}

/**
 * Clears the authentication token
 */
export function clearAuthToken() {
  localStorage.removeItem(AUTH_TOKEN_KEY);
  console.log('Auth token removed');
}

/**
 * Test the authentication with a simple API call
 */
export async function testAuthAPICall() {
  try {
    const response = await fetch('http://127.0.0.1:8000/api/v1/inventory/?skip=0&limit=1', {
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${localStorage.getItem(AUTH_TOKEN_KEY)}`
      }
    });
    
    if (response.ok) {
      const data = await response.json();
      console.log('Auth test successful!', data);
      return true;
    } else {
      console.error('Auth test failed!', response.status, response.statusText);
      const text = await response.text();
      console.error('Response:', text);
      return false;
    }
  } catch (error) {
    console.error('Auth test error:', error);
    return false;
  }
}

// Export all functions as a default object
export default {
  logAuthState,
  setTestToken,
  clearAuthToken,
  testAuthAPICall,
}; 