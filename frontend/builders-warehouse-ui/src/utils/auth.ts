import { AUTH_TOKEN_KEY } from "../config";

/**
 * Get the current authentication token
 */
export const getAuthToken = (): string | null => {
  return localStorage.getItem(AUTH_TOKEN_KEY);
};

/**
 * Set the authentication token
 * @param token The JWT token to set
 */
export const setAuthToken = (token: string): void => {
  localStorage.setItem(AUTH_TOKEN_KEY, token);
};

/**
 * Clear the authentication token
 */
export const clearAuthToken = (): void => {
  localStorage.removeItem(AUTH_TOKEN_KEY);
};

/**
 * Check if the user is authenticated
 */
export const isAuthenticated = (): boolean => {
  return !!getAuthToken();
};

export default {
  getAuthToken,
  setAuthToken,
  clearAuthToken,
  isAuthenticated
}; 