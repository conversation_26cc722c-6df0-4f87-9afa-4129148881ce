import React, { ComponentType, useCallback } from 'react';
import { useError } from '../components/common/ErrorHandler';
import { ApiError } from '../services/apiClient';
import { useAuth } from '../context/AuthContext';

/**
 * Higher-order component that adds API error handling to a component
 *
 * @param WrappedComponent The component to wrap with error handling
 * @returns A new component with error handling
 */
// Type for components wrapped with error handling
export interface WithErrorHandlingProps {
  handleApiCall: <T>(
    apiCall: () => Promise<T>,
    errorMessage?: string
  ) => Promise<T | null>;
}

export function withErrorHandling<P extends object>(
  WrappedComponent: ComponentType<P & WithErrorHandlingProps>
): React.FC<P> {
  const WithErrorHandling: React.FC<P> = (props) => {
    const { showError } = useError();
    const { logout } = useAuth();

    // Create a handler for API calls
    const handleApiCall = useCallback(async <T,>(
      apiCall: () => Promise<T>,
      errorMessage: string = 'An error occurred while processing your request.'
    ): Promise<T | null> => {
      try {
        return await apiCall();
      } catch (error) {
        console.error('API call error:', error);

        if (error instanceof ApiError) {
          // Handle authentication errors
          if (error.isAuthError) {
            showError('Your session has expired. Please log in again.', 'Authentication Error');
            logout();
            return null;
          }

          // Show specific error message from API
          showError(error.message, 'API Error');
        } else {
          // Show generic error message
          showError(errorMessage);
        }

        return null;
      }
    }, [showError, logout]);

    // Pass the handler to the wrapped component
    return <WrappedComponent {...props as P} handleApiCall={handleApiCall} />;
  };

  // Set display name for debugging
  const displayName = WrappedComponent.displayName || WrappedComponent.name || 'Component';
  WithErrorHandling.displayName = `withErrorHandling(${displayName})`;

  return WithErrorHandling;
}

export default withErrorHandling;
