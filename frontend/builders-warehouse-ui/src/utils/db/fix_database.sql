-- Fix for missing sale_type column in invoices table
-- Run this SQL in your PostgreSQL database to fix the 500 error when fetching invoices

-- Check if the column already exists first to avoid errors
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT FROM information_schema.columns 
        WHERE table_name='invoices' AND column_name='sale_type'
    ) THEN
        -- Add the missing column with 'trade' as the default value
        ALTER TABLE invoices ADD COLUMN sale_type VARCHAR(10) NOT NULL DEFAULT 'trade';
        
        -- Output a success message
        RAISE NOTICE 'Column sale_type added successfully to invoices table';
    ELSE
        -- Column already exists
        RAISE NOTICE 'Column sale_type already exists in invoices table';
    END IF;
END$$; 