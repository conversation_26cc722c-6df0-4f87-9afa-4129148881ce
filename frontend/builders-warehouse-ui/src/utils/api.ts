import { API_URL, AUTH_TOKEN_KEY } from '../config';
import { ApiError } from '../services/apiClient';

/**
 * Base API URL with the API version prefix
 */
export const API_BASE_URL = `${API_URL}/api/v1`;

/**
 * Builds a complete API URL from a path
 *
 * @param path - API endpoint path (without the base URL)
 * @returns Full API URL
 */
export const getApiUrl = (path: string): string => {
  // Remove leading slash if present to avoid double slashes
  const cleanPath = path.startsWith('/') ? path.substring(1) : path;
  return `${API_BASE_URL}/${cleanPath}`;
};

/**
 * Default fetch options for API calls
 */
const defaultOptions: RequestInit = {
  headers: {
    'Content-Type': 'application/json',
  },
};

// Store the original fetch to avoid recursion issues
const originalFetch = window.fetch;

/**
 * Make an authenticated API request
 *
 * @param path - API endpoint path
 * @param options - Fetch options
 * @param token - Authentication token (optional)
 * @returns Promise with the response
 */
export const apiRequest = async <T>(
  path: string,
  options: RequestInit = {},
  token?: string | null
): Promise<T> => {
  const url = getApiUrl(path);

  // Create headers object from defaults and options
  const mergedHeaders = new Headers(defaultOptions.headers);

  // Add headers from options
  if (options.headers) {
    const optionsHeaders = new Headers(options.headers);
    optionsHeaders.forEach((value, key) => {
      mergedHeaders.set(key, value);
    });
  }

  // Add authorization header if token is provided
  if (token) {
    mergedHeaders.set('Authorization', `Bearer ${token}`);
  }
  
  // Add marker to prevent recursion if interceptor is active
  mergedHeaders.set('X-Intercepted-Request', 'true');
  
  const fullOptions: RequestInit = {
    ...defaultOptions,
    ...options,
    headers: mergedHeaders,
  };

  console.log(`API Request: ${options.method || 'GET'} ${url}`);

  try {
    // Use original fetch to avoid interceptor recursion
    const response = await originalFetch(url, fullOptions);
    
    if (!response.ok) {
      console.error(`API Error: ${response.status} ${response.statusText}`);

      // Try to parse error message from response
      let errorMessage = `API Error: ${response.status} ${response.statusText}`;
      try {
        const errorData = await response.json();
        if (errorData && errorData.detail) {
          errorMessage = errorData.detail;
        }
      } catch (e) {
        // If we can't parse JSON, use the default error message
      }

      throw new ApiError(errorMessage, response.status);
    }

    // For HEAD and DELETE requests that don't return data
    if (options.method === 'HEAD' || (options.method === 'DELETE' && response.status === 204)) {
      return {} as T;
    }

    const data = await response.json();
    return data as T;
  } catch (error) {
    console.error('API Request failed:', error);
    throw error;
  }
};

/**
 * Get the stored authentication token
 */
export const getAuthToken = (): string | null => {
  return localStorage.getItem(AUTH_TOKEN_KEY);
};

/**
 * API utilities for common operations
 */
export const api = {
  /**
   * GET request
   */
  get: <T>(path: string, token?: string | null): Promise<T> => {
    return apiRequest<T>(path, { method: 'GET' }, token);
  },

  /**
   * POST request
   */
  post: <T>(path: string, data: any, token?: string | null): Promise<T> => {
    return apiRequest<T>(
      path,
      {
        method: 'POST',
        body: JSON.stringify(data),
      },
      token
    );
  },

  /**
   * PUT request
   */
  put: <T>(path: string, data: any, token?: string | null): Promise<T> => {
    return apiRequest<T>(
      path,
      {
        method: 'PUT',
        body: JSON.stringify(data),
      },
      token
    );
  },

  /**
   * DELETE request
   */
  delete: <T>(path: string, token?: string | null): Promise<T> => {
    return apiRequest<T>(path, { method: 'DELETE' }, token);
  },

  /**
   * Form submission (application/x-www-form-urlencoded)
   */
  submitForm: <T>(path: string, formData: Record<string, string>): Promise<T> => {
    return apiRequest<T>(
      path,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
        body: new URLSearchParams(formData).toString(),
      }
    );
  },
};

export default api;