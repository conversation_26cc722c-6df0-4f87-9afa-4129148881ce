import { AUTH_TOKEN_KEY, API_URL, ENDPOINTS } from '../config';

/**
 * Auth Checker
 * 
 * This utility checks for potential authentication issues and provides solutions
 */

/**
 * Check for authentication token and validate it
 * @returns {boolean} - Whether the auth token is valid
 */
export function checkAuthToken() {
  const token = localStorage.getItem(AUTH_TOKEN_KEY);
  if (!token) {
    console.warn('No authentication token found in localStorage. API calls requiring authentication will fail.');
    return false;
  }

  try {
    // Parse the token to check its validity
    const parts = token.split('.');
    if (parts.length !== 3) {
      console.warn('Authentication token is malformed. API calls requiring authentication will fail.');
      return false;
    }

    // Try to decode the token payload
    const payload = JSON.parse(atob(parts[1]));
    
    // Check expiration
    if (payload.exp) {
      const expDate = new Date(payload.exp * 1000);
      const now = new Date();
      
      if (expDate < now) {
        console.warn('Authentication token is expired. API calls requiring authentication will fail.');
        return false;
      }
    }
    
    // Token exists and is not expired
    return true;
  } catch (e) {
    console.warn('Failed to parse authentication token. API calls requiring authentication may fail.', e);
    return false;
  }
}

/**
 * Test an API endpoint with the current authentication token
 * @returns {Promise<boolean>} - Whether the API call was successful
 */
export async function testAuthenticatedAPI() {
  const token = localStorage.getItem(AUTH_TOKEN_KEY);
  if (!token) {
    return false;
  }

  try {
    const response = await fetch(`${API_URL}${ENDPOINTS.CURRENT_USER}`, {
      headers: {
        'Authorization': `Bearer ${token}`
      }
    });
    
    return response.ok;
  } catch (error) {
    console.error('API test failed:', error);
    return false;
  }
}

/**
 * Set a development test token for debugging
 */
export function setDevelopmentToken() {
  const token = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.************************************************************************.l5GArb-LzLXqkYeD4--o3vik0t36QmmS_j3iE5zwoFs';
  localStorage.setItem(AUTH_TOKEN_KEY, token);
  console.log('Development auth token set successfully');
}

/**
 * Check for potential issues during app initialization
 */
export function checkAuthIssues() {
  if (process.env.NODE_ENV === 'development') {
    // Only run these checks in development mode
    const hasValidToken = checkAuthToken();
    
    if (!hasValidToken) {
      console.warn('====== AUTH WARNING ======');
      console.warn('No valid authentication token found. API calls requiring authentication will fail.');
      console.warn('You can set a development token by calling:');
      console.warn('  import { setDevelopmentToken } from "./utils/authChecker";');
      console.warn('  setDevelopmentToken();');
      console.warn('==========================');
    }
  }
}

export default {
  checkAuthToken,
  testAuthenticatedAPI,
  setDevelopmentToken,
  checkAuthIssues
}; 