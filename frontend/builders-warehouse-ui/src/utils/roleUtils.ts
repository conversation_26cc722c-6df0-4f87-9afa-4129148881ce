/**
 * Utility functions for handling user roles
 */
import { User } from '../context/AuthContext';

/**
 * Generic function to get role from an object that might have role property
 * as either a string or an object with a name property
 * @param obj Any object with a role property
 * @returns string representing the role name or null if not available
 */
export const getRoleString = (obj: any): string | null => {
  try {
    if (!obj) return null;
    
    // Check if the role exists
    if (!obj.role) return null;
    
    // Handle simple string role
    if (typeof obj.role === 'string') {
      return obj.role.toLowerCase();
    }
    
    // Handle object role with name property
    if (typeof obj.role === 'object' && obj.role !== null) {
      // Format: { name: 'admin' }
      if ('name' in obj.role && typeof obj.role.name === 'string') {
        return obj.role.name.toLowerCase();
      }
      
      // Format: { id: 1, role: 'admin' }
      if ('role' in obj.role && typeof obj.role.role === 'string') {
        return obj.role.role.toLowerCase();
      }
      
      // Format: { id: 1, value: 'admin' }
      if ('value' in obj.role && typeof obj.role.value === 'string') {
        return obj.role.value.toLowerCase();
      }
    }
    
    // Additional edge case handling - stringifying objects
    if (typeof obj.role === 'object' && obj.role !== null) {
      // Try to stringify the object and extract useful information
      const roleStr = String(obj.role);
      if (roleStr.includes('admin')) return 'admin';
      if (roleStr.includes('manager')) return 'manager';
      if (roleStr.includes('staff')) return 'staff';
    }
    
    // If none of the above patterns match, return default 'staff'
    return 'staff';
  } catch (error) {
    console.error('Error in getRoleString:', error);
    return 'staff'; // Return default role on error
  }
};

/**
 * Check if a user has a specific role
 * @param user The user object
 * @param roleName The role name to check for
 * @returns boolean indicating if the user has the specified role
 */
export const hasRole = (user: User | null, roleName: string): boolean => {
  if (!user) return false;
  
  try {
    const role = getRoleString(user);
    return role === roleName.toLowerCase();
  } catch (error) {
    console.error('Error in hasRole:', error);
    return false;
  }
};

/**
 * Check if a user is an admin
 * @param user The user object
 * @returns boolean indicating if the user is an admin
 */
export const isAdmin = (user: User | null): boolean => {
  if (!user) return false;
  return hasRole(user, 'admin');
};

/**
 * Check if a user is a manager
 * @param user The user object
 * @returns boolean indicating if the user is a manager
 */
export const isManager = (user: User | null): boolean => {
  if (!user) return false;
  return hasRole(user, 'manager');
};

/**
 * Check if a user is staff
 * @param user The user object
 * @returns boolean indicating if the user is staff
 */
export const isStaff = (user: User | null): boolean => {
  if (!user) return false;
  return hasRole(user, 'staff');
}; 