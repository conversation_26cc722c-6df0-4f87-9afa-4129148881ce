const { createProxyMiddleware } = require('http-proxy-middleware');

module.exports = function(app) {
  // Proxy API requests to the backend server
  app.use(
    '/api',
    createProxyMiddleware({
      target: 'http://localhost:8000',
      changeOrigin: true,
      secure: false,
      logLevel: 'debug',
      onProxyReq: function (proxyReq, req, res) {
        console.log('Proxying request:', req.method, req.path);
      },
      onProxyRes: function (proxyRes, req, res) {
        console.log('Proxy response:', proxyRes.statusCode, req.path);
      },
      onError: function (err, req, res) {
        console.error('Proxy error:', err.message);
      }
    })
  );
}; 