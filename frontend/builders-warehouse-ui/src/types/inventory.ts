export interface InventoryItem {
  id: number;
  sku_code: string;
  style_code: string;
  supplier_name: string;
  description: string;
  category: string;
  unit_type: string;
  cost_price: number;
  retail_price: number;
  trade_price: number;
  stock_on_hand: number;
  carton: string;
  units_per_carton: number;
  carton_length: number;
  carton_breadth: number;
  carton_height: number;
  weight_per_unit: number;
  weight_per_carton: number;
  units_per_pallet: number;
  pallet_weight: number;
  notes?: string;
  created_at: string;
  updated_at: string;
  created_by: number;
  updated_by: number;
} 