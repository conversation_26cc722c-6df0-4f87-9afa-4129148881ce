// API configuration
export const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:8000';

// App version
export const APP_VERSION = process.env.REACT_APP_VERSION || '0.1.0';

// CORS and Development configuration
export const CORS_CONFIG = {
  // For development, we allow all origins and disable credentials
  mode: 'cors' as RequestMode,
  credentials: 'omit' as RequestCredentials,
  cache: 'no-cache' as RequestCache,
};

// Authentication configuration
export const AUTH_TOKEN_KEY = 'authToken';
export const USER_DATA_KEY = 'currentUser';

// Retry configuration
export const RETRY_CONFIG = {
  MAX_RETRIES: 3,
  RETRY_DELAY: 1000, // 1 second delay between retries
};

// API endpoints
export const ENDPOINTS = {
  LOGIN: '/api/v1/auth/login',
  CURRENT_USER: '/api/v1/users/me',
  USERS: '/api/v1/users/',
  PURCHASE_ORDERS: '/api/v1/purchase-orders',
  INVOICES: '/api/v1/invoices',
  INVOICES_WITH_QUOTES: '/api/v1/invoices-with-quotes',
  CUSTOMERS: '/api/v1/customers',
  PRICE_LISTS: '/api/v1/price-lists',
  SUPPLIERS: '/api/v1/suppliers/',
  ROLES: '/api/v1/roles',
  USER_ROLES: '/api/v1/roles',
  STORE_TYPES: '/api/v1/store-types/',
  MANAGERS: '/api/v1/users/managers',
  GLOBAL_SEARCH: '/api/v1/search',
};

const config = {
  API_URL,
  APP_VERSION,
  AUTH_TOKEN_KEY,
  USER_DATA_KEY,
  RETRY_CONFIG,
  ENDPOINTS,
};

export default config; 