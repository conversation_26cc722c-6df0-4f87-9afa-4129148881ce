import React from 'react';
import { render, screen } from '@testing-library/react';

// Mock the App component to avoid router dependencies
jest.mock('./App', () => () => <div data-testid="app-component">Mocked App</div>);

import App from './App';

test('renders without crashing', () => {
  render(<App />);
  const appElement = screen.getByTestId('app-component');
  expect(appElement).toBeInTheDocument();
});
