/**
 * Mock Authentication Helper
 * 
 * This script generates a fake JWT token for testing and sets it in localStorage
 * Run this in your browser console when you need to authenticate for development
 */

// Function to generate a mock JWT token
function generateMockToken() {
  // Create a simple mock header
  const header = {
    alg: 'HS256',
    typ: 'JWT'
  };
  
  // Create a mock payload with user data and expiration
  const payload = {
    sub: 'admin',
    name: 'Admin User',
    email: '<EMAIL>',
    role: 'admin',
    exp: Math.floor(Date.now() / 1000) + (60 * 60), // 1 hour from now
    iat: Math.floor(Date.now() / 1000)
  };
  
  // Convert header and payload to base64
  const headerBase64 = btoa(JSON.stringify(header));
  const payloadBase64 = btoa(JSON.stringify(payload));
  
  // Create a signature (this is not cryptographically secure, just for testing)
  const signature = btoa('fake_signature');
  
  // Combine all parts to form the JWT token
  return `${headerBase64}.${payloadBase64}.${signature}`;
}

// Store the token in localStorage
function setAuthToken() {
  const token = generateMockToken();
  localStorage.setItem('authToken', token);
  console.log('Auth token set successfully!');
  console.log('Token:', token);
  return token;
}

// Export the functions for use in browser
window.setAuthToken = setAuthToken;
window.generateMockToken = generateMockToken;

// Auto-set the token when this script runs
setAuthToken(); 