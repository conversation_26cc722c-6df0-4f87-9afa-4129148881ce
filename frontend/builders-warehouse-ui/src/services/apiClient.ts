import { API_URL, AUTH_TOKEN_KEY } from "../config";

// Store recent request timestamps to prevent duplicates
const recentRequests = new Map<string, number>();
const DEBOUNCE_TIME = 600; // Increase from 300ms to 600ms

// Track request counts to detect infinite loops
const requestCountByEndpoint = new Map<string, number>();
const RESET_INTERVAL = 10000; // Reset counters every 10 seconds
const MAX_REQUESTS_PER_ENDPOINT = 15; // Allow up to 15 requests to the same endpoint within RESET_INTERVAL

// Cache for GET requests to prevent unnecessary duplicate calls
const responseCache = new Map<string, { data: any; timestamp: number }>();
const CACHE_TTL = 60000; // Cache lifetime of 60 seconds for most endpoints
const USERS_CACHE_TTL = 15000; // Shorter cache time for users endpoint (15 seconds)

// Periodically reset the request counter
setInterval(() => {
  requestCountByEndpoint.clear();
}, RESET_INTERVAL);

// Periodically clean up cache to avoid memory leaks
setInterval(() => {
  const now = Date.now();
  responseCache.forEach((value, key) => {
    // Determine cache TTL based on endpoint
    const ttl = key.includes("/users") ? USERS_CACHE_TTL : CACHE_TTL;
    if (now - value.timestamp > ttl) {
      responseCache.delete(key);
    }
  });
}, 30000); // Clean up every 30 seconds

// Custom error class for API errors
export class ApiError extends Error {
  status: number;
  isAuthError: boolean;

  constructor(message: string, status: number) {
    super(message);
    this.name = "ApiError";
    this.status = status;
    this.isAuthError = status === 401;
  }
}

interface RequestOptions {
  method?: "GET" | "POST" | "PUT" | "DELETE" | "PATCH";
  headers?: Record<string, string>;
  body?: any;
  requiresAuth?: boolean;
  isFormData?: boolean;
  isMultipartFormData?: boolean;
}

/**
 * Checks if the response is an ngrok security page
 */
const isNgrokSecurityPage = (text: string): boolean => {
  return (
    text.includes("ngrok.com") &&
    (text.includes("ERR_NGROK_6024") ||
      text.includes("Tunnel") ||
      text.includes("ngrok Error"))
  );
};

/**
 * Validates the stored auth token
 */
const isValidToken = (token: string): boolean => {
  try {
    // Basic JWT structure validation
    const parts = token.split(".");
    if (parts.length !== 3) return false;

    // Check if token is expired
    const payload = JSON.parse(atob(parts[1]));
    if (payload.exp && payload.exp * 1000 < Date.now()) {
      console.warn("Auth token is expired");
      localStorage.removeItem(AUTH_TOKEN_KEY);
      return false;
    }

    return true;
  } catch (e) {
    console.warn("Invalid token format:", e);
    return false;
  }
};

/**
 * Handles API requests with appropriate authentication
 * @param endpoint The API endpoint to call (without base URL)
 * @param options Request options including method, headers, body
 * @returns The response data
 */
export const apiRequest = async <T>(
  endpoint: string,
  options: RequestOptions = {},
  retryCount = 0,
  redirectCount = 0
): Promise<T> => {
  const MAX_RETRIES = 3;
  const MAX_REDIRECTS = 5;
  const RETRY_DELAY = 1000; // 1 second

  // Create a unique key for this request to track duplicates
  // Only include the request path (without query parameters) in the key for GET requests
  // This avoids treating pagination requests as the same request
  const method = options.method || "GET";
  const isGetRequest = method === "GET";

  // Create a more robust key for duplicate detection
  // For GET requests: path+query params (no timestamp params)
  // For mutations: method+path+body
  let requestKey;
  let cacheKey = "";

  if (isGetRequest) {
    // Extract path and query params
    const [path, queryString] = endpoint.split("?");

    // Create a cache key that excludes timestamps and cache busters
    if (queryString) {
      // Filter out _t and timestamp parameters for the cache key
      const cleanedParams = queryString
        .split("&")
        .filter(
          (param) =>
            !param.startsWith("_t=") &&
            !param.startsWith("_=") &&
            !param.startsWith("timestamp=")
        )
        .join("&");

      cacheKey = `GET-${path}${cleanedParams ? "?" + cleanedParams : ""}`;

      // For debounce key, include uniqueness factors
      const timestampParam = queryString
        .split("&")
        .find((param) => param.startsWith("_=") || param.startsWith("_t="));
      requestKey = `GET-${path}-${timestampParam || Date.now()}`;
    } else {
      cacheKey = `GET-${path}`;
      requestKey = `GET-${path}-${Date.now()}`;
    }

    // Check cache for GET requests when not retrying
    if (retryCount === 0 && cacheKey) {
      const cachedResponse = responseCache.get(cacheKey);

      // Determine cache TTL based on endpoint
      const ttl = cacheKey.includes("/users") ? USERS_CACHE_TTL : CACHE_TTL;

      if (cachedResponse && Date.now() - cachedResponse.timestamp < ttl) {
        // console.log(`Cache hit for ${cacheKey}`, cachedResponse.data);
        return cachedResponse.data as T;
      } else if (cachedResponse) {
        console.log(`Cache expired for ${cacheKey}, fetching fresh data`);
        responseCache.delete(cacheKey);
      }
    }
  } else {
    // For non-GET requests, include method, full path, and body JSON
    const bodyString = options.body
      ? typeof options.body === "string"
        ? options.body
        : JSON.stringify(options.body)
      : "";
    requestKey = `${method}-${endpoint}-${bodyString}`;
  }

  // Create a base endpoint key for tracking request counts (exclude query params and timestamps)
  const baseEndpointKey = isGetRequest
    ? `GET-${endpoint.split("?")[0]}`
    : `${method}-${endpoint.split("?")[0]}`;

  // Increment the request count for this endpoint
  const currentCount = requestCountByEndpoint.get(baseEndpointKey) || 0;
  requestCountByEndpoint.set(baseEndpointKey, currentCount + 1);

  // Check if this endpoint is experiencing an infinite loop
  if (currentCount >= MAX_REQUESTS_PER_ENDPOINT) {
    console.warn(
      `Potential infinite loop detected for endpoint ${baseEndpointKey}, blocking request`
    );
    throw new ApiError(
      `Too many requests to ${baseEndpointKey} in a short time period. Please try again later.`,
      429
    );
  }

  // Special handling for invoice endpoints which seem to have more issues with infinite loops
  if (baseEndpointKey.includes("/invoices") && isGetRequest) {
    // Set a lower threshold for invoice endpoints
    const INVOICE_MAX_REQUESTS = 8; // Less strict for invoice endpoints

    if (currentCount >= INVOICE_MAX_REQUESTS) {
      console.warn(
        `Invoice endpoint protection: Too many requests to ${baseEndpointKey}`
      );
      throw new ApiError(
        `Rate limited: Too many invoice requests in short succession. Please try again in a moment.`,
        429
      );
    }

    // Add extra random delay for invoice endpoints to help break synchronization issues
    const delay = Math.floor(Math.random() * 100) + 25; // 25-125ms random delay
    await new Promise((resolve) => setTimeout(resolve, delay));
  }

  // Check if we've made this exact request recently
  const lastRequestTime = recentRequests.get(requestKey);
  const now = Date.now();

  if (lastRequestTime && now - lastRequestTime < DEBOUNCE_TIME) {
    console.log(
      `Skipping duplicate request to ${endpoint} (made ${
        now - lastRequestTime
      }ms ago)`
    );
    // For GET requests, throw an error that will be caught by the caller
    // For mutations, return a success response
    if (isGetRequest) {
      throw new ApiError("Request debounced to prevent duplicates", 429);
    } else {
      return { success: true } as T;
    }
  }

  // Store the timestamp of this request
  recentRequests.set(requestKey, now);

  // Clean up old request timestamps (keep map size manageable)
  if (recentRequests.size > 100) {
    const keysToDelete = [];
    // Convert Map entries to Array before iterating to avoid TS2802 error
    const entries = Array.from(recentRequests.entries());
    for (const [key, timestamp] of entries) {
      if (now - timestamp > 10000) {
        // Remove entries older than 10 seconds
        keysToDelete.push(key);
      }
    }
    keysToDelete.forEach((key) => recentRequests.delete(key));
  }

  // Continue with the regular request logic
  const token = localStorage.getItem(AUTH_TOKEN_KEY);
  const requiresAuth = options.requiresAuth !== false;

  const {
    headers = {},
    body,
    isFormData = false,
    isMultipartFormData = false,
  } = options;

  // Set up request headers
  const requestHeaders: Record<string, string> = {
    ...headers, // Include any custom headers passed in
  };

  // Set Accept header for all requests, but only set Content-Type for non-multipart requests
  requestHeaders["Accept"] = "application/json";

  if (!isMultipartFormData) {
    requestHeaders["Content-Type"] = isFormData
      ? "application/x-www-form-urlencoded"
      : "application/json";
  }
  // For multipart form data, let the browser set Content-Type automatically

  // Always add Authorization header for authenticated requests
  if (requiresAuth) {
    if (token && isValidToken(token)) {
      requestHeaders["Authorization"] = `Bearer ${token}`;
    } else {
      console.error(
        `API call to ${endpoint} requires authentication, but no valid token found`
      );
      throw new ApiError("Authentication required", 401);
    }
  }

  // Build request options
  const requestOptions: RequestInit = {
    method,
    headers: requestHeaders,
    credentials: "omit", // Changed from "include" to match backend CORS config
    mode: "cors",
    cache: "no-cache",
  };

  // Add body for non-GET requests if provided
  if (method !== "GET" && body) {
    if (isMultipartFormData) {
      // For FormData objects, don't modify them
      requestOptions.body = body;
      console.log("Setting multipart form data body:", {
        bodyType: body.constructor.name,
        isFormData: body instanceof FormData,
        entries:
          body instanceof FormData
            ? Array.from(body.entries()).map(([key, value]) => ({
                key,
                value:
                  value instanceof File
                    ? `File: ${value.name} (${value.size} bytes, ${value.type})`
                    : value,
              }))
            : "Not FormData",
      });
    } else if (isFormData) {
      const formData = new URLSearchParams();
      Object.entries(body).forEach(([key, value]) => {
        formData.append(key, String(value));
      });
      requestOptions.body = formData.toString();
    } else {
      requestOptions.body =
        typeof body === "string" ? body : JSON.stringify(body);
    }
  }

  try {
    const apiUrl = API_URL || window.location.origin;
    const fullUrl = `${apiUrl}${endpoint}`;

    console.log(`Making ${method} request to ${endpoint}`, {
      url: fullUrl,
      method,
      headers: requestHeaders,
    });

    // Make the request - using normal fetch with redirect='follow'
    const response = await fetch(fullUrl, {
      ...requestOptions,
      redirect: "follow", // Allow browser to handle redirects automatically
    });

    // Log response details
    console.log(`Response received from ${endpoint}:`, {
      status: response.status,
      statusText: response.statusText,
      headers: Object.fromEntries(response.headers),
    });

    // Handle 401 Unauthorized
    if (response.status === 401) {
      localStorage.removeItem(AUTH_TOKEN_KEY);
      throw new ApiError("Authentication token expired or invalid", 401);
    }

    // Handle other error status codes
    if (!response.ok) {
      // Try to get error message from response
      let errorMessage = `API Error: ${response.status} ${response.statusText}`;

      try {
        const errorData = await response.json();
        if (errorData.detail) {
          errorMessage = errorData.detail;
        } else if (errorData.message) {
          errorMessage = errorData.message;
        }
      } catch (e) {
        // Ignore error parsing error
      }

      throw new ApiError(errorMessage, response.status);
    }

    // Parse the response
    let responseData: T;

    try {
      // First check if the response body is empty
      const text = await response.text();
      if (!text) {
        console.warn("Empty response body from API");
        return {} as T;
      }

      // Check for ngrok security page
      if (isNgrokSecurityPage(text)) {
        throw new ApiError(
          "Tunnel is not running. Please check your ngrok connection.",
          502
        );
      }

      // Try to parse as JSON
      try {
        responseData = JSON.parse(text);
      } catch (e) {
        console.error("Failed to parse response as JSON:", e);
        console.log(
          "Response text:",
          text.substring(0, 500) + (text.length > 500 ? "..." : "")
        );
        throw new ApiError("Invalid JSON response", 500);
      }

      // Cache successful GET responses
      if (method === "GET" && cacheKey) {
        responseCache.set(cacheKey, {
          data: responseData,
          timestamp: Date.now(),
        });
        console.log(`Cached response for ${cacheKey}`);
      }

      return responseData;
    } catch (parseError) {
      console.error("Error parsing response:", parseError);
      throw parseError;
    }
  } catch (error) {
    if (error instanceof ApiError) {
      throw error;
    }

    // Handle network errors
    if (error instanceof TypeError && error.message === "Failed to fetch") {
      console.error(`Network error for ${endpoint}:`, error);

      // Retry on network error
      if (retryCount < MAX_RETRIES) {
        console.log(`Retrying request (${retryCount + 1}/${MAX_RETRIES})...`);
        await new Promise((resolve) =>
          setTimeout(resolve, RETRY_DELAY * (retryCount + 1))
        );
        return apiRequest<T>(endpoint, options, retryCount + 1, redirectCount);
      }

      throw new ApiError(
        "Unable to connect to the server. Please check your internet connection.",
        0
      );
    }

    console.error(`Unexpected error for ${endpoint}:`, error);
    throw new ApiError("An unexpected error occurred", 500);
  }
};

const apiClient = {
  get: <T>(
    endpoint: string,
    options: Omit<RequestOptions, "method" | "body"> = {}
  ) => apiRequest<T>(endpoint, { ...options, method: "GET" }),
  post: <T>(
    endpoint: string,
    data: any,
    options: Omit<RequestOptions, "method" | "body"> = {}
  ) => apiRequest<T>(endpoint, { ...options, method: "POST", body: data }),
  postForm: <T>(
    endpoint: string,
    data: any,
    options: Omit<RequestOptions, "method" | "body"> = {}
  ) =>
    apiRequest<T>(endpoint, {
      ...options,
      method: "POST",
      body: data,
      isFormData: true,
    }),
  postMultipartForm: <T>(
    endpoint: string,
    formData: FormData,
    options: Omit<RequestOptions, "method" | "body"> = {}
  ) =>
    apiRequest<T>(endpoint, {
      ...options,
      method: "POST",
      body: formData,
      isMultipartFormData: true,
    }),
  put: <T>(
    endpoint: string,
    data: any,
    options: Omit<RequestOptions, "method" | "body"> = {}
  ) => apiRequest<T>(endpoint, { ...options, method: "PUT", body: data }),
  delete: <T>(
    endpoint: string,
    options: Omit<RequestOptions, "method" | "body"> = {}
  ) => apiRequest<T>(endpoint, { ...options, method: "DELETE" }),
  patch: <T>(
    endpoint: string,
    data: any,
    options: Omit<RequestOptions, "method" | "body"> = {}
  ) => apiRequest<T>(endpoint, { ...options, method: "PATCH", body: data }),
  apiRequest, // Expose the base request function if needed
};

export default apiClient;
