import apiClient from "./apiClient";
import { AUTH_TOKEN_KEY, API_URL } from "../config";

// Define interfaces for Inventory-related data
export interface Inventory {
  id: number;
  sku_code: string;
  style_code: string;
  description: string;
  supplier_name: string;
  category: string;
  unit_type: string;
  cost_price: number;
  retail_price: number;
  trade_price: number;
  stock_on_hand: number;
  created_at: string;
  updated_at: string;
  created_by: number;
  updated_by: number;
  carton: string;
  units_per_carton: number;
  carton_dimensions: string;
  carton_length: number;
  carton_breadth: number;
  carton_height: number;
  weight_per_unit: number;
  weight_per_carton: number;
  units_per_pallet: number;
  pallet_weight: number;
  notes: string;
  image_path?: string;
}

// Backend inventory interface that matches the actual API response
interface BackendInventory {
  id: number;
  sku_code: string;
  style_code: string;
  description: string;
  supplier_name: string;
  category: string;
  unit_type: string;
  cost_price: number;
  retail_price: number;
  trade_price: number;
  stock_on_hand: number;
  created_at: string;
  updated_at: string;
  created_by: number;
  updated_by: number;
  carton: string | number;
  units_per_carton: number;
  carton_dimensions: string;
  weight_per_unit: number;
  weight_per_carton: number;
  units_per_pallet: number;
  pallet_weight: number;
  notes: string;
  image_path?: string;
}

export interface InventoryCreate {
  sku_code: string;
  style_code: string;
  description: string;
  supplier_name: string;
  category: string;
  unit_type: string;
  cost_price: number;
  retail_price: number;
  trade_price: number;
  stock_on_hand: number;
  carton: string;
  units_per_carton: number;
  carton_length: number;
  carton_breadth: number;
  carton_height: number;
  weight_per_unit: number;
  weight_per_carton: number;
  units_per_pallet: number;
  pallet_weight: number;
  notes: string;
}

export interface InventoryUpdate {
  sku_code?: string;
  style_code?: string;
  description?: string;
  supplier_name?: string;
  category?: string;
  unit_type?: string;
  cost_price?: number;
  retail_price?: number;
  trade_price?: number;
  stock_on_hand?: number;
  carton?: string;
  units_per_carton?: number;
  carton_length?: number;
  carton_breadth?: number;
  carton_height?: number;
  weight_per_unit?: number;
  weight_per_carton?: number;
  units_per_pallet?: number;
  pallet_weight?: number;
  notes?: string;
}

export interface CSVImportResult {
  success: number;
  errors: number;
  error_details: string[];
}

export interface InventoryFilter {
  skip?: number;
  limit?: number;
  supplier_name?: string;
  sku_code?: string;
  style_code?: string;
  category?: string;
}

// Helper function to parse carton dimensions
const parseCartonDimensions = (
  dimensionsStr: string | null
): { length: number; breadth: number; height: number } => {
  if (!dimensionsStr) {
    return { length: 0, breadth: 0, height: 0 };
  }

  // Try to parse dimensions in format "LxBxH" or "L x B x H"
  // (e.g., "40x30x25 cm" or "40 x 30 x 25 cm")
  const dimensionsRegex = /(\d+)\s*x\s*(\d+)\s*x\s*(\d+)/i;
  const match = dimensionsStr.match(dimensionsRegex);

  if (match && match.length >= 4) {
    return {
      length: parseInt(match[1], 10) || 0,
      breadth: parseInt(match[2], 10) || 0,
      height: parseInt(match[3], 10) || 0,
    };
  }

  return { length: 0, breadth: 0, height: 0 };
};

// Define pagination response interface
export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
}

export interface InventoryQueryParams {
  page?: number;
  limit?: number;
  search?: string;
  category?: string;
  supplier_name?: string;
}

export interface ImageUploadResult {
  success: Array<{
    file: string;
    sku: string;
  }>;
  errors: Array<{
    file: string;
    error: string;
  }>;
}

export interface SkuImageInfo {
  sku: string;
  has_image: boolean;
  image_path?: string;
  filename?: string;
}

// Add caching for inventory items
const inventoryCache = new Map<
  string,
  {
    timestamp: number;
    data: Inventory[];
  }
>();

const CACHE_DURATION = 30000; // 30 seconds cache

// Helper function to transform backend inventory to frontend format
const transformInventoryItem = (item: any): Inventory | null => {
  if (!item) return null;

  // Parse carton dimensions if they exist
  let dimensions = { length: 0, breadth: 0, height: 0 };
  if (item.carton_dimensions) {
    dimensions = parseCartonDimensions(item.carton_dimensions);
  }

  return {
    ...item,
    description: item.description || "",
    category: item.category || "",
    unit_type: item.unit_type || "",
    cost_price: item.cost_price || 0,
    retail_price: item.retail_price || 0,
    trade_price: item.trade_price || 0,
    stock_on_hand: item.stock_on_hand || 0,
    carton:
      typeof item.carton === "number"
        ? item.carton.toString()
        : item.carton || "1",
    carton_length: dimensions.length,
    carton_breadth: dimensions.breadth,
    carton_height: dimensions.height,
    notes: item.notes || "",
    image_path: item.image_path ? item.image_path.trim() : undefined,
  };
};

// Create the service for handling Inventory-related API calls
const inventoryService = {
  // Get paginated inventory items with total count
  getInventory: async (
    params: InventoryQueryParams = {}
  ): Promise<PaginatedResponse<Inventory>> => {
    try {
      const { page = 1, limit = 10, search, category, supplier_name } = params;

      // Calculate skip based on page number
      const skip = (page - 1) * limit;

      let queryParams = new URLSearchParams();
      queryParams.append("skip", skip.toString());
      queryParams.append("limit", limit.toString());
      if (search) queryParams.append("search", search);
      if (category) queryParams.append("category", category);
      if (supplier_name) queryParams.append("supplier_name", supplier_name);

      const endpoint = `/api/v1/inventory/items?${queryParams.toString()}`;
      const response = await apiClient.get<any>(endpoint);

      // Check if response has items property
      if (response && "items" in response && Array.isArray(response.items)) {
        const items = response.items
          .map(transformInventoryItem)
          .filter(Boolean) as Inventory[];

        return {
          items,
          total: response.total || 0,
          page,
          limit,
        };
      }

      // Fallback for direct array response
      if (Array.isArray(response)) {
        const items = response
          .map(transformInventoryItem)
          .filter(Boolean) as Inventory[];

        return {
          items,
          total: items.length,
          page,
          limit,
        };
      }

      return {
        items: [],
        total: 0,
        page,
        limit,
      };
    } catch (error) {
      console.error("Error fetching inventory:", error);
      throw error;
    }
  },

  // Get inventory items with filtering and caching
  getInventoryItems: async (
    filters: InventoryFilter = {}
  ): Promise<Inventory[]> => {
    try {
      // Generate cache key based on filters
      const cacheKey = JSON.stringify(filters);
      const cachedData = inventoryCache.get(cacheKey);
      const now = Date.now();

      // Return cached data if it's still valid
      if (cachedData && now - cachedData.timestamp < CACHE_DURATION) {
        console.log("Using cached inventory data for filters:", filters);
        return cachedData.data;
      }

      const {
        skip = 0,
        limit = 100,
        supplier_name,
        sku_code,
        style_code,
        category,
      } = filters;

      let queryParams = new URLSearchParams();
      queryParams.append("skip", skip.toString());
      queryParams.append("limit", limit.toString());
      if (supplier_name) queryParams.append("supplier_name", supplier_name);
      if (sku_code) queryParams.append("sku_code", sku_code);
      if (style_code) queryParams.append("style_code", style_code);
      if (category) queryParams.append("category", category);

      console.log(`Fetching inventory items with filters:`, filters);

      const endpoint = `/api/v1/inventory/?${queryParams.toString()}`;
      const response = await apiClient.get<any>(endpoint);

      let inventoryItems: any[] = [];

      // Handle different response formats
      if (response && "items" in response && Array.isArray(response.items)) {
        inventoryItems = response.items;
      } else if (Array.isArray(response)) {
        inventoryItems = response;
      }

      if (inventoryItems.length === 0) {
        console.warn("No inventory items found in API response");
        return [];
      }

      console.log(`Processing ${inventoryItems.length} inventory items`);

      // Transform backend inventory items to frontend format
      const transformedItems = inventoryItems
        .map(transformInventoryItem)
        .filter(Boolean) as Inventory[];

      // Cache the result
      inventoryCache.set(cacheKey, {
        timestamp: now,
        data: transformedItems,
      });

      // Clean up old cache entries
      if (inventoryCache.size > 50) {
        const keysToDelete: string[] = [];
        inventoryCache.forEach((value, key) => {
          if (now - value.timestamp > CACHE_DURATION * 2) {
            keysToDelete.push(key);
          }
        });
        keysToDelete.forEach((key) => inventoryCache.delete(key));
      }

      return transformedItems;
    } catch (error) {
      console.error("Error fetching inventory items:", error);
      throw error;
    }
  },

  // Get all inventory items (simplified method for dropdowns)
  getAllInventory: async (): Promise<Inventory[]> => {
    try {
      // Check if we have cached data for all inventory
      const cacheKey = "all_inventory";
      const cachedData = inventoryCache.get(cacheKey);
      const now = Date.now();

      if (cachedData && now - cachedData.timestamp < CACHE_DURATION) {
        console.log("Using cached all inventory data");
        return cachedData.data;
      }

      // Use getInventoryItems with a reasonable limit for dropdowns
      const items = await inventoryService.getInventoryItems({ limit: 200 });

      // Cache the result
      inventoryCache.set(cacheKey, {
        timestamp: now,
        data: items,
      });

      return items;
    } catch (error) {
      console.error("Error fetching all inventory items:", error);
      throw error;
    }
  },

  // Count inventory items with filtering
  countInventoryItems: async (
    filters: Omit<InventoryFilter, "skip" | "limit"> = {}
  ): Promise<number> => {
    try {
      const { supplier_name, sku_code, style_code, category } = filters;

      let queryParams = new URLSearchParams();
      if (supplier_name) queryParams.append("supplier_name", supplier_name);
      if (sku_code) queryParams.append("sku_code", sku_code);
      if (style_code) queryParams.append("style_code", style_code);
      if (category) queryParams.append("category", category);

      const endpoint = `/api/v1/inventory/count?${queryParams.toString()}`;
      const response = await apiClient.get<{ total: number }>(endpoint);
      return response.total;
    } catch (error) {
      console.error("Error counting inventory items:", error);
      throw error;
    }
  },

  // Get an inventory item by ID
  getInventoryById: async (id: number): Promise<Inventory> => {
    try {
      const response = await apiClient.get<any>(`/api/v1/inventory/${id}`);
      const transformed = transformInventoryItem(response);
      if (!transformed) {
        throw new Error(`Invalid inventory data for ID ${id}`);
      }
      return transformed;
    } catch (error) {
      console.error(`Error fetching inventory by ID ${id}:`, error);
      throw error;
    }
  },

  // Get an inventory item by SKU code
  getInventoryBySku: async (skuCode: string): Promise<Inventory> => {
    try {
      const response = await apiClient.get<any>(
        `/api/v1/inventory/sku/${skuCode}`
      );
      const transformed = transformInventoryItem(response);
      if (!transformed) {
        throw new Error(`Invalid inventory data for SKU ${skuCode}`);
      }
      return transformed;
    } catch (error) {
      console.error(`Error fetching inventory by SKU ${skuCode}:`, error);
      throw error;
    }
  },

  // Create a new inventory item
  createInventory: async (
    inventoryData: InventoryCreate
  ): Promise<Inventory> => {
    try {
      // Create a copy of the data for modification
      const backendData: any = { ...inventoryData };

      // Convert carton dimensions to backend format
      if (
        inventoryData.carton_length ||
        inventoryData.carton_breadth ||
        inventoryData.carton_height
      ) {
        backendData.carton_dimensions = `${inventoryData.carton_length || 0}x${
          inventoryData.carton_breadth || 0
        }x${inventoryData.carton_height || 0} cm`;
      }

      // Remove frontend-specific fields that don't exist in backend
      delete backendData.carton_length;
      delete backendData.carton_breadth;
      delete backendData.carton_height;

      const response = await apiClient.post<any>(
        "/api/v1/inventory/",
        backendData
      );
      const transformed = transformInventoryItem(response);
      if (!transformed) {
        throw new Error("Invalid response from create inventory API");
      }
      return transformed;
    } catch (error) {
      console.error("Error creating inventory:", error);
      throw error;
    }
  },

  // Update an existing inventory item
  updateInventory: async (
    id: number,
    inventoryData: InventoryUpdate
  ): Promise<Inventory> => {
    try {
      // Create a copy of the data for modification
      const backendData: any = { ...inventoryData };

      // Convert carton dimensions to backend format if any dimension is provided
      if (
        inventoryData.carton_length !== undefined ||
        inventoryData.carton_breadth !== undefined ||
        inventoryData.carton_height !== undefined
      ) {
        backendData.carton_dimensions = `${inventoryData.carton_length || 0}x${
          inventoryData.carton_breadth || 0
        }x${inventoryData.carton_height || 0} cm`;
      }

      // Remove frontend-specific fields that don't exist in backend
      delete backendData.carton_length;
      delete backendData.carton_breadth;
      delete backendData.carton_height;

      const response = await apiClient.put<any>(
        `/api/v1/inventory/${id}`,
        backendData
      );
      const transformed = transformInventoryItem(response);
      if (!transformed) {
        throw new Error(
          `Invalid response from update inventory API for ID ${id}`
        );
      }
      return transformed;
    } catch (error) {
      console.error(`Error updating inventory ${id}:`, error);
      throw error;
    }
  },

  // Delete an inventory item
  deleteInventory: async (id: number): Promise<boolean> => {
    try {
      await apiClient.delete(`/api/v1/inventory/${id}`);
      return true;
    } catch (error) {
      console.error(`Error deleting inventory ${id}:`, error);
      throw error;
    }
  },

  // Upload inventory items from a CSV or Excel file
  uploadInventoryFile: async (
    file: File | FormData
  ): Promise<CSVImportResult> => {
    try {
      let formData: FormData;

      if (file instanceof FormData) {
        formData = file;
      } else {
        formData = new FormData();
        formData.append("file", file);
      }

      // Get the auth token
      const token = localStorage.getItem(AUTH_TOKEN_KEY);
      if (!token) {
        throw new Error("No authentication token found");
      }

      // Use fetch directly for file upload with proper headers
      const response = await fetch(`${API_URL}/api/v1/inventory/upload`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${token}`,
          // Don't set Content-Type header - let browser set it with boundary for FormData
        },
        body: formData,
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Upload failed: ${response.status} ${errorText}`);
      }

      const result = await response.json();
      return result;
    } catch (error) {
      console.error("Error uploading inventory file:", error);
      throw error;
    }
  },

  // Upload SKU images (up to 10 at once)
  uploadSkuImages: async (files: File[]): Promise<ImageUploadResult> => {
    try {
      if (files.length === 0) {
        throw new Error("No files selected");
      }

      if (files.length > 10) {
        throw new Error("Maximum 10 files allowed");
      }

      const formData = new FormData();
      files.forEach((file, index) => {
        formData.append("files", file);
      });

      // Get the auth token
      const token = localStorage.getItem(AUTH_TOKEN_KEY);
      if (!token) {
        throw new Error("No authentication token found");
      }

      // Use fetch directly for file upload
      const response = await fetch(
        `${API_URL}/api/v1/inventory/upload-images-bulk`,
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${token}`,
            // Don't set Content-Type header - let browser set it with boundary for FormData
          },
          body: formData,
        }
      );

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Upload failed: ${response.status} ${errorText}`);
      }

      const result = await response.json();
      return result;
    } catch (error) {
      console.error("Error uploading SKU images:", error);
      throw error;
    }
  },

  // Get image info for a specific SKU
  getSkuImageInfo: async (skuCode: string): Promise<SkuImageInfo> => {
    try {
      const response = await apiClient.get<SkuImageInfo>(
        `/api/v1/inventory/sku-image/${skuCode}`
      );
      return response;
    } catch (error) {
      console.error(`Error fetching image info for SKU ${skuCode}:`, error);
      throw error;
    }
  },

  // Clear inventory cache (useful after image uploads)
  clearInventoryCache: () => {
    inventoryCache.clear();
    console.log("Inventory cache cleared");
  },

  // Force refresh inventory data for specific SKUs (useful after image uploads)
  refreshInventoryForSkus: async (skuCodes: string[]): Promise<void> => {
    try {
      // Clear cache first
      inventoryCache.clear();

      // Fetch fresh data for each SKU
      for (const skuCode of skuCodes) {
        try {
          await inventoryService.getInventoryBySku(skuCode);
        } catch (error) {
          console.warn(
            `Failed to refresh inventory for SKU ${skuCode}:`,
            error
          );
        }
      }

      console.log(`Refreshed inventory data for SKUs: ${skuCodes.join(", ")}`);
    } catch (error) {
      console.error("Error refreshing inventory for SKUs:", error);
    }
  },
};

export default inventoryService;
