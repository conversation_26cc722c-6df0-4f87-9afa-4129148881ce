import apiClient from './apiClient';

// Define interfaces for StoreType
export interface StoreType {
  id: number;
  name: string;
  description?: string;
}

// Default store types to use when API fails
const DEFAULT_STORE_TYPES: StoreType[] = [
  { id: 1, name: 'Cran<PERSON>' },
  { id: 2, name: 'Sale' }
];

// Create the service for handling StoreType-related API calls
const storeTypeService = {
  // Get all store types
  getStoreTypes: async (): Promise<StoreType[]> => {
    try {
      return await apiClient.get<StoreType[]>('/api/v1/store-types/');
    } catch (error) {
      console.error('Error fetching store types:', error);
      // Return default types if API fails
      return DEFAULT_STORE_TYPES;
    }
  },

  // Get a store type by ID
  getStoreTypeById: async (id: number): Promise<StoreType> => {
    try {
      return await apiClient.get<StoreType>(`/api/v1/store-types/${id}`);
    } catch (error) {
      console.error(`Error fetching store type with ID ${id}:`, error);
      // Return a default store type if the API fails
      const defaultType = DEFAULT_STORE_TYPES.find(type => type.id === id) || DEFAULT_STORE_TYPES[0];
      return defaultType;
    }
  }
};

export default storeTypeService; 