import apiClient from './apiClient';
import { API_URL, ENDPOINTS } from '../config';

// Interface for authentication response
export interface AuthResponse {
  access_token: string;
  token_type: string;
}

// Interface for user data
export interface UserData {
  id: number;
  username: string;
  email: string;
  is_active: boolean;
}

/**
 * Logs in a user with the provided credentials
 * @param username The username (email) of the user
 * @param password The user's password
 * @returns An object containing the access token and token type
 */
export const login = async (username: string, password: string): Promise<AuthResponse> => {
  const formData = new URLSearchParams();
  formData.append('username', username);
  formData.append('password', password);
  
  const response = await fetch(`${API_URL}${ENDPOINTS.LOGIN}`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/x-www-form-urlencoded',
      'Accept': '*/*',
      'Origin': 'http://localhost:4200',
      'Referer': 'http://localhost:4200/',
    },
    credentials: 'include',
    body: formData.toString(),
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ detail: 'Login failed' }));
    console.error('Login error:', errorData);
    throw new Error(errorData.detail || 'Login failed');
  }

  return response.json();
};

/**
 * Fetches the current user's data using the access token
 * @param token The access token
 * @returns The user's data
 */
export const getCurrentUser = async (token: string): Promise<UserData> => {
  return apiClient.get<UserData>(ENDPOINTS.CURRENT_USER, {
    headers: {
      'Authorization': `Bearer ${token}`
    },
    requiresAuth: false
  });
};

/**
 * Determines the user's role based on their permissions
 * For simplicity, we're assuming all authenticated users are admin for now
 * This would be replaced with actual role checks based on your API
 */
export const getUserRole = (userData: UserData): 'admin' | 'staff' => {
  // In a real application, you would determine the role based on user data
  // For now, we'll assume all users are admin
  return 'admin';
};

export default {
  login,
  getCurrentUser,
  getUserRole,
}; 