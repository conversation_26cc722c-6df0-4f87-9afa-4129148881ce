import apiClient from "./apiClient";
import { getAuthToken } from "../utils/auth";
import { API_URL, ENDPOINTS } from "../config";

// Define interface for API errors
interface ApiError {
  response?: {
    status: number;
    data: any;
  };
  message?: string;
  code?: string;
}

// Supplier interfaces
export interface SupplierBase {
  supplier_name: string;
  phone_no: string;
  email: string;
  address: string;
  price_list?: PriceItem[];
}

export interface SKU {
  id: number;
  code: string;
}

export interface PriceItem {
  sku: SKU;
  description: string;
  style_code?: string;
  rrp_ex_gst?: number;
  bwa_buy_price_50_ex_gst?: number;
  // Calculated fields
  regional_retail_aud_incl?: number;
  regional_trade_aud_incl?: number;
  metro_retail_aud_incl?: number;
  metro_trade_aud_incl?: number;
  regional_retail_vip_aud_incl?: number | null;
  regional_trade_vip1_aud_incl?: number | null;
  regional_trade_vip2_aud_incl?: number | null;
  metro_retail_vip_aud_incl?: number | null;
  metro_trade_vip1_aud_incl?: number | null;
  metro_trade_vip2_aud_incl?: number | null;
  status?: boolean;
}

export interface Supplier {
  id: number;
  name: string;
  supplier_name: string;
  phone: string;
  phone_no: string;
  email: string;
  address: string;
  price_list?: any[];
  is_active?: boolean;
}

// Filter interface for listing suppliers
export interface SupplierFilter {
  skip?: number;
  limit?: number;
  search?: string;
  is_active?: boolean;
}

// Define interfaces for Supplier
export interface PaginatedSuppliers {
  items: Supplier[];
  total: number;
  page: number;
  limit: number;
  data?: Supplier[]; // Backward compatibility for existing components
}

// Store recent API responses to prevent duplicate network requests
const supplierCache = new Map<
  string,
  {
    timestamp: number;
    data: PaginatedSuppliers;
  }
>();

// Create the service for handling Supplier-related API calls
const supplierService = {
  // Get all suppliers with pagination
  getSuppliers: async (
    skipOrFilter: number | SupplierFilter = 0,
    limit: number = 10,
    search?: string
  ): Promise<PaginatedSuppliers> => {
    try {
      // Build query params
      let params = new URLSearchParams();

      // Handle both old and new parameter styles
      if (typeof skipOrFilter === "number") {
        // Original style: separate parameters
        params.append("skip", skipOrFilter.toString());
        params.append("limit", limit.toString());

        if (search) {
          params.append("search", search);
        }
      } else {
        // Filter object style
        const filter = skipOrFilter;
        params.append("skip", (filter.skip || 0).toString());
        params.append("limit", (filter.limit || 10).toString());

        if (filter.search) {
          params.append("search", filter.search);
        }

        if (filter.is_active !== undefined) {
          params.append("is_active", filter.is_active.toString());
        }
      }

      // Use the configured API URL with the SUPPLIERS endpoint
      const proxyUrl = `${API_URL}${ENDPOINTS.SUPPLIERS}?${params.toString()}`;
      console.log("Using supplier API URL:", proxyUrl);

      // Create a cache key based on the URL
      const cacheKey = proxyUrl;

      // Check if we have cached data for this request that's less than 10 seconds old
      const cachedData = supplierCache.get(cacheKey);
      const now = Date.now();
      if (cachedData && now - cachedData.timestamp < 10000) {
        // 10 second cache
        console.log(`Using cached supplier data for: ${proxyUrl}`);
        return cachedData.data;
      }

      // Make API call using fetch instead of apiClient to bypass base URL configuration
      const token = getAuthToken();
      const response = await fetch(proxyUrl, {
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        throw new Error(`API error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      console.log("Supplier proxy response:", data);

      // Map fields for backward compatibility
      const result = {
        ...data,
        data: data.items || [], // Add data field for backward compatibility
      };

      // Store in cache
      supplierCache.set(cacheKey, {
        timestamp: now,
        data: result,
      });

      // Clean up old cache entries if the cache gets too large
      if (supplierCache.size > 100) {
        const keysToDelete = [];
        // Convert Map entries to Array before iterating to avoid TS2802 error
        const entries = Array.from(supplierCache.entries());
        for (const [key, value] of entries) {
          if (now - value.timestamp > 60000) {
            // Remove entries older than 1 minute
            keysToDelete.push(key);
          }
        }
        keysToDelete.forEach((key) => supplierCache.delete(key));
      }

      return result;
    } catch (error) {
      console.error("Error fetching suppliers:", error);

      // Log additional details about the error
      const apiError = error as ApiError;
      if (apiError.response) {
        console.error("API Error Response:", {
          status: apiError.response.status,
          data: apiError.response.data,
        });
      }

      // Return empty result set instead of default suppliers
      const result = {
        items: [],
        total: 0,
        page: 1,
        limit: 10,
        data: [], // Backward compatibility
      };

      return result;
    }
  },

  // Get a supplier by ID (supports both new and old method names)
  getSupplier: async (id: number): Promise<Supplier> => {
    try {
      const token = getAuthToken();
      console.log(`Fetching supplier with ID: ${id}`);
      console.log(`Using URL: ${API_URL}${ENDPOINTS.SUPPLIERS}${id}`);

      const response = await fetch(`${API_URL}${ENDPOINTS.SUPPLIERS}${id}`, {
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        console.error(`Failed to fetch supplier: HTTP ${response.status}`);
        // Try to get more details about the error
        try {
          const errorText = await response.text();
          console.error("Error response body:", errorText);
          throw new Error(
            `API error: ${response.status} ${response.statusText}. Details: ${errorText}`
          );
        } catch (parseError) {
          throw new Error(
            `API error: ${response.status} ${response.statusText}`
          );
        }
      }

      const data = await response.json();
      console.log("Supplier data retrieved successfully:", data);

      // Ensure all fields are defined with fallbacks
      return {
        id: data.id,
        name: data.name || data.supplier_name || "",
        supplier_name: data.supplier_name || data.name || "",
        phone: data.phone || data.phone_no || "",
        phone_no: data.phone_no || data.phone || "",
        email: data.email || "",
        address: data.address || "",
        price_list: data.price_list || [],
        is_active: data.is_active !== undefined ? data.is_active : true,
      };
    } catch (error) {
      console.error(`Error fetching supplier ${id}:`, error);

      // Log additional details about the error
      const apiError = error as ApiError;
      if (apiError.response) {
        console.error("API Error Response:", {
          status: apiError.response.status,
          data: apiError.response.data,
        });
      }

      // Return an empty supplier with the requested ID
      return {
        id: id,
        name: "",
        supplier_name: "",
        phone: "",
        phone_no: "",
        email: "",
        address: "",
        price_list: [],
        is_active: true,
      };
    }
  },

  // Maintain backward compatibility with older method name
  getSupplierById: async (id: number): Promise<Supplier> => {
    return supplierService.getSupplier(id);
  },

  // Create a supplier
  createSupplier: async (
    supplierData: SupplierBase | Omit<Supplier, "id">
  ): Promise<Supplier> => {
    try {
      // Format data to ensure compatibility with backend
      const formattedData = {
        // Use supplier_name/name field (handle both formats)
        supplier_name:
          "supplier_name" in supplierData
            ? supplierData.supplier_name
            : "name" in supplierData
            ? (supplierData as any).name
            : "",
        // Use phone_no/phone field (handle both formats)
        phone_no:
          "phone_no" in supplierData
            ? supplierData.phone_no
            : "phone" in supplierData
            ? (supplierData as any).phone
            : "",
        email: supplierData.email || "",
        address: supplierData.address || "",
        price_list: "price_list" in supplierData ? supplierData.price_list : [],
      };

      // Use fetch with configured API_URL and ENDPOINTS
      const token = getAuthToken();
      const response = await fetch(`${API_URL}${ENDPOINTS.SUPPLIERS}`, {
        method: "POST",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formattedData),
      });

      if (!response.ok) {
        throw new Error(`API error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();

      // Ensure consistent fields in the response
      return {
        ...data,
        name: data.name || data.supplier_name || "",
        supplier_name: data.supplier_name || data.name || "",
        phone: data.phone || data.phone_no || "",
        phone_no: data.phone_no || data.phone || "",
        email: data.email || "",
        address: data.address || "",
      };
    } catch (error) {
      const apiError = error as ApiError;
      console.error("Error creating supplier:", apiError);
      throw error;
    }
  },

  // Update a supplier
  updateSupplier: async (
    id: number,
    supplierData: Partial<SupplierBase> | Partial<Supplier>
  ): Promise<Supplier> => {
    try {
      // Format data to ensure compatibility with backend
      const formattedData: any = {};

      // Handle name field (both formats)
      if (
        "supplier_name" in supplierData &&
        supplierData.supplier_name !== undefined
      ) {
        formattedData.supplier_name = supplierData.supplier_name;
      } else if (
        "name" in supplierData &&
        (supplierData as any).name !== undefined
      ) {
        formattedData.supplier_name = (supplierData as any).name;
      }

      // Handle phone field (both formats)
      if ("phone_no" in supplierData && supplierData.phone_no !== undefined) {
        formattedData.phone_no = supplierData.phone_no;
      } else if (
        "phone" in supplierData &&
        (supplierData as any).phone !== undefined
      ) {
        formattedData.phone_no = (supplierData as any).phone;
      }

      // Copy other fields
      if ("email" in supplierData && supplierData.email !== undefined) {
        formattedData.email = supplierData.email;
      }

      if ("address" in supplierData && supplierData.address !== undefined) {
        formattedData.address = supplierData.address;
      }

      if (
        "price_list" in supplierData &&
        supplierData.price_list !== undefined
      ) {
        formattedData.price_list = supplierData.price_list;
      }

      // Use fetch with configured API_URL
      const token = getAuthToken();
      const response = await fetch(`${API_URL}${ENDPOINTS.SUPPLIERS}/${id}`, {
        method: "PUT",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formattedData),
      });

      if (!response.ok) {
        throw new Error(`API error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();

      // Ensure consistent field names in the response
      return {
        ...data,
        name: data.name || data.supplier_name || "",
        supplier_name: data.supplier_name || data.name || "",
        phone: data.phone || data.phone_no || "",
        phone_no: data.phone_no || data.phone || "",
        email: data.email || "",
        address: data.address || "",
      };
    } catch (error) {
      const apiError = error as ApiError;
      console.error(`Error updating supplier ${id}:`, apiError);
      throw error;
    }
  },

  // Delete a supplier
  deleteSupplier: async (id: number): Promise<void> => {
    try {
      // Use fetch with configured API_URL
      const token = getAuthToken();
      const response = await fetch(`${API_URL}${ENDPOINTS.SUPPLIERS}/${id}`, {
        method: "DELETE",
        headers: {
          Authorization: `Bearer ${token}`,
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        throw new Error(`API error: ${response.status} ${response.statusText}`);
      }
    } catch (error) {
      const apiError = error as ApiError;
      console.error(`Error deleting supplier ${id}:`, apiError);
      throw error;
    }
  },

  // Get a supplier by ID using the new API endpoint
  getSupplierData: async (supplierId: number | string): Promise<Supplier> => {
    try {
      // Use the configured API_URL
      const token = getAuthToken();
      const response = await fetch(
        `${API_URL}${ENDPOINTS.SUPPLIERS}/${supplierId}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
        }
      );

      if (!response.ok) {
        throw new Error(`API error: ${response.status} ${response.statusText}`);
      }

      const data = await response.json();
      console.log("Supplier data response:", data);

      // Map the API response to the Supplier interface
      return {
        id: data.id,
        name: data.name || data.supplier_name || "",
        supplier_name: data.supplier_name || data.name || "",
        phone: data.phone || data.phone_no || "",
        phone_no: data.phone_no || data.phone || "",
        email: data.email || "",
        address: data.address || "",
        price_list: data.price_list || [],
        is_active: data.is_active !== undefined ? data.is_active : true,
      };
    } catch (error) {
      console.error(
        `Error fetching supplier data for ID ${supplierId}:`,
        error
      );

      // Return an empty supplier with the requested ID
      return {
        id:
          typeof supplierId === "string"
            ? parseInt(supplierId, 10)
            : supplierId,
        name: "",
        supplier_name: "",
        phone: "",
        phone_no: "",
        email: "",
        address: "",
        price_list: [],
        is_active: true,
      };
    }
  },
};

export default supplierService;
