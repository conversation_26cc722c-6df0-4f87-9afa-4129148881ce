import apiClient from './apiClient';
import { API_URL, ENDPOINTS } from '../config';

// Define the search result interface
export interface GlobalSearchResult {
  id: string;
  title: string;
  subtitle?: string;
  route: string;
  category: string;
  icon?: string;
  searchParam: string;
  searchField?: string; // The field to search in on the destination page
}

// Define the search options interface
export interface GlobalSearchOptions {
  includeCustomers?: boolean;
  includeInvoices?: boolean;
  includeInventory?: boolean;
  includeQuotes?: boolean;
  includeSuppliers?: boolean;
  includeUsers?: boolean;
  includePurchaseOrders?: boolean;
  limit?: number;
}

// Define backend response interfaces
interface BackendSearchData {
  id: string;
  type: string;
  // Common fields
  name?: string;
  // User fields
  user_name?: string;
  email?: string;
  mobile_number?: string;
  role?: string;
  // Invoice fields
  invoice_number?: string;
  customer_name?: string;
  date?: string;
  total_amount?: number;
  status?: string;
  // Supplier fields
  phone?: string;
  contact_person?: string;
  // Purchase Order fields
  supplier_name?: string;
  issue_date?: string;
  // Customer fields
  phone_no?: string;
  company_name?: string;
  // Inventory fields
  sku_code?: string;
  style_code?: string;
  carton?: string;
  units_per_carton?: number;
  carton_dimensions?: string;
}

interface BackendSearchModule {
  module: string;
  data: BackendSearchData[];
}

interface BackendSearchResponse {
  query: string;
  page: number;
  limit: number;
  results: BackendSearchModule[];
}

/**
 * Convert backend search results to frontend format
 */
const convertBackendResults = (backendResults: BackendSearchModule[]): GlobalSearchResult[] => {
  const frontendResults: GlobalSearchResult[] = [];

  backendResults.forEach(module => {
    module.data.forEach(item => {
      let result: GlobalSearchResult;

      switch (item.type) {
        case 'user':
          result = {
            id: `user-${item.id}`,
            title: item.user_name || item.email || 'Unknown User',
            subtitle: `${item.email || ''} | ${item.role || ''}`.trim().replace(/^\||\|$/g, ''),
            route: '/users',
            category: 'Users',
            searchParam: item.user_name || item.email || '',
          };
          break;

        case 'customer':
          result = {
            id: `customer-${item.id}`,
            title: item.company_name || item.name || 'Unknown Customer',
            subtitle: `${item.email || ''} | ${item.phone || ''}`.trim().replace(/^\||\|$/g, ''),
            route: '/customers',
            category: 'Customers',
            searchParam: item.company_name || item.name || '',
          };
          break;

        case 'invoice':
          result = {
            id: `invoice-${item.id}`,
            title: item.invoice_number || 'Unknown Invoice',
            subtitle: `${item.customer_name || ''} | $${item.total_amount?.toFixed(2) || '0.00'}`.trim().replace(/^\||\|$/g, ''),
            route: '/invoices',
            category: 'Invoices',
            searchParam: item.invoice_number || item.customer_name || '',
            searchField: item.invoice_number ? 'invoiceNo' : 'customerName'
          };
          break;

        case 'supplier':
          result = {
            id: `supplier-${item.id}`,
            title: item.name || 'Unknown Supplier',
            subtitle: `${item.email || ''} | ${item.phone || ''}`.trim().replace(/^\||\|$/g, ''),
            route: '/suppliers',
            category: 'Suppliers',
            searchParam: item.name || '',
          };
          break;

        case 'purchase_order':
          result = {
            id: `purchase_order-${item.id}`,
            title: `PO-${item.id}`,
            subtitle: `${item.supplier_name || ''} | ${item.status || ''}`.trim().replace(/^\||\|$/g, ''),
            route: '/purchase-orders',
            category: 'Purchase Orders',
            searchParam: item.id,
          };
          break;

        case 'inventory_item':
          result = {
            id: `inventory-${item.id}`,
            title: item.sku_code || 'Unknown SKU',
            subtitle: `${item.style_code || ''} | ${item.supplier_name || ''}`.trim().replace(/^\||\|$/g, ''),
            route: '/inventory',
            category: 'Inventory',
            searchParam: item.sku_code || '',
            searchField: 'sku_code'
          };
          break;

        default:
          return; // Skip unknown types
      }

      if (result.title && result.title.trim() !== '') {
        frontendResults.push(result);
      }
    });
  });

  return frontendResults;
};

/**
 * Global search using backend API
 */
export const globalSearch = async (
  query: string,
  options: GlobalSearchOptions = {}
): Promise<GlobalSearchResult[]> => {
  if (!query || query.trim() === '' || query.trim().length < 2) {
    return [];
  }

  const {
    limit = 5
  } = options;

  try {
    // Make API call to backend search endpoint
    const response = await apiClient.get<BackendSearchResponse>(
      `${ENDPOINTS.GLOBAL_SEARCH}?query=${encodeURIComponent(query.trim())}&limit=${limit}`
    );

    if (!response || !response.results) {
      console.warn('Invalid search response format from backend');
      return [];
    }

    // Convert backend results to frontend format
    const frontendResults = convertBackendResults(response.results);

    console.log(`Backend search completed: ${frontendResults.length} results for "${query}"`);
    return frontendResults;

  } catch (error) {
    console.error('Error performing backend global search:', error);
    
    // Return empty results on error instead of throwing
    return [];
  }
};

// Individual search functions are now deprecated since we use backend global search
// But keeping them for backward compatibility in case they're used elsewhere

/**
 * @deprecated Use globalSearch instead - this now calls the backend
 */
const searchCustomers = async (query: string, limit: number = 5): Promise<GlobalSearchResult[]> => {
  const results = await globalSearch(query, { limit, includeCustomers: true });
  return results.filter(r => r.category === 'Customers');
};

/**
 * @deprecated Use globalSearch instead - this now calls the backend
 */
const searchInvoices = async (query: string, limit: number = 5): Promise<GlobalSearchResult[]> => {
  const results = await globalSearch(query, { limit, includeInvoices: true });
  return results.filter(r => r.category === 'Invoices');
};

/**
 * @deprecated Use globalSearch instead - this now calls the backend
 */
const searchInventory = async (query: string, limit: number = 5): Promise<GlobalSearchResult[]> => {
  const results = await globalSearch(query, { limit, includeInventory: true });
  return results.filter(r => r.category === 'Inventory');
};

/**
 * @deprecated Use globalSearch instead - this now calls the backend
 */
const searchQuotes = async (query: string, limit: number = 5): Promise<GlobalSearchResult[]> => {
  const results = await globalSearch(query, { limit, includeQuotes: true });
  return results.filter(r => r.category === 'Quotes');
};

/**
 * @deprecated Use globalSearch instead - this now calls the backend
 */
const searchSuppliers = async (query: string, limit: number = 5): Promise<GlobalSearchResult[]> => {
  const results = await globalSearch(query, { limit, includeSuppliers: true });
  return results.filter(r => r.category === 'Suppliers');
};

// Export service with all functions
const globalSearchService = {
  searchInvoices,
  searchCustomers,
  searchQuotes,
  searchInventory,
  searchSuppliers,
  globalSearch
};

export default globalSearchService;
