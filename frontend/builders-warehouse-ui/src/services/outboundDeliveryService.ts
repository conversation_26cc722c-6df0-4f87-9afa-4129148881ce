import api from "./apiClient";
import { format } from "date-fns";

// Types
export interface OutboundDelivery {
  invoice_no: string;
  invoice_date: string;
  linked_po: string;
  sku: string;
  description: string;
  status: string;
  notes?: string;
  hasNotes?: boolean;
}

export interface DeliveredOrder extends OutboundDelivery {
  delivered_date: string;
}

export interface PagedResponse<T> {
  data: T[];
  pagination: {
    total: number;
    page: number;
    limit: number;
  };
}

export interface OutboundDeliveryFilters {
  status?: string;
  sku?: string;
  start_date?: string;
  end_date?: string;
  page?: number;
  limit?: number;
}

export interface DeliveredOrderFilters {
  sku?: string;
  start_date?: string;
  end_date?: string;
  invoice_date?: string;
  page?: number;
  limit?: number;
}

// Add a new interface for creating outbound deliveries
export interface CreateOutboundDeliveryParams {
  invoice_no: string;
  invoice_date: string;
  linked_po: string;
  sku: string;
  description: string;
  status?: string;
  notes?: string;
}

// Add interface for notes response
export interface NotesResponse {
  invoice_no: string;
  notes: string | null;
  hasNotes: boolean;
}

class OutboundDeliveryError extends Error {
  constructor(message: string, public statusCode?: number) {
    super(message);
    this.name = "OutboundDeliveryError";
  }
}

// Service
const outboundDeliveryService = {
  // Get outbound deliveries in process
  getOutboundDeliveries: async (
    pageOrFilters: number | OutboundDeliveryFilters = 1,
    limit: number = 10,
    filters?: OutboundDeliveryFilters
  ): Promise<PagedResponse<OutboundDelivery>> => {
    try {
      const params = new URLSearchParams();

      // Handle both old and new parameter styles
      if (typeof pageOrFilters === "number") {
        // Original style: separate parameters
        params.append("page", pageOrFilters.toString());
        params.append("limit", limit.toString());

        if (filters) {
          if (filters.status) params.append("status", filters.status);
          if (filters.sku) params.append("sku", filters.sku);
          if (filters.start_date)
            params.append("start_date", filters.start_date);
          if (filters.end_date) params.append("end_date", filters.end_date);
        }
      } else {
        // Filter object style
        const filterObj = pageOrFilters;
        if (filterObj.status) params.append("status", filterObj.status);
        if (filterObj.sku) params.append("sku", filterObj.sku);
        if (filterObj.start_date)
          params.append("start_date", filterObj.start_date);
        if (filterObj.end_date) params.append("end_date", filterObj.end_date);
        if (filterObj.page) params.append("page", filterObj.page.toString());
        if (filterObj.limit) params.append("limit", filterObj.limit.toString());
      }

      const response = await api.get<PagedResponse<OutboundDelivery>>(
        `/api/v1/outbound/?${params.toString()}`
      );

      if (!response || !response.data) {
        throw new OutboundDeliveryError("Invalid response format from server");
      }

      return response;
    } catch (error) {
      if (error instanceof OutboundDeliveryError) {
        throw error;
      }

      if (error instanceof Error) {
        if (error.message.includes("Network Error")) {
          throw new OutboundDeliveryError(
            "Network error: Please check your internet connection"
          );
        }
        if (error.message.includes("timeout")) {
          throw new OutboundDeliveryError(
            "Request timed out: Please try again"
          );
        }
        if (error.message.includes("404")) {
          throw new OutboundDeliveryError("No deliveries found", 404);
        }
        if (error.message.includes("401")) {
          throw new OutboundDeliveryError(
            "Unauthorized: Please log in again",
            401
          );
        }
        if (error.message.includes("403")) {
          throw new OutboundDeliveryError(
            "Access denied: You don't have permission to view this data",
            403
          );
        }
        if (error.message.includes("500")) {
          throw new OutboundDeliveryError(
            "Server error: Please try again later",
            500
          );
        }
      }

      throw new OutboundDeliveryError("Failed to fetch deliveries");
    }
  },

  // Create a new outbound delivery
  createOutboundDelivery: async (
    params: CreateOutboundDeliveryParams
  ): Promise<OutboundDelivery> => {
    try {
      const response = await api.post<OutboundDelivery>(
        "/api/v1/outbound/",
        params
      );

      if (!response) {
        throw new OutboundDeliveryError("Invalid response format from server");
      }

      return response;
    } catch (error) {
      if (error instanceof OutboundDeliveryError) {
        throw error;
      }

      if (error instanceof Error) {
        if (error.message.includes("Network Error")) {
          throw new OutboundDeliveryError(
            "Network error: Please check your internet connection"
          );
        }
        if (error.message.includes("timeout")) {
          throw new OutboundDeliveryError(
            "Request timed out: Please try again"
          );
        }
        if (error.message.includes("400")) {
          throw new OutboundDeliveryError("Invalid delivery data", 400);
        }
        if (error.message.includes("401")) {
          throw new OutboundDeliveryError(
            "Unauthorized: Please log in again",
            401
          );
        }
        if (error.message.includes("403")) {
          throw new OutboundDeliveryError(
            "Access denied: You don't have permission to create deliveries",
            403
          );
        }
        if (error.message.includes("500")) {
          throw new OutboundDeliveryError(
            "Server error: Please try again later",
            500
          );
        }
      }

      throw new OutboundDeliveryError("Failed to create outbound delivery");
    }
  },

  // Update outbound delivery status
  updateDeliveryStatus: async (
    invoiceNo: string,
    status: string
  ): Promise<{ message: string }> => {
    try {
      const response = await api.patch<{ message: string }>(
        `/api/v1/outbound/${invoiceNo}/status/`,
        { status }
      );

      if (!response) {
        throw new OutboundDeliveryError("Invalid response format from server");
      }

      return response;
    } catch (error) {
      if (error instanceof OutboundDeliveryError) {
        throw error;
      }

      if (error instanceof Error) {
        if (error.message.includes("Network Error")) {
          throw new OutboundDeliveryError(
            "Network error: Please check your internet connection"
          );
        }
        if (error.message.includes("timeout")) {
          throw new OutboundDeliveryError(
            "Request timed out: Please try again"
          );
        }
        if (error.message.includes("400")) {
          throw new OutboundDeliveryError("Invalid status value", 400);
        }
        if (error.message.includes("404")) {
          throw new OutboundDeliveryError("Invoice not found", 404);
        }
        if (error.message.includes("401")) {
          throw new OutboundDeliveryError(
            "Unauthorized: Please log in again",
            401
          );
        }
        if (error.message.includes("403")) {
          throw new OutboundDeliveryError(
            "Access denied: You don't have permission to update delivery status",
            403
          );
        }
        if (error.message.includes("500")) {
          throw new OutboundDeliveryError(
            "Server error: Please try again later",
            500
          );
        }
      }

      throw new OutboundDeliveryError("Failed to update delivery status");
    }
  },

  // Get delivery notes
  getDeliveryNotes: async (invoiceNo: string): Promise<NotesResponse> => {
    try {
      const response = await api.get<NotesResponse>(
        `/api/v1/outbound/${invoiceNo}/notes/`
      );

      if (!response) {
        throw new OutboundDeliveryError("Invalid response format from server");
      }

      return response;
    } catch (error) {
      if (error instanceof OutboundDeliveryError) {
        throw error;
      }

      if (error instanceof Error) {
        if (error.message.includes("Network Error")) {
          throw new OutboundDeliveryError(
            "Network error: Please check your internet connection"
          );
        }
        if (error.message.includes("timeout")) {
          throw new OutboundDeliveryError(
            "Request timed out: Please try again"
          );
        }
        if (error.message.includes("404")) {
          throw new OutboundDeliveryError("Invoice not found", 404);
        }
        if (error.message.includes("401")) {
          throw new OutboundDeliveryError(
            "Unauthorized: Please log in again",
            401
          );
        }
        if (error.message.includes("403")) {
          throw new OutboundDeliveryError(
            "Access denied: You don't have permission to view notes",
            403
          );
        }
        if (error.message.includes("500")) {
          throw new OutboundDeliveryError(
            "Server error: Please try again later",
            500
          );
        }
      }

      throw new OutboundDeliveryError("Failed to get delivery notes");
    }
  },

  // Update delivery notes
  updateDeliveryNotes: async (
    invoiceNo: string,
    notes: string
  ): Promise<NotesResponse> => {
    try {
      const response = await api.patch<NotesResponse>(
        `/api/v1/outbound/${invoiceNo}/notes/`,
        { notes }
      );

      if (!response) {
        throw new OutboundDeliveryError("Invalid response format from server");
      }

      return response;
    } catch (error) {
      if (error instanceof OutboundDeliveryError) {
        throw error;
      }

      if (error instanceof Error) {
        if (error.message.includes("Network Error")) {
          throw new OutboundDeliveryError(
            "Network error: Please check your internet connection"
          );
        }
        if (error.message.includes("timeout")) {
          throw new OutboundDeliveryError(
            "Request timed out: Please try again"
          );
        }
        if (error.message.includes("400")) {
          throw new OutboundDeliveryError("Invalid notes data", 400);
        }
        if (error.message.includes("404")) {
          throw new OutboundDeliveryError("Invoice not found", 404);
        }
        if (error.message.includes("401")) {
          throw new OutboundDeliveryError(
            "Unauthorized: Please log in again",
            401
          );
        }
        if (error.message.includes("403")) {
          throw new OutboundDeliveryError(
            "Access denied: You don't have permission to update notes",
            403
          );
        }
        if (error.message.includes("500")) {
          throw new OutboundDeliveryError(
            "Server error: Please try again later",
            500
          );
        }
      }

      throw new OutboundDeliveryError("Failed to update delivery notes");
    }
  },

  // Get delivered orders
  getDeliveredOrders: async (
    pageOrFilters: number | DeliveredOrderFilters = 1,
    limit: number = 10,
    filters?: DeliveredOrderFilters
  ): Promise<PagedResponse<DeliveredOrder>> => {
    try {
      const params = new URLSearchParams();

      // Handle both old and new parameter styles
      if (typeof pageOrFilters === "number") {
        // Original style: separate parameters
        params.append("page", pageOrFilters.toString());
        params.append("limit", limit.toString());

        if (filters) {
          if (filters.sku) params.append("sku", filters.sku);
          if (filters.start_date)
            params.append("start_date", filters.start_date);
          if (filters.end_date) params.append("end_date", filters.end_date);
        }
      } else {
        // Filter object style
        const filterObj = pageOrFilters;
        if (filterObj.sku) params.append("sku", filterObj.sku);
        if (filterObj.start_date)
          params.append("start_date", filterObj.start_date);
        if (filterObj.end_date) params.append("end_date", filterObj.end_date);
        if (filterObj.invoice_date)
          params.append("invoice_date", filterObj.invoice_date);
        if (filterObj.page) params.append("page", filterObj.page.toString());
        if (filterObj.limit) params.append("limit", filterObj.limit.toString());
      }

      const response = await api.get<PagedResponse<DeliveredOrder>>(
        `/api/v1/outbound/delivered?${params.toString()}`
      );

      if (!response || !response.data) {
        throw new OutboundDeliveryError("Invalid response format from server");
      }

      return response;
    } catch (error) {
      if (error instanceof OutboundDeliveryError) {
        throw error;
      }

      if (error instanceof Error) {
        if (error.message.includes("Network Error")) {
          throw new OutboundDeliveryError(
            "Network error: Please check your internet connection"
          );
        }
        if (error.message.includes("timeout")) {
          throw new OutboundDeliveryError(
            "Request timed out: Please try again"
          );
        }
        if (error.message.includes("404")) {
          throw new OutboundDeliveryError("No delivered orders found", 404);
        }
        if (error.message.includes("401")) {
          throw new OutboundDeliveryError(
            "Unauthorized: Please log in again",
            401
          );
        }
        if (error.message.includes("403")) {
          throw new OutboundDeliveryError(
            "Access denied: You don't have permission to view this data",
            403
          );
        }
        if (error.message.includes("500")) {
          throw new OutboundDeliveryError(
            "Server error: Please try again later",
            500
          );
        }
      }

      throw new OutboundDeliveryError("Failed to fetch delivered orders");
    }
  },
};

export default outboundDeliveryService;
