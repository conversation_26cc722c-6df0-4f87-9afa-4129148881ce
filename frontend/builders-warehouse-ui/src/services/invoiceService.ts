import apiClient from "./apiClient";
import { ENDPOINTS, API_URL, AUTH_TOKEN_KEY } from "../config";
import {
  addStoreFilterToParams,
  StoreFilterOptions,
} from "../hooks/useStoreFilter";

// API Error interface for better typing
interface ApiError {
  response?: {
    status?: number;
    data?: any;
    headers?: any;
  };
  message?: string;
  code?: string;
}

// Enum types from the backend
export enum SaleTypeEnum {
  TRADE = "trade",
  RETAIL = "retail",
  STORE = "store",
}

export enum PaymentModeEnum {
  CASH = "cash",
  BANK_TRANSFER = "bank_transfer",
  CREDIT_CARD = "credit_card",
}

// Invoice item interfaces
export interface InvoiceItemCreate {
  sku: string;
  description: string;
  units: number;
  boxes: number;
  pieces: number;
  m2: number;
  unit_price: number | string;
}

export interface InvoiceItemOut extends InvoiceItemCreate {
  id: string;
  invoice_id: string;
  total_price: number;
  created_at: string;
  updated_at: string;
}

// Invoice interfaces
export interface InvoiceCreate {
  store_type?: string | { name: string; id?: number };
  store_type_id?: number;
  sale_type: SaleTypeEnum;
  mode_of_payment: PaymentModeEnum;
  purchase_order_number?: string;
  customer_id: string;
  deliver_to_address?: string;
  date: string; // Format: YYYY-MM-DD
  dont_send_po: boolean;
  linked_quote_id?: string;
  notes?: string;
  shipping: number | string;
  total_order?: number | string;
  credit_card_surcharge?: number | string;
  total_gst?: number | string;
  grand_total?: number | string;
  items: InvoiceItemCreate[];
}

export interface InvoiceUpdate {
  store_type?: string | { name: string; id?: number };
  store_type_id?: number;
  sale_type?: SaleTypeEnum;
  mode_of_payment?: PaymentModeEnum;
  purchase_order_number?: string;
  customer_id?: string;
  deliver_to_address?: string;
  date?: string; // Format: YYYY-MM-DD
  dont_send_po?: boolean;
  linked_quote_id?: string;
  notes?: string;
  shipping?: number | string;
  total_order?: number | string;
  credit_card_surcharge?: number | string;
  total_gst?: number | string;
  grand_total?: number | string;
  items?: InvoiceItemCreate[];
}

export interface InvoiceOut {
  id: string;
  store_type_id?: number;
  store_type?: {
    id: number;
    name: string;
  };
  sale_type: SaleTypeEnum;
  mode_of_payment: PaymentModeEnum;
  purchase_order_number?: string;
  customer_id: string;
  company_id?: string;
  deliver_to_address?: string;
  date: string; // Format: YYYY-MM-DD from API
  dont_send_po: boolean;
  linked_quote_id?: string;
  notes?: string;
  shipping: number;
  invoice_number?: {
    id: string;
    formatted_number: string;
  };
  invoice_no?: string; // For backward compatibility
  company?: {
    id: string;
    name: string;
  };
  total_order: string | number;
  credit_card_surcharge: string | number;
  total_gst: string | number;
  grand_total: string | number;
  created_at: string;
  updated_at: string;
  items: InvoiceItemOut[];
}

export interface InvoiceResponse {
  id: string;
  invoice_number?: {
    id: string;
    formatted_number: string;
  };
  invoice_no?: string; // For backward compatibility
  company?: {
    id: string;
    name: string;
  };
  customer_id?: string;
  company_id?: string;
  customer_name?: string; // For backward compatibility
  company_name?: string; // Added field from API response
  store_type?: {
    id: number;
    name: string;
  };
  store_type_id?: number; // For backward compatibility
  store_type_name?: string; // Added field from API response
  status?: string; // Status field (draft, saved, etc.)
  date: string; // Format: YYYY-MM-DD from API
  grand_total: string | number; // Can be string or number based on API response
  notes?: string;
  mode_of_payment?: string; // Add this property for completeness
  linked_quote_id?: string; // Add field for quotes that were converted to invoices
  po_id?: string | null; // Purchase order ID if available
}

export interface InvoiceFilters {
  customer_id?: string;
  store_type?: string;
  start_date?: string;
  end_date?: string;
  search?: string;
}

export interface InvoicePagination {
  total: number;
  items: InvoiceResponse[];
  page: number;
  size: number;
}

// Helper functions for type conversions
export const ensureNumber = (value: any): number => {
  if (typeof value === "number") return value;
  if (typeof value === "string") {
    const parsed = parseFloat(value);
    return isNaN(parsed) ? 0 : parsed;
  }
  return 0;
};

export const ensureString = (value: any): string => {
  if (value === null || value === undefined) return "";
  return String(value);
};

export const ensureBoolean = (value: any): boolean => {
  return Boolean(value);
};

/**
 * Get all invoices with pagination and optional filtering
 */
export const getInvoices = async (
  page: number = 1,
  limit: number = 10,
  filters?: InvoiceFilters,
  storeFilter?: StoreFilterOptions
): Promise<InvoicePagination> => {
  const skip = (page - 1) * limit;

  // Add a unique timestamp to prevent duplicate request detection
  const uniqueTimestamp = Date.now() + Math.floor(Math.random() * 1000);
  let queryParams = new URLSearchParams();
  queryParams.append("skip", skip.toString());
  queryParams.append("limit", limit.toString());
  queryParams.append("_", uniqueTimestamp.toString());

  if (filters) {
    if (filters.customer_id) {
      queryParams.append("customer_id", filters.customer_id);
    }
    if (filters.store_type) {
      queryParams.append("store_type", filters.store_type);
    }
    if (filters.start_date) {
      queryParams.append("start_date", filters.start_date);
    }
    if (filters.end_date) {
      queryParams.append("end_date", filters.end_date);
    }
    if (filters.search) {
      queryParams.append("search", filters.search);
    }
  }

  // Apply store-based filtering for staff/manager users
  if (storeFilter) {
    queryParams = addStoreFilterToParams(queryParams, storeFilter);
  }

  try {
    const queryString = queryParams.toString();
    console.log(`Fetching invoices with params: ${queryString}`);
    const response = await apiClient.get<InvoicePagination>(
      `${ENDPOINTS.INVOICES}?${queryString}`
    );
    console.log("Raw API response for invoices:", response);
    return response;
  } catch (error) {
    console.error("Error fetching invoices:", error);
    throw error;
  }
};

/**
 * Create a new invoice
 */
export const createInvoice = async (
  invoiceData: InvoiceCreate | any
): Promise<InvoiceOut> => {
  // Keep company_id in the payload since it's required by the backend
  const cleanData = { ...invoiceData };

  // Handle store_type correctly - if it's an object with name, keep it as is
  let storeTypeValue = cleanData.store_type;
  if (typeof storeTypeValue === "string" && !cleanData.store_type_id) {
    // Convert string store_type to object format
    storeTypeValue = { name: storeTypeValue };
  }

  // Ensure proper data types for all fields
  const payload = {
    ...cleanData,
    store_type: storeTypeValue,
    date: cleanData.date?.includes("T")
      ? cleanData.date.split("T")[0]
      : cleanData.date,
    shipping:
      typeof cleanData.shipping === "string"
        ? cleanData.shipping
        : ensureNumber(cleanData.shipping),
    dont_send_po: ensureBoolean(cleanData.dont_send_po),
    // Include total fields as strings if they exist
    total_order:
      cleanData.total_order !== undefined
        ? String(cleanData.total_order)
        : "0",
    credit_card_surcharge:
      cleanData.credit_card_surcharge !== undefined
        ? String(cleanData.credit_card_surcharge)
        : "0",
    total_gst:
      cleanData.total_gst !== undefined
        ? String(cleanData.total_gst)
        : "0",
    grand_total:
      cleanData.grand_total !== undefined
        ? String(cleanData.grand_total)
        : "0",
    // Ensure company_id is present
    company_id: cleanData.company_id || "00000000-0000-4000-8000-000000000001",
    items: cleanData.items?.map((item: any) => ({
      ...item,
      units: ensureNumber(item.units),
      boxes: ensureNumber(item.boxes),
      pieces: ensureNumber(item.pieces),
      m2: ensureNumber(item.m2),
      unit_price: String(ensureNumber(item.unit_price))
    }))
  };

  console.log(
    "Creating invoice with payload:",
    JSON.stringify(payload, null, 2)
  );

  try {
    const token = localStorage.getItem(AUTH_TOKEN_KEY);
    if (!token) {
      throw new Error("No authentication token found");
    }

    // Make a direct fetch call to have more control over the request
    const response = await fetch(`${API_URL}${ENDPOINTS.INVOICES}`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(payload)
    });

    if (!response.ok) {
      const errorText = await response.text();
      console.error("API Error Response:", errorText);
      throw new Error(`Failed to create invoice: ${response.status} ${response.statusText} - ${errorText}`);
    }

    const result = await response.json();
    console.log("Invoice created successfully, response:", result);
    return result;
  } catch (error) {
    console.error("Error creating invoice:", error);

    // Cast to ApiError type for better type safety
    const apiError = error as ApiError;

    // Log detailed error info for debugging
    if (apiError.response?.data?.detail) {
      console.error("API validation error details:", apiError.response?.data);
    }

    throw error;
  }
};

/**
 * Get a single invoice by ID
 */
export const getInvoice = async (invoiceId: string): Promise<InvoiceOut> => {
  try {
    const response = await apiClient.get<InvoiceOut>(
      `${ENDPOINTS.INVOICES}/${invoiceId}`
    );
    return response;
  } catch (error) {
    console.error(`Error fetching invoice ${invoiceId}:`, error);
    throw error;
  }
};

/**
 * Update an invoice
 */
export const updateInvoice = async (
  invoiceId: string,
  invoiceData: InvoiceUpdate | any
): Promise<InvoiceOut> => {
  // Keep company_id in the payload since it's required by the backend
  const cleanData = { ...invoiceData };

  // Handle store_type correctly - if it's an object with name, keep it as is
  let storeTypeValue = cleanData.store_type;
  if (typeof storeTypeValue === "string" && !cleanData.store_type_id) {
    // Convert string store_type to object format
    storeTypeValue = { name: storeTypeValue };
  }

  // Make sure the date is in the correct format if provided
  const payload = {
    ...cleanData,
    store_type: storeTypeValue,
  };

  if (payload.date) {
    payload.date = payload.date.includes("T")
      ? payload.date.split("T")[0] // Handle ISO strings
      : payload.date; // Already formatted as YYYY-MM-DD
  }

  // Ensure proper data types for all fields
  if (payload.shipping !== undefined) {
    payload.shipping =
      typeof payload.shipping === "string"
        ? payload.shipping
        : ensureNumber(payload.shipping);
  }

  if (payload.dont_send_po !== undefined) {
    payload.dont_send_po = ensureBoolean(payload.dont_send_po);
  }

  // Handle total fields (keep as strings if they're already strings)
  if (payload.total_order !== undefined) {
    payload.total_order =
      typeof payload.total_order === "string"
        ? payload.total_order
        : String(payload.total_order);
  }

  if (payload.credit_card_surcharge !== undefined) {
    payload.credit_card_surcharge =
      typeof payload.credit_card_surcharge === "string"
        ? payload.credit_card_surcharge
        : String(payload.credit_card_surcharge);
  }

  if (payload.total_gst !== undefined) {
    payload.total_gst =
      typeof payload.total_gst === "string"
        ? payload.total_gst
        : String(payload.total_gst);
  }

  if (payload.grand_total !== undefined) {
    payload.grand_total =
      typeof payload.grand_total === "string"
        ? payload.grand_total
        : String(payload.grand_total);
  }

  // Ensure proper data types for items if present
  if (payload.items) {
    payload.items = payload.items.map((item: any) => ({
      ...item,
      units: ensureNumber(item.units),
      boxes: ensureNumber(item.boxes),
      pieces: ensureNumber(item.pieces),
      m2: ensureNumber(item.m2),
      unit_price:
        typeof item.unit_price === "string"
          ? item.unit_price
          : ensureNumber(item.unit_price),
    }));
  }

  console.log(
    `Updating invoice ${invoiceId} with payload:`,
    JSON.stringify(payload, null, 2)
  );

  try {
    const response = await apiClient.put<InvoiceOut>(
      `${ENDPOINTS.INVOICES}/${invoiceId}`,
      payload
    );
    console.log("Invoice updated successfully, response:", response);
    return response;
  } catch (error) {
    console.error("Error updating invoice:", error);

    // Cast to ApiError type for better type safety
    const apiError = error as ApiError;

    // Log detailed error info for debugging
    if (apiError.response?.data?.detail) {
      console.error("API validation error details:", apiError.response?.data);
    }

    throw error;
  }
};

/**
 * Generate a PDF for an invoice
 */
export const generateInvoicePdf = async (invoiceId: string): Promise<Blob> => {
  const token = localStorage.getItem(AUTH_TOKEN_KEY);

  if (!token) {
    throw new Error("Authentication required to generate invoice PDF");
  }

  console.log("Attempting to generate PDF for invoice:", invoiceId);
  console.log(
    "Using endpoint:",
    `${API_URL}${ENDPOINTS.INVOICES}/${invoiceId}/pdf`
  );

  try {
    const response = await fetch(
      `${API_URL}${ENDPOINTS.INVOICES}/${invoiceId}/pdf`,
      {
        method: "GET",
        headers: {
          Authorization: `Bearer ${token}`,
          Accept: "application/pdf",
        },
      }
    );

    console.log("PDF generation response status:", response.status);
    console.log("PDF generation response headers:", response.headers);

    if (!response.ok) {
      // Log detailed error information
      const errorText = await response.text();
      console.error("PDF generation failed with status:", response.status);
      console.error("Error response body:", errorText);

      if (response.status === 404) {
        throw new Error(
          "PDF generation endpoint not found. Please contact support."
        );
      } else if (response.status === 403) {
        throw new Error("You do not have permission to download this invoice.");
      } else if (response.status === 401) {
        throw new Error("Authentication required to generate invoice PDF");
      } else {
        throw new Error(
          `Failed to generate invoice PDF: ${response.status} ${response.statusText}`
        );
      }
    }

    const blob = await response.blob();
    console.log("PDF blob generated successfully, size:", blob.size, "bytes");
    return blob;
  } catch (error) {
    console.error("Error in generateInvoicePdf:", error);
    throw error;
  }
};

/**
 * Email an invoice to the customer
 */
export const emailInvoice = async (invoiceId: string): Promise<void> => {
  await apiClient.post<void>(`${ENDPOINTS.INVOICES}/${invoiceId}/email`, {});
};

// Create a named export object
const invoiceService = {
  createInvoice,
  getInvoices,
  getInvoice,
  updateInvoice,
  generateInvoicePdf,
  emailInvoice,
};

export default invoiceService;
