import apiClient from './apiClient';
import { format } from 'date-fns';
import { addStoreFilterToParams, StoreFilterOptions } from '../hooks/useStoreFilter';

// Define interfaces for Quote-related data
export interface QuoteItem {
  id?: string;
  quote_id?: string;
  // Frontend form fields
  sku: string;
  description: string;
  units: number;
  boxes: number;
  pieces: number;
  m2: number;
  unit_price: number;
  total_price?: number;
  // Backend API field names (optional as they're mapped during API calls)
  sku_code?: string;
  quantity_units?: number;
  quantity_boxes?: number;
  quantity_pieces?: number;
  quantity_m2?: number;
  created_at?: string;
  updated_at?: string;
}

// Interface for creating a quote item for the API
export interface QuoteItemCreate {
  sku_code: string;
  description: string;
  quantity_units: number;
  quantity_boxes: number;
  quantity_pieces: number;
  quantity_m2: number;
  unit_price: number;
}

export interface QuoteCreate {
  company_id: string;
  store_type_id: number;
  customer_id: string | number;  // Allow both string and number
  deliver_to_address?: string;
  date: string; // Format: YYYY-MM-DD
  notes?: string;
  items: QuoteItemCreate[];
}

export interface Quote {
  id: string;
  quote_no: string;
  store_type: string;
  sale_type: string;
  customer_id: string;
  customer_name: string;
  deliver_to_address?: string;
  date: string;
  notes?: string;
  total_gst: number;
  grand_total: number;
  total_order: number;
  shipping: number;
  is_archived: boolean;
  created_at: string;
  updated_at: string;
  quote_items: QuoteItem[];
}

export interface QuotesResponse {
  items: Quote[];
  total: number;
  page: number;
  size: number;
}

export interface QuoteFilter {
  search?: string;
  start_date?: string;
  end_date?: string;
  page?: number;
  limit?: number;
  store_type?: string;
}

// Create the service for handling Quote-related API calls
const quoteService = {
  // Get current (non-archived) quotes with pagination and filtering
  getCurrentQuotes: async (filters: QuoteFilter = {}, storeFilter?: StoreFilterOptions): Promise<QuotesResponse> => {
    const { search, start_date, end_date, page = 1, limit = 10, store_type } = filters;

    let queryParams = new URLSearchParams();
    if (search) queryParams.append('search', search);
    if (start_date) queryParams.append('start_date', start_date);
    if (end_date) queryParams.append('end_date', end_date);
    if (store_type) queryParams.append('store_type', store_type);
    queryParams.append('page', page.toString());
    queryParams.append('limit', limit.toString());

    // Apply store-based filtering for staff/manager users
    if (storeFilter) {
      queryParams = addStoreFilterToParams(queryParams, storeFilter);
    }

    const endpoint = `/api/v1/quotes/current?${queryParams.toString()}`;
    return apiClient.get<QuotesResponse>(endpoint);
  },

  // Get archived quotes with pagination and filtering
  getArchivedQuotes: async (filters: QuoteFilter = {}, storeFilter?: StoreFilterOptions): Promise<QuotesResponse> => {
    const { search, start_date, end_date, page = 1, limit = 10, store_type } = filters;

    let queryParams = new URLSearchParams();
    if (search) queryParams.append('search', search);
    if (start_date) queryParams.append('start_date', start_date);
    if (end_date) queryParams.append('end_date', end_date);
    if (store_type) queryParams.append('store_type', store_type);
    queryParams.append('page', page.toString());
    queryParams.append('limit', limit.toString());

    // Apply store-based filtering for staff/manager users
    if (storeFilter) {
      queryParams = addStoreFilterToParams(queryParams, storeFilter);
    }

    const endpoint = `/api/v1/quotes/archived?${queryParams.toString()}`;
    return apiClient.get<QuotesResponse>(endpoint);
  },

  // Get a quote by ID
  getQuoteById: async (quoteId: string): Promise<Quote> => {
    return apiClient.get<Quote>(`/api/v1/quotes/${quoteId}`);
  },

  // Create a new quote
  createQuote: async (quoteData: QuoteCreate): Promise<Quote> => {
    return apiClient.post<Quote>('/api/v1/quotes', quoteData);
  },

  // Update an existing quote
  updateQuote: async (quoteId: string, quoteData: Partial<QuoteCreate>): Promise<Quote> => {
    return apiClient.put<Quote>(`/api/v1/quotes/${quoteId}`, quoteData);
  },

  // Convert a quote to an invoice
  convertToInvoice: async (quoteId: string): Promise<Quote> => {
    return apiClient.put<Quote>(`/api/v1/quotes/${quoteId}/convert`, {});
  },

  // Renew an archived quote
  renewQuote: async (quoteId: string): Promise<Quote> => {
    return apiClient.put<Quote>(`/api/v1/quotes/${quoteId}/renew`, {});
  },

  // Archive a quote
  archiveQuote: async (quoteId: string): Promise<Quote> => {
    return apiClient.put<Quote>(`/api/v1/quotes/${quoteId}/archive`, {});
  },

  // Unarchive a quote
  unarchiveQuote: async (quoteId: string): Promise<Quote> => {
    return apiClient.put<Quote>(`/api/v1/quotes/${quoteId}/unarchive`, {});
  },

  // Delete a quote
  deleteQuote: async (quoteId: string): Promise<void> => {
    return apiClient.delete<void>(`/api/v1/quotes/${quoteId}`);
  },

  // Format a date for API requests
  formatDate: (date: Date): string => {
    return format(date, 'yyyy-MM-dd');
  }
};

export default quoteService; 