import apiClient from './apiClient';
import { API_URL, ENDPOINTS } from '../config';

export interface Role {
  id: number;
  name: string;
  description?: string;
}

// Default fallback roles in case API fails
const DEFAULT_ROLES: Role[] = [
  { id: 1, name: 'admin', description: 'Administrator' },
  { id: 2, name: 'manager', description: 'Manager' },
  { id: 3, name: 'staff', description: 'Staff Member' }
];

// Simple cache for roles to avoid unnecessary API calls
const rolesCache: {
  data: Role[] | null;
  timestamp: number;
} = {
  data: null,
  timestamp: 0
};

/**
 * Fetch all roles from the API
 * @returns Promise with array of roles
 */
export const getRoles = async (): Promise<Role[]> => {
  try {
    // Check if we have cached roles less than 5 minutes old
    const now = Date.now();
    if (rolesCache.data && (now - rolesCache.timestamp < 5 * 60 * 1000)) {
      console.log('Using cached roles data');
      return rolesCache.data;
    }

    // Fetch roles from API - ensure endpoint has trailing slash for API compatibility
    console.log(`Fetching roles from: ${ENDPOINTS.ROLES}`);
    const rolesEndpoint = ENDPOINTS.ROLES.endsWith('/') ? ENDPOINTS.ROLES : `${ENDPOINTS.ROLES}/`;
    const roles = await apiClient.get<Role[]>(rolesEndpoint);
    
    // Cache the roles and timestamp
    rolesCache.data = roles;
    rolesCache.timestamp = now;
    
    return roles;
  } catch (error) {
    console.error('Error fetching roles:', error);
    // Return default roles as fallback if API fails
    return DEFAULT_ROLES;
  }
};

/**
 * Get a single role by ID
 * @param id Role ID
 * @returns Promise with role data
 */
export const getRoleById = async (id: number): Promise<Role> => {
  try {
    // Check if we have cached roles
    if (rolesCache.data) {
      const cachedRole = rolesCache.data.find(role => role.id === id);
      if (cachedRole) {
        return cachedRole;
      }
    }
    
    // Fetch role by ID - ensure endpoint has trailing slash for API compatibility
    const rolesEndpoint = ENDPOINTS.ROLES.endsWith('/') ? ENDPOINTS.ROLES : `${ENDPOINTS.ROLES}/`;
    return await apiClient.get<Role>(`${rolesEndpoint}${id}`);
  } catch (error) {
    console.error(`Error fetching role with ID ${id}:`, error);
    // Return a default role based on ID as fallback
    const defaultRole = DEFAULT_ROLES.find(r => r.id === id);
    if (defaultRole) {
      return defaultRole;
    }
    // Return the first default role if no match by ID
    return DEFAULT_ROLES[0];
  }
};

const roleService = {
  getRoles,
  getRoleById
};

export default roleService; 