import apiClient from "./apiClient";
import { API_URL, ENDPOINTS } from "../config";
import { UserRole } from "../types/common";
import { getRoleString } from "../utils/roleUtils";

// Define user interfaces matching the backend API model
export interface ManagerInfo {
  id: string;
  user_name: string;
  store_type?: {
    id: number;
    name: string;
  };
}

export interface StoreTypeInfo {
  id: number;
  name: string;
}

export interface UserBase {
  user_name: string;
  email: string;
  mobile_number: string;
}

export interface UserCreate extends UserBase {
  password: string;
  role: UserRole;
  store_type_id?: number | null;
  manager_id?: string | null;
}

export interface UserUpdate {
  user_name?: string;
  email?: string;
  mobile_number?: string | null;
  password?: string;
  is_active?: boolean;
  role?: UserRole;
  store_type_id?: number | null;
  manager_id?: string | null;
}

export interface UserResponse {
  id: string | object; // Handle both string IDs and UUID objects
  user_name: string;
  email: string;
  mobile_number: string | null; // Allow null for mobile_number
  is_active: boolean;
  role: string | UserRole;
  created_at: string;
  updated_at?: string | null;
  store_type?: StoreTypeInfo | null;
  manager?: ManagerInfo | null;
}

// Interface for the UI components
export interface UserDisplay {
  id: string;
  name: string;
  email: string;
  role: string;
  status: string;
  mobile: string;
  storeType: string;
  storeTypeId?: number | null;
  manager: string;
  managerId?: string | null;
  createdAt?: string;
  updatedAt?: string | null;

  // Additional properties to support API response variations
  user_name?: string;
  mobile_number?: string;
  is_active?: boolean;
  store_type?: {
    id: number;
    name: string;
  } | null;
}

/**
 * Paginated response from the API
 * The API can return data in different formats:
 * 1. New format: { items: UserResponse[], total: number, page: number, pages: number }
 * 2. Old format: { data: UserResponse[], total: number }
 */
export interface PaginatedResponse<T> {
  data?: T[]; // Old API format uses 'data' array
  items?: T[]; // New API format uses 'items' array
  total: number; // Both formats include total count
  page?: number; // New format includes current page number
  pages?: number; // New format includes total pages
}

// API prefix for users
const API_PREFIX = "/api/v1";
const USERS_ENDPOINT = `${API_PREFIX}/users`;

/**
 * Get all users with optional pagination and search
 */
export const getUsers = async (params?: {
  skip?: number;
  limit?: number;
  search?: string;
  role?: UserRole;
  store_type_id?: number;
}): Promise<PaginatedResponse<UserDisplay>> => {
  try {
    // Build query parameters
    const queryParams = new URLSearchParams();
    if (params?.skip !== undefined)
      queryParams.append("skip", params.skip.toString());
    if (params?.limit !== undefined)
      queryParams.append("limit", params.limit.toString());
    if (params?.search) queryParams.append("search", params.search);
    if (params?.role) queryParams.append("role", params.role);
    if (params?.store_type_id)
      queryParams.append("store_type_id", params.store_type_id.toString());

    const queryString = queryParams.toString();
    const endpoint = queryString
      ? `${USERS_ENDPOINT}?${queryString}`
      : USERS_ENDPOINT;

    console.log("Fetching users from endpoint:", endpoint);
    const response = await apiClient.get<any>(endpoint);
    console.log(
      "RAW API Response from getUsers:",
      JSON.stringify(response, null, 2)
    );

    // Enhanced response handling to fix data not displaying issue
    let userData: UserResponse[] = [];
    let total = 0;

    // Check for null or undefined response
    if (!response) {
      console.warn("Received null or undefined response from API");
      return { data: [], items: [], total: 0, page: 1, pages: 0 };
    }

    // Handle response format with 'items' array (new API format)
    if (
      typeof response === "object" &&
      "items" in response &&
      Array.isArray(response.items)
    ) {
      userData = response.items;
      total = response.total || userData.length;
      console.log(`New format response with ${userData.length} items`);
    }
    // Handle response format with 'data' array
    else if (
      typeof response === "object" &&
      "data" in response &&
      Array.isArray(response.data)
    ) {
      userData = response.data;
      total = response.total || userData.length;
      console.log(`Response with data property: ${userData.length} items`);
    }
    // Handle direct array response (old API format)
    else if (Array.isArray(response)) {
      userData = response;
      total = userData.length;
      console.log(`Direct array response with ${userData.length} items`);
    }
    // Handle unexpected response format
    else {
      console.warn("Unexpected API response format:", response);
      return { data: [], items: [], total: 0, page: 1, pages: 0 };
    }

    // Log first user for debugging
    if (userData.length > 0) {
      console.log("First user data:", JSON.stringify(userData[0], null, 2));
    } else {
      console.log("No users returned from API");
    }

    // Map API users to display format and handle potential nulls
    const mappedUsers: UserDisplay[] = userData
      .filter((user) => user !== null && user !== undefined)
      .map((user) => {
        try {
          return mapApiUserToDisplayUser(user);
        } catch (error) {
          console.error("Error mapping user:", error, user);
          // Return a minimal valid user object if mapping fails
          return {
            id: user.id?.toString() || "unknown",
            name: user.user_name || "Unknown User",
            email: user.email || "",
            role: typeof user.role === "string" ? user.role : "staff",
            status: user.is_active === false ? "inactive" : "active",
            mobile: user.mobile_number || "",
            storeType: user.store_type?.name || "",
            manager: user.manager?.user_name || "",
          };
        }
      });

    // Return a consistent structure with both data and items populated
    // This ensures compatibility regardless of which property the component uses
    return {
      data: mappedUsers,
      items: mappedUsers,
      total: total,
      page:
        typeof response === "object" && "page" in response ? response.page : 1,
      pages:
        typeof response === "object" && "pages" in response
          ? response.pages
          : Math.ceil(total / 10),
    };
  } catch (error) {
    console.error("Error fetching users:", error);
    if (error instanceof Error) {
      console.error("Error details:", error.message, error.stack);
    }
    return {
      data: [],
      items: [],
      total: 0,
      page: 1,
      pages: 0,
    };
  }
};

/**
 * Get a user by ID
 */
export const getUserById = async (id: string): Promise<UserDisplay> => {
  try {
    const response = await apiClient.get<UserResponse>(
      `${USERS_ENDPOINT}/${id}`
    );
    return mapApiUserToDisplayUser(response);
  } catch (error) {
    console.error(`Error fetching user ${id}:`, error);
    throw error;
  }
};

/**
 * Get managers for dropdown selection
 */
export const getManagers = async (): Promise<ManagerInfo[]> => {
  try {
    // Use dedicated managers endpoint from ENDPOINTS
    console.log(`Fetching managers from: ${ENDPOINTS.MANAGERS}`);
    const managers = await apiClient.get<ManagerInfo[]>(
      `${ENDPOINTS.MANAGERS}/`
    );

    // Log the raw data to see what we're getting
    if (managers.length > 0) {
      console.log(
        "Manager data example:",
        JSON.stringify(managers[0], null, 2)
      );
    }

    // The API now returns managers with the store_type object included
    return managers.map((manager) => ({
      id: manager.id,
      user_name: manager.user_name,
      store_type: manager.store_type,
    }));
  } catch (error) {
    console.error("Error fetching managers:", error);
    throw error;
  }
};

/**
 * Create a new user
 */
export const createUser = async (
  userData: UserCreate
): Promise<UserDisplay> => {
  try {
    const response = await apiClient.post<UserResponse>(
      USERS_ENDPOINT,
      userData
    );
    return mapApiUserToDisplayUser(response);
  } catch (error) {
    console.error("Error creating user:", error);
    throw error;
  }
};

/**
 * Update a user
 */
export const updateUser = async (
  id: string,
  userData: UserUpdate
): Promise<UserDisplay> => {
  try {
    const response = await apiClient.put<UserResponse>(
      `${USERS_ENDPOINT}/${id}`,
      userData
    );
    return mapApiUserToDisplayUser(response);
  } catch (error) {
    console.error(`Error updating user ${id}:`, error);
    throw error;
  }
};

/**
 * Delete a user
 */
export const deleteUser = async (id: string): Promise<void> => {
  try {
    await apiClient.delete(`${USERS_ENDPOINT}/${id}`);
  } catch (error) {
    console.error(`Error deleting user ${id}:`, error);
    throw error;
  }
};

/**
 * Convert API user model to UI display model
 */
export const mapApiUserToDisplayUser = (apiUser: UserResponse): UserDisplay => {
  // Validate input to ensure we have a valid user object
  if (!apiUser) {
    console.error("Received null or undefined apiUser");
    throw new Error("Invalid user data received");
  }

  console.log("Mapping API user to display format:", apiUser);

  // Get role string with fallback
  let roleStr = "staff"; // Default

  try {
    if (typeof apiUser.role === "string") {
      roleStr = apiUser.role;
    } else if (apiUser.role) {
      roleStr = getRoleString(apiUser) || "staff";
    }
  } catch (error) {
    console.error("Error parsing role:", error);
  }

  // Ensure id is always a string
  let id: string;
  if (typeof apiUser.id === "string") {
    id = apiUser.id;
  } else if (typeof apiUser.id === "object" && apiUser.id !== null) {
    id = String(apiUser.id);
  } else {
    // Generate a fallback ID if none exists
    id = `temp-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
    console.warn(`Generated temporary ID for user: ${id}`);
  }

  // Ensure mobile_number is always a string
  const mobile =
    apiUser.mobile_number === null || apiUser.mobile_number === undefined
      ? ""
      : String(apiUser.mobile_number);

  // Extract manager info with fallbacks to handle different formats
  let managerName = "";
  let managerId = null;

  if (apiUser.manager) {
    // Check if manager is an object with user_name
    if (typeof apiUser.manager === "object" && apiUser.manager !== null) {
      managerName = apiUser.manager.user_name || "";
      managerId = apiUser.manager.id || null;
      console.log(
        `Manager parsed from object: ${managerName} (ID: ${managerId})`
      );
    }
    // Or just a string
    else if (typeof apiUser.manager === "string") {
      managerName = apiUser.manager;
      console.log(`Manager parsed from string: ${managerName}`);
    }
  }

  // Safely extract store type information
  let storeType = "";
  let storeTypeId = null;

  if (apiUser.store_type) {
    if (typeof apiUser.store_type === "object" && apiUser.store_type !== null) {
      storeType = apiUser.store_type.name || "";
      storeTypeId = apiUser.store_type.id || null;
    } else if (typeof apiUser.store_type === "string") {
      storeType = apiUser.store_type;
    }
  }

  // Handle status with explicit fallback
  const status =
    apiUser.is_active === undefined
      ? "active"
      : apiUser.is_active
      ? "active"
      : "inactive";

  return {
    id: id,
    name: apiUser.user_name || "",
    email: apiUser.email || "",
    role: roleStr,
    mobile: mobile,
    manager: managerName,
    storeType: storeType,
    managerId: managerId,
    storeTypeId: storeTypeId,
    status: status,
    is_active: apiUser.is_active !== false, // Default to true if undefined
    createdAt: apiUser.created_at || "",
    updatedAt: apiUser.updated_at || null,
  };
};

/**
 * Map display user to API format for creation
 */
export const mapDisplayUserToApiCreate = (
  displayUser: {
    name: string;
    email: string;
    password: string;
    role: string;
    mobile: string;
    storeTypeId?: number | null;
    managerId?: string | null;
  },
  managers?: ManagerInfo[]
): UserCreate => {
  // Base fields that are always required
  const apiUser: UserCreate = {
    user_name: displayUser.name,
    email: displayUser.email,
    password: displayUser.password,
    role: displayUser.role as UserRole,
    mobile_number: displayUser.mobile,
  };

  // Add role-specific fields
  if (displayUser.role === "admin") {
    // Backend requires a valid store_type_id even for admin users
    apiUser.store_type_id = displayUser.storeTypeId || 1; // Use first store type as default for admin
    // Manager is optional for admin
    apiUser.manager_id = displayUser.managerId || null;
  } else if (displayUser.role === "manager") {
    // Manager needs store_type_id but not manager_id
    apiUser.store_type_id = displayUser.storeTypeId || null;
    apiUser.manager_id = null;
  } else if (displayUser.role === "staff") {
    // Staff users should get their store_type_id from their manager
    apiUser.manager_id = displayUser.managerId || null;

    if (displayUser.managerId && managers) {
      // Find the manager to get their store type ID
      const selectedManager = managers.find(
        (m) => m.id === displayUser.managerId
      );

      // Log the manager object to debug
      console.log(
        `Looking for store type in manager:`,
        selectedManager
          ? JSON.stringify(selectedManager, null, 2)
          : "Manager not found"
      );

      if (selectedManager) {
        // Get store type ID directly from the store_type object
        if (
          selectedManager.store_type &&
          selectedManager.store_type.id !== undefined
        ) {
          apiUser.store_type_id = selectedManager.store_type.id;
          console.log(
            `Using manager's store type ID: ${selectedManager.store_type.id} for staff user`
          );
        } else {
          // If manager has no store type (unlikely), use provided value or fallback
          apiUser.store_type_id = displayUser.storeTypeId || 1; // Fallback to Cranbourne (id: 1)
          console.log(
            `Manager found but no store type available, using: ${apiUser.store_type_id}`
          );
        }
      } else {
        // If manager not found, use provided value or fallback
        apiUser.store_type_id = displayUser.storeTypeId || 1; // Fallback to Cranbourne
        console.log(
          `Manager not found, using fallback store type: ${apiUser.store_type_id}`
        );
      }
    } else {
      // If no manager ID, use provided value or fallback
      apiUser.store_type_id = displayUser.storeTypeId || 1; // Fallback to Cranbourne
      console.log(
        `No manager ID provided for staff user, using: ${apiUser.store_type_id}`
      );
    }
  }

  return apiUser;
};

/**
 * Map display user to API format for updates
 */
export const mapDisplayUserToApiUpdate = (
  displayUser: {
    name?: string;
    email?: string;
    password?: string;
    role?: string;
    status?: string;
    mobile?: string;
    storeTypeId?: number | null;
    managerId?: string | null;
  },
  managers?: ManagerInfo[]
): UserUpdate => {
  // Base fields that can be updated
  const apiUser: UserUpdate = {
    user_name: displayUser.name,
    email: displayUser.email,
    password: displayUser.password,
    mobile_number: displayUser.mobile,
    is_active: displayUser.status === "active",
  };

  // Only add role-specific fields if role is provided
  if (displayUser.role) {
    apiUser.role = displayUser.role as UserRole;

    if (displayUser.role === "admin") {
      // Backend requires a valid store_type_id even for admin users
      apiUser.store_type_id = displayUser.storeTypeId || 1; // Use first store type as default for admin
      // Manager is optional for admin
      apiUser.manager_id = displayUser.managerId || null;
    } else if (displayUser.role === "manager") {
      // Manager needs store_type_id but not manager_id
      apiUser.store_type_id = displayUser.storeTypeId || null;
      apiUser.manager_id = null;
    } else if (displayUser.role === "staff") {
      // Staff users should get their store_type_id from their manager
      apiUser.manager_id = displayUser.managerId || null;

      if (displayUser.managerId && managers) {
        // Find the manager to get their store type ID
        const selectedManager = managers.find(
          (m) => m.id === displayUser.managerId
        );

        // Log the manager object to debug
        console.log(
          `Looking for store type in manager:`,
          selectedManager
            ? JSON.stringify(selectedManager, null, 2)
            : "Manager not found"
        );

        if (selectedManager) {
          // Get store type ID directly from the store_type object
          if (
            selectedManager.store_type &&
            selectedManager.store_type.id !== undefined
          ) {
            apiUser.store_type_id = selectedManager.store_type.id;
            console.log(
              `Using manager's store type ID: ${selectedManager.store_type.id} for staff user`
            );
          } else {
            // If manager has no store type (unlikely), use provided value or fallback
            apiUser.store_type_id = displayUser.storeTypeId || 1; // Fallback to Cranbourne (id: 1)
            console.log(
              `Manager found but no store type available, using: ${apiUser.store_type_id}`
            );
          }
        } else {
          // If manager not found, use provided value or fallback
          apiUser.store_type_id = displayUser.storeTypeId || 1; // Fallback to Cranbourne
          console.log(
            `Manager not found, using fallback store type: ${apiUser.store_type_id}`
          );
        }
      } else {
        // If no manager ID, use provided value or fallback
        apiUser.store_type_id = displayUser.storeTypeId || 1; // Fallback to Cranbourne
        console.log(
          `No manager ID provided for staff user, using: ${apiUser.store_type_id}`
        );
      }
    }
  }

  return apiUser;
};

// Add a test function to directly map API response for debugging
export const testMapApiUsers = (apiResponse: any): UserDisplay[] => {
  console.log("Testing direct mapping of API response:", apiResponse);

  if (!apiResponse || !apiResponse.items || !Array.isArray(apiResponse.items)) {
    console.error("Invalid API response format for mapping test");
    return [];
  }

  try {
    const users = apiResponse.items.map((user: any) => {
      return {
        id: String(user.id || ""),
        name: String(user.user_name || ""),
        email: String(user.email || ""),
        role: String(user.role || "staff"),
        status: user.is_active === false ? "inactive" : "active",
        mobile: String(user.mobile_number || ""),
        storeType: user.store_type?.name ? String(user.store_type.name) : "",
        manager: user.manager?.user_name ? String(user.manager.user_name) : "",
      };
    });

    console.log("Mapped users:", users);
    return users;
  } catch (error) {
    console.error("Error in test mapping:", error);
    return [];
  }
};

export default {
  getUsers,
  getUserById,
  getManagers,
  createUser,
  updateUser,
  deleteUser,
  mapApiUserToDisplayUser,
  mapDisplayUserToApiCreate,
  mapDisplayUserToApiUpdate,
  testMapApiUsers,
};
