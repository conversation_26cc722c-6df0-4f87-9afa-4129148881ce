import api from "./apiClient";
import { format } from "date-fns";

// Types
export interface InboundDelivery {
  id: number;
  po_number: string;
  supplier_name: string;
  sku: string;
  quantity_ordered: number;
  quantity_received: number;
  linked_invoice?: string;
  ordered_by: string;
  balance_order: number;
  ship_to_customer: boolean;
  po_date: string;
  new_expected_date?: string;
  status: string;
  created_at: string;
  updated_at: string;
}

export interface CalendarDay {
  date: string;
  po_numbers: string[];
  status: string;
}

export interface CalendarResponse {
  month: number;
  year: number;
  days: CalendarDay[];
}

export interface PagedInboundDeliveryResponse {
  po_number: string;
  delivery_date?: string;
  data: InboundDelivery[];
  pagination: {
    total: number;
    page: number;
    limit: number;
  };
}

export interface UpdateQuantityResponse {
  po_number: string;
  quantity_received: number;
  balance_order: number;
}

export interface UpdateDateResponse {
  po_number: string;
  new_expected_date: string;
}

class InboundDeliveryError extends Error {
  constructor(message: string, public statusCode?: number) {
    super(message);
    this.name = "InboundDeliveryError";
  }
}

// Service
const inboundDeliveryService = {
  // Get calendar highlights for a specific month/year
  getCalendarHighlights: async (
    month: number,
    year: number
  ): Promise<CalendarResponse> => {
    try {
      const response = await api.get<CalendarResponse>(
        `/api/v1/inbound-delivery/calendar?month=${month}&year=${year}`
      );

      // If response is empty or missing days, create a default response
      if (!response || !response.days) {
        console.warn("Invalid response format from server, creating default calendar");
        return {
          month,
          year,
          days: []
        };
      }

      return response;
    } catch (error) {
      console.error("Error in getCalendarHighlights:", error);
      
      // Instead of throwing an error, return a default calendar structure
      // This allows the UI to always render something even if the API fails
      const defaultCalendar: CalendarResponse = {
        month,
        year,
        days: []
      };
      
      // Log the error for debugging but don't throw it
      if (error instanceof InboundDeliveryError) {
        console.error("InboundDeliveryError:", error.message);
      } else if (error instanceof Error) {
        console.error("Error:", error.message);
        }
      
      return defaultCalendar;
    }
  },

  // Get deliveries by date with pagination
  getDeliveriesByDate: async (
    date: Date,
    supplier?: string,
    page: number = 1,
    limit: number = 10
  ): Promise<PagedInboundDeliveryResponse> => {
    try {
      const formattedDate = format(date, "yyyy-MM-dd");
      let url = `/api/v1/inbound-delivery/date/${formattedDate}?page=${page}&limit=${limit}`;

      if (supplier) {
        url += `&supplier=${encodeURIComponent(supplier)}`;
      }

      const response = await api.get<PagedInboundDeliveryResponse>(url);

      if (!response || !response.data) {
        // Return empty results instead of throwing an error
        return {
          po_number: "All",
          delivery_date: formattedDate,
          data: [],
          pagination: {
            total: 0,
            page: page,
            limit: limit
          }
        };
      }

      return response;
    } catch (error) {
      // Handle errors, but return empty data for 404 errors
      if (error instanceof Error) {
        if (error.message.includes("404")) {
          // Return empty results instead of throwing an error for 404
          return {
            po_number: "All",
            delivery_date: format(date, "yyyy-MM-dd"),
            data: [],
            pagination: {
              total: 0,
              page: page,
              limit: limit
            }
          };
        }
        
        // For other error types, log and throw appropriate errors
        if (error.message.includes("Network Error")) {
          throw new InboundDeliveryError(
            "Network error: Please check your internet connection"
          );
        }
        if (error.message.includes("timeout")) {
          throw new InboundDeliveryError("Request timed out: Please try again");
        }
        if (error.message.includes("401")) {
          throw new InboundDeliveryError(
            "Unauthorized: Please log in again",
            401
          );
        }
        if (error.message.includes("403")) {
          throw new InboundDeliveryError(
            "Access denied: You don't have permission to view this data",
            403
          );
        }
        if (error.message.includes("500")) {
          throw new InboundDeliveryError(
            "Server error: Please try again later",
            500
          );
        }
      }

      // For any other errors, return empty data structure
      return {
        po_number: "All",
        delivery_date: format(date, "yyyy-MM-dd"),
        data: [],
        pagination: {
          total: 0,
          page: page,
          limit: limit
        }
      };
    }
  },

  // Get detailed PO data with pagination
  getPODetails: async (
    poNumber: string,
    supplier?: string,
    page: number = 1,
    limit: number = 10
  ): Promise<PagedInboundDeliveryResponse> => {
    try {
      let url = `/api/v1/inbound-delivery/po/${poNumber}?page=${page}&limit=${limit}`;
      if (supplier) {
        url += `&supplier=${encodeURIComponent(supplier)}`;
      }

      const response = await api.get<PagedInboundDeliveryResponse>(url);

      if (!response || !response.data) {
        // Return empty results instead of throwing an error
        return {
          po_number: poNumber,
          data: [],
          pagination: {
            total: 0,
            page: page,
            limit: limit
          }
        };
      }

      return response;
    } catch (error) {
      // Handle errors, but return empty data for 404 errors
      if (error instanceof Error) {
        if (error.message.includes("404")) {
          // Return empty results instead of throwing an error for 404
          return {
            po_number: poNumber,
            data: [],
            pagination: {
              total: 0,
              page: page,
              limit: limit
            }
          };
        }
        
        // For other error types, log and throw appropriate errors
        if (error.message.includes("Network Error")) {
          throw new InboundDeliveryError(
            "Network error: Please check your internet connection"
          );
        }
        if (error.message.includes("timeout")) {
          throw new InboundDeliveryError("Request timed out: Please try again");
        }
        if (error.message.includes("401")) {
          throw new InboundDeliveryError(
            "Unauthorized: Please log in again",
            401
          );
        }
        if (error.message.includes("403")) {
          throw new InboundDeliveryError(
            "Access denied: You don't have permission to view this data",
            403
          );
        }
        if (error.message.includes("500")) {
          throw new InboundDeliveryError(
            "Server error: Please try again later",
            500
          );
        }
      }

      // For any other errors, return empty data structure
      return {
        po_number: poNumber,
        data: [],
        pagination: {
          total: 0,
          page: page,
          limit: limit
        }
      };
    }
  },

  // Update ship to customer status
  updateShipToCustomer: async (
    poNumber: string,
    shipToCustomer: boolean
  ): Promise<any> => {
    try {
      const response = await api.patch<any>(
        `/api/v1/inbound-delivery/ship/${poNumber}?ship_to_customer=${shipToCustomer}`,
        { ship_to_customer: shipToCustomer }
      );

      // The backend returns an object with success, message, po_number, ship_to_customer, and toast
      if (!response || typeof response !== 'object') {
        throw new InboundDeliveryError("Invalid response format from server");
      }

      // Check if the operation was successful
      if (response.success === false) {
        throw new InboundDeliveryError(response.message || "Failed to update ship to customer status");
      }

      return response;
    } catch (error) {
      if (error instanceof InboundDeliveryError) {
        throw error;
      }

      if (error instanceof Error) {
        if (error.message.includes("Network Error")) {
          throw new InboundDeliveryError(
            "Network error: Please check your internet connection"
          );
        }
        if (error.message.includes("timeout")) {
          throw new InboundDeliveryError("Request timed out: Please try again");
        }
        if (error.message.includes("404")) {
          throw new InboundDeliveryError("PO not found", 404);
        }
        if (error.message.includes("401")) {
          throw new InboundDeliveryError(
            "Unauthorized: Please log in again",
            401
          );
        }
        if (error.message.includes("403")) {
          throw new InboundDeliveryError(
            "Access denied: You don't have permission to update this PO",
            403
          );
        }
        if (error.message.includes("500")) {
          throw new InboundDeliveryError(
            "Server error: Please try again later",
            500
          );
        }
      }

      throw new InboundDeliveryError(
        "Failed to update ship to customer status"
      );
    }
  },

  // Update quantity received
  updateQuantityReceived: async (
    poNumber: string,
    sku: string | null,
    quantityReceived: number,
    expectedDate?: string
  ): Promise<any> => {
    try {
      const response = await api.patch(`/api/v1/inbound-delivery/update-quantity`, {
        po_number: poNumber,
        sku: sku,
        quantity_received: quantityReceived,
        expected_date: expectedDate
      });
      
      return response;
    } catch (error) {
      console.error("Error updating quantity received:", error);
      throw error;
    }
  },

  // Update expected date
  updateExpectedDate: async (
    poNumber: string,
    newExpectedDate: string
  ): Promise<UpdateDateResponse> => {
    try {
      const response = await api.patch<UpdateDateResponse>(
        `/api/v1/inbound-delivery/po/${poNumber}/date`,
        { new_expected_date: newExpectedDate }
      );

      if (!response || !response.new_expected_date) {
        throw new InboundDeliveryError("Invalid response format from server");
      }

      return response;
    } catch (error) {
      if (error instanceof InboundDeliveryError) {
        throw error;
      }

      if (error instanceof Error) {
        if (error.message.includes("Network Error")) {
          throw new InboundDeliveryError(
            "Network error: Please check your internet connection"
          );
        }
        if (error.message.includes("timeout")) {
          throw new InboundDeliveryError("Request timed out: Please try again");
        }
        if (error.message.includes("400")) {
          throw new InboundDeliveryError(
            "Invalid date format. Please use YYYY-MM-DD",
            400
          );
        }
        if (error.message.includes("404")) {
          throw new InboundDeliveryError("PO not found", 404);
        }
        if (error.message.includes("401")) {
          throw new InboundDeliveryError(
            "Unauthorized: Please log in again",
            401
          );
        }
        if (error.message.includes("403")) {
          throw new InboundDeliveryError(
            "Access denied: You don't have permission to update this PO",
            403
          );
        }
        if (error.message.includes("500")) {
          throw new InboundDeliveryError(
            "Server error: Please try again later",
            500
          );
        }
      }

      throw new InboundDeliveryError("Failed to update expected date");
    }
  },
};

export default inboundDeliveryService;
