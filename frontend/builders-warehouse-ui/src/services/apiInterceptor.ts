import { AUTH_TOKEN_KEY } from "../config";

/**
 * API Interceptor
 *
 * This utility provides functions to wrap fetch API calls with
 * common headers and error handling for authentication.
 */

// Type definitions for better type safety
type FetchFunction = typeof fetch;
interface InterceptorOptions {
  tokenKey?: string;
  onAuthError?: () => void;
}

// Store original fetch to avoid recursion
const originalFetch = window.fetch;

/**
 * Creates a fetch interceptor that automatically adds authentication headers
 * to requests based on the token in localStorage.
 */
export function createFetchInterceptor(
  options: InterceptorOptions = {}
): FetchFunction {
  const {
    tokenKey = AUTH_TOKEN_KEY,
    onAuthError = () => {
      console.warn("Authentication error - redirecting to login");
      // You might add code here to redirect to login page
    },
  } = options;

  // Create a wrapper for the fetch function
  return function interceptedFetch(
    input: RequestInfo | URL,
    init: RequestInit = {}
  ): Promise<Response> {
    // Clone the init object to avoid mutating the original
    const modifiedInit: RequestInit = {
      ...init,
      // Updated CORS settings for development
      credentials: "omit", // Changed from "include" to "omit" for broader compatibility
      mode: "cors",
      redirect: "follow",
      // Additional CORS-friendly settings
      cache: "no-cache",
    };

    // Create headers object if it doesn't exist
    if (!modifiedInit.headers) {
      modifiedInit.headers = {};
    }

    // Convert headers to plain object if it's a Headers instance
    if (modifiedInit.headers instanceof Headers) {
      const headerValues: Record<string, string> = {};
      modifiedInit.headers.forEach((value, key) => {
        headerValues[key] = value;
      });
      modifiedInit.headers = headerValues;
    }

    // Check if this is a FormData request (file upload)
    const isFormDataRequest = modifiedInit.body instanceof FormData;

    // Ensure headers is an object
    if (typeof modifiedInit.headers === "object") {
      // Add common headers, but don't override Content-Type for FormData requests
      modifiedInit.headers = {
        ...modifiedInit.headers,
        Accept: "application/json",
      };

      // Only set Content-Type if it's not a FormData request
      // For FormData, the browser will automatically set the correct Content-Type with boundary
      if (!isFormDataRequest) {
        modifiedInit.headers = {
          ...modifiedInit.headers,
          "Content-Type": "application/json",
        };
      }
    }

    // Check if request already has our special marker to avoid recursion
    if (
      typeof modifiedInit.headers === "object" &&
      (modifiedInit.headers as Record<string, string>)[
        "X-Intercepted-Request"
      ] === "true"
    ) {
      // This request has already been intercepted, use original fetch
      return originalFetch(input, init);
    }

    // Get the auth token
    const token = localStorage.getItem(tokenKey);

    // Add auth token if it exists and Authorization header isn't already set
    if (
      token &&
      typeof modifiedInit.headers === "object" &&
      !("Authorization" in modifiedInit.headers)
    ) {
      modifiedInit.headers = {
        ...modifiedInit.headers,
        Authorization: `Bearer ${token}`,
      };
    }

    // Add marker to avoid recursion
    if (typeof modifiedInit.headers === "object") {
      modifiedInit.headers = {
        ...modifiedInit.headers,
        "X-Intercepted-Request": "true",
      };
    }

    // Log request details in development
    if (process.env.NODE_ENV !== "production") {
      console.log("Intercepted fetch request:", {
        url: typeof input === "string" ? input : input.toString(),
        method: modifiedInit.method || "GET",
        headers: modifiedInit.headers,
        isFormData: isFormDataRequest,
      });
    }

    // Make the fetch call using the ORIGINAL fetch to avoid recursion
    return originalFetch(input, modifiedInit)
      .then(async (response) => {
        // Log response details in development
        if (process.env.NODE_ENV !== "production") {
          console.log("Intercepted fetch response:", {
            url: typeof input === "string" ? input : input.toString(),
            status: response.status,
            headers: Object.fromEntries(response.headers),
          });
        }

        // Special handling for 401 (Unauthorized) responses
        if (response.status === 401) {
          // Call the onAuthError callback
          onAuthError();
        }

        return response;
      })
      .catch((error) => {
        console.error("Fetch error:", error);

        // Enhanced error handling
        // Rather than just propagating the error, wrap it with additional info
        const enhancedError = new Error(
          `Network error while fetching ${
            typeof input === "string" ? input : input.toString()
          }: ${error.message}`
        );
        (enhancedError as any).originalError = error;
        throw enhancedError;
      });
  };
}

/**
 * Replaces the global fetch function with the intercepted version
 * Returns a function that can be called to restore the original fetch
 */
export function setupGlobalInterceptor(
  options: InterceptorOptions = {}
): () => void {
  const interceptedFetch = createFetchInterceptor(options);

  // Replace global fetch
  window.fetch = interceptedFetch;

  // Return function to restore original fetch
  return function restoreOriginalFetch() {
    window.fetch = originalFetch;
  };
}

/**
 * Simple helper to make an authenticated fetch request
 */
export async function authenticatedFetch(
  url: string,
  options: RequestInit = {}
): Promise<Response> {
  const token = localStorage.getItem(AUTH_TOKEN_KEY);

  // Create headers with authorization
  const headers = new Headers(options.headers || {});
  if (token) {
    headers.set("Authorization", `Bearer ${token}`);
  }

  // Add common headers
  headers.set("Accept", "application/json");

  // Don't set Content-Type for FormData requests
  if (!(options.body instanceof FormData) && !headers.has("Content-Type")) {
    headers.set("Content-Type", "application/json");
  }

  // Important: Add marker to avoid recursion when global interceptor is active
  headers.set("X-Intercepted-Request", "true");

  // Return fetch with auth headers and CORS settings
  return originalFetch(url, {
    ...options,
    headers,
    // Updated CORS settings for development
    credentials: "omit", // Changed from "include" for broader compatibility
    mode: "cors",
    redirect: "follow",
    cache: "no-cache",
  });
}

export default {
  createFetchInterceptor,
  setupGlobalInterceptor,
  authenticatedFetch,
};
