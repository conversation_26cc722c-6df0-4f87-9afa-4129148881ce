import apiClient from "./apiClient";
import { format } from "date-fns";
import { addStoreFilterToParams, StoreFilterOptions } from "../hooks/useStoreFilter";

// Define enum for purchase order status
export enum PurchaseOrderStatus {
  PENDING = "pending",
  ORDERED = "ordered",
  DELIVERED = "delivered",
  CANCELLED = "cancelled",
}

// Define interfaces for Purchase Order-related data
export interface PurchaseOrderDetail {
  id?: number | string;
  purchase_order_id?: number;
  sku: string;
  description: string;
  quantity: number;
  quantity_ordered?: number;
  quantity_received?: number;
  expected_delivery_date?: string;
  unit_price?: number;
  total_price?: number;
  total?: number;
  notes?: string;
  created_at?: string;
  updated_at?: string;
}

export interface PurchaseOrderCreate {
  supplier_id: number;
  date: string; // Format: YYYY-MM-DD
  reference_number?: string;
  notes?: string;
  status: PurchaseOrderStatus;
  email_sent: boolean;
  email_status?: "NOT_SENT" | "SENT" | "ALREADY_SENT";
  details: PurchaseOrderDetail[];
}

export interface PurchaseOrder {
  id: number;
  supplier_id: number;
  supplier_name: string;
  date: string;
  reference_number?: string;
  notes?: string;
  status: PurchaseOrderStatus;
  email_sent: boolean;
  email_status?: "NOT_SENT" | "SENT" | "ALREADY_SENT";
  isEmailSending?: boolean;
  total_amount: number;
  created_at: string;
  updated_at: string;
  details: PurchaseOrderDetail[];
}

export interface PurchaseOrdersResponse {
  items: PurchaseOrder[];
  total: number;
  page: number;
  size: number;
}

export interface PurchaseOrderFilter {
  supplier_name?: string;
  start_date?: string;
  end_date?: string;
  status?: PurchaseOrderStatus;
  skip?: number;
  limit?: number;
  store_type?: string;
}

export interface EmailStatusUpdate {
  email_sent: boolean;
  email_status?: "NOT_SENT" | "SENT" | "ALREADY_SENT";
}

export interface DetailNotesUpdate {
  notes: string;
}

export interface DetailUpdate {
  quantity?: number;
  unit_price?: number;
  notes?: string;
  expected_delivery_date?: string;
}

// Create the service for handling Purchase Order-related API calls
export const purchaseOrderService = {
  // Get purchase orders with pagination and filtering
  getPurchaseOrders: async (
    filters: PurchaseOrderFilter = {},
    storeFilter?: StoreFilterOptions
  ): Promise<PurchaseOrdersResponse> => {
    const {
      supplier_name,
      start_date,
      end_date,
      status,
      skip = 0,
      limit = 100,
      store_type,
    } = filters;

    let queryParams = new URLSearchParams();
    queryParams.append("skip", skip.toString());
    queryParams.append("limit", limit.toString());
    if (supplier_name) queryParams.append("supplier_name", supplier_name);
    if (start_date) queryParams.append("start_date", start_date);
    if (end_date) queryParams.append("end_date", end_date);
    if (status) queryParams.append("status", status);
    if (store_type) queryParams.append("store_type", store_type);
    
    // Apply store-based filtering for staff/manager users
    if (storeFilter) {
      queryParams = addStoreFilterToParams(queryParams, storeFilter);
    }

    try {
      const endpoint = `/api/v1/purchase-orders?${queryParams.toString()}`;
      const response = await apiClient.get<any>(endpoint);
      
      // Map backend response to frontend format
      const mappedItems = (response?.items || []).map((item: any) => ({
        id: item.id,
        supplier_id: item.supplier_id,
        supplier_name: item.supplier_name,
        date: item.date || item.issue_date, // Map issue_date to date
        reference_number: item.po_number, // Map po_number to reference_number
        po_number: item.po_number, // Keep original field for compatibility
        notes: item.notes,
        status: item.status,
        email_sent: item.email_sent || false,
        email_status: item.email_status || "NOT_SENT",
        total_amount: item.total_amount || 0,
        created_at: item.created_at,
        updated_at: item.updated_at,
        details: item.details || []
      }));
      
      // Handle potentially incomplete responses by providing defaults
      return {
        items: mappedItems,
        total: response?.total || 0,
        page: response?.page || 1,
        size: response?.size || limit
      };
    } catch (error) {
      console.error("Error in getPurchaseOrders:", error);
      // Return a valid empty response instead of throwing an error
      return {
        items: [],
        total: 0,
        page: 1,
        size: limit
      };
    }
  },

  // Get a purchase order by ID
  getPurchaseOrderById: async (poId: number): Promise<PurchaseOrder> => {
    try {
      const response = await apiClient.get<any>(`/api/v1/purchase-orders/${poId}`);
      
      // Map backend response to frontend format
      const mappedResponse = {
        id: response.id,
        supplier_id: response.supplier_id,
        supplier_name: response.supplier_name,
        date: response.date || response.issue_date, // Map issue_date to date
        reference_number: response.po_number, // Map po_number to reference_number
        po_number: response.po_number, // Keep original field for compatibility
        notes: response.notes,
        status: response.status,
        email_sent: response.email_sent || false,
        email_status: response.email_status || "NOT_SENT",
        total_amount: response.total_amount || 0,
        created_at: response.created_at,
        updated_at: response.updated_at,
        details: Array.isArray(response.details) ? response.details : (Array.isArray(response.items) ? response.items : [])
      };
      
      return mappedResponse;
    } catch (error) {
      console.error(`Error in getPurchaseOrderById for ID ${poId}:`, error);
      throw error;
    }
  },

  // Create a new purchase order
  createPurchaseOrder: async (
    poData: PurchaseOrderCreate
  ): Promise<PurchaseOrder> => {
    return apiClient.post<PurchaseOrder>("/api/v1/purchase-orders", poData);
  },

  // Update an existing purchase order
  updatePurchaseOrder: async (
    poId: number,
    poData: Partial<PurchaseOrderCreate>
  ): Promise<PurchaseOrder> => {
    return apiClient.put<PurchaseOrder>(
      `/api/v1/purchase-orders/${poId}`,
      poData
    );
  },

  // Delete a purchase order
  deletePurchaseOrder: async (poId: number): Promise<void> => {
    return apiClient.delete<void>(`/api/v1/purchase-orders/${poId}`);
  },

  // Update email status
  updateEmailStatus: async (
    poId: number,
    emailStatus: EmailStatusUpdate
  ): Promise<PurchaseOrder> => {
    return apiClient.patch<PurchaseOrder>(
      `/api/v1/purchase-orders/${poId}/email-status`,
      emailStatus
    );
  },

  // Update purchase order detail
  updatePurchaseOrderDetail: async (
    detailId: number | string,
    detailUpdate: DetailUpdate
  ): Promise<PurchaseOrderDetail> => {
    return apiClient.patch<PurchaseOrderDetail>(
      `/api/v1/purchase-orders/details/${detailId}`,
      detailUpdate
    );
  },

  // Update purchase order detail notes
  updatePurchaseOrderDetailNotes: async (
    poId: number,
    detailId: number | string,
    notesUpdate: DetailNotesUpdate
  ): Promise<PurchaseOrderDetail> => {
    return apiClient.patch<PurchaseOrderDetail>(
      `/api/v1/purchase-orders/${poId}/details/${detailId}/notes`,
      notesUpdate
    );
  },

  // Send purchase order email to supplier
  sendPurchaseOrderEmail: async (poId: number): Promise<{
    success: boolean;
    message: string;
    email_sent: boolean;
    email_status: string;
    supplier_email: string;
    po_number: string;
    already_sent?: boolean;
  }> => {
    return apiClient.post<{
      success: boolean;
      message: string;
      email_sent: boolean;
      email_status: string;
      supplier_email: string;
      po_number: string;
      already_sent?: boolean;
    }>(`/api/v1/purchase-orders/${poId}/send-email`, {});
  },

  // Format a date for API requests
  formatDate: (date: Date): string => {
    return format(date, "yyyy-MM-dd");
  },
};

export default purchaseOrderService;
