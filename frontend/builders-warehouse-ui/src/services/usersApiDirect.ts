import { API_URL, ENDPOINTS } from '../config';

// Define interface for the raw API user item
interface RawApiUser {
  id: string | object;
  user_name?: string;
  email?: string;
  mobile_number?: string | null;
  is_active?: boolean;
  role?: string;
  created_at?: string;
  updated_at?: string | null;
  store_type?: {
    id?: number;
    name?: string;
  } | null;
  manager?: {
    id?: string;
    user_name?: string;
  } | null;
}

// Simple function to directly fetch users from the API
export const fetchUsersDirectly = async (skip: number, limit: number, search?: string) => {
  try {
    // Build query parameters
    const queryParams = new URLSearchParams();
    queryParams.append('skip', skip.toString());
    queryParams.append('limit', limit.toString());
    if (search) {
      queryParams.append('search', search);
    }
    
    // Get token from localStorage
    const token = localStorage.getItem('authToken');
    
    // Make direct API call
    const response = await fetch(`${API_URL}${ENDPOINTS.USERS}?${queryParams.toString()}`, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Authorization': token ? `Bearer ${token}` : ''
      }
    });
    
    // Check if response is successful
    if (!response.ok) {
      console.error(`API error: ${response.status}`);
      throw new Error(`API error: ${response.status}`);
    }
    
    // Parse response data
    const data = await response.json();
    console.log('Direct API response:', data);
    
    // Extract and map users
    if (data && data.items && Array.isArray(data.items)) {
      const displayUsers = data.items.map((item: RawApiUser) => ({
        id: String(item.id || ''),
        name: String(item.user_name || ''),
        email: String(item.email || ''),
        role: String(item.role || 'staff'),
        status: item.is_active === false ? 'inactive' : 'active',
        mobile: String(item.mobile_number || ''),
        storeType: item.store_type?.name || '',
        manager: item.manager?.user_name || '',
      }));
      
      return {
        users: displayUsers,
        total: data.total || displayUsers.length,
        page: data.page || 1,
        pages: data.pages || 1
      };
    }
    
    // Handle error case
    console.error('API response missing items array:', data);
    return { users: [], total: 0, page: 1, pages: 1 };
  } catch (error) {
    console.error('Error fetching users directly:', error);
    return { users: [], total: 0, page: 1, pages: 1 };
  }
}; 