import { API_URL } from '../config';

export interface PasswordResetRequest {
  email: string;
}

export interface PasswordResetResponse {
  message: string;
  success: boolean;
}

export interface PasswordResetConfirm {
  token: string;
  new_password: string;
}

export interface PasswordResetConfirmResponse {
  message: string;
  success: boolean;
}

class PasswordResetService {
  private baseUrl = `${API_URL}/api/v1/password-reset`;

  async requestPasswordReset(email: string): Promise<PasswordResetResponse> {
    try {
      const response = await fetch(`${this.baseUrl}/request`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ email }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Password reset request error:', error);
      throw error;
    }
  }

  async confirmPasswordReset(token: string, newPassword: string): Promise<PasswordResetConfirmResponse> {
    try {
      const response = await fetch(`${this.baseUrl}/confirm`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          token,
          new_password: newPassword,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.detail || `HTTP error! status: ${response.status}`);
      }

      return await response.json();
    } catch (error) {
      console.error('Password reset confirmation error:', error);
      throw error;
    }
  }
}

export const passwordResetService = new PasswordResetService(); 