import apiClient from './apiClient';
import { API_URL, ENDPOINTS, AUTH_TOKEN_KEY } from '../config';

// Define types for price list data
export interface PriceList {
  id: string;
  name: string;
  currency: string;
}

// Default price lists to use if API fails - using verified IDs from the database
export const DEFAULT_PRICE_LISTS: PriceList[] = [
  { id: '5272b303-6358-4bd8-aeb9-b5023437da53', name: 'Standard', currency: 'AUD' },
  { id: 'cc885aa2-f365-4e42-91ed-553771804d1c', name: 'Premium', currency: 'AUD' },
  { id: 'bde4439c-be05-421f-9359-d5efceb4ba8e', name: 'Wholesale', currency: 'AUD' },
  { id: '5d53d010-ab64-45ec-a197-3e453744e795', name: 'Regional Retail AUD Incl', currency: 'AUD' },
  { id: '4c25b60a-281e-467b-9c2c-7449c36d1d0a', name: 'Metro Trade AUD Incl', currency: 'AUD' },
  { id: '762f0693-aaa6-4c5c-a9b8-a05c55e05abc', name: 'Metro Retail AUD Incl', currency: 'AUD' },
  { id: '7ea9a07e-3534-460a-93a6-10a3a41810c2', name: 'Regional Trade AUD Incl', currency: 'AUD' }
];

// Price list service functions
const priceListService = {
  // Get all price lists
  getPriceLists: async (): Promise<PriceList[]> => {
    try {
      // Try to fetch from API first
      const token = localStorage.getItem(AUTH_TOKEN_KEY);
      console.log('Fetching price lists with token available:', !!token);
      
      const response = await fetch(`${API_URL}${ENDPOINTS.PRICE_LISTS}/`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'Authorization': token ? `Bearer ${token}` : '',
        },
        credentials: 'include',
        mode: 'cors'
      });
      
      if (!response.ok) {
        console.warn(`Server responded with status: ${response.status}, falling back to default price lists`);
        console.log('Default price lists used:', DEFAULT_PRICE_LISTS);
        return DEFAULT_PRICE_LISTS;
      }
      
      const data = await response.json();
      console.log('Price lists fetched from API:', data);
      
      if (!data || !Array.isArray(data) || data.length === 0) {
        console.warn('Received empty or invalid response, falling back to default price lists');
        return DEFAULT_PRICE_LISTS;
      }
      
      return data;
    } catch (error) {
      console.error('Error fetching price lists:', error);
      console.warn('Falling back to default price lists');
      return DEFAULT_PRICE_LISTS;
    }
  },
  
  // Fetch price lists for dropdown from the required endpoint
  fetchPriceListsForDropdown: async (): Promise<PriceList[]> => {
    try {
      console.log('Fetching price lists for dropdown from API...');
      
      // Get the auth token from localStorage using the correct key
      const token = localStorage.getItem(AUTH_TOKEN_KEY);
      console.log('Auth token available:', !!token);
      
      // Try different approaches in sequence until one works
      let response = null;
      let errorMessages: string[] = [];
      
      // Approach 1: API_URL + endpoint with auth and trailing slash
      try {
        const endpoint = `${API_URL}${ENDPOINTS.PRICE_LISTS}/`;
        console.log(`Trying endpoint with trailing slash: ${endpoint}`);
        const authResponse = await fetch(endpoint, {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            'Authorization': token ? `Bearer ${token}` : '',
          },
          credentials: 'include',
          mode: 'cors'
        });

        if (authResponse.ok) {
          console.log('Endpoint with trailing slash succeeded!');
          response = authResponse;
        } else {
          errorMessages.push(`Endpoint with trailing slash failed: ${authResponse.status}`);
        }
      } catch (error: any) {
        errorMessages.push(`Endpoint with trailing slash error: ${error?.message || 'Unknown error'}`);
      }
      
      // Approach 2: API_URL without trailing slash
      if (!response) {
        try {
          const directUrl = `${API_URL}${ENDPOINTS.PRICE_LISTS}`;
          console.log(`Trying direct URL without trailing slash: ${directUrl}`);
          const directResponse = await fetch(directUrl, {
            method: 'GET',
            headers: {
              'Accept': 'application/json',
              'Content-Type': 'application/json',
              'Authorization': token ? `Bearer ${token}` : '',
            },
            credentials: 'include',
            mode: 'cors'
          });
    
          if (directResponse.ok) {
            console.log('Direct URL without trailing slash succeeded!');
            response = directResponse;
          } else {
            errorMessages.push(`Direct URL without trailing slash failed: ${directResponse.status}`);
          }
        } catch (error: any) {
          errorMessages.push(`Direct URL without trailing slash error: ${error?.message || 'Unknown error'}`);
        }
      }
      
      // If all approaches failed
      if (!response) {
        console.error('All price list fetch approaches failed:', errorMessages.join(', '));
        console.warn('Using default price lists as fallback');
        return DEFAULT_PRICE_LISTS;
      }
      
      // Parse the response
      const data = await response.json();
      console.log('Price lists fetched for dropdown:', data);
      
      // Return default lists for invalid data formats
      if (!data || !Array.isArray(data) || data.length === 0) {
        console.warn('Empty or invalid price list response format, using defaults');
        return DEFAULT_PRICE_LISTS;
      }
      
      // Convert any non-standard format to our expected format
      const formattedLists = data.map(item => ({
        id: item.id || item.ID || item._id || item.uuid || 'unknown',
        name: item.name || item.NAME || item.title || 'Unnamed List',
        currency: item.currency || item.CURRENCY || 'AUD'
      }));
      
      console.log('Formatted price lists:', formattedLists);
      return formattedLists;
      
    } catch (error) {
      console.error('Error fetching price lists for dropdown:', error);
      console.warn('Using default price lists as fallback due to error');
      return DEFAULT_PRICE_LISTS;
    }
  },

  // Get a single price list by ID
  getPriceListById: async (priceListId: string): Promise<PriceList | null> => {
    try {
      // Try to get from API first
      const token = localStorage.getItem(AUTH_TOKEN_KEY);
      const response = await fetch(`${API_URL}${ENDPOINTS.PRICE_LISTS}/${priceListId}/`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'Authorization': token ? `Bearer ${token}` : '',
        },
        credentials: 'include',
        mode: 'cors'
      });
      
      if (!response.ok) {
        // If API fails, check if we have this ID in default lists
        const defaultPriceList = DEFAULT_PRICE_LISTS.find(p => p.id === priceListId);
        if (defaultPriceList) return defaultPriceList;
        return null;
      }
      
      const data = await response.json();
      
      if (!data) {
        const defaultPriceList = DEFAULT_PRICE_LISTS.find(p => p.id === priceListId);
        if (defaultPriceList) return defaultPriceList;
        return null;
      }
      
      return data;
    } catch (error) {
      console.error(`Error fetching price list ${priceListId}:`, error);
      // Try to find in defaults
      const defaultPriceList = DEFAULT_PRICE_LISTS.find(p => p.id === priceListId);
      if (defaultPriceList) return defaultPriceList;
      return null;
    }
  }
};

// Add test utility for the browser console (doesn't affect UI)
export const testPriceListFetch = async (): Promise<void> => {
  console.group('Price List Dropdown Functionality Test');
  try {
    console.log('1. Testing direct API call...');
    const token = localStorage.getItem(AUTH_TOKEN_KEY);
    
    // Test both endpoints
    console.log('1a. Testing configured endpoint...');
    try {
      const configResponse = await fetch(`${API_URL}${ENDPOINTS.PRICE_LISTS}`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'Authorization': token ? `Bearer ${token}` : '',
        },
        credentials: 'include', 
        mode: 'cors'
      });
      
      console.log('Configured API response status:', configResponse.status);
      
      if (configResponse.ok) {
        const configData = await configResponse.json();
        console.log('Configured endpoint data:', configData);
      }
    } catch (configError) {
      console.error('Configured endpoint test failed:', configError);
    }
    
    console.log('1b. Testing hardcoded endpoint...');
    const hardcodedResponse = await fetch(`${API_URL}${ENDPOINTS.PRICE_LISTS}`, {
      method: 'GET',
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'Authorization': token ? `Bearer ${token}` : '',
      }
    });
    
    console.log('Hardcoded API response status:', hardcodedResponse.status);
    
    if (!hardcodedResponse.ok) {
      throw new Error(`API error: ${hardcodedResponse.status}`);
    }
    
    const data = await hardcodedResponse.json();
    console.log('2. Raw API response:', data);
    
    console.log('3. Testing service function...');
    const serviceLists = await priceListService.fetchPriceListsForDropdown();
    console.log('4. Service function result:', serviceLists);
    
    console.log('5. Test successful!');
  } catch (error) {
    console.error('Test failed:', error);
  } finally {
    console.groupEnd();
  }
};

export default priceListService; 