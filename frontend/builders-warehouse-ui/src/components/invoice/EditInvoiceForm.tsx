import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import CustomerSelect from '../common/CustomerSelect';
import quoteService from '../../services/quoteService';
import inventoryService from '../../services/inventoryService';
import storeTypeService from '../../services/storeTypeService';
import SkuImage from '../common/SkuImage';
import {
  createInvoice,
  SaleTypeEnum,
  PaymentModeEnum,
} from '../../services/invoiceService';
import apiClient from '../../services/apiClient';
import { useStoreFilter, getDefaultStoreType } from '../../hooks/useStoreFilter';

// Breadcrumb navigation with enhanced styling
const BreadcrumbNav = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 0;
  font-size: 1.1rem;
  color: #6B7280;
  width: 100%;
  padding: 12px 20px;
  background-color: #F9FAFB;
  border-top: 1px solid #E5E7EB;
  border-left: 1px solid #E5E7EB;
  border-right: 1px solid #E5E7EB;
  border-top-left-radius: 4px;
  border-top-right-radius: 4px;
`;

const BreadcrumbSeparator = styled.span`
  margin: 0 0.5rem;
  color: #6B7280;
`;

const EditInvoiceTitle = styled.span`
  font-weight: 700;
  color: #111827;
  font-size: 1.25rem;
`;

const InvoiceText = styled.span`
  color: #6B7280;
  font-weight: 500;
`;

const BackButton = styled.a`
  display: flex;
  align-items: center;
  color: #042B41;
  text-decoration: none;
  font-weight: 500;
  margin-bottom: 1rem;
  
  &::before {
    content: '←';
    margin-right: 8px;
    font-size: 16px;
  }
`;

const FormContainer = styled.form`
  width: 100%;
  border: 1px solid #E5E7EB;
  border-radius: 0 0 4px 4px;
  padding: 1rem;
  background-color: white;
  margin-top: 0;
`;

const FormRow = styled.div`
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
`;

const FormColumn = styled.div`
  flex: 1;
  display: flex;
  flex-direction: column;
`;

const FormBoxesContainer = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 0.75rem;
  margin-bottom: 1rem;
  width: 100%;
`;

const FormBoxHeading = styled.div`
  font-size: 0.875rem;
  font-weight: 500;
  color: #111827;
  margin-bottom: 0.5rem;
  white-space: nowrap;
  
  &::after {
    content: attr(data-required);
    color: #ef4444;
  }
`;

const FormItemBox = styled.div`
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  flex: 1;

  &.notes-field {
    flex: 2;
  }

  &.quote-field {
    flex: 2;
  }

  &.date-field {
    flex: 1.2;
    min-width: 180px;
  }

  &.checkbox-field {
    flex: 1;
    display: flex;
    align-items: flex-start;
    margin-top: 0;
  }
`;

const FormInput = styled.input<{ disabled?: boolean }>`
  padding: 0.5rem;
  border: 1px solid #D1D5DB;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  background-color: ${props => props.disabled ? '#F9FAFB' : 'white'};
  color: ${props => props.disabled ? '#6B7280' : '#111827'};
  height: 36px;
  width: 100%;
  
  &:focus {
    outline: none;
    border-color: ${props => props.disabled ? '#D1D5DB' : '#042B41'};
    box-shadow: ${props => props.disabled ? 'none' : '0 0 0 2px rgba(4, 43, 65, 0.1)'};
  }
  
  &::placeholder {
    color: #9CA3AF;
  }
`;

const FormSelect = styled.select`
  padding: 0.5rem;
  border: 1px solid #D1D5DB;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  background-color: white;
  height: 36px;
  width: 100%;
  appearance: none;
  
  &:focus {
    outline: none;
    border-color: #042B41;
    box-shadow: 0 0 0 2px rgba(4, 43, 65, 0.1);
  }
`;

// Add SelectWrapper for custom dropdown icon
const SelectWrapper = styled.div`
  position: relative;

  &::after {
    content: '';
    position: absolute;
    top: 50%;
    right: 12px;
    transform: translateY(-50%);
    width: 0;
    height: 0;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-top: 5px solid #333;
    pointer-events: none;
  }
`;

const FormTextarea = styled.textarea`
  padding: 0.5rem;
  border: 1px solid #D1D5DB;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  min-height: 36px;
  resize: vertical;
  background-color: white;
  width: 100%;
  
  &:focus {
    outline: none;
    border-color: #042B41;
    box-shadow: 0 0 0 2px rgba(4, 43, 65, 0.1);
  }
`;

// Specifically for the Notes field
const NotesTextarea = styled(FormTextarea)`
  height: 36px;
  resize: none;
`;

const CheckboxContainer = styled.div`
  display: flex;
  align-items: center;
  margin-top: 25px;
  white-space: nowrap;
  justify-content: flex-end;
`;

const CheckboxInput = styled.input`
  width: 16px;
  height: 16px;
  margin: 0 0.375rem 0 0;
  cursor: pointer;
`;

const CheckboxLabel = styled.label`
  font-size: 0.875rem;
  color: #374151;
  cursor: pointer;
  line-height: 1;
  user-select: none;
  font-weight: 500;
`;

const StoreType = styled.div`
  padding: 0.5rem;
  border: 1px solid #D1D5DB;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  background-color: #F9FAFB;
  height: 36px;
  display: flex;
  align-items: center;
  width: 100%;
`;

const BottomRowContainer = styled.div`
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  margin-bottom: 1.5rem;
  width: 100%;
`;

const DateAndCheckboxContainer = styled.div`
  display: flex;
  flex-direction: column;
  flex: 1;
`;

const Table = styled.table`
  width: 100%;
  border-collapse: collapse;
  margin-top: 0;
  border: 1px solid #FFFFFF;
`;

const TableHeader = styled.thead`
  background-color: #042B41;
  color: white;
  
  th {
    padding: 0.75rem;
    text-align: left;
    font-weight: 500;
    font-size: 0.875rem;
    border: 0.5px solid white;
    
    &:nth-child(8), &:nth-child(9) {
      text-align: right;
    }
  }
`;

const TableBody = styled.tbody`
  tr {
    border-bottom: 1px solid #E5E7EB;
    
    &:last-child {
      border-bottom: none;
    }
  }
  
  td {
    padding: 0.75rem;
    font-size: 0.875rem;
    border: 1px solid #E5E7EB;
    
    &:nth-child(8), &:nth-child(9) {
      text-align: right;
    }
  }
`;

const TotalSection = styled.div`
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  margin-top: 2rem;
`;

const TotalRow = styled.div`
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-bottom: 0.75rem;
  
  &:last-child {
    margin-bottom: 0;
    margin-top: 0.5rem;
  }
`;

// Add tooltip styling
const Tooltip = styled.div`
  position: absolute;
  top: -60px;
  right: 0;
  width: 250px;
  padding: 10px;
  background-color: #333;
  color: white;
  border-radius: 4px;
  font-size: 12px;
  z-index: 100;
  visibility: hidden;
  opacity: 0;
  transition: visibility 0s, opacity 0.3s ease;
  
  &:after {
    content: '';
    position: absolute;
    bottom: -10px;
    right: 10px;
    border-width: 10px 10px 0;
    border-style: solid;
    border-color: #333 transparent transparent;
  }
`;

const InfoIconWrapper = styled.div`
  position: relative;
  display: inline-flex;
  margin-right: 8px;
  
  &:hover ${Tooltip} {
    visibility: visible;
    opacity: 1;
  }
`;

const InfoIcon = styled.span`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background-color: #3B82F6;
  color: white;
  font-size: 12px;
  font-weight: bold;
  cursor: help;
`;

const TotalLabel = styled.span`
  font-size: 0.875rem;
  font-weight: 500;
  width: 180px;
  text-align: right;
  margin-right: 1rem;
  display: flex;
  justify-content: flex-end;
  align-items: center;
`;

const TotalValue = styled.span`
  font-size: 0.875rem;
  font-weight: 500;
  width: 80px;
  text-align: right;
`;

const TotalInput = styled.input`
  font-size: 0.875rem;
  font-weight: 500;
  width: 80px;
  text-align: right;
  padding: 4px 8px;
  border: 1px solid #D1D5DB;
  border-radius: 4px;
`;

const Divider = styled.hr`
  width: 280px;
  margin: 0.5rem 0;
  border: none;
  border-top: 1px solid #E5E7EB;
  align-self: flex-end;
`;

const GrandTotalLabel = styled(TotalLabel)`
  font-weight: 600;
`;

const GrandTotalValue = styled(TotalValue)`
  font-weight: 600;
`;

const ButtonsContainer = styled.div`
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-top: 2rem;
  width: 640px;
  margin-left: auto;
  margin-right: auto;
`;

const ActionButton = styled.button`
  padding: 0.75rem 2rem;
  border-radius: 0.375rem;
  font-weight: 500;
  font-size: 0.875rem;
  cursor: pointer;
  width: 200px;
  height: 48px;
  transition: background-color 0.2s;
`;

const CancelButton = styled(ActionButton)`
  border: 1px solid #D1D5DB;
  background-color: white;
  color: #111827;
  
  &:hover {
    background-color: #F9FAFB;
  }
`;

const SubmitButton = styled(ActionButton)`
  border: none;
  background-color: #042B41;
  color: white;
  
  &:hover {
    background-color: #031F30;
  }
`;

const SaveAsDraftButton = styled(CancelButton)``;

const QuantityInput = styled(FormInput)`
  width: 60px;
  text-align: center;
`;

const PriceInput = styled(FormInput)`
  width: 80px;
  text-align: right;
`;

const AddItemButton = styled.button`
  background-color: #042B41;
  color: white;
  border: none;
  width: 28px;
  height: 28px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 18px;
  
  &:hover {
    background-color: #031F30;
  }
`;

const RemoveItemButton = styled.button`
  background-color: white;
  color: #EF4444;
  border: 1px solid #EF4444;
  width: 28px;
  height: 28px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 18px;
  
  &:hover {
    background-color: #FEF2F2;
  }
`;

// Add a wrapper styled component for CustomerSelect
const CustomerSelectWrapper = styled.div`
  input {
    height: 36px !important;
    font-size: 0.875rem;
    padding: 0.5rem;
    border-radius: 0.25rem;
  }
`;

// Create a DateInput component with the right styling
const DateInput = styled(FormInput)`
  height: 36px;
  padding: 0.375rem 0.5rem;
  width: 100%;
  min-width: 130px;
`;

// Date input wrapper without calendar icon
const DateInputWrapper = styled.div`
  position: relative;
  width: 100%;
  min-width: 180px;
`;

// Styled date input that shows the date picker
const StyledDateInput = styled(FormInput)`
  height: 36px;
  padding: 0.375rem 0.5rem;
  width: 100%;
  min-width: 180px;
  cursor: pointer;
  font-size: 0.875rem;
  
  &::-webkit-calendar-picker-indicator {
    opacity: 0;
    cursor: pointer;
    position: absolute;
    left: 0;
    top: 0;
    right: 0;
    bottom: 0;
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
  }
  
  &::-webkit-inner-spin-button {
    -webkit-appearance: none;
    display: none;
  }
  
  /* Additional styling for better calendar positioning */
  &::-webkit-datetime-edit {
    width: 100%;
    padding: 0;
  }
  
  &::-webkit-datetime-edit-fields-wrapper {
    width: 100%;
  }
`;

// Create a QuoteSelect component for consistent styling - now searchable
const QuoteSelectWrapper = styled.div`
  position: relative;
  width: 100%;
`;

const QuoteSearchInput = styled(FormInput)`
  height: 36px;
  padding: 0.375rem 0.5rem;
  width: 100%;
`;

const QuoteDropdown = styled.div<{ isOpen: boolean }>`
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #D1D5DB;
  border-top: none;
  border-radius: 0 0 0.25rem 0.25rem;
  max-height: 200px;
  overflow-y: auto;
  z-index: 1000;
  display: ${props => props.isOpen ? 'block' : 'none'};
`;

const QuoteOption = styled.div`
  padding: 0.5rem;
  cursor: pointer;
  font-size: 0.875rem;
  
  &:hover {
    background-color: #F3F4F6;
  }
`;

// SKU Select component - now searchable
const SKUSelectWrapper = styled.div`
  position: relative;
  width: 100%;
`;

const SKUSearchInput = styled(FormInput)`
  width: 100%;
`;

const SKUDropdown = styled.div<{ isOpen: boolean }>`
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #D1D5DB;
  border-top: none;
  border-radius: 0 0 0.25rem 0.25rem;
  max-height: 200px;
  overflow-y: auto;
  z-index: 1000;
  display: ${props => props.isOpen ? 'block' : 'none'};
`;

const SKUOption = styled.div`
  padding: 0.375rem;
  cursor: pointer;
  font-size: 0.875rem;
  background-color: white;
  border-bottom: 1px solid #E5E7EB;
  display: flex;
  align-items: center;
  gap: 8px;
  
  &:hover {
    background-color: #F3F4F6;
  }
  
  &:last-child {
    border-bottom: none;
  }
`;

const SKUOptionContent = styled.div`
  flex: 1;
`;

const SelectedSKUContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
`;

// Add refresh button styles
const RefreshButton = styled.button`
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 2px 4px;
  margin-left: 8px;
  border-radius: 3px;
  font-size: 12px;
  opacity: 0.8;
  
  &:hover {
    opacity: 1;
    background-color: rgba(255, 255, 255, 0.1);
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const HeaderWithRefresh = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
`;

// Helper text for PO Number
const HelperText = styled.div`
  font-size: 0.75rem;
  color: #6B7280;
  margin-top: 0.25rem;
  font-style: italic;
`;

interface InvoiceItemQuantity {
  units: number | null;
  boxes: number | null;
  pieces: number | null;
  m2: number | null;
  total: number;
}

interface InvoiceItem {
  id: string;
  sku: string;
  description: string;
  quantity: InvoiceItemQuantity;
  unitPrice: number;
  totalPrice: number;
  [key: string]: any; // Allow string indexing
}

interface InvoiceTotals {
  orderTotal: number;
  surcharge: number;
  gst: number;
  shipping: number;
  grandTotal: number;
}

// Update the Quote interface to include both possible structures
interface Quote {
  id: string;
  quote_no: string;
  quote_items?: any[];
  items?: any[];
  deliver_to_address?: string;
}

// Define interface for the new structure of quote items from API
interface QuoteItemAPI {
  sku_code?: string;
  sku?: string;
  description?: string;
  quantity_units?: number;
  quantity_boxes?: number;
  quantity_pieces?: number;
  quantity_m2?: number;
  units?: number;
  boxes?: number;
  pieces?: number;
  m2?: number;
  unit_price?: number;
  totalPrice?: number;
  total_price?: number;
}

interface InvoiceData {
  id?: string; // Add optional id field
  storeType: string;
  saleType: string;
  modeOfPayment: string;
  poNumber: string;
  invoiceTo: string;
  deliverToAddress: string;
  invoiceNo: string;
  notes: string;
  linkedQuote: string;
  date: string;
  dontSendPo: boolean;
  items: InvoiceItem[];
  totals: InvoiceTotals;
}

interface EditInvoiceProps {
  initialData?: InvoiceData;
  onSubmit: (data: InvoiceData) => void;
  onSaveAsDraft: (data: InvoiceData) => void;
  onCancel: () => void;
  isLoading?: boolean;
}

// Custom styled section for the Create Invoice header - only keep one definition
const CreateInvoiceHeader = styled.div`
  font-size: 1.5rem;
  font-weight: 600;
  color: #111827;
  margin: 1rem 0;
`;

// Create a new Button component to replace the removed buttons
const Button = styled.button<{ primary?: boolean; secondary?: boolean }>`
  padding: 0.75rem 1.5rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s;
  
  ${({ primary }) => primary && `
    background-color: #042B41;
    color: white;
    border: 1px solid #042B41;
    
    &:hover:not(:disabled) {
      background-color: #033957;
    }
  `}
  
  ${({ secondary }) => secondary && `
    background-color: white;
    color: #042B41;
    border: 1px solid #D1D5DB;
    
    &:hover:not(:disabled) {
      background-color: #F9FAFB;
    }
  `}
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
`;

// Add interface for StoreType
interface StoreType {
  id: number;
  name: string;
  description?: string;
}

// Define ApiError interface for error type safety
interface ApiError {
  response?: {
    status: number;
    data: {
      detail?: string | any[];
      message?: string;
      [key: string]: any;
    };
    headers?: any;
  };
  message?: string;
  code?: string;
}

const EditInvoiceForm: React.FC<EditInvoiceProps> = ({
  initialData,
  onSubmit,
  onSaveAsDraft,
  onCancel,
  isLoading: parentIsLoading = false
}) => {
  const navigate = useNavigate();
  const storeFilter = useStoreFilter();

  // State for the form
  const [formData, setFormData] = useState<InvoiceData>({
    storeType: '', // Empty by default, user must select
    saleType: '',
    modeOfPayment: '',
    poNumber: '',
    invoiceTo: '',
    deliverToAddress: '',
    invoiceNo: '',
    notes: '',
    linkedQuote: '',
    date: new Date().toISOString().split('T')[0], // Today's date in YYYY-MM-DD format
    dontSendPo: false,
    items: [],
    totals: {
      orderTotal: 0,
      surcharge: 0,
      gst: 0,
      shipping: 0,
      grandTotal: 0
    }
  });

  // Local loading state for form operations
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const [isLoadingQuotes, setIsLoadingQuotes] = useState<boolean>(false);
  const [quotes, setQuotes] = useState<Quote[]>([]);
  const [inventoryItems, setInventoryItems] = useState<any[]>([]);
  const [storeTypes, setStoreTypes] = useState<StoreType[]>([]);
  const [isLoadingStoreTypes, setIsLoadingStoreTypes] = useState(false);
  const [isLoadingInventory, setIsLoadingInventory] = useState(false);
  const [lastInventoryRefresh, setLastInventoryRefresh] = useState<number>(Date.now());

  // State for searchable dropdowns
  const [quoteSearchTerm, setQuoteSearchTerm] = useState('');
  const [isQuoteDropdownOpen, setIsQuoteDropdownOpen] = useState(false);
  const [skuSearchTerms, setSKUSearchTerms] = useState<{ [key: number]: string }>({});
  const [openSKUDropdowns, setOpenSKUDropdowns] = useState<{ [key: number]: boolean }>({});

  // Ref for quote dropdown to handle outside clicks
  const quoteDropdownRef = useRef<HTMLDivElement>(null);

  // Initialize form with provided data if available
  useEffect(() => {
    if (initialData) {
      setFormData(initialData);
    } else {
      // Set default store type for staff/manager users
      const defaultStore = getDefaultStoreType(storeFilter);
      if (defaultStore) {
        setFormData(prev => ({
          ...prev,
          storeType: defaultStore.name
        }));
      }
    }
    // Intentionally only depend on initialData to prevent infinite rerenders
    // storeFilter is only used for initial setup and doesn't need to trigger rerenders
  }, [initialData]);

  // Validate the linkedQuote whenever it changes
  useEffect(() => {
    if (formData?.linkedQuote && formData.linkedQuote.trim() !== '') {
      const isValidUuid = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(formData.linkedQuote);

      if (!isValidUuid) {
        console.warn('Non-UUID quote ID detected:', formData.linkedQuote);
      }
    }
    // Using formData?.linkedQuote directly in the dependency array might cause rerenders
    // as formData is an object that changes on every render, even if linkedQuote is the same
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [formData?.linkedQuote]);

  // Fetch quotes for linking
  useEffect(() => {
    fetchQuotes();
  }, []);

  // Function to fetch quotes
  const fetchQuotes = async () => {
    setIsLoadingQuotes(true);
    try {
      // Fetch all current quotes with larger limit to get comprehensive data
      const response = await quoteService.getCurrentQuotes({ limit: 100 });

      if (response && response.items) {
        setQuotes(response.items.map(quote => ({
          id: quote.id,
          quote_no: quote.quote_no,
          items: quote.quote_items
        })));
        console.log('Successfully loaded quotes:', response.items.length);
      } else {
        console.warn('Quote data was returned in an unexpected format');
        setQuotes([]);
      }
    } catch (error) {
      console.error('Error fetching quotes:', error);
      // Keep last known good state if there was an error
    } finally {
      setIsLoadingQuotes(false);
    }
  };

  // Function to fetch store types
  const fetchStoreTypes = async () => {
    setIsLoadingStoreTypes(true);
    try {
      const response = await storeTypeService.getStoreTypes();
      if (response && response.length > 0) {
        setStoreTypes(response);
      } else {
        console.warn('No store types returned from API, using defaults');
        // Set default store types if API returns empty array
        setStoreTypes([
          { id: 1, name: 'Cranbourne' },
          { id: 2, name: 'Sale' }
        ]);
      }
    } catch (error) {
      console.error('Error fetching store types:', error);
      // Set default store types if API fails
      setStoreTypes([
        { id: 1, name: 'Cranbourne' },
        { id: 2, name: 'Sale' }
      ]);
    } finally {
      setIsLoadingStoreTypes(false);
    }
  };

  // Function to fetch inventory items for SKU dropdown
  const fetchInventoryItems = async (force: boolean = false) => {
    setIsLoadingInventory(true);
    try {
      // Clear cache if force refreshing to ensure fresh data
      if (force) {
        inventoryService.clearInventoryCache();
      }

      const response = await inventoryService.getAllInventory();
      // Log the response to debug
      console.log('Fetched inventory items:', response);
      setInventoryItems(response || []);

      // Update refresh timestamp when force refreshing
      if (force) {
        setLastInventoryRefresh(Date.now());
        console.log('Inventory data refreshed with latest images');
      }
    } catch (error) {
      console.error('Error fetching inventory items:', error);
      setInventoryItems([]);
    } finally {
      setIsLoadingInventory(false);
    }
  };

  // Function to load a quote's items into the invoice
  const loadQuoteItems = async (quoteId: string) => {
    if (!quoteId) return;

    try {
      console.log(`Loading quote items for quote ID: ${quoteId}`);

      // Use the specific API endpoint for getting quote details
      const quote: any = await apiClient.get(`/api/v1/quotes/${quoteId}`);

      if (!quote) {
        console.error('Quote not found:', quoteId);
        return;
      }

      console.log('Quote data loaded:', quote);

      // Convert quote items to invoice items format - handling both possible API response structures
      const quoteItems: QuoteItemAPI[] = (quote.items || quote.quote_items || []) as QuoteItemAPI[];

      const invoiceItems = quoteItems.length > 0
        ? quoteItems.map((item: QuoteItemAPI) => ({
          id: String(Math.random()),
          sku: item.sku_code || item.sku || '',
          description: item.description || '',
          quantity: {
            units: item.quantity_units || item.units || 0,
            boxes: item.quantity_boxes || item.boxes || 0,
            pieces: item.quantity_pieces || item.pieces || 0,
            m2: item.quantity_m2 || item.m2 || 0,
            total: (item.quantity_units || item.units || 0) +
              (item.quantity_boxes || item.boxes || 0) +
              (item.quantity_pieces || item.pieces || 0) +
              (item.quantity_m2 || item.m2 || 0)
          },
          unitPrice: item.unit_price || 0,
          totalPrice: item.totalPrice || item.total_price ||
            (item.unit_price || 0) * ((item.quantity_units || item.units || 0) +
              (item.quantity_boxes || item.boxes || 0) +
              (item.quantity_pieces || item.pieces || 0) +
              (item.quantity_m2 || item.m2 || 0))
        }))
        : [];

      console.log('Transformed invoice items:', invoiceItems);

      // Update form data with quote information
      setFormData(prev => ({
        ...prev,
        deliverToAddress: quote.deliver_to_address || prev.deliverToAddress,
        items: invoiceItems.length > 0 ? invoiceItems : prev.items
      }));

      // Recalculate totals after updating items
      if (invoiceItems.length > 0) {
        calculateTotals(invoiceItems);
      }
    } catch (error) {
      console.error('Error loading quote:', error);
      // Keep the form in its current state if there was an error
    }
  };

  // Use effect to fetch necessary data
  useEffect(() => {
    fetchQuotes();
    fetchStoreTypes();
    fetchInventoryItems();
    // These functions don't depend on any state that changes, so they only need to run once
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Auto-refresh inventory data every 30 seconds to pick up newly uploaded images
  useEffect(() => {
    const interval = setInterval(() => {
      // Only auto-refresh if page is visible and not currently loading
      if (!document.hidden && !isLoadingInventory) {
        console.log('Auto-refreshing inventory data...');
        fetchInventoryItems(true);
      }
    }, 30000); // 30 seconds

    return () => clearInterval(interval);
  }, [isLoadingInventory]);

  // Refresh inventory when page becomes visible (user returns from other tabs/windows)
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!document.hidden && !isLoadingInventory) {
        console.log('Page became visible, refreshing inventory data...');
        fetchInventoryItems(true);
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('focus', handleVisibilityChange);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('focus', handleVisibilityChange);
    };
  }, [isLoadingInventory]);

  // Handle outside clicks for quote dropdown
  useEffect(() => {
    const handleOutsideClick = (event: MouseEvent) => {
      if (quoteDropdownRef.current && !quoteDropdownRef.current.contains(event.target as Node)) {
        setIsQuoteDropdownOpen(false);
      }
    };

    if (isQuoteDropdownOpen) {
      document.addEventListener('mousedown', handleOutsideClick);
    }

    return () => {
      document.removeEventListener('mousedown', handleOutsideClick);
    };
  }, [isQuoteDropdownOpen]);

  // Form field change handlers
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;

    // If the linked quote changes, load its items
    if (name === 'linkedQuote' && value) {
      loadQuoteItems(value);
    }

    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleCheckboxChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: checked
    }));
  };

  // Item-specific changes
  const handleItemChange = (index: number, field: string, value: any) => {
    const newItems = [...formData.items];

    if (field.startsWith('quantity.')) {
      const quantityField = field.split('.')[1];
      newItems[index].quantity = {
        ...newItems[index].quantity,
        [quantityField]: Number(value) || 0
      };

      // Recalculate total quantity
      let total = 0;
      if (newItems[index].quantity.units) total += Number(newItems[index].quantity.units);
      if (newItems[index].quantity.boxes) total += Number(newItems[index].quantity.boxes);
      if (newItems[index].quantity.pieces) total += Number(newItems[index].quantity.pieces);
      if (newItems[index].quantity.m2) total += Number(newItems[index].quantity.m2);

      newItems[index].quantity.total = total;
    } else if (field === 'sku') {
      newItems[index].sku = value;

      // If SKU is entered, try to fetch inventory data
      if (value && value.trim().length > 0) {
        // Find matching inventory item from our existing list first for immediate response
        const matchingItem = inventoryItems.find(item => item.sku_code === value);
        if (matchingItem) {
          // Immediately update description and unit price
          newItems[index].description = matchingItem.description || matchingItem.style_code || '';

          // Only update unit price if it's not set yet
          if (newItems[index].unitPrice === 0) {
            newItems[index].unitPrice = matchingItem.trade_price;
            // Recalculate total price
            newItems[index].totalPrice = newItems[index].quantity.total * matchingItem.trade_price;
          }
        }

        // Also fetch from API to ensure data is accurate and up-to-date
        const fetchInventoryData = async () => {
          try {
            const inventory = await inventoryService.getInventoryBySku(value);
            if (inventory) {
              // Update description and potentially unit price
              newItems[index].description = inventory.description || inventory.style_code || '';

              // Only update unit price if it's not set yet
              if (newItems[index].unitPrice === 0) {
                newItems[index].unitPrice = inventory.trade_price;
                // Recalculate total price
                newItems[index].totalPrice = newItems[index].quantity.total * inventory.trade_price;
              }

              setFormData(prev => ({
                ...prev,
                items: newItems
              }));
              calculateTotals();
            }
          } catch (error) {
            console.error('Error fetching inventory data:', error);
            // Don't show error to user for better UX
          }
        };

        fetchInventoryData();
      }
    } else if (field === 'description') {
      newItems[index].description = value;
    } else if (field === 'unitPrice') {
      newItems[index].unitPrice = Number(value) || 0;
    }

    // Update total price whenever quantity or unit price changes
    if (field === 'unitPrice' || field.startsWith('quantity.')) {
      newItems[index].totalPrice = newItems[index].quantity.total * newItems[index].unitPrice;
    }

    setFormData(prev => ({
      ...prev,
      items: newItems
    }));

    calculateTotals();
  };

  // Add a new empty item to the invoice
  const handleAddItem = () => {
    // Create a unique ID for the new item
    const newId = Math.random().toString(36).substring(2, 11);

    setFormData(prev => {
      const newItem: InvoiceItem = {
        id: newId,
        sku: '',
        description: '',
        quantity: {
          units: null,
          boxes: null,
          pieces: null,
          m2: null,
          total: 0
        },
        unitPrice: 0,
        totalPrice: 0
      };

      const updatedItems = [...prev.items, newItem];

      return {
        ...prev,
        items: updatedItems
      };
    });
  };

  // Remove an item from the invoice
  const handleRemoveItem = (index: number) => {
    setFormData(prev => {
      const updatedItems = prev.items.filter((_, i) => i !== index);

      // Recalculate totals
      const totals = calculateTotals(updatedItems);

      return {
        ...prev,
        items: updatedItems,
        totals
      };
    });
  };

  // Calculate invoice totals
  const calculateTotals = (items = formData.items) => {
    const orderTotal = items.reduce((sum, item) => sum + (item?.totalPrice || 0), 0);
    // Safely check modeOfPayment
    const surcharge = orderTotal * (formData?.modeOfPayment === 'Credit Card' ? 0.015 : 0);
    const subtotal = orderTotal + surcharge;
    const gst = subtotal * 0.1; // 10% GST
    // Safely access shipping with fallback
    const shipping = formData?.totals?.shipping || 0;
    const grandTotal = subtotal + gst + shipping;

    // Update the form data with the new totals
    setFormData(prev => ({
      ...prev,
      totals: {
        orderTotal,
        surcharge,
        gst,
        shipping,
        grandTotal
      }
    }));

    return {
      orderTotal,
      surcharge,
      gst,
      shipping,
      grandTotal
    };
  };

  // Format currency for display
  const formatCurrency = (amount: number) => {
    return `$${amount.toFixed(2)}`;
  };

  // Add a utility function to handle different quote ID formats
  const getQuoteIdForApi = (quoteId: string): string | undefined => {
    if (!quoteId || quoteId.trim() === '') {
      return undefined;
    }

    // Check if it's a valid UUID already
    if (/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(quoteId)) {
      return quoteId;
    }

    // Try to find the full UUID from the quotes array based on numeric ID or quote_no
    const foundQuote = quotes.find(q => q.id === quoteId || q.quote_no === quoteId);
    if (foundQuote) {
      return foundQuote.id;
    }

    // If we can't match it, return undefined to skip this field
    return undefined;
  };

  // Add a utility function to verify if a string is a valid UUID format
  const isValidUuid = (id: string): boolean => {
    return /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(id);
  };

  // Filter quotes based on search term
  const filteredQuotes = quotes.filter(quote =>
    quote.quote_no.toLowerCase().includes(quoteSearchTerm.toLowerCase()) ||
    quote.id.toLowerCase().includes(quoteSearchTerm.toLowerCase())
  );

  // Filter inventory items based on search term for specific row
  const getFilteredInventoryItems = (rowIndex: number) => {
    const searchTerm = skuSearchTerms[rowIndex] || '';
    if (!searchTerm) return inventoryItems.slice(0, 10); // Show first 10 if no search

    return inventoryItems.filter(item =>
      item.sku_code?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.description?.toLowerCase().includes(searchTerm.toLowerCase())
    ).slice(0, 10); // Limit to 10 results
  };

  // Handle quote selection
  const handleQuoteSelect = (quote: Quote) => {
    setFormData(prev => ({
      ...prev,
      linkedQuote: quote.id
    }));
    setQuoteSearchTerm(quote.quote_no);
    setIsQuoteDropdownOpen(false);
    loadQuoteItems(quote.id);
  };

  // Handle SKU selection
  const handleSKUSelect = (index: number, item: any) => {
    handleItemChange(index, 'sku', item.sku_code);
    setSKUSearchTerms(prev => ({
      ...prev,
      [index]: item.sku_code
    }));
    setOpenSKUDropdowns(prev => ({
      ...prev,
      [index]: false
    }));
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate form data
    // Check for required fields
    const requiredFields = [
      { field: 'saleType', label: 'Sale Type' },
      { field: 'modeOfPayment', label: 'Mode of Payment' },
      { field: 'invoiceTo', label: 'Invoice To' },
      { field: 'deliverToAddress', label: 'Deliver to Address' },
      { field: 'date', label: 'Date' }
    ];

    // Only require PO Number if sale type is not "Store"
    if (formData.saleType.toLowerCase() !== 'store') {
      requiredFields.push({ field: 'poNumber', label: 'PO Number' });
    }

    for (const { field, label } of requiredFields) {
      if (!formData[field as keyof InvoiceData]) {
        alert(`Please fill in the ${label} field`);
        return;
      }
    }

    // Validate items
    const hasValidItems = formData.items.some(item => {
      const hasQuantity =
        (item.quantity.units && item.quantity.units > 0) ||
        (item.quantity.boxes && item.quantity.boxes > 0) ||
        (item.quantity.pieces && item.quantity.pieces > 0) ||
        (item.quantity.m2 && item.quantity.m2 > 0);

      return item.sku && item.description && hasQuantity && item.unitPrice > 0;
    });

    if (!hasValidItems) {
      alert('Please add at least one item with SKU, description, quantity, and price');
      return;
    }

    // Get properly formatted quote ID for API
    const processedQuoteId = getQuoteIdForApi(formData.linkedQuote);
    console.log('Processed Quote ID:', processedQuoteId);

    // Find the store type ID from the selected store type name
    const selectedStoreType = storeTypes.find(type => type.name === formData.storeType);
    const storeTypeId = selectedStoreType ? selectedStoreType.id : 1; // Default to 1 if not found

    // Handle customer_id - convert to string as the schema expects UUID4 but database uses integers
    const originalCustomerId = formData.invoiceTo;
    console.log('Original customer ID:', originalCustomerId, 'Type:', typeof originalCustomerId);

    // Ensure we have a valid customer ID string
    let customerIdString: string;
    if (typeof originalCustomerId === 'number') {
      customerIdString = String(originalCustomerId);
    } else if (typeof originalCustomerId === 'string') {
      customerIdString = originalCustomerId;
    } else {
      throw new Error('Invalid customer ID type');
    }
    console.log('Customer ID as string:', customerIdString);

    // Generate a UUID for customer_id since the schema expects UUID4
    // We'll use a deterministic approach based on the integer ID
    const customerUuid = `00000000-0000-4000-8000-${customerIdString.padStart(12, '0')}`;
    console.log('Generated customer UUID:', customerUuid);

    // Generate a UUID for company_id (required field)
    const companyUuid = '00000000-0000-4000-8000-000000000001'; // Default company UUID
    console.log('Using company UUID:', companyUuid);

    // Convert UI model to API model
    const apiData: any = {
      store_type_id: storeTypeId, // Use store_type_id instead of store_type
      // Handle sale_type properly, converting from UI format to API enum
      sale_type: formData.saleType.toLowerCase() === 'trade' ?
        SaleTypeEnum.TRADE :
        formData.saleType.toLowerCase() === 'retail' ?
          SaleTypeEnum.RETAIL :
          formData.saleType.toLowerCase() === 'store' ?
            SaleTypeEnum.STORE :
            SaleTypeEnum.TRADE, // Default fallback

      // Handle mode_of_payment properly, converting from UI format to API enum
      mode_of_payment:
        formData.modeOfPayment.toLowerCase() === 'credit card' ?
          PaymentModeEnum.CREDIT_CARD :
          formData.modeOfPayment.toLowerCase() === 'cash' ?
            PaymentModeEnum.CASH :
            formData.modeOfPayment.toLowerCase() === 'bank transfer' ?
              PaymentModeEnum.BANK_TRANSFER :
              PaymentModeEnum.CASH, // Default fallback

      purchase_order_number: formData.poNumber,
      customer_id: customerUuid, // Use the generated UUID
      company_id: companyUuid, // Include required company_id field
      deliver_to_address: formData.deliverToAddress,
      date: formData.date,
      dont_send_po: formData.dontSendPo,
      notes: formData.notes,
      shipping: String(formData.totals.shipping || 0),
      total_order: String(formData.totals.orderTotal || 0),
      credit_card_surcharge: String(formData.totals.surcharge || 0),
      total_gst: String(formData.totals.gst || 0),
      grand_total: String(formData.totals.grandTotal || 0),

      // Map invoice items properly
      items: formData.items.filter(item => {
        // Only include items with at least one quantity field filled
        return (
          (item.quantity.units || 0) > 0 ||
          (item.quantity.boxes || 0) > 0 ||
          (item.quantity.pieces || 0) > 0 ||
          (item.quantity.m2 || 0) > 0
        ) && item.sku && item.description;
      }).map(item => ({
        sku: item.sku,
        description: item.description,
        units: item.quantity.units || 0,
        boxes: item.quantity.boxes || 0,
        pieces: item.quantity.pieces || 0,
        m2: item.quantity.m2 || 0,
        unit_price: String(item.unitPrice)
      }))
    };

    // Only add linked_quote_id if it's a valid UUID
    if (processedQuoteId && isValidUuid(processedQuoteId)) {
      apiData.linked_quote_id = processedQuoteId;
      console.log('Added linked_quote_id to payload:', processedQuoteId);
    } else if (formData.linkedQuote) {
      console.log('Skipping linked_quote_id as it is not a valid UUID:', formData.linkedQuote);
    }

    console.log('Submitting form data:', JSON.stringify(apiData, null, 2));

    // Submit the form without showing alerts afterwards
    setIsSubmitting(true);
    try {
      // Direct call to API with modified data
      const result = await createInvoice(apiData);
      console.log('Invoice created successfully:', result);

      // Call the parent's onSubmit handler with the result from the API
      onSubmit({
        ...formData,
        // If the backend returned an ID, add it to the data
        id: result.id,
        invoiceNo: result.invoice_number?.formatted_number || result.invoice_no || formData.invoiceNo
      });

      // Navigate after a short delay to ensure the onSubmit has time to process
      setTimeout(() => {
        navigate('/invoices');
      }, 100);
    } catch (error: any) {
      console.error('Error creating invoice:', error);

      // Cast to ApiError for type safety
      const apiError = error as ApiError;

      // Show error details in console for debugging
      if (apiError.response && apiError.response.data) {
        console.error('API error response:', apiError.response.data);
      }

      // Display a more specific error message if available
      if (apiError.response && apiError.response.data) {
        // Check if there's a structured error message
        const errorData = apiError.response.data;
        if (errorData.detail) {
          if (typeof errorData.detail === 'string') {
            alert(`Error: ${errorData.detail}`);
          } else if (Array.isArray(errorData.detail)) {
            // Format validation errors
            const validationErrors = errorData.detail.map((err: any) =>
              `${err.loc.join('.')} - ${err.msg}`
            ).join('\n');
            alert(`Validation errors:\n${validationErrors}`);
          }
        } else {
          alert(`Error ${apiError.response.status}: ${JSON.stringify(errorData)}`);
        }
      } else {
        alert('There was an error creating the invoice. Please try again.');
      }
    } finally {
      setIsSubmitting(false);
    }
  };
  // Handle save as draft
  const handleSaveAsDraftInternal = (e: React.MouseEvent) => {
    e.preventDefault();
    onSaveAsDraft(formData);
  };

  const handleCancel = (e: React.MouseEvent) => {
    e.preventDefault();
    onCancel();
  };

  // Add shipping input handler
  const handleShippingChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseFloat(e.target.value.replace(/[^\d.-]/g, '')) || 0;

    setFormData(prev => ({
      ...prev,
      totals: {
        ...prev.totals,
        shipping: value,
        // Recalculate grand total
        grandTotal: prev.totals.orderTotal + prev.totals.surcharge + prev.totals.gst + value
      }
    }));
  };

  // Add total order input handler
  const handleTotalOrderChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseFloat(e.target.value.replace(/[^\d.-]/g, '')) || 0;

    // Calculate surcharge based on payment mode
    const surcharge = formData.modeOfPayment === 'Credit Card' ? value * 0.015 : 0;
    const subtotal = value + surcharge;
    const gst = subtotal * 0.1;

    setFormData(prev => ({
      ...prev,
      totals: {
        ...prev.totals,
        orderTotal: value,
        surcharge,
        gst,
        grandTotal: subtotal + gst + prev.totals.shipping
      }
    }));
  };

  // Add surcharge input handler
  const handleSurchargeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseFloat(e.target.value.replace(/[^\d.-]/g, '')) || 0;
    const subtotal = formData.totals.orderTotal + value;
    const gst = subtotal * 0.1;

    setFormData(prev => ({
      ...prev,
      totals: {
        ...prev.totals,
        surcharge: value,
        gst,
        grandTotal: subtotal + gst + prev.totals.shipping
      }
    }));
  };

  // Add GST input handler
  const handleGSTChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseFloat(e.target.value.replace(/[^\d.-]/g, '')) || 0;

    setFormData(prev => ({
      ...prev,
      totals: {
        ...prev.totals,
        gst: value,
        grandTotal: prev.totals.orderTotal + prev.totals.surcharge + value + prev.totals.shipping
      }
    }));
  };

  // Check if PO Number should be disabled
  const isPONumberDisabled = formData.saleType.toLowerCase() === 'store';

  // Add handler for customer selection
  const handleCustomerSelect = (customerId: string, customerName: string) => {
    setFormData(prev => ({
      ...prev,
      invoiceTo: customerId // Store the customer ID
    }));
  };

  // Manual refresh function
  const refreshInventoryData = () => {
    if (!isLoadingInventory) {
      console.log('Manual inventory refresh triggered');
      fetchInventoryItems(true);
    }
  };

  return (
    <>


      {/* Removed breadcrumb navigation as it's now in the page component */}

      <FormContainer onSubmit={handleSubmit}>
        <FormBoxesContainer>
          <FormItemBox>
            <FormBoxHeading>Store Type</FormBoxHeading>
            <SelectWrapper>
              <FormSelect
                name="storeType"
                value={formData.storeType}
                onChange={handleInputChange}
                disabled={isLoadingStoreTypes || storeFilter.isStoreRestricted}
                style={{
                  backgroundColor: storeFilter.isStoreRestricted ? '#F9FAFB' : 'white',
                  cursor: storeFilter.isStoreRestricted ? 'not-allowed' : 'pointer'
                }}
              >
                <option value="">Select Store Type</option>
                {storeTypes && storeTypes.map(type => (
                  <option key={type.id} value={type.name}>
                    {type.name}
                  </option>
                ))}
              </FormSelect>
            </SelectWrapper>
            {storeFilter.isStoreRestricted && (
              <div style={{ fontSize: '12px', color: '#6B7280', marginTop: '4px' }}>
                Store type is auto-selected based on your role
              </div>
            )}
          </FormItemBox>

          <FormItemBox>
            <FormBoxHeading data-required="*">Sale Type</FormBoxHeading>
            <SelectWrapper>
              <FormSelect name="saleType" value={formData.saleType} onChange={handleInputChange}>
                <option value="">Select Sale Type</option>
                <option value="Trade">Trade</option>
                <option value="Retail">Retail</option>
                <option value="Store">Store</option>
              </FormSelect>
            </SelectWrapper>
          </FormItemBox>

          <FormItemBox>
            <FormBoxHeading data-required="*">Mode of Payment</FormBoxHeading>
            <SelectWrapper>
              <FormSelect name="modeOfPayment" value={formData.modeOfPayment} onChange={handleInputChange}>
                <option value="">Select Payment Mode</option>
                <option value="Credit Card">Credit Card</option>
                <option value="Cash">Cash</option>
                <option value="Bank Transfer">Bank Transfer</option>
              </FormSelect>
            </SelectWrapper>
          </FormItemBox>

          <FormItemBox>
            <FormBoxHeading data-required={isPONumberDisabled ? "" : "*"}>Your PO Number</FormBoxHeading>
            <FormInput
              type="text"
              name="poNumber"
              value={formData.poNumber}
              onChange={handleInputChange}
              disabled={isPONumberDisabled}
              placeholder={isPONumberDisabled ? "Not required for Store" : ""}
            />
          </FormItemBox>

          <FormItemBox>
            <FormBoxHeading data-required="*">Invoice To</FormBoxHeading>
            <CustomerSelectWrapper>
              <CustomerSelect
                name="invoiceTo"
                value={formData.invoiceTo}
                onChange={(customerId, customerName) => handleCustomerSelect(customerId, customerName)}
                required
                placeholder="Search Customer"
              />
            </CustomerSelectWrapper>
          </FormItemBox>

          <FormItemBox>
            <FormBoxHeading data-required="*">Deliver to Address</FormBoxHeading>
            <FormInput
              type="text"
              name="deliverToAddress"
              value={formData.deliverToAddress}
              onChange={handleInputChange}
              required
            />
          </FormItemBox>

          <FormItemBox>
            <FormBoxHeading>Invoice No</FormBoxHeading>
            <FormInput
              type="text"
              name="invoiceNo"
              value={formData.invoiceNo}
              onChange={handleInputChange}
              placeholder="Auto-generated"
              disabled={true}
            />
          </FormItemBox>
        </FormBoxesContainer>

        <BottomRowContainer>
          <FormItemBox className="notes-field">
            <FormBoxHeading>Notes</FormBoxHeading>
            <NotesTextarea
              name="notes"
              value={formData.notes}
              onChange={handleInputChange}
            />
          </FormItemBox>

          <FormItemBox className="quote-field">
            <FormBoxHeading>Linked to Quote</FormBoxHeading>
            <QuoteSelectWrapper ref={quoteDropdownRef}>
              <QuoteSearchInput
                type="text"
                value={quoteSearchTerm}
                onChange={(e) => {
                  setQuoteSearchTerm(e.target.value);
                  setIsQuoteDropdownOpen(true);
                }}
                onFocus={() => setIsQuoteDropdownOpen(true)}
                placeholder="Search Quote"
                disabled={isLoadingQuotes}
              />
              <QuoteDropdown isOpen={isQuoteDropdownOpen && filteredQuotes.length > 0}>
                {filteredQuotes.map(quote => (
                  <QuoteOption
                    key={quote.id}
                    onClick={() => handleQuoteSelect(quote)}
                  >
                    {quote.quote_no}
                  </QuoteOption>
                ))}
              </QuoteDropdown>
            </QuoteSelectWrapper>
          </FormItemBox>

          <FormItemBox className="date-field">
            <FormBoxHeading data-required="*">Date</FormBoxHeading>
            <DateInputWrapper>
              <StyledDateInput
                type="date"
                name="date"
                value={formData.date}
                onChange={handleInputChange}
                required
              />
            </DateInputWrapper>
          </FormItemBox>

          <FormItemBox className="checkbox-field">
            <CheckboxContainer>
              <CheckboxInput
                type="checkbox"
                id="dontSendPo"
                name="dontSendPo"
                checked={formData.dontSendPo}
                onChange={handleCheckboxChange}
              />
              <CheckboxLabel htmlFor="dontSendPo">Don't Send PO</CheckboxLabel>
            </CheckboxContainer>
          </FormItemBox>
        </BottomRowContainer>

        <Table>
          <TableHeader>
            <tr>
              <th>
                <HeaderWithRefresh>
                  BWA SKU
                  <RefreshButton
                    type="button"
                    onClick={refreshInventoryData}
                    disabled={isLoadingInventory}
                    title="Refresh inventory data to pick up newly uploaded images"
                  >
                    {isLoadingInventory ? '↻' : '↻'}
                  </RefreshButton>
                </HeaderWithRefresh>
              </th>
              <th>Description</th>
              <th colSpan={5} style={{ textAlign: 'center' }}>Quantity</th>
              <th>Unit Price<br />excl GST</th>
              <th>Total Price</th>
              <th></th>
            </tr>
            <tr>
              <th></th>
              <th></th>
              <th>Units</th>
              <th>Boxes</th>
              <th>Pieces</th>
              <th>M2</th>
              <th>Total</th>
              <th></th>
              <th></th>
              <th></th>
            </tr>
          </TableHeader>
          <TableBody>
            {formData.items.length === 0 ? (
              <tr>
                <td colSpan={10} style={{ textAlign: 'center' }}>
                  <AddItemButton type="button" onClick={handleAddItem}>+</AddItemButton>
                </td>
              </tr>
            ) : (
              formData.items.map((item, index) => (
                <tr key={item.id}>
                  <td>
                    <SKUSelectWrapper>
                      {item.sku && inventoryItems.find(inv => inv.sku_code === item.sku) ? (
                        <SelectedSKUContainer>
                          <SkuImage
                            imagePath={inventoryItems.find(inv => inv.sku_code === item.sku)?.image_path}
                            alt="SKU"
                            width={32}
                            height={32}
                            enableModal={true}
                            showBlankWhenNoImage={true}
                            key={`${item.sku}-${lastInventoryRefresh}`}
                          />
                          <SKUSearchInput
                            type="text"
                            value={skuSearchTerms[index] || item.sku}
                            onChange={(e) => {
                              setSKUSearchTerms(prev => ({
                                ...prev,
                                [index]: e.target.value
                              }));
                              setOpenSKUDropdowns(prev => ({
                                ...prev,
                                [index]: true
                              }));
                            }}
                            onFocus={() => setOpenSKUDropdowns(prev => ({
                              ...prev,
                              [index]: true
                            }))}
                            placeholder="Search SKU"
                            style={{ flex: 1 }}
                          />
                        </SelectedSKUContainer>
                      ) : (
                        <SKUSearchInput
                          type="text"
                          value={skuSearchTerms[index] || item.sku}
                          onChange={(e) => {
                            setSKUSearchTerms(prev => ({
                              ...prev,
                              [index]: e.target.value
                            }));
                            setOpenSKUDropdowns(prev => ({
                              ...prev,
                              [index]: true
                            }));
                          }}
                          onFocus={() => setOpenSKUDropdowns(prev => ({
                            ...prev,
                            [index]: true
                          }))}
                          placeholder="Search SKU"
                        />
                      )}
                      <SKUDropdown isOpen={openSKUDropdowns[index] && getFilteredInventoryItems(index).length > 0}>
                        {getFilteredInventoryItems(index).map(invItem => (
                          <SKUOption
                            key={invItem.id}
                            onClick={() => handleSKUSelect(index, invItem)}
                          >
                            <SkuImage
                              imagePath={invItem.image_path}
                              alt="SKU"
                              width={40}
                              height={40}
                              enableModal={true}
                              showBlankWhenNoImage={true}
                              key={`${invItem.id}-${lastInventoryRefresh}`}
                            />
                            <SKUOptionContent>
                              <div style={{ fontWeight: '500' }}>{invItem.sku_code}</div>
                              {invItem.description && (
                                <div style={{ fontSize: '0.75rem', color: '#6B7280', marginTop: '2px' }}>
                                  {invItem.description}
                                </div>
                              )}
                            </SKUOptionContent>
                          </SKUOption>
                        ))}
                      </SKUDropdown>
                    </SKUSelectWrapper>
                  </td>
                  <td>
                    <FormInput
                      type="text"
                      value={item.description}
                      onChange={(e) => handleItemChange(index, 'description', e.target.value)}
                      style={{ width: '100%' }}
                    />
                  </td>
                  <td>
                    <QuantityInput
                      type="text"
                      value={item.quantity.units || ''}
                      onChange={(e) => handleItemChange(index, 'quantity.units', e.target.value)}
                    />
                  </td>
                  <td>
                    <QuantityInput
                      type="text"
                      value={item.quantity.boxes || ''}
                      onChange={(e) => handleItemChange(index, 'quantity.boxes', e.target.value)}
                    />
                  </td>
                  <td>
                    <QuantityInput
                      type="text"
                      value={item.quantity.pieces || ''}
                      onChange={(e) => handleItemChange(index, 'quantity.pieces', e.target.value)}
                    />
                  </td>
                  <td>
                    <QuantityInput
                      type="text"
                      value={item.quantity.m2 || ''}
                      onChange={(e) => handleItemChange(index, 'quantity.m2', e.target.value)}
                    />
                  </td>
                  <td>
                    <QuantityInput
                      type="text"
                      value={item.quantity.total}
                      readOnly
                    />
                  </td>
                  <td>
                    <PriceInput
                      type="number"
                      value={item.unitPrice}
                      onChange={(e) => handleItemChange(index, 'unitPrice', e.target.value)}
                      step="0.01"
                    />
                  </td>
                  <td>
                    <PriceInput
                      type="text"
                      value={formatCurrency(item.totalPrice)}
                      readOnly
                    />
                  </td>
                  <td style={{ display: 'flex', gap: '4px' }}>
                    <AddItemButton type="button" onClick={handleAddItem}>+</AddItemButton>
                    {formData.items.length > 1 && (
                      <RemoveItemButton type="button" onClick={() => handleRemoveItem(index)}>×</RemoveItemButton>
                    )}
                  </td>
                </tr>
              ))
            )}
          </TableBody>
        </Table>

        <TotalSection>
          <TotalRow>
            <TotalLabel>Total Order:</TotalLabel>
            <TotalInput
              type="text"
              value={formatCurrency(formData.totals.orderTotal)}
              onChange={handleTotalOrderChange}
            />
          </TotalRow>
          <TotalRow>
            <TotalLabel>Credit Card Surcharge:</TotalLabel>
            <TotalInput
              type="text"
              value={formatCurrency(formData.totals.surcharge)}
              onChange={handleSurchargeChange}
            />
          </TotalRow>
          <TotalRow>
            <TotalLabel>Total GST:</TotalLabel>
            <TotalInput
              type="text"
              value={formatCurrency(formData.totals.gst)}
              onChange={handleGSTChange}
            />
          </TotalRow>
          <TotalRow>
            <TotalLabel>
              <InfoIconWrapper>
                <InfoIcon>i</InfoIcon>
                <Tooltip>
                  Shipping is calculated based on order weight and distance.
                  For local deliveries: $10 base + $1 per kg.
                  For interstate: $20 base + $2 per kg.
                </Tooltip>
              </InfoIconWrapper>
              Shipping:
            </TotalLabel>
            <TotalInput
              type="text"
              value={formatCurrency(formData.totals.shipping)}
              onChange={handleShippingChange}
            />
          </TotalRow>
          <Divider />
          <TotalRow>
            <GrandTotalLabel>Grand Total:</GrandTotalLabel>
            <GrandTotalValue>{formatCurrency(formData.totals.grandTotal)}</GrandTotalValue>
          </TotalRow>
        </TotalSection>

        <div style={{ display: 'flex', justifyContent: 'flex-end', marginTop: '2rem', gap: '1rem' }}>
          <Button type="button" secondary onClick={handleCancel}>
            Cancel
          </Button>
          <Button type="button" secondary onClick={handleSaveAsDraftInternal} disabled={isSubmitting || parentIsLoading}>
            Save as Draft
          </Button>
          <Button type="submit" primary disabled={isSubmitting || parentIsLoading}>
            {isSubmitting || parentIsLoading ? 'Saving' : 'Save Invoice'}
          </Button>
        </div>
      </FormContainer>
    </>
  );
};

export default EditInvoiceForm; 