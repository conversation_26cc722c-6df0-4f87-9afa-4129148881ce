import React, { useState, useEffect, useRef } from 'react';
import styled from 'styled-components';
import builderLogo from '../../assets/Builder-Logo.png';
import { getInvoice, generateInvoicePdf } from '../../services/invoiceService';
import { format, parseISO } from 'date-fns';
import customerService from '../../services/customerService';
// @ts-ignore
import html2pdf from 'html2pdf.js';

const ModalOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
`;

const ModalContainer = styled.div`
  background-color: white;
  border-radius: 8px;
  width: 95vw;
  max-width: 1200px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
`;

const ModalHeader = styled.div`
  padding: 8px 16px;
  border-bottom: none;
  text-align: right;
`;

const ModalTitle = styled.h2`
  font-size: 16px;
  color: #333;
  margin: 0;
  display: none; /* Hide the title completely */
`;

const ModalBody = styled.div`
  padding: 16px;
`;

const PrintableContent = styled.div`
  background: white;
  font-family: Arial, sans-serif;
  padding: 20px;
  max-width: 1100px;
  margin: 0 auto;
  
  @media print {
    margin: 0;
    padding: 20px;
    font-size: 12px;
    page-break-inside: avoid;
    transform: rotate(0deg);
  }
`;

const InvoiceContainer = styled.div`
  border: 2px solid #333;
  padding: 20px;
  margin-bottom: 20px;
  background: white;
`;

const InvoiceTitle = styled.h1`
  text-align: center;
  font-size: 28px;
  font-weight: bold;
  color: #333;
  margin: 0 0 20px 0;
  padding: 0;
`;

const InvoiceHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
`;

const CompanyLogo = styled.div`
  width: 180px;
  display: flex;
  flex-direction: column;
  
  img {
    max-width: 100%;
    height: auto;
    margin-bottom: 5px;
  }
  
  .tagline {
    font-style: italic;
    font-size: 14px;
    color: #042B41;
    margin-top: 0;
    font-family: cursive;
  }
`;

const CompanyInfo = styled.div`
  text-align: right;
  font-size: 11px;
  line-height: 1.3;
  color: #333;
  
  p {
    margin: 1px 0;
  }
  
  .company-name {
    font-weight: bold;
    font-size: 12px;
  }
`;

const MainContentContainer = styled.div`
  display: flex;
  gap: 15px;
  margin-bottom: 15px;
`;

const LeftSection = styled.div`
  flex: 1;
`;

const RightSection = styled.div`
  width: 300px;
  display: flex;
  flex-direction: column;
`;

const InfoTable = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  margin-bottom: 15px;
  border: 2px solid #333;
`;

const InfoCell = styled.div`
  padding: 12px;
  border: 1px solid #333;
  font-size: 13px;
  min-height: 50px;
`;

const InfoLabel = styled.div`
  font-weight: bold;
  font-size: 12px;
  margin-bottom: 6px;
  color: #333;
`;

const TaxInvoiceLabel = styled.div`
  background-color: #0096db;
  color: white;
  padding: 8px 15px;
  text-align: center;
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 0;
  border: 2px solid #333;
`;

const InvoiceDetails = styled.div`
  display: flex;
  flex-direction: column;
  border: 2px solid #333;
  border-top: none;
  background-color: white;
`;

const InvoiceDetail = styled.div`
  display: flex;
  justify-content: space-between;
  width: 100%;
  padding: 6px 15px;
  font-size: 13px;
  border-bottom: 1px solid #333;
  
  &:last-child {
    border-bottom: none;
  }
  
  span:first-child {
    font-weight: 600;
  }
  
  span:last-child {
    text-align: right;
    font-weight: 500;
  }
`;

const InvoiceTable = styled.table`
  width: 100%;
  border-collapse: collapse;
  margin: 15px 0;
  font-size: 12px;
  border: 2px solid #333;
  
  th, td {
    border: 1px solid #333;
    padding: 6px 4px;
    text-align: center;
  }
  
  th {
    background-color: #042B41;
    color: white;
    font-size: 11px;
    font-weight: 600;
    text-align: center;
    vertical-align: middle;
    height: 35px;
  }
  
  td {
    padding: 6px 4px;
    vertical-align: middle;
    font-size: 11px;
    height: 30px;
  }
  
  .code-column { 
    width: 12%; 
    text-align: left;
  }
  .description-column { 
    width: 25%; 
    text-align: left;
  }
  .quantity-column { width: 8%; }
  .unit-price-column { 
    width: 10%; 
    text-align: right;
  }
  .total-price-column { 
    width: 10%; 
    text-align: right;
  }
`;

const OrderTotal = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  margin-left: auto;
  width: 220px;
  background-color: #f8f9fa;
  border: 2px solid #333;
  margin-top: 15px;
  
  div {
    padding: 6px 12px;
    border: 1px solid #333;
    font-size: 13px;
  }
  
  div:nth-child(even) {
    text-align: right;
    font-weight: 700;
    background-color: white;
  }
  
  div:nth-child(odd) {
    text-align: left;
    font-weight: 600;
    background-color: #e9ecef;
  }
`;

const PaymentInfo = styled.div`
  margin: 20px 0 0 0;
  padding: 12px;
  border: 2px solid #333;
  font-size: 11px;
  line-height: 1.4;
  background-color: #f9f9f9;
  
  p {
    margin: 4px 0;
  }
  
  p:first-child {
    margin-top: 0;
  }
  
  p:last-child {
    margin-bottom: 0;
  }
  
  strong {
    font-weight: 700;
  }
`;

const ButtonContainer = styled.div`
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 24px;
`;

const Button = styled.button<{ primary?: boolean; disabled?: boolean }>`
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 24px;
  border: none;
  border-radius: 4px;
  font-weight: 500;
  cursor: ${props => props.disabled ? 'not-allowed' : 'pointer'};
  background-color: ${props => props.disabled ? '#ccc' : (props.primary ? '#042B41' : 'white')};
  color: ${props => props.disabled ? '#666' : (props.primary ? 'white' : '#042B41')};
  border: ${props => props.primary ? 'none' : '1px solid #042B41'};
  opacity: ${props => props.disabled ? 0.6 : 1};
  
  &:hover {
    background-color: ${props => props.disabled ? '#ccc' : (props.primary ? '#0A3D5A' : '#f9fafb')};
  }
`;

interface InvoiceItem {
  id?: string;
  invoice_id?: string;
  code: string;
  description: string;
  sku?: string;
  units?: number;
  boxes?: number;
  pieces?: number;
  m2?: number;
  unit_price: number;
  total_price: number;
  created_at?: string;
  updated_at?: string;
  quantity?: {
    units: number | null;
    boxes: number | null;
    pieces: number | null;
    m2: number | null;
    total: number;
  };
  unitPrice?: number;
  totalPrice?: number;
}

interface InvoiceData {
  invoiceNumber: string;
  date: string;
  reference: string;
  customerName: string;
  deliveryAddress: string;
  items: InvoiceItem[];
  orderTotal: number;
  gst: number;
  grandTotal: number;
  paymentTerms: string;
  bankDetails: string;
}

interface InvoiceViewProps {
  invoiceId: string;
  onClose: () => void;
  printMode?: boolean;
}

const InvoiceView: React.FC<InvoiceViewProps> = ({ invoiceId, onClose, printMode }) => {
  const [invoice, setInvoice] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [companyName, setCompanyName] = useState<string>('');
  const [downloading, setDownloading] = useState(false);
  const pdfRef = useRef<HTMLDivElement>(null);

  // Fetch invoice data when component mounts or invoiceId changes
  useEffect(() => {
    const fetchInvoiceData = async () => {
      // Reset all state when invoiceId changes
      setLoading(true);
      setError(null);
      setInvoice(null);
      setCompanyName('');
      setDownloading(false);

      try {
        console.log('Fetching invoice with ID:', invoiceId);
        const invoiceData = await getInvoice(invoiceId);
        console.log('Invoice data received:', invoiceData);

        setInvoice(invoiceData);

        // If we have a company_id but no company name, fetch the company details
        if (invoiceData.company_id && (!invoiceData.company || !invoiceData.company.name)) {
          try {
            console.log('Fetching customer details for company_id:', invoiceData.company_id);
            const customerData = await customerService.getCustomerById(invoiceData.company_id);
            if (customerData && customerData.name) {
              console.log('Found company name:', customerData.name);
              setCompanyName(customerData.name);
            }
          } catch (customerErr) {
            console.error('Error fetching company details:', customerErr);
            // Continue with the invoice even if company details fetch fails
          }
        }
      } catch (err) {
        console.error('Error fetching invoice:', err);
        let errorMessage = 'Failed to load invoice. Please try again.';

        // Provide more specific error messages based on the error type
        if (err && typeof err === 'object' && 'response' in err) {
          const apiError = err as any;
          if (apiError.response?.status === 404) {
            errorMessage = 'Invoice not found. It may have been deleted or the ID is incorrect.';
          } else if (apiError.response?.status === 403) {
            errorMessage = 'You do not have permission to view this invoice.';
          } else if (apiError.response?.status >= 500) {
            errorMessage = 'Server error. Please try again later.';
          }
        }

        setError(errorMessage);
      } finally {
        setLoading(false);
      }
    };

    if (invoiceId) {
      fetchInvoiceData();
    } else {
      setError('No invoice ID provided');
      setLoading(false);
    }
  }, [invoiceId]); // Re-run when invoiceId changes

  const generateClientSidePdf = async () => {
    if (!pdfRef.current || downloading) return;

    setDownloading(true);

    try {
      console.log('Generating PDF using html2pdf.js');

      // Get invoice number for filename
      const invoiceNumber = invoice?.invoice_number?.formatted_number ||
        (typeof invoice?.invoice_number === 'object' && 'id' in invoice.invoice_number
          ? invoice.invoice_number.formatted_number
          : invoice?.invoice_no || 'invoice');

      const options = {
        margin: 10,
        filename: `${invoiceNumber}.pdf`,
        image: { type: 'jpeg', quality: 0.98 },
        html2canvas: {
          scale: 2,
          useCORS: true,
          allowTaint: true
        },
        jsPDF: {
          unit: 'mm',
          format: 'a4',
          orientation: 'portrait'
        }
      };

      await html2pdf().set(options).from(pdfRef.current).save();
      console.log('PDF generated and downloaded successfully');

    } catch (error) {
      console.error('Error generating PDF:', error);
      alert('Failed to generate PDF. Please try again.');
    } finally {
      setDownloading(false);
    }
  };

  const handleDownload = async () => {
    if (!invoiceId || downloading) return;

    setDownloading(true);

    try {
      if (printMode) {
        // In print mode, just open the print dialog directly
        console.log('Print mode activated, opening print dialog');
        window.print();
        return;
      }

      console.log('Trying backend PDF generation first');

      try {
        // Try to generate the PDF blob from the backend first
        const pdfBlob = await generateInvoicePdf(invoiceId);
        console.log('Backend PDF generated successfully, size:', pdfBlob.size, 'bytes');

        // Create a download URL
        const downloadUrl = window.URL.createObjectURL(pdfBlob);

        // Get invoice number for filename
        const invoiceNumber = invoice?.invoice_number?.formatted_number ||
          (typeof invoice?.invoice_number === 'object' && 'id' in invoice.invoice_number
            ? invoice.invoice_number.formatted_number
            : invoice?.invoice_no || 'invoice');

        // Create a temporary download link
        const downloadLink = document.createElement('a');
        downloadLink.href = downloadUrl;
        downloadLink.download = `${invoiceNumber}.pdf`;
        downloadLink.style.display = 'none';

        // Append to body, click, and remove
        document.body.appendChild(downloadLink);
        downloadLink.click();
        document.body.removeChild(downloadLink);

        // Clean up the URL
        window.URL.revokeObjectURL(downloadUrl);

        console.log('Backend PDF download completed successfully');
        return;

      } catch (backendError) {
        console.warn('Backend PDF generation failed, falling back to client-side generation:', backendError);

        // Fallback to client-side PDF generation
        await generateClientSidePdf();
      }

    } catch (error) {
      console.error('Error in download process:', error);
      alert('Failed to download invoice PDF. Please try again.');
    } finally {
      setDownloading(false);
    }
  };

  const formatDate = (dateStr: string): string => {
    try {
      if (!dateStr) return 'N/A';
      const date = parseISO(dateStr);
      return format(date, 'dd/MM/yyyy');
    } catch (err) {
      return dateStr || 'N/A';
    }
  };

  if (loading) {
    return (
      <ModalOverlay>
        <ModalContainer>
          <ModalHeader>
            <ModalTitle>Loading Invoice</ModalTitle>
          </ModalHeader>
          <ModalBody style={{ textAlign: 'center', padding: '2rem' }}>
            <p>Loading invoice data</p>
          </ModalBody>
        </ModalContainer>
      </ModalOverlay>
    );
  }

  if (error || !invoice) {
    return (
      <ModalOverlay>
        <ModalContainer>
          <ModalHeader>
            <ModalTitle>Error</ModalTitle>
          </ModalHeader>
          <ModalBody style={{ textAlign: 'center', padding: '2rem' }}>
            <p>{error || 'Failed to load invoice data'}</p>
            <Button onClick={onClose}>Close</Button>
          </ModalBody>
        </ModalContainer>
      </ModalOverlay>
    );
  }

  // Extract data from API response with better error handling
  const invoiceNumber = invoice.invoice_number?.formatted_number ||
    (typeof invoice.invoice_number === 'object' && 'id' in invoice.invoice_number
      ? invoice.invoice_number.formatted_number
      : invoice.invoice_no || 'N/A');

  const customerName = companyName || invoice.company?.name || invoice.customer_name || 'N/A';
  const date = formatDate(invoice.date || invoice.invoice_date);
  const reference = invoice.purchase_order_number || invoice.po_number || '';
  const deliveryAddress = invoice.deliver_to_address || invoice.delivery_address || 'N/A';

  // Handle items array - it could be nested in an 'items' property or be the direct array
  let itemsArray: any[] = [];
  if (invoice.items) {
    if (Array.isArray(invoice.items)) {
      itemsArray = invoice.items;
    } else if (invoice.items.items && Array.isArray(invoice.items.items)) {
      itemsArray = invoice.items.items;
    }
  }

  console.log('Processing items array:', itemsArray);

  const items: InvoiceItem[] = itemsArray.map((item: any, index: number) => {
    console.log(`Processing item ${index}:`, item);

    return {
      ...item,
      code: item.sku || item.sku_code || item.code || 'N/A',
      description: item.description || 'N/A',
      quantity: {
        units: item.units || item.quantity_units || null,
        boxes: item.boxes || item.quantity_boxes || null,
        pieces: item.pieces || item.quantity_pieces || null,
        m2: item.m2 || item.quantity_m2 || null,
        total: (item.units || item.quantity_units || 0) +
          (item.boxes || item.quantity_boxes || 0) +
          (item.pieces || item.quantity_pieces || 0) +
          (item.m2 || item.quantity_m2 || 0)
      },
      unitPrice: item.unit_price || item.unitPrice || 0,
      totalPrice: item.total_price || item.totalPrice || 0
    };
  });

  // Calculate financial totals with better error handling
  const orderTotal = typeof invoice.total_order === 'string'
    ? parseFloat(invoice.total_order) || 0
    : (invoice.total_order || 0);

  const gst = typeof invoice.total_gst === 'string'
    ? parseFloat(invoice.total_gst) || 0
    : (invoice.total_gst || 0);

  const grandTotal = typeof invoice.grand_total === 'string'
    ? parseFloat(invoice.grand_total) || 0
    : (invoice.grand_total || 0);

  console.log('Rendered invoice data:', {
    invoiceNumber,
    customerName,
    date,
    reference,
    deliveryAddress,
    itemsCount: items.length,
    orderTotal,
    gst,
    grandTotal
  });

  return (
    <ModalOverlay>
      <ModalContainer>
        <ModalHeader>
          {/* No title needed */}
        </ModalHeader>
        <ModalBody>
          <PrintableContent ref={pdfRef}>
            <InvoiceTitle>Invoice</InvoiceTitle>
            <InvoiceContainer>
              <InvoiceHeader>
                <CompanyLogo>
                  <img src={builderLogo} alt="Builders Warehouse Australia" width="180" />
                  <div className="tagline">a better way</div>
                </CompanyLogo>
                <CompanyInfo>
                  <p className="company-name">BWA Supplies Pty Ltd (ACN ***********)</p>
                  <p>trading as Builders Warehouse Australia</p>
                  <p>(ABN **************)</p>
                  <p></p>
                  <p>Office & Showroom: 214 High St Cranbourne 3977</p>
                  <p>www.builderswarehouseaustralia.com.au</p>
                  <p>Tel: 03 8764 9142 | <EMAIL></p>
                </CompanyInfo>
              </InvoiceHeader>

              <MainContentContainer>
                <LeftSection>
                  <InfoTable>
                    <InfoCell>
                      <InfoLabel>INVOICE TO</InfoLabel>
                      <div>{customerName}</div>
                    </InfoCell>
                    <InfoCell>
                      <InfoLabel>DELIVERY INSTRUCTIONS</InfoLabel>
                      <div>{deliveryAddress}</div>
                    </InfoCell>
                  </InfoTable>
                </LeftSection>
                <RightSection>
                  <TaxInvoiceLabel>TAX INVOICE</TaxInvoiceLabel>
                  <InvoiceDetails>
                    <InvoiceDetail>
                      <span><strong>Invoice No:</strong></span>
                      <span>{invoiceNumber}</span>
                    </InvoiceDetail>
                    <InvoiceDetail>
                      <span><strong>Date:</strong></span>
                      <span>{date}</span>
                    </InvoiceDetail>
                    <InvoiceDetail>
                      <span><strong>Your Ref:</strong></span>
                      <span>{reference}</span>
                    </InvoiceDetail>
                  </InvoiceDetails>
                </RightSection>
              </MainContentContainer>

              <InvoiceTable>
                <thead>
                  <tr>
                    <th className="code-column">CODE</th>
                    <th className="description-column">DESCRIPTION</th>
                    <th className="quantity-column">Units</th>
                    <th className="quantity-column">Boxes</th>
                    <th className="quantity-column">Pieces</th>
                    <th className="quantity-column">M2</th>
                    <th className="quantity-column">TOTAL</th>
                    <th className="unit-price-column">UNIT PRICE<br />exc. GST</th>
                    <th className="total-price-column">TOTAL<br />PRICE</th>
                  </tr>
                </thead>
                <tbody>
                  {items.length > 0 ? (
                    items.map((item: InvoiceItem, index: number) => (
                      <tr key={index}>
                        <td className="code-column" style={{ textAlign: 'left' }}>{item.code}</td>
                        <td className="description-column" style={{ textAlign: 'left' }}>{item.description}</td>
                        <td className="quantity-column">
                          {item.quantity?.units || item.units || ''}
                        </td>
                        <td className="quantity-column">
                          {item.quantity?.boxes || item.boxes || ''}
                        </td>
                        <td className="quantity-column">
                          {item.quantity?.pieces || item.pieces || ''}
                        </td>
                        <td className="quantity-column">
                          {item.quantity?.m2 || item.m2 || ''}
                        </td>
                        <td className="quantity-column" style={{ fontWeight: 'bold' }}>
                          {item.quantity?.total ||
                            item.quantity?.units ||
                            item.quantity?.boxes ||
                            item.quantity?.pieces ||
                            item.quantity?.m2 ||
                            item.units ||
                            item.boxes ||
                            item.pieces ||
                            item.m2 ||
                            1}
                        </td>
                        <td className="unit-price-column" style={{ textAlign: 'right' }}>$ {(item.unitPrice || item.unit_price || 0).toFixed(2)}</td>
                        <td className="total-price-column" style={{ textAlign: 'right' }}>$ {(item.totalPrice || item.total_price || 0).toFixed(2)}</td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan={9} style={{ textAlign: 'center', padding: '2rem' }}>
                        No items found for this invoice
                      </td>
                    </tr>
                  )}
                </tbody>
              </InvoiceTable>

              <div style={{ display: 'flex', justifyContent: 'flex-end' }}>
                <OrderTotal>
                  <div><strong>ORDER TOTAL:</strong></div>
                  <div>${orderTotal.toFixed(2)}</div>
                  <div><strong>TOTAL GST:</strong></div>
                  <div>${gst.toFixed(2)}</div>
                  <div><strong>GRAND TOTAL:</strong></div>
                  <div>${grandTotal.toFixed(2)}</div>
                </OrderTotal>
              </div>
            </InvoiceContainer>

            <PaymentInfo>
              <p>This is a payment claim under the Building and Construction Industry Security of Payment Act 2002</p>
              <p>Please refer to Builders Warehouse Australia (BWA) Terms & Conditions Of Sale</p>
              <p>BWA will not accept any responsibility for quantity calculations. Quantities must be confirmed by a qualified tradesman</p>
              <p><strong>Payment Terms: Due and payable prior to receiving goods</strong></p>
              <p><strong>Electronic Funds Transfer details:</strong></p>
              <p>BSB: 013542 - Account: ********* - Account Name: Builders Warehouse Australia</p>
            </PaymentInfo>
          </PrintableContent>

          <ButtonContainer>
            <Button
              primary
              onClick={handleDownload}
              disabled={downloading}
            >
              <span>{downloading ? '↻' : (printMode ? '🖨️' : '↓')}</span>
              {downloading
                ? (printMode ? 'Printing' : 'Downloading')
                : (printMode ? 'Print' : 'Download PDF')
              }
            </Button>
            <Button onClick={onClose}>
              Close
            </Button>
          </ButtonContainer>
        </ModalBody>
      </ModalContainer>
    </ModalOverlay>
  );
};

export default InvoiceView; 