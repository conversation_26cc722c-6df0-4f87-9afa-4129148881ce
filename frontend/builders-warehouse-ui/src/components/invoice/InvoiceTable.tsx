import React, { useState, useEffect, useCallback } from 'react';
import { Link } from 'react-router-dom';
import Button from '../common/Button';
import Table from '../common/Table';
import SearchBar from '../common/SearchBar';
import { getInvoices, InvoiceResponse, InvoicePagination } from '../../services/invoiceService';
import _ from 'lodash';

interface Invoice {
  id: string;
  invoiceNumber: string;
  customer: {
    id: string;
    name: string;
  };
  issueDate: string;
  dueDate: string;
  status: 'paid' | 'unpaid' | 'overdue' | 'partially_paid';
  amount: number;
  'customer.name': string;
  linkedQuoteId?: string;
}

interface InvoiceTableProps {
  onViewInvoice: (invoiceId: string) => void;
  onEditInvoice: (invoiceId: string) => void;
}

const LoadingSkeleton = () => (
  <div className="space-y-4">
    {/* Table header skeleton */}
    <div className="grid grid-cols-7 gap-4 border-b border-gray-200 bg-gray-50 p-4">
      {[...Array(7)].map((_, i) => (
        <div key={i} className="h-6 bg-gray-200 rounded animate-pulse"></div>
      ))}
    </div>
    
    {/* Table rows skeleton */}
    {[...Array(5)].map((_, rowIndex) => (
      <div key={rowIndex} className="grid grid-cols-7 gap-4 border-b border-gray-200 p-4">
        <div className="h-6 bg-gray-100 rounded animate-pulse"></div>
        <div className="h-6 bg-gray-100 rounded animate-pulse"></div>
        <div className="h-6 bg-gray-100 rounded animate-pulse"></div>
        <div className="h-6 bg-gray-100 rounded animate-pulse"></div>
        <div className="h-6 bg-gray-100 rounded animate-pulse w-20"></div>
        <div className="h-6 bg-gray-100 rounded animate-pulse w-24"></div>
        <div className="flex space-x-2">
          <div className="h-8 bg-gray-100 rounded animate-pulse w-16"></div>
          <div className="h-8 bg-gray-100 rounded animate-pulse w-16"></div>
        </div>
      </div>
    ))}
  </div>
);

const InvoiceTable: React.FC<InvoiceTableProps> = ({
  onViewInvoice,
  onEditInvoice,
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');
  const [invoices, setInvoices] = useState<Invoice[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [searchLoading, setSearchLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [totalItems, setTotalItems] = useState<number>(0);
  const [itemsPerPage, setItemsPerPage] = useState<number>(10);
  const [dataLoaded, setDataLoaded] = useState<boolean>(false);
  const [retryCount, setRetryCount] = useState<number>(0);
  const [lastFetchConfig, setLastFetchConfig] = useState<string>('');

  // Debounced search handler
  const debouncedSearch = useCallback(
    _.debounce((term: string) => {
      setDebouncedSearchTerm(term);
      setCurrentPage(1); // Reset to first page on search
    }, 300),
    []
  );

  // Handle search input change
  const handleSearchChange = (value: string) => {
    setSearchTerm(value);
    setSearchLoading(true);
    debouncedSearch(value);
  };

  // Effect to clear search loading state
  useEffect(() => {
    if (searchLoading) {
      const timer = setTimeout(() => {
        setSearchLoading(false);
      }, 300);
      return () => clearTimeout(timer);
    }
  }, [searchLoading]);

  // Fetch invoices from the API
  useEffect(() => {
    let isMounted = true;
    
    // Create a unique key for this fetch configuration
    const fetchConfig = `page-${currentPage}-size-${itemsPerPage}`;
    
    // Skip fetch if data is already loaded for this configuration or if we've exceeded retry limit
    if (dataLoaded && fetchConfig === lastFetchConfig) {
      console.log('Data already loaded for this configuration, skipping fetch');
      return;
    }
    
    if (retryCount >= 3) {
      console.log('Maximum retry attempts reached, stopping fetch');
      setError('Unable to load data after several attempts. Please refresh the page.');
      return;
    }
    
    if (loading) {
      console.log('Previous fetch still in progress, skipping');
      return;
    }
    
    const fetchInvoiceData = async () => {
      try {
        setLoading(true);
        console.log(`Fetching invoices (attempt ${retryCount + 1}/3) for ${fetchConfig}`);
        
        const response = await getInvoices(currentPage, itemsPerPage);
        console.log('Response from invoice API:', response);
        
        // Only update state if component is still mounted
        if (isMounted) {
          setTotalItems(response.total);
          
          // Transform API response to match the Invoice interface
          const transformedInvoices = response.items.map((item: InvoiceResponse) => {
            return {
              id: item.id,
              invoiceNumber: item.invoice_number 
                           ? (typeof item.invoice_number === 'object' 
                              ? item.invoice_number.formatted_number 
                              : item.invoice_number) 
                           : item.invoice_no || '',
              customer: {
                id: item.customer_id || item.company_id || '',
                name: item.customer_name || item.company_name || (item.company ? item.company.name : ''),
              },
              issueDate: item.date,
              dueDate: calculateDueDate(item.date),
              status: determineStatus(item),
              amount: typeof item.grand_total === 'string' ? parseFloat(item.grand_total) : (item.grand_total || 0),
              'customer.name': item.customer_name || item.company_name || (item.company ? item.company.name : ''),
              linkedQuoteId: item.linked_quote_id,
            };
          });
          
          console.log('Transformed invoices:', transformedInvoices);
          setInvoices(transformedInvoices);
          setError(null);
          
          // Mark as loaded and save the fetch configuration
          setDataLoaded(true);
          setLastFetchConfig(fetchConfig);
          
          // Reset retry count on success
          setRetryCount(0);
        }
      } catch (err) {
        console.error('Error fetching invoices:', err);
        
        // Only update state if component is still mounted
        if (isMounted) {
          let errorMessage = 'Failed to load invoices. Please try again later.';
          
          // Check if this is a debounce error
          if (err && typeof err === 'object' && 'message' in err) {
            const errorObj = err as { message: string, status?: number };
            
            if (errorObj.message.includes('debounce') || errorObj.status === 429) {
              console.log('Rate limiting error detected, will retry if under max attempts');
              errorMessage = 'Too many requests. Please wait';
              
              // Increment retry count but don't retry immediately to avoid hammering the server
              if (retryCount < 2) { // Allow 3 total attempts (0, 1, 2)
                setRetryCount(prev => prev + 1);
                // We'll let the cleanup timeout handle the retry after a delay
              } else {
                errorMessage = 'Failed to load after multiple attempts. Please refresh the page.';
              }
            } else {
              errorMessage = errorObj.message;
            }
          }
          
          setError(errorMessage);
        }
      } finally {
        if (isMounted) {
          setLoading(false);
        }
      }
    };

    fetchInvoiceData();
    
    // Cleanup function to prevent state updates if component unmounts
    return () => {
      isMounted = false;
    };
  }, [currentPage, itemsPerPage, retryCount]); 
  
  // Reset data loaded state when page or items per page changes
  useEffect(() => {
    const newFetchConfig = `page-${currentPage}-size-${itemsPerPage}`;
    if (newFetchConfig !== lastFetchConfig) {
      setDataLoaded(false);
    }
  }, [currentPage, itemsPerPage, lastFetchConfig]);
  
  // Retry logic with exponential backoff
  useEffect(() => {
    if (retryCount > 0 && retryCount < 3 && !loading) {
      const backoffTime = Math.pow(2, retryCount) * 1000; // Exponential backoff: 2s, 4s
      console.log(`Scheduling retry #${retryCount} in ${backoffTime}ms`);
      
      const retryTimeout = setTimeout(() => {
        console.log(`Executing retry #${retryCount}`);
        // This will trigger the data fetching effect
        setRetryCount(current => current); // Force effect to run again
      }, backoffTime);
      
      return () => clearTimeout(retryTimeout);
    }
  }, [currentPage, itemsPerPage]); // Only re-fetch when page or items per page changes

  // Helper function to calculate due date (usually 30 days after issue date)
  const calculateDueDate = (issueDate: string): string => {
    if (!issueDate) return '';
    try {
      const date = new Date(issueDate);
      date.setDate(date.getDate() + 30); // Add 30 days for due date
      return date.toISOString().split('T')[0]; // Format as YYYY-MM-DD
    } catch (e) {
      console.error('Error calculating due date:', e);
      return '';
    }
  };

  // Helper function to determine invoice status
  const determineStatus = (invoice: InvoiceResponse): 'paid' | 'unpaid' | 'overdue' | 'partially_paid' => {
    // For now, we'll use basic logic based on the current date and issue date
    // In a real application, this would come from the backend
    try {
      if (!invoice.date) return 'unpaid';
      
      const issueDate = new Date(invoice.date);
      const dueDate = new Date(invoice.date);
      dueDate.setDate(dueDate.getDate() + 30); // 30 days from issue
      const today = new Date();
      
      // If due date is past, mark as overdue
      if (today > dueDate) {
        return 'overdue';
      }
      
      // In a real app, you'd have a paid status from API
      // This is just placeholder logic
      return 'unpaid';
    } catch (e) {
      console.error('Error determining status:', e);
      return 'unpaid';
    }
  };

  const filteredInvoices = invoices.filter((invoice) => {
    if (!debouncedSearchTerm) return true;
    
    const searchLower = debouncedSearchTerm.toLowerCase();
    return (
      invoice.invoiceNumber.toLowerCase().includes(searchLower) ||
      invoice.customer.name.toLowerCase().includes(searchLower)
  );
  });

  const getStatusBadgeClass = (status: Invoice['status']) => {
    switch (status) {
      case 'paid':
        return 'bg-green-100 text-green-800';
      case 'unpaid':
        return 'bg-yellow-100 text-yellow-800';
      case 'overdue':
        return 'bg-red-100 text-red-800';
      case 'partially_paid':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusDisplay = (status: Invoice['status']) => {
    switch (status) {
      case 'paid':
        return 'Paid';
      case 'unpaid':
        return 'Unpaid';
      case 'overdue':
        return 'Overdue';
      case 'partially_paid':
        return 'Partially Paid';
      default:
        return status;
    }
  };

  const columns = [
    { 
      header: 'Invoice', 
      accessor: 'invoiceNumber' as keyof Invoice,
      cell: (value: string, row: Invoice) => (
        <div className="flex items-center">
          <Link 
            to={`/invoices/${row.id}`}
            className="text-blue-600 hover:text-blue-900 font-medium"
          >
            {value}
          </Link>
          {row.linkedQuoteId && (
            <span 
              className="ml-2 px-2 py-0.5 bg-purple-100 text-purple-800 text-xs rounded-full"
              title={`Converted from Quote #${row.linkedQuoteId}`}
            >
              From Quote
            </span>
          )}
        </div>
      )
    },
    { 
      header: 'Customer', 
      accessor: 'customer.name' as keyof Invoice,
      cell: (value: string, row: Invoice) => (
        <Link 
          to={`/customers/${row.customer.id}`}
          className="text-gray-900 hover:text-gray-600"
        >
          {row.customer.name}
        </Link>
      )
    },
    { header: 'Issue Date', accessor: 'issueDate' as keyof Invoice },
    { header: 'Due Date', accessor: 'dueDate' as keyof Invoice },
    { 
      header: 'Status', 
      accessor: 'status' as keyof Invoice,
      cell: (value: Invoice['status']) => (
        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusBadgeClass(value)}`}>
          {getStatusDisplay(value)}
        </span>
      )
    },
    { 
      header: 'Amount', 
      accessor: 'amount' as keyof Invoice,
      cell: (value: number) => (
        <span className="font-medium">${value.toFixed(2)}</span>
      )
    },
    {
      header: 'Actions',
      accessor: 'id' as keyof Invoice,
      cell: (value: string, row: Invoice) => (
        <div className="flex space-x-2">
          <Button
            onClick={() => onViewInvoice(value)}
            variant="secondary"
            size="sm"
          >
            View
          </Button>
          <Button
            onClick={() => onEditInvoice(value)}
            variant="primary"
            size="sm"
          >
            Edit
          </Button>
          {row.linkedQuoteId && (
            <Link 
              to={`/quotes/${row.linkedQuoteId}`}
              className="px-2 py-1 bg-indigo-100 text-indigo-700 text-xs rounded hover:bg-indigo-200 flex items-center"
            >
              View Quote
            </Link>
          )}
        </div>
      ),
    },
  ];

  return (
    <div className="flex flex-col">
      <div className="mb-4">
        <SearchBar
          value={searchTerm}
          onChange={handleSearchChange}
          placeholder="Search by invoice number or customer name"
        />
      </div>
      <div className="bg-white rounded-lg shadow overflow-hidden">
        {loading ? (
          <LoadingSkeleton />
        ) : error ? (
          <div className="p-4 text-center">
            <div className="inline-flex items-center text-red-600 bg-red-50 px-4 py-2 rounded-lg">
              <svg className="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 20 20">
                <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
              </svg>
              {error}
            </div>
          </div>
        ) : (
          <div className="transition-opacity duration-200">
            <Table 
              data={filteredInvoices} 
              columns={columns}
              emptyMessage="No invoices found. Adjust your search or create a new invoice."
            />
          </div>
        )}
      </div>
    </div>
  );
};

export default InvoiceTable; 