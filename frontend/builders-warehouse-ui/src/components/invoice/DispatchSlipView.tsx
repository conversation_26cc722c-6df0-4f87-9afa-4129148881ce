import React, { useState, useEffect, useRef } from 'react';
import styled from 'styled-components';
import builderLogo from '../../assets/Builder-Logo.png';
import { getInvoice } from '../../services/invoiceService';
import { format, parseISO } from 'date-fns';
import customerService from '../../services/customerService';
// @ts-ignore
import html2pdf from 'html2pdf.js';

const ModalOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
`;

const ModalContainer = styled.div`
  background-color: white;
  border-radius: 8px;
  width: 95vw;
  max-width: 1200px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
`;

const ModalHeader = styled.div`
  padding: 8px 16px;
  border-bottom: none;
  text-align: right;
`;

const ModalTitle = styled.h2`
  font-size: 16px;
  color: #333;
  margin: 0;
  display: none;
`;

const ModalBody = styled.div`
  padding: 16px;
`;

const PrintableContent = styled.div`
  background: white;
  font-family: Arial, sans-serif;
  padding: 20px;
  max-width: 1100px;
  margin: 0 auto;
  
  @media print {
    margin: 0;
    padding: 20px;
    font-size: 12px;
    page-break-inside: avoid;
    transform: rotate(0deg);
  }
`;

const DispatchContainer = styled.div`
  border: 2px solid #333;
  padding: 20px;
  margin-bottom: 20px;
  background: white;
`;

const DispatchTitle = styled.h1`
  text-align: center;
  font-size: 28px;
  font-weight: bold;
  color: #333;
  margin: 0 0 20px 0;
  padding: 0;
`;

const DispatchHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
`;

const CompanyLogo = styled.div`
  width: 180px;
  display: flex;
  flex-direction: column;
  
  img {
    max-width: 100%;
    height: auto;
    margin-bottom: 5px;
  }
  
  .tagline {
    font-style: italic;
    font-size: 14px;
    color: #042B41;
    margin-top: 0;
    font-family: cursive;
  }
`;

const CompanyInfo = styled.div`
  text-align: right;
  font-size: 11px;
  line-height: 1.3;
  color: #333;
  
  p {
    margin: 1px 0;
  }
  
  .company-name {
    font-weight: bold;
    font-size: 12px;
  }
`;

const MainContentContainer = styled.div`
  display: flex;
  gap: 15px;
  margin-bottom: 15px;
`;

const LeftSection = styled.div`
  flex: 1;
`;

const RightSection = styled.div`
  width: 300px;
  display: flex;
  flex-direction: column;
`;

const InfoTable = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr;
  margin-bottom: 15px;
  border: 2px solid #333;
`;

const InfoCell = styled.div`
  padding: 12px;
  border: 1px solid #333;
  font-size: 13px;
  min-height: 50px;
`;

const InfoLabel = styled.div`
  font-weight: bold;
  font-size: 12px;
  margin-bottom: 6px;
  color: #333;
`;

const DispatchSlipLabel = styled.div`
  background-color: #0096db;
  color: white;
  padding: 8px 15px;
  text-align: center;
  font-weight: bold;
  font-size: 16px;
  margin-bottom: 0;
  border: 2px solid #333;
`;

const DispatchDetails = styled.div`
  display: flex;
  flex-direction: column;
  border: 2px solid #333;
  border-top: none;
  background-color: white;
`;

const DispatchDetail = styled.div`
  display: flex;
  justify-content: space-between;
  width: 100%;
  padding: 6px 15px;
  font-size: 13px;
  border-bottom: 1px solid #333;
  
  &:last-child {
    border-bottom: none;
  }
  
  span:first-child {
    font-weight: 600;
  }
  
  span:last-child {
    text-align: right;
    font-weight: 500;
  }
`;

const DispatchTable = styled.table`
  width: 100%;
  border-collapse: collapse;
  margin: 15px 0;
  font-size: 12px;
  border: 2px solid #333;
  
  th, td {
    border: 1px solid #333;
    padding: 6px 4px;
    text-align: center;
  }
  
  th {
    background-color: #042B41;
    color: white;
    font-size: 11px;
    font-weight: 600;
    text-align: center;
    vertical-align: middle;
    height: 35px;
  }
  
  td {
    padding: 6px 4px;
    vertical-align: middle;
    font-size: 11px;
    height: 30px;
  }
  
  .code-column { 
    width: 15%; 
    text-align: left;
  }
  .description-column { 
    width: 40%; 
    text-align: left;
  }
  .quantity-column { width: 11.25%; }
`;

const ContactInfo = styled.div`
  margin: 20px 0 0 0;
  padding: 12px;
  border: 2px solid #333;
  font-size: 12px;
  line-height: 1.4;
  background-color: #f9f9f9;
  
  p {
    margin: 4px 0;
  }
  
  p:first-child {
    margin-top: 0;
  }
  
  p:last-child {
    margin-bottom: 0;
  }
  
  strong {
    font-weight: 700;
  }
`;

const ButtonContainer = styled.div`
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 24px;
`;

const Button = styled.button<{ primary?: boolean; disabled?: boolean }>`
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 24px;
  border: none;
  border-radius: 4px;
  font-weight: 500;
  cursor: ${props => props.disabled ? 'not-allowed' : 'pointer'};
  background-color: ${props => props.disabled ? '#ccc' : (props.primary ? '#042B41' : 'white')};
  color: ${props => props.disabled ? '#666' : (props.primary ? 'white' : '#042B41')};
  border: ${props => props.primary ? 'none' : '1px solid #042B41'};
  opacity: ${props => props.disabled ? 0.6 : 1};
  
  &:hover {
    background-color: ${props => props.disabled ? '#ccc' : (props.primary ? '#0A3D5A' : '#f9fafb')};
  }
`;

interface DispatchItem {
    id?: string;
    invoice_id?: string;
    code: string;
    description: string;
    sku?: string;
    units?: number;
    boxes?: number;
    pieces?: number;
    m2?: number;
    created_at?: string;
    updated_at?: string;
    quantity?: {
        units: number | null;
        boxes: number | null;
        pieces: number | null;
        m2: number | null;
        total: number;
    };
}

interface DispatchSlipViewProps {
    invoiceId: string;
    onClose: () => void;
    printMode?: boolean;
}

const DispatchSlipView: React.FC<DispatchSlipViewProps> = ({ invoiceId, onClose, printMode }) => {
    const [invoice, setInvoice] = useState<any>(null);
    const [loading, setLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);
    const [companyName, setCompanyName] = useState<string>('');
    const [customerMobile, setCustomerMobile] = useState<string>('');
    const [customerEmail, setCustomerEmail] = useState<string>('');
    const [customerData, setCustomerData] = useState<any>(null);
    const [downloading, setDownloading] = useState(false);
    const fetchInProgressRef = useRef(false);
    const pdfRef = useRef<HTMLDivElement>(null);

    // Fetch invoice data when component mounts or invoiceId changes
    useEffect(() => {
        const fetchInvoiceData = async () => {
            // Prevent duplicate calls
            if (fetchInProgressRef.current) {
                console.log('Invoice fetch already in progress, skipping duplicate call');
                return;
            }

            // Reset all state when invoiceId changes
            setLoading(true);
            setError(null);
            setInvoice(null);
            setCompanyName('');
            setCustomerMobile('');
            setCustomerEmail('');
            setCustomerData(null);
            setDownloading(false);
            fetchInProgressRef.current = true;

            try {
                console.log('Fetching invoice for dispatch slip with ID:', invoiceId);
                const invoiceData = await getInvoice(invoiceId);
                console.log('Invoice data received for dispatch slip:', invoiceData);

                setInvoice(invoiceData);

                // If we have a company_id, fetch the complete customer details including mobile
                if (invoiceData.company_id) {
                    try {
                        console.log('Fetching customer details for company_id:', invoiceData.company_id);
                        const customerData = await customerService.getCustomerById(invoiceData.company_id);
                        if (customerData) {
                            console.log('Found customer data:', customerData);
                            if (customerData.name) {
                                setCompanyName(customerData.name);
                            }
                            if (customerData.phone) {
                                console.log('Found customer mobile:', customerData.phone);
                                setCustomerMobile(customerData.phone);
                            }
                            if (customerData.email) {
                                console.log('Found customer email:', customerData.email);
                                setCustomerEmail(customerData.email);
                            }
                            setCustomerData(customerData);
                        }
                    } catch (customerErr) {
                        console.error('Error fetching customer details:', customerErr);
                        // Continue with the dispatch slip even if customer details fetch fails
                    }
                }
            } catch (err) {
                console.error('Error fetching invoice for dispatch slip:', err);
                let errorMessage = 'Failed to load dispatch slip data. Please try again.';

                // Provide more specific error messages based on the error type
                if (err && typeof err === 'object' && 'response' in err) {
                    const apiError = err as any;
                    if (apiError.response?.status === 404) {
                        // Enhanced error message for 404 - invoice not found
                        errorMessage = `Invoice "${invoiceId}" not found in the system. This may be because:
                        
• The invoice number shown in the outbound delivery list may not match an actual invoice in the database
• The invoice may have been deleted or archived
• There may be a data synchronization issue between systems

Please contact your system administrator to verify the invoice data.`;
                    } else if (apiError.response?.status === 403) {
                        errorMessage = 'You do not have permission to view this dispatch slip.';
                    } else if (apiError.response?.status >= 500) {
                        errorMessage = 'Server error. Please try again later.';
                    }
                } else if (err && typeof err === 'object' && 'message' in err) {
                    // Handle network errors or other issues
                    const genericError = err as any;
                    if (genericError.message.includes('404')) {
                        errorMessage = `Invoice "${invoiceId}" not found in the system. The invoice number shown in the delivery list may not correspond to an actual invoice record.`;
                    }
                }

                setError(errorMessage);
            } finally {
                setLoading(false);
                fetchInProgressRef.current = false;
            }
        };

        if (invoiceId) {
            fetchInvoiceData();
        } else {
            setError('No invoice ID provided');
            setLoading(false);
            fetchInProgressRef.current = false;
        }
    }, [invoiceId]);

    const generateClientSidePdf = async () => {
        if (!pdfRef.current || downloading) return;

        setDownloading(true);

        try {
            console.log('Generating Dispatch Slip PDF using html2pdf.js');

            // Get invoice number for filename
            const invoiceNumber = invoice?.invoice_number?.formatted_number ||
                (typeof invoice?.invoice_number === 'object' && 'id' in invoice.invoice_number
                    ? invoice.invoice_number.formatted_number
                    : invoice?.invoice_no || 'dispatch-slip');

            const options = {
                margin: 10,
                filename: `dispatch-slip-${invoiceNumber}.pdf`,
                image: { type: 'jpeg', quality: 0.98 },
                html2canvas: {
                    scale: 2,
                    useCORS: true,
                    allowTaint: true
                },
                jsPDF: {
                    unit: 'mm',
                    format: 'a4',
                    orientation: 'portrait'
                }
            };

            await html2pdf().set(options).from(pdfRef.current).save();
            console.log('Dispatch Slip PDF generated and downloaded successfully');

        } catch (error) {
            console.error('Error generating Dispatch Slip PDF:', error);
            alert('Failed to generate Dispatch Slip PDF. Please try again.');
        } finally {
            setDownloading(false);
        }
    };

    const handlePrint = () => {
        if (printMode) {
            // In print mode, just open the print dialog directly
            console.log('Print mode activated for dispatch slip, opening print dialog');
            window.print();
            return;
        }

        // For regular mode, generate PDF
        generateClientSidePdf();
    };

    const formatDate = (dateStr: string): string => {
        try {
            if (!dateStr) return 'N/A';
            const date = parseISO(dateStr);
            return format(date, 'dd/MM/yyyy');
        } catch (err) {
            return dateStr || 'N/A';
        }
    };

    if (loading) {
        return (
            <ModalOverlay>
                <ModalContainer>
                    <ModalHeader>
                        <ModalTitle>Loading Dispatch Slip</ModalTitle>
                    </ModalHeader>
                    <ModalBody style={{ textAlign: 'center', padding: '2rem' }}>
                        <p>Loading dispatch slip data...</p>
                    </ModalBody>
                </ModalContainer>
            </ModalOverlay>
        );
    }

    if (error || !invoice) {
        return (
            <ModalOverlay>
                <ModalContainer>
                    <ModalHeader>
                        <ModalTitle>Invoice Not Found</ModalTitle>
                    </ModalHeader>
                    <ModalBody style={{ padding: '2rem' }}>
                        <div style={{
                            textAlign: 'left',
                            lineHeight: '1.6',
                            color: '#333',
                            backgroundColor: '#fff3cd',
                            border: '1px solid #ffeaa7',
                            borderRadius: '8px',
                            padding: '20px',
                            marginBottom: '20px'
                        }}>
                            <h3 style={{
                                color: '#856404',
                                marginTop: '0',
                                marginBottom: '15px',
                                fontSize: '18px',
                                fontWeight: '600'
                            }}>
                                Unable to Load Dispatch Slip
                            </h3>
                            <div style={{
                                whiteSpace: 'pre-line',
                                fontSize: '14px',
                                color: '#856404'
                            }}>
                                {error || 'Failed to load dispatch slip data'}
                            </div>
                        </div>
                        <div style={{ textAlign: 'center' }}>
                            <Button onClick={onClose}>Close</Button>
                        </div>
                    </ModalBody>
                </ModalContainer>
            </ModalOverlay>
        );
    }

    // Extract data from API response with better error handling
    const invoiceNumber = invoice.invoice_number?.formatted_number ||
        (typeof invoice.invoice_number === 'object' && 'id' in invoice.invoice_number
            ? invoice.invoice_number.formatted_number
            : invoice.invoice_no || 'N/A');

    const customerName = companyName || invoice.company?.name || invoice.customer_name || 'N/A';
    const date = formatDate(invoice.date || invoice.invoice_date);
    const reference = invoice.purchase_order_number || invoice.po_number || '';

    // Enhanced delivery address handling with fallback to customer billing address
    let deliveryAddress = invoice.deliver_to_address || invoice.delivery_address || '';
    let deliveryAddressNote = '';

    if (!deliveryAddress || deliveryAddress.trim() === '') {
        // Try to get customer billing address as fallback
        if (customerData && customerData.billing_address) {
            let fallbackAddress = customerData.billing_address;
            if (customerData.billing_suburb) {
                fallbackAddress += `, ${customerData.billing_suburb}`;
            }
            if (customerData.billing_postcode) {
                fallbackAddress += ` ${customerData.billing_postcode}`;
            }
            deliveryAddress = fallbackAddress;
            deliveryAddressNote = '(Using customer billing address)';
        } else {
            deliveryAddress = 'No delivery address specified';
            deliveryAddressNote = '(Please contact customer for delivery details)';
        }
    }

    // Handle items array - it could be nested in an 'items' property or be the direct array
    let itemsArray: any[] = [];
    if (invoice.items) {
        if (Array.isArray(invoice.items)) {
            itemsArray = invoice.items;
        } else if (invoice.items.items && Array.isArray(invoice.items.items)) {
            itemsArray = invoice.items.items;
        }
    }

    console.log('Processing items array for dispatch slip:', itemsArray);

    const items: DispatchItem[] = itemsArray.map((item: any, index: number) => {
        console.log(`Processing dispatch item ${index}:`, item);

        return {
            ...item,
            code: item.sku || item.sku_code || item.code || 'N/A',
            description: item.description || 'N/A',
            quantity: {
                units: item.units || item.quantity_units || null,
                boxes: item.boxes || item.quantity_boxes || null,
                pieces: item.pieces || item.quantity_pieces || null,
                m2: item.m2 || item.quantity_m2 || null,
                total: (item.units || item.quantity_units || 0) +
                    (item.boxes || item.quantity_boxes || 0) +
                    (item.pieces || item.quantity_pieces || 0) +
                    (item.m2 || item.quantity_m2 || 0)
            }
        };
    });

    console.log('Rendered dispatch slip data:', {
        invoiceNumber,
        customerName,
        customerMobile,
        customerEmail,
        date,
        reference,
        deliveryAddress,
        itemsCount: items.length
    });

    return (
        <ModalOverlay>
            <ModalContainer>
                <ModalHeader>
                    {/* No title needed */}
                </ModalHeader>
                <ModalBody>
                    <PrintableContent ref={pdfRef}>
                        <DispatchTitle>Dispatch Slip</DispatchTitle>
                        <DispatchContainer>
                            <DispatchHeader>
                                <CompanyLogo>
                                    <img src={builderLogo} alt="Builders Warehouse Australia" width="180" />
                                    <div className="tagline">a better way</div>
                                </CompanyLogo>
                                <CompanyInfo>
                                    <p className="company-name">BWA Supplies Pty Ltd (ACN ***********)</p>
                                    <p>trading as Builders Warehouse Australia</p>
                                    <p>(ABN **************)</p>
                                    <p></p>
                                    <p>Office & Showroom: 214 High St Cranbourne 3977</p>
                                    <p>www.builderswarehouseaustralia.com.au</p>
                                    <p>Tel: 03 8764 9142 | <EMAIL></p>
                                </CompanyInfo>
                            </DispatchHeader>

                            <MainContentContainer>
                                <LeftSection>
                                    <InfoTable>
                                        <InfoCell>
                                            <InfoLabel>DISPATCH TO</InfoLabel>
                                            <div>{customerName}</div>
                                            {customerMobile && (
                                                <div style={{ marginTop: '4px', fontWeight: 'bold' }}>
                                                    Mobile: {customerMobile}
                                                </div>
                                            )}
                                            {customerEmail && (
                                                <div style={{ marginTop: '2px', fontSize: '11px' }}>
                                                    Email: {customerEmail}
                                                </div>
                                            )}
                                        </InfoCell>
                                        <InfoCell>
                                            <InfoLabel>DELIVERY ADDRESS</InfoLabel>
                                            <div>{deliveryAddress}</div>
                                            {deliveryAddressNote && (
                                                <div style={{ marginTop: '4px', fontSize: '11px', color: '#666' }}>
                                                    {deliveryAddressNote}
                                                </div>
                                            )}
                                        </InfoCell>
                                    </InfoTable>
                                </LeftSection>
                                <RightSection>
                                    <DispatchSlipLabel>DISPATCH SLIP</DispatchSlipLabel>
                                    <DispatchDetails>
                                        <DispatchDetail>
                                            <span><strong>Invoice No:</strong></span>
                                            <span>{invoiceNumber}</span>
                                        </DispatchDetail>
                                        <DispatchDetail>
                                            <span><strong>Date:</strong></span>
                                            <span>{date}</span>
                                        </DispatchDetail>
                                        <DispatchDetail>
                                            <span><strong>Your Ref:</strong></span>
                                            <span>{reference}</span>
                                        </DispatchDetail>
                                    </DispatchDetails>
                                </RightSection>
                            </MainContentContainer>

                            <DispatchTable>
                                <thead>
                                    <tr>
                                        <th className="code-column">CODE</th>
                                        <th className="description-column">DESCRIPTION</th>
                                        <th className="quantity-column">Units</th>
                                        <th className="quantity-column">Boxes</th>
                                        <th className="quantity-column">Pieces</th>
                                        <th className="quantity-column">M2</th>
                                        <th className="quantity-column">TOTAL QTY</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {items.length > 0 ? (
                                        items.map((item: DispatchItem, index: number) => (
                                            <tr key={index}>
                                                <td className="code-column" style={{ textAlign: 'left' }}>{item.code}</td>
                                                <td className="description-column" style={{ textAlign: 'left' }}>{item.description}</td>
                                                <td className="quantity-column">
                                                    {item.quantity?.units || item.units || ''}
                                                </td>
                                                <td className="quantity-column">
                                                    {item.quantity?.boxes || item.boxes || ''}
                                                </td>
                                                <td className="quantity-column">
                                                    {item.quantity?.pieces || item.pieces || ''}
                                                </td>
                                                <td className="quantity-column">
                                                    {item.quantity?.m2 || item.m2 || ''}
                                                </td>
                                                <td className="quantity-column" style={{ fontWeight: 'bold' }}>
                                                    {item.quantity?.total ||
                                                        item.quantity?.units ||
                                                        item.quantity?.boxes ||
                                                        item.quantity?.pieces ||
                                                        item.quantity?.m2 ||
                                                        item.units ||
                                                        item.boxes ||
                                                        item.pieces ||
                                                        item.m2 ||
                                                        1}
                                                </td>
                                            </tr>
                                        ))
                                    ) : (
                                        <tr>
                                            <td colSpan={7} style={{ textAlign: 'center', padding: '2rem' }}>
                                                No items found for this dispatch slip
                                            </td>
                                        </tr>
                                    )}
                                </tbody>
                            </DispatchTable>
                        </DispatchContainer>

                        <ContactInfo>
                            <p><strong>Important Instructions for Transporter:</strong></p>
                            <p>• Please contact the customer before delivery at the mobile number provided above</p>
                            <p>• Verify delivery address and ensure someone is available to receive the goods</p>
                            <p>• Check all items against this dispatch slip before leaving the delivery location</p>
                            <p>• Get signature confirmation from the customer upon successful delivery</p>
                            <p>• Contact BWA office at 03 8764 9142 for any delivery issues or questions</p>
                        </ContactInfo>
                    </PrintableContent>

                    <ButtonContainer>
                        <Button onClick={onClose}>Close</Button>
                        <Button primary onClick={handlePrint} disabled={downloading}>
                            {downloading ? 'Generating...' : 'Print Dispatch Slip'}
                        </Button>
                    </ButtonContainer>
                </ModalBody>
            </ModalContainer>
        </ModalOverlay>
    );
};

export default DispatchSlipView; 