import React, { useState, useEffect, useMemo, useCallback, useRef } from 'react';
import styled from 'styled-components';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import Layout from '../../layout/Layout';
import Pagination from '../../common/Pagination';
import DateRangePicker from '../../common/DateRangePicker';
import EditInvoiceForm from '../EditInvoiceForm';
import InvoiceView from '../InvoiceView';
import invoicesCreateIcon from '../../../assets/invoicesCreateIcon.png';
import invoicesEditIcon from '../../../assets/invoicesEditIcon.png';
import invoicesPrintIcon from '../../../assets/invoicesPrintIcon.png';
import invoicesNotesIcon from '../../../assets/invoicesNotesIcon.png';
import invoicesMailIcon from '../../../assets/invoicesMailIcon.png';
import searchIcon from '../../../assets/navSearchIcon.png';
import backButtonIcon from '../../../assets/backButton.png';
import customerAddIcon from '../../../assets/customerAddIcon.png';
import { format, parse, parseISO } from 'date-fns';
import {
  getInvoices,
  getInvoice,
  updateInvoice,
  createInvoice,
  generateInvoicePdf,
  emailInvoice,
  InvoiceResponse,
  InvoicePagination,
  InvoiceOut,
  InvoiceCreate,
  InvoiceUpdate,
  InvoiceFilters,
  SaleTypeEnum,
  PaymentModeEnum
} from '../../../services/invoiceService';
import quoteService from '../../../services/quoteService';
import apiClient from '../../../services/apiClient';
import { AUTH_TOKEN_KEY, API_URL, ENDPOINTS } from '../../../config';
import { CustomBackButton } from '../../ui/DesignSystem';
import PageLoadingSpinner from '../../ui/PageLoadingSpinner';
import { useToast } from '../../../hooks/useToast';
import { Toast } from '../../common/Toast';
import { useStoreFilter } from '../../../hooks/useStoreFilter';
import LoadingSpinner from '../../ui/LoadingSpinner';
import _ from 'lodash';

// Add CSS for spinner animation
const spinnerStyle = `
  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

// Inject the styles into the document head
if (typeof document !== 'undefined') {
  const styleElement = document.createElement('style');
  styleElement.textContent = spinnerStyle;
  if (!document.head.querySelector('style[data-spinner]')) {
    styleElement.setAttribute('data-spinner', 'true');
    document.head.appendChild(styleElement);
  }
}

const PageContainer = styled.div`
  padding: 0 32px 24px 32px;
`;

const PageTitle = styled.h1`
  color: #042B41;
  font-size: 32px;
  font-weight: 800;
  margin: 0;
`;

const HeaderContainer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
`;

const ControlsContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 1rem;
`;

const SearchContainer = styled.div`
  position: relative;
  width: 193px;
  height: 36px;
  background: rgba(255, 255, 255, 1);
`;

const SearchIconWrapper = styled.div`
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  color: #9CA3AF;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: none;
`;

const SearchInput = styled.input`
  width: 100%;
  height: 100%;
  padding: 10px 15px 10px 40px;
  border: 1px solid #E5E7EB;
  border-radius: 10px;
  font-size: 14px;
  background: rgba(255, 255, 255, 1);

  &::placeholder {
    color: #9CA3AF;
    opacity: 0.6;
  }
`;

const DatePickerWrapper = styled.div`
  width: 245px;
  height: 36px;
  background: rgba(255, 255, 255, 1);

  /* Override DateRangePicker styles for this page */
  & > div {
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 1);
  }

  .calendar-icon {
    width: 16px;
    height: 16px;
    margin-right: 8px;
  }

  span {
    opacity: 0.6;
  }
`;

const CreateButton = styled.button`
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: #042B41;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  margin-left: auto;
  height: 36px;

  &:hover {
    background-color: #0A3D5A;
  }

  img {
    width: 16px;
    height: 16px;
  }
`;

const Table = styled.table`
  width: 100%;
  border-collapse: collapse;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  overflow: hidden;
  background-color: white;
  border: 1px solid #e0e0e0;
`;

const TableHeader = styled.thead`
  background-color: #042B41;

  th {
    padding: 10px 12px;
    text-align: center;
    font-weight: 500;
    height: 48px;
    vertical-align: middle;
    border: 1px solid #0A3D5A;
    color: white !important;
    font-size: 14px !important;
    font-weight: bold !important;
    
    &:nth-child(5) {
      text-align: right; /* Right-align the Grand Total header */
    }
  }
`;

const TableBody = styled.tbody`
  tr {
    background-color: white;
    border-bottom: 1px solid #e0e0e0;
    height: 48px;

    &:last-child {
      border-bottom: none;
    }

    &:hover {
      background-color: #f9fafb;
    }
  }

  td {
    padding: 10px 12px;
    color: #333;
    vertical-align: middle;
    border: 1px solid #e0e0e0;
    font-size: 14px;
    text-align: center;
    
    &:nth-child(5) {
      text-align: right; /* Right-align the Grand Total values */
    }
  }
`;

const ActionButton = styled.button`
  background: none;
  border: none;
  cursor: pointer;
  font-size: 1.2rem;
  padding: 3px;
  display: flex;
  align-items: center;
  justify-content: center;

  img {
    width: 18px;
    height: 18px;
    object-fit: contain;
  }
`;

const NotesIcon = styled.span<{ hasNotes?: boolean }>`
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;

  img {
    width: 18px;
    height: 18px;
    object-fit: contain;
    filter: ${props => props.hasNotes ? 'invert(15%) sepia(95%) saturate(6932%) hue-rotate(358deg) brightness(95%) contrast(112%)' : 'none'};
  }
`;

const PaginationWrapper = styled.div`
  display: flex;
  justify-content: flex-end;
  margin-top: 1.5rem;
`;

// Notes Modal Components
const NotesModalOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
`;

const NotesModalContent = styled.div`
  background-color: white;
  border-radius: 8px;
  width: 100%;
  max-width: 600px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 20px;
`;

const NotesModalTitle = styled.h2`
  font-size: 1.5rem;
  color: #333;
  margin: 0 0 20px 0;
  text-align: center;
`;

const NotesTextarea = styled.textarea`
  width: 100%;
  height: 150px;
  padding: 12px 15px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  font-size: 1rem;
  line-height: 1.5;
  margin-bottom: 20px;
  resize: none;

  &:focus {
    outline: none;
    border-color: #042B41;
  }
`;

const NotesButtonContainer = styled.div`
  display: flex;
  justify-content: center;
  gap: 16px;
`;

const NotesButton = styled.button`
  padding: 10px 20px;
  border-radius: 6px;
  font-weight: 500;
  font-size: 0.9rem;
  cursor: pointer;
  min-width: 120px;
`;

const SaveNotesButton = styled(NotesButton)`
  background-color: #042B41;
  color: white;
  border: none;

  &:hover {
    background-color: #031f30;
  }
`;

const CloseNotesButton = styled(NotesButton)`
  background-color: white;
  color: #333;
  border: 1px solid #e0e0e0;

  &:hover {
    background-color: #f5f5f5;
  }
`;

const CancelButton = styled.button`
  padding: 10px 20px;
  border-radius: 6px;
  font-weight: 500;
  font-size: 0.9rem;
  cursor: pointer;
  min-width: 120px;
`;

// Restore the original back button styling to match other pages
const StyledBackButton = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  border: none;
  color: #042B41;
  font-size: 1.25rem;
  cursor: pointer;
  padding: 6px;
  border-radius: 4px;
  transition: background-color 0.2s;
  
  &:hover {
    background-color: rgba(4, 43, 65, 0.05);
  }
  
  svg {
    stroke-width: 2px;
  }
`;

// Add a styled component for the breadcrumb
const BreadcrumbContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 10px;
  font-size: 24px;
  font-weight: 600;
`;

const BreadcrumbItem = styled.span`
  font-weight: 600;
  color: #042B41;
  cursor: pointer;

  &:hover {
    text-decoration: underline;
  }

  &:last-child {
    color: #6B7280;
    font-weight: 500;
    text-decoration: none;
    cursor: default;

    &:hover {
      text-decoration: none;
    }
  }
`;

const BreadcrumbSeparator = styled.span`
  color: #6B7280;
`;

// Interface definitions
interface Invoice {
  id: string;
  invoiceNo: string;
  customerName: string;
  storeType: string;
  date: string;
  total: string;
  notes?: string;
  status?: string;
  data?: InvoiceData;
  hasNotes?: boolean;
}

interface InvoiceData {
  storeType: string;
  saleType: string;
  modeOfPayment: string;
  poNumber: string;
  invoiceTo: string;
  deliverToAddress: string;
  invoiceNo: string;
  notes: string;
  linkedQuote: string;
  date: string;
  dontSendPo: boolean;
  items: InvoiceItem[];
  totals: {
    orderTotal: number;
    surcharge: number;
    gst: number;
    shipping: number;
    grandTotal: number;
  };
}

interface InvoiceItem {
  id: string;
  sku: string;
  description: string;
  quantity: {
    units: number | null;
    boxes: number | null;
    pieces: number | null;
    m2: number | null;
    total: number;
  };
  unitPrice: number;
  totalPrice: number;
}

// Define Quote interface for the quotes state
interface Quote {
  id: string;
  quote_no: string;
  items?: any[];
  quote_items?: any[];
}

enum PageView {
  LIST,
  EDIT,
  CREATE
}

// Add the missing styled components
const ActionButtonsContainer = styled.div`
  display: flex;
  gap: 6px;
  justify-content: center;
`;

const PaginationContainer = styled.div`
  margin-top: 1.5rem;
  display: flex;
  justify-content: center;
`;

// Notes column styling
interface NotesColumnProps {
  hasNotes?: boolean;
}

const NotesColumn = styled.td<NotesColumnProps>`
  cursor: pointer;
  padding: 10px 12px;
  color: #333;
  vertical-align: middle;
  border: 1px solid #e0e0e0;
  font-size: 14px;
  text-align: center;
  
  img {
    width: 18px;
    height: 18px;
    object-fit: contain;
    filter: ${props => props.hasNotes ? 'invert(15%) sepia(95%) saturate(6932%) hue-rotate(358deg) brightness(95%) contrast(112%)' : 'none'};
  }
  
  &:hover img {
    opacity: 0.7;
  }
`;

const LoadingRow = styled.tr`
  td {
    text-align: center !important;
    padding: 40px 20px !important;
    border: none !important;
  }
`;

const LoadingContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 12px;
`;

const LoadingText = styled.p`
  margin: 0;
  font-size: 14px;
  color: #6B7280;
  font-weight: 500;
`;

const StatusBadge = styled.span<{ status: string }>`
  display: inline-block;
  padding: 4px 8px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  border-radius: 4px;
  border: 1px solid;
  
  ${props => props.status === 'draft' || props.status === 'DRAFTED' ? `
    background-color: #fef2f2;
    color: #dc2626;
    border-color: #dc2626;
  ` : `
    background-color: #f0fdf4;
    color: #16a34a;
    border-color: #16a34a;
  `}
`;

const InvoicesPage: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const searchParams = new URLSearchParams(location.search);
  const shouldCreateInvoice = searchParams.get('create') === 'true';
  const toast = useToast();
  const storeFilter = useStoreFilter();

  // State management
  const [invoices, setInvoices] = useState<Invoice[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isSearching, setIsSearching] = useState<boolean>(false); // Add separate search loading state
  const [totalItems, setTotalItems] = useState<number>(0);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [itemsPerPage, setItemsPerPage] = useState<number>(10);
  const [searchQuery, setSearchQuery] = useState<string>('');
  const [selectedDateRange, setSelectedDateRange] = useState<string>('');
  const [filters, setFilters] = useState<InvoiceFilters>({});
  const [error, setError] = useState<string | null>(null);
  const [currentView, setCurrentView] = useState<PageView>(PageView.LIST);
  const [editingInvoiceId, setEditingInvoiceId] = useState<string | null>(null);
  const [currentInvoice, setCurrentInvoice] = useState<InvoiceOut | null>(null);
  const [selectedInvoiceId, setSelectedInvoiceId] = useState<string | null>(null);
  const [showNotesModal, setShowNotesModal] = useState<boolean>(false);
  const [notesInvoiceId, setNotesInvoiceId] = useState<string | null>(null);
  const [notesValue, setNotesValue] = useState<string>('');
  const [isSavingNotes, setIsSavingNotes] = useState<boolean>(false);
  const [isViewInvoiceOpen, setIsViewInvoiceOpen] = useState<boolean>(false);
  const [viewInvoiceId, setViewInvoiceId] = useState<string | null>(null);

  // Track data fetching status without aggressive caching
  const [dataFetched, setDataFetched] = useState<boolean>(false);
  const [lastFetchKey, setLastFetchKey] = useState<string>('');

  // Quotes state
  const [quotes, setQuotes] = useState<Quote[]>([]);

  // Debounced search functionality
  const [searchTimeout, setSearchTimeout] = useState<ReturnType<typeof setTimeout> | null>(null);

  // Simplified debounced search handler
  const debouncedSearch = useCallback((searchValue: string) => {
    // Clear existing timeout
    if (searchTimeout) {
      clearTimeout(searchTimeout);
    }

    // Set new timeout
    const newTimeout = setTimeout(() => {
      console.log('Executing debounced search for:', searchValue);
      
      // Update filters without clearing cache states
      setFilters(prevFilters => ({
        ...prevFilters,
        search: searchValue || undefined
      }));

      // Reset to first page
      setCurrentPage(1);
    }, 300); // 300ms debounce

    setSearchTimeout(newTimeout);
  }, [searchTimeout]);

  // Helper function to show toast
  const showToast = (message: string, type: 'error' | 'success' | 'info' = 'error') => {
    toast.showToast(message, { type });
  };

  // Add a utility function to handle different quote ID formats
  const getQuoteIdForApi = (quoteId: string): string | undefined => {
    if (!quoteId || quoteId.trim() === '') {
      return undefined;
    }

    // Check if it's a valid UUID already
    if (/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(quoteId)) {
      return quoteId;
    }

    // Try to find the full UUID from the quotes array based on numeric ID or quote_no
    const foundQuote = quotes.find(q => q.id === quoteId || q.quote_no === quoteId);
    if (foundQuote) {
      return foundQuote.id;
    }

    // If we can't match it, return undefined to skip this field
    return undefined;
  };

  // Function to fetch quotes
  const fetchQuotes = async () => {
    setIsLoading(true);
    try {
      // Implement quote service call to fetch quotes
      const response = await quoteService.getCurrentQuotes({ limit: 100 });

      if (response && response.items) {
        setQuotes(response.items.map((quote: any) => ({
          id: quote.id,
          quote_no: quote.quote_no,
          items: quote.quote_items || []
        })));
        console.log('Successfully loaded quotes:', response.items.length);
      } else {
        console.warn('Quote data was returned in an unexpected format');
        setQuotes([]);
      }
    } catch (error) {
      console.error('Error fetching quotes:', error);
      // Keep quotes empty on error
      setQuotes([]);
    } finally {
      setIsLoading(false);
    }
  };

  // UseEffect to fetch quotes on component mount
  useEffect(() => {
    fetchQuotes();
  }, []);

  // Initialize date range on component mount
  useEffect(() => {
    // Set default date range: 30 days in the past to today (inclusive)
    const today = new Date();
    const pastDate = new Date(today);
    pastDate.setDate(today.getDate() - 30);

    // Format dates in DD-MM-YYYY format for display
    const formattedStart = formatDate(pastDate);
    const formattedEnd = formatDate(today);

    // Set initial date range
    const initialDateRange = `${formattedStart} - ${formattedEnd}`;
    setSelectedDateRange(initialDateRange);

    // Also update filters with these dates
    setFilters({
      ...filters,
      start_date: formatDateForBackend(pastDate),
      end_date: formatDateForBackend(today)
    });
  }, []);

  // Helper function to format date as DD/MM/YYYY
  const formatDate = (date: Date): string => {
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = date.getFullYear();
    return `${day}/${month}/${year}`;
  };

  // Format date as YYYY-MM-DD for backend
  const formatDateForBackend = (date: Date): string => {
    return date.toISOString().split('T')[0];
  };

  // Updated memoizedFilters with stringified value instead of object references
  const memoizedFilters = useMemo(() => {
    // Create a stable key for the current filters
    const filterKey = JSON.stringify(filters);
    return filters;
  }, [JSON.stringify(filters)]);

  // Simplified fetch function for smooth operation
  const fetchInvoices = useCallback(async (page: number, perPage: number, queryFilters: InvoiceFilters) => {
    console.log('Fetching invoices with page:', page, 'perPage:', perPage, 'filters:', queryFilters);

    // Create a fetch key for this specific request configuration
    const fetchKey = `page-${page}-size-${perPage}-filters-${JSON.stringify(queryFilters)}`;

    // Check if there's a search query active
    const hasSearchQuery = queryFilters.search && queryFilters.search.length > 0;

    // For search, use separate loading state to avoid flickering
    if (hasSearchQuery) {
      setIsSearching(true);
    } else {
      // Skip if we already have data for this exact configuration (non-search)
      if (lastFetchKey === fetchKey && dataFetched) {
        console.log(`Data already loaded for configuration ${fetchKey}, skipping fetch`);
        return;
      }
      setIsLoading(true);
    }

    setError(null); // Clear any previous errors

    try {
      // Use the service function to get invoices with store filtering
      const data = await getInvoices(page, perPage, queryFilters, storeFilter);

      if (!data || !data.items) {
        setError("Failed to load invoices: invalid data format");
        setInvoices([]);
        setTotalItems(0);
        return;
      }

      console.log('Raw invoice data:', data.items);

      // Clear error state explicitly on successful data fetch
      setError(null);

      // Check if we have invoice items in the response
      if (data.items.length === 0) {
        setInvoices([]);
        setTotalItems(data.total || 0);
        setDataFetched(true);
        setLastFetchKey(fetchKey);
        return;
      }

      // Map API response to UI model
      const mappedInvoices = data.items.map((invoice: InvoiceResponse) => {
        // Handle grand_total correctly whether it's a string or number
        const total = typeof invoice.grand_total === 'number'
          ? invoice.grand_total.toFixed(2)
          : (parseFloat(invoice.grand_total || '0') || 0).toFixed(2);

        // Get invoice number from nested object or use invoice_no as fallback
        const invoiceNo = invoice.invoice_number?.formatted_number ||
          (typeof invoice.invoice_number === 'object' && 'id' in invoice.invoice_number
            ? invoice.invoice_number.formatted_number
            : invoice.invoice_no || 'No number');

        // Use multiple fields for customerName with fallbacks
        const customerName = invoice.customer_name ||
          invoice.company?.name ||
          invoice.company_name ||
          'Unknown';

        // Use store_type_id or store_type with proper fallbacks
        let storeTypeValue: string = 'Unknown';
        if (invoice.store_type && typeof invoice.store_type === 'object' && invoice.store_type.name) {
          storeTypeValue = invoice.store_type.name;
        } else if (invoice.store_type_name) {
          storeTypeValue = invoice.store_type_name;
        } else if (typeof invoice.store_type === 'string') {
          storeTypeValue = invoice.store_type;
        } else if (invoice.store_type_id !== undefined) {
          // Map store type IDs to names
          if (invoice.store_type_id === 1) {
            storeTypeValue = 'Cranbourne';
          } else if (invoice.store_type_id === 2) {
            storeTypeValue = 'Sale';
          } else {
            storeTypeValue = `Store ${invoice.store_type_id}`;
          }
        }

        return {
          id: invoice.id,
          invoiceNo,  // Use the resolved invoice number
          customerName,  // Use the resolved customer name
          storeType: storeTypeValue,  // Always a string
          date: invoice.date ? format(parseISO(invoice.date), 'dd/MM/yyyy') : 'Unknown',
          total, // This is now safely a string formatted with 2 decimal places
          notes: invoice.notes,
          hasNotes: !!invoice.notes && invoice.notes.trim() !== '',
          status: invoice.status
        };
      });

      setInvoices(mappedInvoices);
      setTotalItems(data.total || mappedInvoices.length);
      setDataFetched(true);
      setLastFetchKey(fetchKey);

    } catch (err) {
      console.error("Failed to fetch invoices:", err);

      // Handle rate limit errors gracefully
      if (err && typeof err === 'object' && 'message' in err) {
        const errorObj = err as { message: string, status?: number };

        if (errorObj.message.includes('debounce') || errorObj.status === 429) {
          console.log('Rate limiting detected, please wait...');
          setError("Loading data, please wait");
          
          // Retry after a short delay for rate limits
          setTimeout(() => {
            fetchInvoices(page, perPage, queryFilters);
          }, 2000);
          return;
        } else {
          setError("Failed to load invoices. " + errorObj.message);
        }
      } else {
        setError("Failed to load invoices. Please try again later.");
      }

      setInvoices([]);
      setTotalItems(0);
    } finally {
      setIsLoading(false);
      setIsSearching(false);
    }
  }, [lastFetchKey, dataFetched, storeFilter]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (searchTimeout) {
        clearTimeout(searchTimeout);
      }
    };
  }, [searchTimeout]);

  // Simplified data fetching effect - only fetch when parameters change
  useEffect(() => {
    // Only fetch data when in LIST view
    if (currentView === PageView.LIST) {
      console.log('🔍 Checking if we should fetch invoice data');
      fetchInvoices(currentPage, itemsPerPage, memoizedFilters);
    }
  }, [currentPage, itemsPerPage, memoizedFilters, currentView, fetchInvoices]);

  // Handle search
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const searchValue = e.target.value;
    setSearchQuery(searchValue);

    // Use debounced search to avoid too many API calls
    debouncedSearch(searchValue);
  };

  // Handle date range changes
  const handleDateRangeChange = (newDateRange: string) => {
    setSelectedDateRange(newDateRange);

    // Parse the date range and update filters
    if (newDateRange) {
      const [startStr, endStr] = newDateRange.split(' - ');

      try {
        // Parse from DD/MM/YYYY format
        const [startDay, startMonth, startYear] = startStr.split('/').map(Number);
        const [endDay, endMonth, endYear] = endStr.split('/').map(Number);

        const startDate = new Date(startYear, startMonth - 1, startDay);
        const endDate = new Date(endYear, endMonth - 1, endDay);

        // Validate date range (30 days)
        const diffTime = Math.abs(endDate.getTime() - startDate.getTime());
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

        if (diffDays > 30) {
          // If range is more than 30 days, adjust start date to be 30 days before end date
          startDate.setDate(endDate.getDate() - 30);
        }

        // Format dates for backend (YYYY-MM-DD)
        const formattedStartDate = formatDateForBackend(startDate);
        const formattedEndDate = formatDateForBackend(endDate);

        console.log(`Setting date range: ${formattedStartDate} to ${formattedEndDate}`);

        setFilters({
          ...filters,
          start_date: formattedStartDate,
          end_date: formattedEndDate
        });
      } catch (err) {
        console.error("Error parsing date range:", err);
      }
    } else {
      // Clear date filters
      const { start_date, end_date, ...restFilters } = filters;
      setFilters(restFilters);
    }
  };

  // Pagination handlers
  const handlePageChange = (pageNumber: number) => setCurrentPage(pageNumber);

  const handleItemsPerPageChange = (newItemsPerPage: number) => {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1); // Reset to first page when changing items per page
  };

  // View handlers
  const handleCreateInvoice = () => {
    // Navigate to the create invoice view using URL parameter
    // This way we avoid direct state manipulation which can lead to race conditions
    navigate('?create=true', { replace: true });
  };

  const handleEditInvoice = (invoiceId: string) => {
    // Navigate to the edit invoice view using URL parameter
    // This way we avoid direct state manipulation which can lead to race conditions
    navigate(`?edit=${invoiceId}`, { replace: true });
  };

  const handleEditInvoiceForm = async (invoiceId: string) => {
    setIsLoading(true);
    try {
      console.log('Loading invoice for edit:', invoiceId);

      // Always try to fetch the full invoice data from the API first
      try {
        console.log('Attempting to fetch full invoice data from API for ID:', invoiceId);
        const invoiceData = await getInvoice(invoiceId);
        console.log("Successfully loaded invoice data from API:", invoiceData);

        // Set the current invoice data
        setCurrentInvoice(invoiceData);
        setSelectedInvoiceId(invoiceId);

        // Navigate to edit view
        navigate(`/invoices?edit=${invoiceId}`, { replace: true });
        setCurrentView(PageView.EDIT);

        console.log('Edit view set up with full API data');
        return;
      } catch (apiError) {
        console.warn('Failed to fetch invoice from API, trying fallback approach:', apiError);

        // Fallback: Check if we're dealing with a numeric ID and use existing data
        const isNumericId = /^\d+$/.test(invoiceId);

        if (isNumericId) {
          console.log('Numeric ID detected, using existing data as fallback');

          // Find the invoice in our already loaded list
          const existingInvoice = invoices.find(inv => inv.id === invoiceId);
          if (existingInvoice) {
            // Create a more complete InvoiceOut object with available data
            const fallbackInvoiceData: InvoiceOut = {
              id: existingInvoice.id,
              store_type: {
                id: 1, // Placeholder ID
                name: existingInvoice.storeType || 'Cranbourne'
              },
              sale_type: SaleTypeEnum.TRADE, // Default, user can change
              mode_of_payment: PaymentModeEnum.CASH, // Default, user can change
              customer_id: '', // Will need to be selected by user
              date: existingInvoice.date || new Date().toISOString().split('T')[0],
              dont_send_po: false,
              shipping: 0,
              notes: existingInvoice.notes || '',
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString(),
              items: [], // Start with empty items, user will need to add
              // Add required fields with better defaults
              total_order: parseFloat(existingInvoice.total?.replace(/[^\d.-]/g, '') || '0') || 0,
              credit_card_surcharge: '0',
              total_gst: '0',
              grand_total: existingInvoice.total || '0',
              // Add invoice number data
              invoice_number: {
                id: existingInvoice.id,
                formatted_number: existingInvoice.invoiceNo || ''
              },
              // Add company info if available
              company: existingInvoice.customerName ? {
                id: '',
                name: existingInvoice.customerName
              } : undefined,
              // Add delivery address placeholder
              deliver_to_address: '',
              // Add purchase order number placeholder
              purchase_order_number: ''
            };

            // Set the current invoice data
            setCurrentInvoice(fallbackInvoiceData);
            setSelectedInvoiceId(invoiceId);

            // Navigate to edit view
            navigate(`/invoices?edit=${invoiceId}`, { replace: true });
            setCurrentView(PageView.EDIT);

            console.log('Edit view set up with fallback data for numeric ID');
            toast.showToast("Note: Some invoice details may need to be re-entered as they couldn't be fully loaded.", { type: 'info' });
            return;
          }
        }

        // If we couldn't find invoice in existing list or it's not numeric
        console.error('Could not load invoice data:', apiError);
        toast.showToast("Could not load this invoice for editing. Please refresh the page and try again.", { type: 'error' });
        return;
      }
    } catch (err) {
      console.error("Failed to fetch invoice details:", err);
      toast.showToast("Failed to load invoice details. Please try again later.", { type: 'error' });
    } finally {
      setIsLoading(false);
    }
  };

  const handleCloseInvoice = () => {
    // Navigate back to the list view
    navigate('/invoices', { replace: true });

    // Reset view state
    setCurrentView(PageView.LIST);
    setSelectedInvoiceId(null);
    setCurrentInvoice(null);

    // Clear any errors
    setError(null);

    // Trigger a fresh data fetch when returning to list view
    setDataFetched(false);
    setLastFetchKey('');
  };

  // Notes modal handlers
  const handleViewNotes = async (invoiceId: string) => {
    setIsLoading(true);
    try {
      // First check if we're dealing with a numeric ID
      const isNumericId = /^\d+$/.test(invoiceId);

      if (isNumericId) {
        // For numeric IDs, don't try to load the full invoice
        // Just use a special case for notes viewing
        console.log('Numeric ID detected, using workaround for notes');

        // Find the invoice in our already loaded list
        const existingInvoice = invoices.find(inv => inv.id === invoiceId);
        if (existingInvoice) {
          // Use the notes we already have
          setNotesValue(existingInvoice.notes || '');
          setNotesInvoiceId(invoiceId);
          setShowNotesModal(true);
          return;
        }

        // If we couldn't find it in existing invoices, show an error
        setError("Cannot load notes for this invoice. Try refreshing the page.");
        return;
      }

      // For UUID IDs, proceed normally
      const invoiceData = await getInvoice(invoiceId);
      setNotesValue(invoiceData?.notes || '');
      setNotesInvoiceId(invoiceId);
      setShowNotesModal(true);
    } catch (err) {
      console.error("Failed to fetch invoice notes:", err);
      setError("Failed to load invoice notes. Please try again later.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleSaveNotes = async () => {
    if (!notesInvoiceId) {
      console.error('No invoice ID provided for saving notes');
      return;
    }

    setIsSavingNotes(true);
    try {
      await updateInvoice(notesInvoiceId, { notes: notesValue });

      // Update the notes in the local state
      setInvoices(invoices.map(inv =>
        inv.id === notesInvoiceId
          ? { ...inv, notes: notesValue, hasNotes: !!notesValue && notesValue.trim() !== '' }
          : inv
      ));

      setShowNotesModal(false);
      toast.showToast("Notes saved successfully!", { type: 'success' });
    } catch (err) {
      console.error("Failed to save notes:", err);
      toast.showToast("Failed to save notes. Please try again.", { type: 'error' });
    } finally {
      setIsSavingNotes(false);
    }
  };

  // API action handlers
  const handleSubmitInvoice = async (data: InvoiceData) => {
    setIsLoading(true);
    try {
      // Check if this is a numeric ID
      const isNumericId = selectedInvoiceId && /^\d+$/.test(selectedInvoiceId);

      // Convert form data to API data
      const apiData: any = {
        store_type_id: typeof data.storeType === 'number' ? data.storeType : undefined,
        store_type: typeof data.storeType === 'string' ? {
          name: data.storeType
        } : undefined,
        sale_type: (data.saleType && data.saleType.toLowerCase() === 'retail')
          ? SaleTypeEnum.RETAIL
          : (data.saleType && data.saleType.toLowerCase() === 'store')
            ? SaleTypeEnum.STORE
            : SaleTypeEnum.TRADE,
        mode_of_payment: (data.modeOfPayment && data.modeOfPayment.toLowerCase() === 'credit card')
          ? PaymentModeEnum.CREDIT_CARD
          : (data.modeOfPayment && data.modeOfPayment.toLowerCase() === 'bank transfer')
            ? PaymentModeEnum.BANK_TRANSFER
            : PaymentModeEnum.CASH,
        purchase_order_number: data.poNumber,
        customer_id: data.invoiceTo ? String(data.invoiceTo) : undefined,
        deliver_to_address: data.deliverToAddress,
        date: data.date,
        dont_send_po: data.dontSendPo,
        notes: data.notes,
        shipping: String(data.totals.shipping || 0),
        total_order: String(data.totals.orderTotal || 0),
        credit_card_surcharge: String(data.totals.surcharge || 0),
        total_gst: String(data.totals.gst || 0),
        grand_total: String(data.totals.grandTotal || 0),
        items: data.items.filter(item => {
          return (
            (item.quantity.units || 0) > 0 ||
            (item.quantity.boxes || 0) > 0 ||
            (item.quantity.pieces || 0) > 0 ||
            (item.quantity.m2 || 0) > 0
          ) && item.sku && item.description;
        }).map(item => ({
          sku: item.sku,
          description: item.description,
          units: item.quantity.units || 0,
          boxes: item.quantity.boxes || 0,
          pieces: item.quantity.pieces || 0,
          m2: item.quantity.m2 || 0,
          unit_price: String(item.unitPrice)
        }))
      };

      // Add quote ID if valid
      const processedQuoteId = getQuoteIdForApi(data.linkedQuote);
      if (processedQuoteId && /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(processedQuoteId)) {
        apiData.linked_quote_id = processedQuoteId;
      }

      // For numeric IDs, we need to create a new invoice rather than update
      if (isNumericId && currentView === PageView.EDIT) {
        console.log("Creating new invoice instead of updating numeric ID invoice");

        // Create a new invoice
        const result = await createInvoice(apiData);
        console.log("New invoice created successfully as replacement for numeric ID invoice:", result);

        // Show success message
        setError("A new invoice has been created with your changes. The original invoice with numeric ID cannot be updated.");
      } else if (currentView === PageView.EDIT && selectedInvoiceId) {
        // This is an edit of an existing invoice with a valid UUID
        console.log(`Updating invoice ${selectedInvoiceId} with data:`, apiData);

        try {
          // Update the invoice using the PUT endpoint
          const response = await fetch(`${API_URL}${ENDPOINTS.INVOICES}/${selectedInvoiceId}`, {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json',
              'Authorization': `Bearer ${localStorage.getItem(AUTH_TOKEN_KEY)}`
            },
            body: JSON.stringify(apiData)
          });

          if (!response.ok) {
            const errorText = await response.text();
            console.error("Direct API call failed:", errorText);

            // Handle specific error cases
            if (response.status === 404) {
              throw new Error(`Invoice with ID ${selectedInvoiceId} was not found. It may have been deleted or the ID is invalid.`);
            } else {
              throw new Error(`Failed to update invoice: ${response.status} ${response.statusText}`);
            }
          }

          const result = await response.json();
          console.log("Invoice updated successfully via direct API call:", result);
        } catch (updateError) {
          console.error("Error updating invoice:", updateError);

          // Try direct API call as fallback
          console.log("Trying direct API call as fallback");
          const token = localStorage.getItem(AUTH_TOKEN_KEY);

          if (!token) {
            throw new Error("Authentication token not found");
          }

          const response = await fetch(`${API_URL}${ENDPOINTS.INVOICES}/${selectedInvoiceId}`, {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json',
              'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify(apiData)
          });

          if (!response.ok) {
            const errorText = await response.text();
            console.error("Direct API call failed:", errorText);
            throw new Error(`Failed to update invoice: ${response.status} ${response.statusText}`);
          }

          const result = await response.json();
          console.log("Invoice updated successfully via direct API call:", result);
        }
      } else if (currentView === PageView.CREATE) {
        // This is a new invoice
        console.log("Creating new invoice with data:", apiData);

        try {
          const response = await fetch(`${API_URL}${ENDPOINTS.INVOICES}`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json',
              'Authorization': `Bearer ${localStorage.getItem(AUTH_TOKEN_KEY)}`
            },
            body: JSON.stringify(apiData)
          });

          if (!response.ok) {
            const errorText = await response.text();
            console.error("Direct API call failed:", errorText);
            throw new Error(`Failed to create invoice: ${response.status} ${response.statusText} - ${errorText}`);
          }

          const result = await response.json();
          console.log("Invoice created successfully via direct API call:", result);
          
          // Show success toast
          toast.showToast('New Invoice Created Successfully', { type: 'success' });
        } catch (createError) {
          console.error("Error creating invoice with service:", createError);

          // Try direct API call as fallback with better error logging
          console.log("Trying direct API call for creation");
          const token = localStorage.getItem(AUTH_TOKEN_KEY);

          if (!token) {
            throw new Error("Authentication token not found");
          }

          console.log("Sending payload:", JSON.stringify(apiData, null, 2));

          const response = await fetch(`${API_URL}${ENDPOINTS.INVOICES}`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json',
              'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify(apiData)
          });

          if (!response.ok) {
            const errorText = await response.text();
            console.error("Direct API call failed:", errorText);
            throw new Error(`Failed to create invoice: ${response.status} ${response.statusText} - ${errorText}`);
          }

          const result = await response.json();
          console.log("Invoice created successfully via direct API call:", result);
        }
      }

      console.log("Invoice submission successful");

      // Reset view and refresh data - with delay to prevent race conditions
      setTimeout(() => {
        // Reset to the first page
        setCurrentPage(1);

        // Clear any existing filters
        setFilters({});
        setSearchQuery('');
        setSelectedDateRange('');

        // Navigate back to list view
        navigate('', { replace: true });

        // Let the URL change effect handle the view transition
      }, 500); // 500ms delay to allow backend processing

    } catch (err) {
      console.error("Failed to save invoice:", err);
      setError(`Failed to ${currentView === PageView.CREATE ? 'create' : 'update'} invoice. Please try again later.`);
      // Show error toast
      toast.showToast(`Failed to ${currentView === PageView.CREATE ? 'create' : 'update'} invoice. Please try again later.`, { type: 'error' });
    } finally {
      setIsLoading(false);
    }
  };

  // Handle save as draft
  const handleSaveAsDraft = async (data: InvoiceData) => {
    // Save as draft should bypass validation
    setIsLoading(true);

    try {
      // Check if this is a numeric ID
      const isNumericId = selectedInvoiceId && /^\d+$/.test(selectedInvoiceId);

      // Prepare invoice data for API
      const invoiceApiData: any = {
        store_type_id: typeof data.storeType === 'number' ? data.storeType : undefined,
        store_type: typeof data.storeType === 'string' ? {
          name: data.storeType || 'draft_type'
        } : undefined,
        // Convert strings to proper enum values
        sale_type: (data.saleType && data.saleType.toLowerCase() === 'retail')
          ? SaleTypeEnum.RETAIL
          : (data.saleType && data.saleType.toLowerCase() === 'store')
            ? SaleTypeEnum.STORE
            : SaleTypeEnum.TRADE,

        // Convert string values to proper enum values
        mode_of_payment: (data.modeOfPayment && data.modeOfPayment.toLowerCase() === 'credit card')
          ? PaymentModeEnum.CREDIT_CARD
          : (data.modeOfPayment && data.modeOfPayment.toLowerCase() === 'bank transfer')
            ? PaymentModeEnum.BANK_TRANSFER
            : PaymentModeEnum.CASH,

        purchase_order_number: data.poNumber || 'Draft',
        // Always use a valid customer ID - even for drafts
        customer_id: data.invoiceTo ? String(data.invoiceTo) : "********-0000-0000-0000-********0001", // Valid dummy UUID
        deliver_to_address: data.deliverToAddress || 'Draft',
        date: data.date || new Date().toISOString().split('T')[0],
        dont_send_po: data.dontSendPo,
        notes: `[DRAFT] ${data.notes || ''}`,
        shipping: String(data.totals.shipping || 0),
        total_order: String(data.totals.orderTotal || 0),
        credit_card_surcharge: String(data.totals.surcharge || 0),
        total_gst: String(data.totals.gst || 0),
        grand_total: String(data.totals.grandTotal || 0),
        items: data.items.length > 0 ? data.items.map(item => ({
          sku: item.sku || 'DRAFT-SKU',
          description: item.description || 'Draft Item',
          units: item.quantity.units || 0,
          boxes: item.quantity.boxes || 0,
          pieces: item.quantity.pieces || 0,
          m2: item.quantity.m2 || 0,
          unit_price: String(item.unitPrice || 0)
        })) : [
          // Include at least one dummy item if no items yet
          {
            sku: 'DRAFT-SKU',
            description: 'Draft Item',
            units: 1,
            boxes: 0,
            pieces: 0,
            m2: 0,
            unit_price: "0"
          }
        ]
      };

      // Add a single valid quote ID if needed
      const processedQuoteId = getQuoteIdForApi(data.linkedQuote);
      if (processedQuoteId && /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(processedQuoteId)) {
        invoiceApiData.linked_quote_id = processedQuoteId;
      }

      console.log('Saving draft invoice with data:', invoiceApiData);

      // For numeric IDs, we can't use the normal API flow for updating
      if (isNumericId && currentView === PageView.EDIT) {
        // Create a new invoice rather than trying to update the numeric ID one
        console.log('Numeric ID detected, creating new invoice instead of updating');
        try {
          const result = await createInvoice(invoiceApiData);
          console.log('New invoice created successfully as a replacement for numeric ID invoice:', result);

          // Show success message
          setError("A new invoice has been created with your changes. The original invoice with numeric ID cannot be updated.");

          // Reset view and refresh list
          setCurrentView(PageView.LIST);
          await fetchInvoices(1, itemsPerPage, {});
          return;
        } catch (error) {
          console.error('Failed to create replacement invoice:', error);
          throw error; // Let the outer catch handle it
        }
      }

      // Try to use standard API
      try {
        if (currentView === PageView.CREATE) {
          const result = await createInvoice(invoiceApiData);
          console.log('Draft saved successfully:', result);
        } else if (selectedInvoiceId && !isNumericId) {
          // Only update if it's not a numeric ID
          const result = await updateInvoice(selectedInvoiceId, invoiceApiData);
          console.log('Draft updated successfully:', result);
        }
      } catch (apiError: any) {
        console.error('Standard API call failed, trying direct API call:', apiError);

        // Fall back to direct API call if the standard functions fail
        const token = localStorage.getItem(AUTH_TOKEN_KEY);

        if (!token) {
          throw new Error("Authentication token not found");
        }

        console.log("Sending draft payload:", JSON.stringify(invoiceApiData, null, 2));

        if (currentView === PageView.CREATE) {
          const response = await fetch(`${API_URL}${ENDPOINTS.INVOICES}`, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json',
              'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify(invoiceApiData)
          });

          if (!response.ok) {
            const errorText = await response.text();
            console.error("Direct API call failed:", errorText);
            throw new Error(`Failed to create draft invoice: ${response.status} ${response.statusText} - ${errorText}`);
          }

          const result = await response.json();
          console.log("Draft created successfully via direct API call:", result);
        } else if (selectedInvoiceId && !isNumericId) {
          // Only update if it's not a numeric ID
          const response = await fetch(`${API_URL}${ENDPOINTS.INVOICES}/${selectedInvoiceId}`, {
            method: 'PUT',
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json',
              'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify(invoiceApiData)
          });

          if (!response.ok) {
            const errorText = await response.text();
            console.error("Direct API call failed:", errorText);
            throw new Error(`Failed to update draft invoice: ${response.status} ${response.statusText} - ${errorText}`);
          }

          const result = await response.json();
          console.log("Draft updated successfully via direct API call:", result);
        }
      }

      // Show success indication
      console.log('Draft saved successfully');

      // Add delay to prevent race conditions with navigation and immediate fetching
      setTimeout(() => {
        // Navigate back to list view
        navigate('', { replace: true });
      }, 500); // 500ms delay to allow backend processing

    } catch (err) {
      console.error("Failed to save draft:", err);
      setError("Failed to save draft. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  // Helper function to convert API invoice to UI model
  const getEditInvoiceData = (): InvoiceData | undefined => {
    if (!currentInvoice) return undefined;

    console.log('Converting invoice data for edit form:', currentInvoice);

    // Helper function to safely convert string or number to number
    const safeNumberConversion = (value: string | number | undefined): number => {
      if (value === undefined || value === null) return 0;
      if (typeof value === 'number') return value;
      if (typeof value === 'string') {
        // Remove currency symbols and non-numeric characters except decimal point and minus
        const cleanValue = value.replace(/[^\d.-]/g, '');
        return parseFloat(cleanValue) || 0;
      }
      return 0;
    };

    // Determine the sale type with better fallback handling
    let saleTypeDisplay = 'Trade'; // Default
    const saleType = String(currentInvoice.sale_type || '').toLowerCase();
    if (saleType === 'retail' || currentInvoice.sale_type === SaleTypeEnum.RETAIL) {
      saleTypeDisplay = 'Retail';
    } else if (saleType === 'trade' || currentInvoice.sale_type === SaleTypeEnum.TRADE) {
      saleTypeDisplay = 'Trade';
    } else if (saleType === 'store' || currentInvoice.sale_type === SaleTypeEnum.STORE) {
      saleTypeDisplay = 'Store';
    }

    // Determine the payment mode with better fallback handling
    let paymentModeDisplay = 'Cash'; // Default
    const modeOfPayment = String(currentInvoice.mode_of_payment || '').toLowerCase();
    if (modeOfPayment === 'cash' || currentInvoice.mode_of_payment === PaymentModeEnum.CASH) {
      paymentModeDisplay = 'Cash';
    } else if (modeOfPayment === 'credit_card' || modeOfPayment === 'credit card' || currentInvoice.mode_of_payment === PaymentModeEnum.CREDIT_CARD) {
      paymentModeDisplay = 'Credit Card';
    } else if (modeOfPayment === 'bank_transfer' || modeOfPayment === 'bank transfer' || currentInvoice.mode_of_payment === PaymentModeEnum.BANK_TRANSFER) {
      paymentModeDisplay = 'Bank Transfer';
    }

    // Extract customer information - try multiple possible sources
    let customerInfo = '';
    if (currentInvoice.customer_id) {
      customerInfo = currentInvoice.customer_id;
    } else if (currentInvoice.company?.name) {
      customerInfo = currentInvoice.company.name;
    } else if (currentInvoice.company?.id) {
      customerInfo = currentInvoice.company.id;
    }

    // Make sure required fields have default values to prevent errors
    const formData: InvoiceData = {
      storeType: currentInvoice.store_type?.name || 'Cranbourne',
      saleType: saleTypeDisplay,
      modeOfPayment: paymentModeDisplay,
      poNumber: currentInvoice.purchase_order_number || '',
      invoiceTo: customerInfo,
      deliverToAddress: currentInvoice.deliver_to_address || '',
      invoiceNo: currentInvoice.invoice_number?.formatted_number || currentInvoice.invoice_no || '',
      notes: currentInvoice.notes || '',
      linkedQuote: currentInvoice.linked_quote_id || '',
      date: currentInvoice.date || new Date().toISOString().split('T')[0],
      dontSendPo: !!currentInvoice.dont_send_po,
      items: (currentInvoice.items || []).map((item, index) => ({
        id: item.id || String(Math.random()),
        sku: item.sku || '',
        description: item.description || '',
        quantity: {
          units: safeNumberConversion(item.units),
          boxes: safeNumberConversion(item.boxes),
          pieces: safeNumberConversion(item.pieces),
          m2: safeNumberConversion(item.m2),
          total: safeNumberConversion(item.units) +
            safeNumberConversion(item.boxes) +
            safeNumberConversion(item.pieces) +
            safeNumberConversion(item.m2)
        },
        unitPrice: safeNumberConversion(item.unit_price),
        totalPrice: safeNumberConversion(item.total_price)
      })),
      totals: {
        orderTotal: safeNumberConversion(currentInvoice.total_order),
        surcharge: safeNumberConversion(currentInvoice.credit_card_surcharge),
        gst: safeNumberConversion(currentInvoice.total_gst),
        shipping: safeNumberConversion(currentInvoice.shipping),
        grandTotal: safeNumberConversion(currentInvoice.grand_total)
      }
    };

    console.log('Converted form data for edit:', formData);
    return formData;
  };

  // Print invoice handler - updated to open InvoiceView modal
  const handlePrintInvoice = async (invoiceId: string) => {
    console.log('Opening invoice view modal for print, ID:', invoiceId);

    // Close any existing modal first to ensure clean state
    if (isViewInvoiceOpen) {
      setIsViewInvoiceOpen(false);
      setViewInvoiceId(null);

      // Small delay to ensure the previous modal is fully unmounted
      setTimeout(() => {
        setViewInvoiceId(invoiceId);
        setIsViewInvoiceOpen(true);
        console.log('Invoice view modal opened for print with ID:', invoiceId);
      }, 100);
    } else {
      setViewInvoiceId(invoiceId);
      setIsViewInvoiceOpen(true);
      console.log('Invoice view modal opened for print with ID:', invoiceId);
    }
  };

  // Function to close the invoice view modal
  const handleCloseInvoiceView = () => {
    console.log('Closing invoice view modal');
    setIsViewInvoiceOpen(false);
    setViewInvoiceId(null);
    console.log('Invoice view modal closed');
  };

  // Email invoice handler - updated with better error handling
  const handleEmailInvoice = async (invoiceId: string) => {
    setIsLoading(true);
    try {
      // Check if we're dealing with a numeric ID
      const isNumericId = /^\d+$/.test(invoiceId);

      if (isNumericId) {
        console.log('Numeric ID detected, cannot email invoice with numeric ID');
        toast.showToast("Error while Emailing invoice", { type: 'error' });
        return;
      }

      // For UUID IDs, proceed normally
      await emailInvoice(invoiceId);
      console.log("Invoice has been emailed successfully:", invoiceId);
      toast.showToast("Invoice has been emailed successfully!", { type: 'success' });
    } catch (err) {
      console.error("Failed to email invoice:", err);
      toast.showToast("Failed to send email. Please try again later.", { type: 'error' });
    } finally {
      setIsLoading(false);
    }
  };

  const handleBack = () => {
    // Navigate back to the home page
    navigate('/home');
  };

  // Initialize view state based on URL parameters
  useEffect(() => {
    const urlParams = new URLSearchParams(location.search);
    
    if (urlParams.get('create') === 'true') {
      setCurrentView(PageView.CREATE);
    } else if (urlParams.get('edit')) {
      const editId = urlParams.get('edit');
      if (editId) {
        setEditingInvoiceId(editId);
        setCurrentView(PageView.EDIT);
      }
    } else {
      setCurrentView(PageView.LIST);
    }
  }, [location.search]);

  // Render the list view
  const renderListView = () => {
    return (
      <div style={{ marginTop: '-1px' }}>
        <div style={{ display: 'flex', alignItems: 'center', marginBottom: '0px', padding: '8px 32px' }}>
          <StyledBackButton onClick={handleBack}>
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M19 12H5M12 19l-7-7 7-7" />
            </svg>
          </StyledBackButton>
          <PageTitle style={{ marginLeft: '12px', margin: '0' }}>Invoices</PageTitle>
        </div>
        <PageContainer>
          <HeaderContainer>
            <div></div>
          </HeaderContainer>

          <ControlsContainer>
            <SearchContainer>
              <SearchIconWrapper>
                <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <circle cx="11" cy="11" r="8"></circle>
                  <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
                </svg>
              </SearchIconWrapper>
              <SearchInput
                type="text"
                placeholder="Search a Company"
                value={searchQuery}
                onChange={handleSearchChange}
              />
            </SearchContainer>

            <DatePickerWrapper>
              <DateRangePicker
                value={selectedDateRange}
                onChange={handleDateRangeChange}
              />
            </DatePickerWrapper>

            <CreateButton onClick={handleCreateInvoice}>
              <img src={invoicesCreateIcon} alt="Create" />
              Create Invoice
            </CreateButton>
          </ControlsContainer>

          {/* Display error above the table if there is one and it's not a loading message */}
          {error && !error.includes("Loading data, please wait") && !isLoading && !isSearching && (
            <div style={{ marginBottom: '1rem', padding: '1rem', backgroundColor: '#FEF2F2', color: '#B91C1C', borderRadius: '0.5rem' }}>
              {error}
            </div>
          )}

          {/* Show search indicator when searching */}
          {isSearching && (
            <div style={{ marginBottom: '1rem', padding: '1rem', backgroundColor: '#EFF6FF', color: '#1E40AF', borderRadius: '0.5rem' }}>
              <div style={{ display: 'flex', alignItems: 'center', gap: '0.5rem' }}>
                <div style={{ 
                  width: '16px', 
                  height: '16px', 
                  border: '2px solid #1E40AF', 
                  borderTop: '2px solid transparent', 
                  borderRadius: '50%', 
                  animation: 'spin 1s linear infinite' 
                }}></div>
                Searching invoices...
              </div>
            </div>
          )}

          <Table>
            <TableHeader>
              <tr>
                <th style={{ width: '140px', fontSize: '14px', fontWeight: 'bold', color: '#042b41' }}>
                  Invoice No
                </th>
                <th style={{ width: '200px', fontSize: '14px', fontWeight: 'bold', color: '#042b41' }}>
                  Company Name
                </th>
                <th style={{ width: '120px', fontSize: '14px', fontWeight: 'bold', color: '#042b41' }}>
                  Store Type
                </th>
                <th style={{ width: '100px', fontSize: '14px', fontWeight: 'bold', color: '#042b41' }}>
                  Date
                </th>
                <th style={{ width: '120px', fontSize: '14px', fontWeight: 'bold', color: '#042b41', textAlign: 'right' }}>
                  Grand Total (AUD)
                </th>
                <th style={{ width: '80px', fontSize: '14px', fontWeight: 'bold', color: '#042b41' }}>
                  Status
                </th>
                <th style={{ width: '60px', fontSize: '14px', fontWeight: 'bold', color: '#042b41' }}>
                  Notes
                </th>
                <th style={{ width: '140px', fontSize: '14px', fontWeight: 'bold', color: '#042b41' }}>
                  Actions
                </th>
              </tr>
            </TableHeader>
            <TableBody>
              {renderTableContent()}
            </TableBody>
          </Table>

          {!isLoading && invoices.length > 0 && (
            <PaginationContainer>
              <Pagination
                currentPage={currentPage}
                totalPages={Math.ceil(totalItems / itemsPerPage)}
                onPageChange={handlePageChange}
                totalItems={totalItems}
                itemsPerPage={itemsPerPage}
                onItemsPerPageChange={handleItemsPerPageChange}
                itemsPerPageOptions={[10, 20, 50]}
                showItemsPerPage={true}
              />
            </PaginationContainer>
          )}

          {showNotesModal && (
            <NotesModalOverlay>
              <NotesModalContent>
                <NotesModalTitle>Invoice Notes</NotesModalTitle>
                <NotesTextarea
                  value={notesValue}
                  onChange={(e) => setNotesValue(e.target.value)}
                  placeholder="Enter notes for this invoice"
                />
                <NotesButtonContainer>
                  <CloseNotesButton onClick={() => setShowNotesModal(false)}>
                    Cancel
                  </CloseNotesButton>
                  <SaveNotesButton onClick={handleSaveNotes}>
                    {isSavingNotes ? 'Saving' : 'Save Notes'}
                  </SaveNotesButton>
                </NotesButtonContainer>
              </NotesModalContent>
            </NotesModalOverlay>
          )}
        </PageContainer>
      </div>
    );
  };

  // Remove the renderSkeletonRows function and replace with consistent loading
  const renderTableContent = () => {
    // Show loading state for initial data load
    if (isLoading && !isSearching) {
      return (
        <LoadingRow>
          <td colSpan={8}>
            <LoadingContainer>
              <LoadingSpinner size="lg" />
              <LoadingText>Loading invoices</LoadingText>
            </LoadingContainer>
          </td>
        </LoadingRow>
      );
    }

    // Show minimal content during search to avoid flickering
    if (isSearching) {
      return (
        <tr>
          <td colSpan={8} style={{ textAlign: 'center', padding: '20px' }}>
            <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', gap: '0.5rem' }}>
              <div style={{ 
                width: '16px', 
                height: '16px', 
                border: '2px solid #1E40AF', 
                borderTop: '2px solid transparent', 
                borderRadius: '50%', 
                animation: 'spin 1s linear infinite' 
              }}></div>
              Searching...
            </div>
          </td>
        </tr>
      );
    }

    // Show no results message
    if (invoices.length === 0) {
      const hasActiveSearch = searchQuery && searchQuery.trim().length > 0;
      return (
        <tr>
          <td colSpan={8} style={{ textAlign: 'center', padding: '20px' }}>
            {hasActiveSearch ? 
              `No invoices found matching "${searchQuery}"` : 
              'No invoices found.'
            }
          </td>
        </tr>
      );
    }

    return invoices.map(invoice => (
      <tr key={invoice.id}>
        <td>{invoice.invoiceNo}</td>
        <td>{invoice.customerName}</td>
        <td>{invoice.storeType}</td>
        <td>{invoice.date}</td>
        <td style={{ textAlign: 'right' }}>{invoice.total}</td>
        <td>
          <StatusBadge status={invoice.status || 'saved'}>
            {invoice.status === 'draft' ? 'DRAFTED' : 'SAVED'}
          </StatusBadge>
        </td>
        <NotesColumn
          hasNotes={invoice.hasNotes}
          onClick={() => handleViewNotes(invoice.id)}
        >
          <img src={invoicesNotesIcon} alt="Notes" />
        </NotesColumn>
        <td>
          <ActionButtonsContainer>
            <ActionButton onClick={() => handleEditInvoice(invoice.id)} title="Edit">
              <img src={invoicesEditIcon} alt="Edit" />
            </ActionButton>
            <ActionButton onClick={() => handlePrintInvoice(invoice.id)} title="Print">
              <img src={invoicesPrintIcon} alt="Print" />
            </ActionButton>
            <ActionButton onClick={() => handleEmailInvoice(invoice.id)} title="Email">
              <img src={invoicesMailIcon} alt="Email" />
            </ActionButton>
          </ActionButtonsContainer>
        </td>
      </tr>
    ));
  };

  return (
    <Layout>
      {/* Toast notification */}
      {toast.isVisible && (
        <Toast
          message={toast.message}
          type={toast.type}
          onClose={() => { }}
        />
      )}

      {currentView === PageView.LIST && renderListView()}
      {currentView === PageView.EDIT && (
        <>
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: '0px', padding: '8px 32px' }}>
            <StyledBackButton onClick={handleCloseInvoice}>
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M19 12H5M12 19l-7-7 7-7" />
              </svg>
            </StyledBackButton>
            <BreadcrumbContainer style={{ marginLeft: '12px', margin: '0' }}>
              <BreadcrumbItem>
                <span
                  style={{ color: '#042B41', textDecoration: 'none', cursor: 'pointer' }}
                  onClick={handleCloseInvoice}
                >
                  Invoice
                </span>
              </BreadcrumbItem>
              <BreadcrumbSeparator>&gt;</BreadcrumbSeparator>
              <BreadcrumbItem>Edit Invoice</BreadcrumbItem>
            </BreadcrumbContainer>
          </div>
          <div style={{ padding: '0 32px' }}>
            <EditInvoiceForm
              initialData={getEditInvoiceData()}
              onSubmit={handleSubmitInvoice}
              onSaveAsDraft={handleSaveAsDraft}
              onCancel={handleCloseInvoice}
            />
          </div>
        </>
      )}
      {currentView === PageView.CREATE && (
        <>
          <div style={{ display: 'flex', alignItems: 'center', marginBottom: '0px', padding: '8px 32px' }}>
            <StyledBackButton onClick={handleCloseInvoice}>
              <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <path d="M19 12H5M12 19l-7-7 7-7" />
              </svg>
            </StyledBackButton>
            <BreadcrumbContainer style={{ marginLeft: '12px', margin: '0' }}>
              <BreadcrumbItem>
                <span
                  style={{ color: '#042B41', textDecoration: 'none', cursor: 'pointer' }}
                  onClick={handleCloseInvoice}
                >
                  Invoice
                </span>
              </BreadcrumbItem>
              <BreadcrumbSeparator>&gt;</BreadcrumbSeparator>
              <BreadcrumbItem>Create New Invoice</BreadcrumbItem>
            </BreadcrumbContainer>
          </div>
          <div style={{ padding: '0 32px' }}>
            <EditInvoiceForm
              onSubmit={handleSubmitInvoice}
              onSaveAsDraft={handleSaveAsDraft}
              onCancel={handleCloseInvoice}
            />
          </div>
        </>
      )}

      {/* Invoice View Modal for Print */}
      {isViewInvoiceOpen && viewInvoiceId && (
        <InvoiceView
          invoiceId={viewInvoiceId}
          onClose={handleCloseInvoiceView}
          printMode={true}
        />
      )}
    </Layout>
  );
};

export default InvoicesPage;