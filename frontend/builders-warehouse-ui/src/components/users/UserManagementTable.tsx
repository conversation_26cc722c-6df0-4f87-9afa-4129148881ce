import React, { useState } from 'react';
import Table from '../common/Table';
import SearchBar from '../common/SearchBar';
import Button from '../common/Button';

type UserRole = 'admin' | 'manager' | 'sales' | 'warehouse' | 'accounting';

interface User {
  id: string;
  name: string;
  email: string;
  role: UserRole;
  department: string;
  isActive: boolean;
  lastLogin: string;
}

interface UserManagementTableProps {
  users: User[];
  onEditUser: (userId: string) => void;
  onToggleUserStatus: (userId: string, isActive: boolean) => void;
  onResetPassword: (userId: string) => void;
}

const UserManagementTable: React.FC<UserManagementTableProps> = ({
  users,
  onEditUser,
  onToggleUserStatus,
  onResetPassword,
}) => {
  const [searchTerm, setSearchTerm] = useState('');

  const filteredUsers = users.filter(
    (user) =>
      user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.role.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.department.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getRoleBadgeClass = (role: UserRole) => {
    switch (role) {
      case 'admin':
        return 'bg-red-100 text-red-800';
      case 'manager':
        return 'bg-blue-100 text-blue-800';
      case 'sales':
        return 'bg-green-100 text-green-800';
      case 'warehouse':
        return 'bg-yellow-100 text-yellow-800';
      case 'accounting':
        return 'bg-purple-100 text-purple-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const capitalizeFirstLetter = (string: string) => {
    return string.charAt(0).toUpperCase() + string.slice(1);
  };

  const columns = [
    { header: 'Name', accessor: 'name' as keyof User },
    { header: 'Email', accessor: 'email' as keyof User },
    {
      header: 'Role',
      accessor: 'role' as keyof User,
      cell: (value: UserRole) => (
        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getRoleBadgeClass(value)}`}>
          {capitalizeFirstLetter(value)}
        </span>
      ),
    },
    { header: 'Department', accessor: 'department' as keyof User },
    {
      header: 'Status',
      accessor: 'isActive' as keyof User,
      cell: (value: boolean) => (
        <span
          className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
            value ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
          }`}
        >
          {value ? 'Active' : 'Inactive'}
        </span>
      ),
    },
    { header: 'Last Login', accessor: 'lastLogin' as keyof User },
    {
      header: 'Actions',
      accessor: 'id' as keyof User,
      cell: (value: string, row: User) => (
        <div className="flex space-x-2">
          <Button onClick={() => onEditUser(value)} variant="primary" size="sm">
            Edit
          </Button>
          <Button
            onClick={() => onToggleUserStatus(value, !row.isActive)}
            variant={row.isActive ? 'danger' : 'success'}
            size="sm"
          >
            {row.isActive ? 'Deactivate' : 'Activate'}
          </Button>
          <Button onClick={() => onResetPassword(value)} variant="secondary" size="sm">
            Reset Password
          </Button>
        </div>
      ),
    },
  ];

  return (
    <div className="flex flex-col">
      <div className="mb-4">
        <SearchBar
          value={searchTerm}
          onChange={setSearchTerm}
          placeholder="Search by name, email, role, or department"
        />
      </div>
      <Table
        data={filteredUsers}
        columns={columns}
        emptyMessage="No users found. Add new users to get started."
      />
    </div>
  );
};

export default UserManagementTable; 