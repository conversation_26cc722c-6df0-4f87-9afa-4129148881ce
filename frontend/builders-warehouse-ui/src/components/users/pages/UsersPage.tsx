import React, { useState, useEffect, ReactElement, useRef } from 'react';
import styled from 'styled-components';
import { useNavigate } from 'react-router-dom';
import Layout from '../../layout/Layout';
import Pagination from '../../common/Pagination';
import {
  UserDisplay,
  UserResponse,
  ManagerInfo,
  mapApiUserToDisplayUser,
  getManagers,
  mapDisplayUserToApiUpdate,
  mapDisplayUserToApiCreate,
  testMapApiUsers,
  deleteUser
} from '../../../services/users';
import { useAuth } from '../../../context/AuthContext';
import apiClient from '../../../services/apiClient';
import { isAdmin } from '../../../utils/roleUtils';
import roleService, { Role } from '../../../services/roleService';
import { ENDPOINTS, API_URL } from '../../../config';
import storeTypeService, { StoreType } from '../../../services/storeTypeService';
import {
  PageContainer,
  PageHeaderComponent,
  SearchContainer,
  SearchField,
  AddButtonComponent,
  BackButtonComponent,
  SearchInputComponent
} from '../../ui/DesignSystem';
import UserManagementTable from '../UserManagementTable';
import { getUsers } from '../../../services/users';
import UserEditIcon from '../../../assets/UserEditIcon.png';
import userDeleteIcon from '../../../assets/userDeleteIcon.png';
import PageLoadingSpinner from '../../ui/PageLoadingSpinner';
import LoadingSpinner from '../../ui/LoadingSpinner';
import { useToast } from '../../../hooks/useToast';
import { Toast } from '../../common/Toast';

const GridContainer = styled.div`
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  border: 1px solid #e0e0e0;
`;

const GridHeader = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr 1.5fr 0.8fr 1fr 1fr 0.7fr;
  background-color: #042B41;
  color: white;

  div {
    padding: 10px 12px;
    font-weight: 500;
    font-size: 14px;
    height: 48px;
    display: flex;
    align-items: center;
    vertical-align: middle;
  }
`;

const GridRow = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr 1.5fr 0.8fr 1fr 1fr 0.7fr;
  border-bottom: 1px solid #e0e0e0;
  transition: background-color 0.2s;
  height: 48px;

  &:last-child {
    border-bottom: none;
  }

  &:hover {
    background-color: #f9fafb;
  }

  div {
    padding: 10px 12px;
    color: #333;
    display: flex;
    align-items: center;
    border-right: 1px solid #e0e0e0;
    overflow: hidden;
    word-break: break-word;
    overflow-wrap: break-word;
    line-height: 1.4;
    font-size: 14px;
    height: 48px;
    box-sizing: border-box;

    &:last-child {
      border-right: none;
    }
  }
`;

const ActionButtons = styled.div`
  display: flex;
  gap: 6px;

  button {
    background: none;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    padding: 3px;
    transition: all 0.2s;

    img {
      width: 18px;
      height: 18px;
    }

    &.edit {
      color: #042B41;

      &:hover {
        background-color: #f0f8ff;
        border-color: #c0d5e8;
        transform: translateY(-1px);
      }
    }

    &.delete {
      color: #dc3545;

      &:hover {
        background-color: #fff5f5;
        border-color: #ffccd1;
        transform: translateY(-1px);
      }
    }

    &:disabled {
      color: #ccc;
      cursor: not-allowed;
      transform: none;
    }
  }
`;

const ModalOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  animation: fadeIn 0.2s ease-out;

  @keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
  }
`;

const ModalContent = styled.div`
  background-color: white;
  border-radius: 10px;
  width: 850px;
  max-width: 90%;
  padding: 2.5rem;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
  animation: slideUp 0.3s ease-out;

  @keyframes slideUp {
    from { transform: translateY(20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
  }
`;

const ModalTitle = styled.h2`
  font-size: 1.75rem;
  margin: 0 0 1.75rem 0;
  color: #333;
  font-weight: 600;
`;

const FormGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1.75rem;
`;

const FormGroup = styled.div`
  margin-bottom: 1.75rem;
`;

const FormLabel = styled.label`
  display: block;
  margin-bottom: 0.6rem;
  font-weight: 500;
  color: #333;

  .required {
    color: #dc3545;
    margin-left: 2px;
  }
`;

const FormInput = styled.input`
  width: 100%;
  padding: 0.85rem;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  font-size: 0.95rem;
  transition: all 0.2s;

  &:focus {
    outline: none;
    border-color: #042B41;
    box-shadow: 0 0 0 2px rgba(4, 43, 65, 0.1);
  }

  &:disabled {
    background-color: #f9f9f9;
    cursor: not-allowed;
  }
`;

const FormSelect = styled.select`
  width: 100%;
  padding: 0.85rem;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  font-size: 0.95rem;
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 24 24' fill='none' stroke='%23333' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpolyline points='6 9 12 15 18 9'%3E%3C/polyline%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 0.85rem center;
  background-size: 12px;
  transition: all 0.2s;

  &:focus {
    outline: none;
    border-color: #042B41;
    box-shadow: 0 0 0 2px rgba(4, 43, 65, 0.1);
  }

  &:disabled {
    background-color: #f9f9f9;
    cursor: not-allowed;
  }
`;

const ButtonGroup = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 2rem;
`;

const CancelButton = styled.button`
  background-color: white;
  color: #333;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  padding: 0.85rem 1.75rem;
  cursor: pointer;
  font-size: 0.95rem;
  font-weight: 500;
  transition: all 0.2s;

  &:hover {
    background-color: #f5f5f5;
    border-color: #d0d0d0;
  }

  &:disabled {
    background-color: #f9f9f9;
    color: #999;
    cursor: not-allowed;
  }
`;

const SubmitButton = styled.button`
  background-color: #042B41;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 0.85rem 1.75rem;
  cursor: pointer;
  font-size: 0.95rem;
  font-weight: 500;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 130px;

  svg {
    margin-right: 0.5rem;
  }

  &:hover {
    background-color: #031f30;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  }

  &:active {
    transform: translateY(0);
  }

  &:disabled {
    background-color: #ccc;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
  }
`;

const ErrorMessage = styled.div`
  color: #dc3545;
  background-color: #fff5f5;
  border: 1px solid #ffccd1;
  border-radius: 6px;
  padding: 0.85rem 1rem;
  margin-bottom: 1.5rem;
  display: flex;
  align-items: flex-start;
  white-space: pre-line;
  line-height: 1.4;
  font-size: 0.9rem;

  svg {
    margin-right: 0.75rem;
    min-width: 16px;
    margin-top: 0.1rem;
    flex-shrink: 0;
  }

  /* Ensure error text wraps properly and maintains readability */
  & > span {
    flex: 1;
  }
`;

const FieldErrorMessage = styled.div`
  color: #dc3545;
  font-size: 0.8rem;
  margin-top: 0.25rem;
  line-height: 1.3;
  
  /* Style each error as a separate line */
  & > div {
    margin-bottom: 0.15rem;
  }
  
  & > div:last-child {
    margin-bottom: 0;
  }
`;

// Add interface for form data
interface UserFormData {
  name: string;
  email: string;
  password: string;
  role: string;
  status: string;
  mobile: string;
  storeTypeId: number | null;
  managerId: string | null;
}

// Add interface for Manager
interface Manager {
  id: string;
  user_name: string;
}

// Add an enhanced manager interface that includes store type
interface EnhancedManager extends Manager {
  store_type_id?: number;
  store_type?: {
    id: number;
    name: string;
  };
}

// Simplified - direct string role rather than object with ID
interface RoleData {
  value: string;
  label: string;
}

// Add skeleton loader and empty state components
const LoadingRow = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  grid-column: 1 / -1;
`;

const LoadingContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 12px;
`;

const LoadingText = styled.p`
  margin: 0;
  font-size: 14px;
  color: #6B7280;
  font-weight: 500;
`;

// Add this styled component before the UsersPage component definition
const ActionButton = styled.button`
  background: none;
  border: none;
  color: #6B7280;
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  
  img {
    width: 22px;
    height: 22px;
  }
  
  &:hover {
    background-color: #F3F4F6;
    color: #042B41;
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

// Interface for API error responses
interface ApiError {
  response?: {
    status: number;
    headers: any;
    data: any;
  };
  message?: string;
  code?: string;
}

// Add AddButton styled component
const AddButton = styled.button`
  display: flex;
  align-items: center;
  gap: 10px;
  background-color: #042B41;
  color: white;
  border: none;
  border-radius: 10px;
  padding: 12px 20px;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  
  &:hover {
    background-color: #0A3D5A;
  }
  
  svg {
    width: 18px;
    height: 18px;
    stroke: currentColor;
  }
`;

// Completely redefine the layout components for proper alignment
const HeaderSection = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  margin-top: 10px;
`;

// Add a wrapper for the back button with proper margin
const BackButtonWrapper = styled.div`
  margin-right: 12px;
`;

// Custom header with proper alignment
const CustomHeader = styled.div`
  display: flex;
  align-items: center;
  gap: 16px;
  margin-bottom: 24px;
  margin-top: 10px;
`;

const PageTitle = styled.h1`
  font-size: 28px;
  font-weight: 700;
  margin: 0;
  padding: 0;
  color: #333;
`;

// Create a single container for search and actions that's properly aligned with the table
const SearchActionContainer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  width: 100%;
  gap: 16px;
`;

// Create a styled wrapper for the SearchInputComponent
const StyledSearchInput = styled.div`
  flex: 1;
  max-width: 500px;
  
  /* Make sure the input inside takes full width */
  input {
    width: 100%;
  }
`;

const EmptyStateContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 1rem;
  text-align: center;
  grid-column: 1 / -1;
`;

const EmptyStateIcon = styled.div`
  width: 60px;
  height: 60px;
  background-color: #f3f4f6;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
  
  svg {
    width: 30px;
    height: 30px;
    color: #9ca3af;
  }
`;

const EmptyStateTitle = styled.h3`
  font-size: 1.2rem;
  color: #111827;
  margin-bottom: 0.5rem;
`;

const EmptyStateDescription = styled.p`
  font-size: 0.9rem;
  color: #6b7280;
  max-width: 300px;
  margin-bottom: 1.5rem;
`;

const EmptyStateButton = styled.button`
  background-color: #042B41;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 0.6rem 1.2rem;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  
  &:hover {
    background-color: #0A3D5A;
  }
`;

const UsersPage: React.FC = () => {
  const navigate = useNavigate();
  const { isAuthenticated, user: currentAuthUser } = useAuth();
  const toast = useToast();
  const [currentPage, setCurrentPage] = useState(1);
  const [searchQuery, setSearchQuery] = useState('');
  const [debouncedSearchQuery, setDebouncedSearchQuery] = useState('');
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [users, setUsers] = useState<UserDisplay[]>([]);
  const [totalUsers, setTotalUsers] = useState<number>(0);
  const [managers, setManagers] = useState<EnhancedManager[]>([]);
  const [storeTypes, setStoreTypes] = useState<StoreType[]>([]);
  const [currentUser, setCurrentUser] = useState<UserDisplay | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  // Replace hardcoded roles with state that will be populated from API
  const [roles, setRoles] = useState<RoleData[]>([
    { value: 'admin', label: 'Admin' },
    { value: 'manager', label: 'Manager' },
    { value: 'staff', label: 'Staff' }
  ]);

  // Add dataLoaded flag to track if data was ever successfully loaded
  const [dataLoaded, setDataLoaded] = useState(false);
  const [fetchingInProgress, setFetchingInProgress] = useState(false);
  const [loadingManagersAndStoreTypes, setLoadingManagersAndStoreTypes] = useState(false);

  // Add ref for the search input to maintain focus
  const searchInputRef = useRef<HTMLInputElement>(null);

  // Check if current user has admin role using the utility function
  const userIsAdmin = isAdmin(currentAuthUser);

  // Individual field error states for inline validation
  const [fieldErrors, setFieldErrors] = useState<{
    password: string[];
    email: string[];
    mobile: string[];
  }>({
    password: [],
    email: [],
    mobile: []
  });

  // State to track original form data and changes for Edit User modal
  const [originalFormData, setOriginalFormData] = useState<UserFormData | null>(null);
  const [hasFormChanges, setHasFormChanges] = useState(false);

  // Debounce search query to prevent excessive API calls and focus loss
  useEffect(() => {
    const timer = setTimeout(() => {
      setDebouncedSearchQuery(searchQuery);
    }, 500); // 500ms delay

    return () => clearTimeout(timer);
  }, [searchQuery]);

  // Reset to first page when debounced search query changes
  useEffect(() => {
    if (dataLoaded) {
      setCurrentPage(1);
    }
  }, [debouncedSearchQuery, dataLoaded]);


  // Form state
  const [formData, setFormData] = useState<UserFormData>({
    name: '',
    email: '',
    password: '',
    role: '',
    status: 'active',
    mobile: '',
    storeTypeId: null,
    managerId: null
  });


  // Load managers and store types
  const loadManagersAndStoreTypes = async () => {
    setLoadingManagersAndStoreTypes(true);
    try {
      console.log('Loading managers and store types...');

      // Load managers
      const managersData = await getManagers();
      console.log('Loaded managers:', managersData);
      setManagers(managersData);

      // Load store types
      const storeTypesData = await storeTypeService.getStoreTypes();
      console.log('Loaded store types:', storeTypesData);
      setStoreTypes(storeTypesData);

    } catch (error) {
      console.error('Error loading managers and store types:', error);
      setError('Failed to load managers and store types. Please try again.');
    } finally {
      setLoadingManagersAndStoreTypes(false);
    }
  };

  // Fetch users from API
  const fetchUsers = async () => {
    // If we're already loading, don't trigger another load
    if (isLoading) {
      return;
    }

    setIsLoading(true);
    setError(null); // Clear previous errors

    try {
      // Calculate skip from currentPage and itemsPerPage
      const skip = (currentPage - 1) * itemsPerPage;

      // Build query parameters
      const queryParams = new URLSearchParams();
      queryParams.append('skip', skip.toString());
      queryParams.append('limit', itemsPerPage.toString());
      if (debouncedSearchQuery) {
        queryParams.append('search', debouncedSearchQuery);
      }

      // Direct API call to ensure we get the correct response format
      const token = localStorage.getItem('authToken');
      const response = await fetch(`${API_URL}${ENDPOINTS.USERS}?${queryParams.toString()}`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Authorization': token ? `Bearer ${token}` : ''
        }
      });

      if (!response.ok) {
        throw new Error(`API error: ${response.status}`);
      }

      const data = await response.json();

      if (data && data.items && Array.isArray(data.items)) {
        // Directly map the API response to our display format
        const displayUsers = data.items.map((item: any) => ({
          id: String(item.id || ''),
          name: String(item.user_name || ''),
          email: String(item.email || ''),
          role: String(item.role || 'staff'),
          status: item.is_active === false ? 'inactive' : 'active',
          mobile: String(item.mobile_number || ''),
          storeType: item.store_type?.name || '',
          manager: item.manager?.user_name || '',
        }));

        setUsers(displayUsers);
        setTotalUsers(data.total || displayUsers.length);
        setDataLoaded(true);
      } else {
        setUsers([]);
        setTotalUsers(0);
      }
    } catch (err) {
      console.error('Error fetching users:', err);
      setError('Failed to load users. Please try again.');

      if (!dataLoaded) {
        setUsers([]);
        setTotalUsers(0);
      }
    } finally {
      setIsLoading(false);
    }
  };

  // Helper function to reset form data to initial state
  const resetFormData = () => {
    console.log('resetFormData called - clearing all form fields');
    setFormData({
      name: '',
      email: '',
      password: '',
      role: '',
      status: 'active',
      mobile: '',
      storeTypeId: null,
      managerId: null
    });
    setCurrentUser(null);
    setError(null);
    setFieldErrors({
      password: [],
      email: [],
      mobile: []
    });
    console.log('resetFormData completed - form should be clean');
  };

  // Validation functions for inline error messaging
  const validatePassword = (password: string): string[] => {
    const errors: string[] = [];

    if (password.length < 8) {
      errors.push('Password must be at least 8 characters long');
    }

    if (!/[A-Z]/.test(password)) {
      errors.push('Password must contain at least one uppercase letter (A-Z)');
    }

    if (!/[a-z]/.test(password)) {
      errors.push('Password must contain at least one lowercase letter (a-z)');
    }

    if (!/[0-9]/.test(password)) {
      errors.push('Password must contain at least one number (0-9)');
    }

    if (!/[!@#$%^&*()_+\-=[\]{};':"\\|,.<>?]/.test(password)) {
      errors.push('Password must contain at least one special character (!@#$%^&*()_+-=[]{}|;:,.<>?)');
    }

    return errors;
  };

  const validateEmail = (email: string): string[] => {
    const errors: string[] = [];

    if (!email) {
      errors.push('Email is required');
      return errors;
    }

    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      errors.push('Please enter a valid email address');
    }

    return errors;
  };

  const validateMobile = (mobile: string): string[] => {
    const errors: string[] = [];

    if (!mobile) {
      errors.push('Mobile number is required');
      return errors;
    }

    const mobileRegex = /^(\+61\d{8,9}|0\d{8,9})$/;
    if (!mobileRegex.test(mobile)) {
      errors.push('Please enter a valid Australian mobile number (+61xxxxxxxx or 0xxxxxxxxx)');
    }

    return errors;
  };

  // Function to handle mobile number input restrictions (only numbers and + allowed)
  const handleMobileKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    const char = e.key;
    const currentValue = (e.target as HTMLInputElement).value;

    // Allow backspace, delete, tab, escape, enter, and arrow keys
    if (
      e.key === 'Backspace' ||
      e.key === 'Delete' ||
      e.key === 'Tab' ||
      e.key === 'Escape' ||
      e.key === 'Enter' ||
      e.key === 'ArrowLeft' ||
      e.key === 'ArrowRight' ||
      e.key === 'ArrowUp' ||
      e.key === 'ArrowDown'
    ) {
      return;
    }

    // Allow + only at the beginning and only one +
    if (char === '+') {
      if (currentValue.length === 0 && !currentValue.includes('+')) {
        return; // Allow + at the beginning
      } else {
        e.preventDefault(); // Prevent + if not at beginning or already exists
        return;
      }
    }

    // Allow only numbers (0-9)
    if (!/[0-9]/.test(char)) {
      e.preventDefault();
    }
  };

  // Function to check if form has changes from original data
  const checkForFormChanges = (currentData: UserFormData, originalData: UserFormData | null): boolean => {
    if (!originalData) return false;

    // Compare all relevant fields (excluding password as it's optional in edit mode)
    return (
      currentData.name !== originalData.name ||
      currentData.email !== originalData.email ||
      currentData.mobile !== originalData.mobile ||
      currentData.role !== originalData.role ||
      currentData.status !== originalData.status ||
      currentData.storeTypeId !== originalData.storeTypeId ||
      currentData.managerId !== originalData.managerId ||
      Boolean(currentData.password && currentData.password.length > 0) // Password change if not empty
    );
  };

  // Track form changes for Edit User modal
  useEffect(() => {
    if (currentUser && originalFormData) {
      const hasChanges = checkForFormChanges(formData, originalFormData);
      setHasFormChanges(hasChanges);
    } else {
      setHasFormChanges(false);
    }
  }, [formData, originalFormData, currentUser]);

  // Helper function to render field errors
  const renderFieldErrors = (fieldName: 'password' | 'email' | 'mobile') => {
    const errors = fieldErrors[fieldName];
    if (!errors || errors.length === 0) return null;

    return (
      <FieldErrorMessage>
        {errors.map((error, index) => (
          <div key={index}>{error}</div>
        ))}
      </FieldErrorMessage>
    );
  };

  // Function to validate all fields and return combined errors
  const validateAllFields = (): boolean => {
    const emailErrors = validateEmail(formData.email);
    const mobileErrors = validateMobile(formData.mobile);

    // Password validation only required for new users (Add mode)
    let passwordErrors: string[] = [];
    if (!currentUser && formData.password) {
      passwordErrors = validatePassword(formData.password);
    } else if (!currentUser && !formData.password) {
      passwordErrors = ['Password is required'];
    }

    // Update all field errors at once
    setFieldErrors({
      password: passwordErrors,
      email: emailErrors,
      mobile: mobileErrors
    });

    // Check basic required fields
    if (!formData.name || !formData.email || (!currentUser && !formData.password) || !formData.role) {
      setError("Please fill in all required fields.");
      return false;
    }

    // Role-specific validations
    if (formData.role === 'staff' && !formData.managerId) {
      setError("Manager is required for users with Staff role.");
      return false;
    }

    // Store type is mandatory for managers
    if (formData.role === 'manager' && !formData.storeTypeId) {
      setError("Store type is required for users with Manager role.");
      return false;
    }

    // For staff users, check if store type can be determined from manager
    if (formData.role === 'staff' && formData.managerId && !formData.storeTypeId) {
      const selectedManager = managers.find(m => m.id === formData.managerId);
      if (selectedManager && selectedManager.store_type_id !== undefined) {
        // Update form data with manager's store type
        setFormData(prev => ({
          ...prev,
          storeTypeId: selectedManager.store_type_id!
        }));
      }
    }

    // Return true if no field errors and no global validation issues
    return emailErrors.length === 0 && mobileErrors.length === 0 && passwordErrors.length === 0;
  };

  const handleBack = () => {
    navigate('/home');
  };

  const handleAddUser = async () => {
    // Check if user has admin role
    if (!userIsAdmin) {
      setError("You don't have permission to add users. Only administrators can add users.");
      return;
    }

    // Reset form data first to ensure clean state
    console.log('Resetting form data for Add User modal');
    resetFormData();

    // Load managers and store types if not already loaded
    if (managers.length === 0 || storeTypes.length === 0) {
      await loadManagersAndStoreTypes();
    }

    setShowAddModal(true);
  };

  const handleEditUser = async (user: UserDisplay) => {
    // Check if user has admin role
    if (!userIsAdmin) {
      setError("You don't have permission to edit users. Only administrators can edit users.");
      return;
    }

    // Clear any previous errors and field errors
    setError(null);
    setFieldErrors({
      password: [],
      email: [],
      mobile: []
    });

    // Load managers and store types if not already loaded
    if (managers.length === 0 || storeTypes.length === 0) {
      await loadManagersAndStoreTypes();
    }

    setCurrentUser(user);
    const initialFormData = {
      name: user.name,
      email: user.email,
      password: '', // Don't populate password for security
      role: user.role,
      status: user.status,
      mobile: user.mobile,
      storeTypeId: user.storeTypeId || null,
      managerId: user.managerId || null
    };

    setFormData(initialFormData);
    setOriginalFormData(initialFormData); // Store original data for comparison
    setHasFormChanges(false); // Reset changes flag
    setShowEditModal(true);
  };

  const handleDeleteUser = async (id: string) => {
    // Check if user has admin role
    if (!userIsAdmin) {
      setError("You don't have permission to delete users. Only administrators can delete users.");
      return;
    }

    // Prevent users from deleting their own account
    if (currentAuthUser && currentAuthUser.id === id) {
      setError("You cannot delete your own account. Please ask another administrator to deactivate your account if needed.");
      return;
    }

    if (window.confirm('Are you sure you want to delete this user?')) {
      setIsLoading(true);
      try {
        // Get user name for toast message before deletion
        const userToDelete = users.find(user => user.id === id);
        const userName = userToDelete?.name || 'User';

        // Call the proper DELETE API endpoint to actually delete the user
        await deleteUser(id);

        // Show success toast
        toast.showToast(`User "${userName}" has been deleted successfully!`, { type: 'success' });

        // Calculate pagination logic
        const currentPageUserCount = users.length;
        const isLastUserOnPage = currentPageUserCount === 1;
        const isNotFirstPage = currentPage > 1;
        const newTotalUsers = totalUsers - 1;

        // Update total users count first
        setTotalUsers(newTotalUsers);

        // If this is the last user on a page that's not the first page, go to previous page
        if (isLastUserOnPage && isNotFirstPage) {
          console.log(`Last user on page ${currentPage}, navigating to page ${currentPage - 1}`);
          setCurrentPage(currentPage - 1);
          // The useEffect will automatically trigger fetchUsers when currentPage changes
        } else {
          // Refresh data immediately if staying on the same page
          await fetchUsers();
        }

        setError(null); // Clear any previous errors
      } catch (error) {
        console.error('Error deleting user:', error);
        setError('Failed to delete user. Please try again.');
      } finally {
        setIsLoading(false);
      }
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;

    // Store the original form update logic
    let shouldUpdateForm = true;
    let formUpdate: Partial<UserFormData> = { [name]: value };

    // If changing the role, apply role-specific requirements
    if (name === 'role') {
      if (value === 'manager') {
        // Manager: Store type is mandatory, no manager
        formUpdate = {
          ...formUpdate,
          managerId: null // Managers don't have a manager
        };
      }
      else if (value === 'admin') {
        // Admin: Store type is optional
        // Don't modify storeTypeId for admin - it's optional
      }
      else if (value === 'staff') {
        // Staff: Manager is mandatory, store type should auto-populate from manager
        // If a manager is already selected, get their store type
        const currentManagerId = formData.managerId;
        if (currentManagerId) {
          const selectedManager = managers.find(m => m.id === currentManagerId);
          if (selectedManager) {
            // Check for store_type object first
            if (selectedManager.store_type && selectedManager.store_type.id !== undefined) {
              // Update with manager's store type from store_type object
              const storeTypeId = selectedManager.store_type.id;
              formUpdate = {
                ...formUpdate,
                storeTypeId: storeTypeId
              };
              console.log(`Auto-populated store type from existing manager: ${storeTypeId}`);
            }
          }
        }

        // Default case for role change
        setFormData(prev => ({
          ...prev,
          ...formUpdate
        }));
        shouldUpdateForm = false;
      }
      else {
        // Default case
        setFormData(prev => ({
          ...prev,
          ...formUpdate
        }));
        shouldUpdateForm = false;
      }
    }
    // If changing manager, auto-populate store type for staff role and handle manager selection for all roles
    else if (name === 'managerId') {
      // Convert empty value to null
      const managerId = value === '' ? null : value;
      console.log(`Manager selected: ${managerId}, user role: ${formData.role}`);

      // Update form data with the manager ID
      if (managerId === null) {
        // If manager was cleared, clear store type for staff role
        if (formData.role === 'staff') {
          setFormData(prev => ({
            ...prev,
            managerId: null,
            storeTypeId: null // Clear store type when manager is cleared for staff
          }));
          console.log("Manager cleared, also clearing store type for staff role");
        } else {
          // For other roles, just clear manager
          setFormData(prev => ({
            ...prev,
            managerId: null
          }));
        }
        shouldUpdateForm = false;
      } else {
        // Find selected manager
        const selectedManager = managers.find(manager => manager.id === managerId);
        console.log("Selected manager:", selectedManager);

        // For staff role, auto-populate store type if available
        if (formData.role === 'staff' && selectedManager) {
          if (selectedManager.store_type && selectedManager.store_type.id !== undefined) {
            const storeTypeId = selectedManager.store_type.id;
            setFormData(prev => ({
              ...prev,
              managerId: managerId,
              storeTypeId: storeTypeId
            }));
            console.log(`Auto-populated store type ${storeTypeId} from manager ${selectedManager.user_name}`);
            shouldUpdateForm = false;
          }
        } else {
          // For other roles or if no store type found, just update manager
          setFormData(prev => ({
            ...prev,
            managerId: managerId
          }));
          shouldUpdateForm = false;
        }
      }
    }

    // Default form update if not handled above
    if (shouldUpdateForm) {
      // Handle storeTypeId conversion
      if (name === 'storeTypeId') {
        setFormData(prev => ({
          ...prev,
          storeTypeId: value === '' ? null : parseInt(value, 10)
        }));
      } else {
        setFormData(prev => ({
          ...prev,
          ...formUpdate
        }));
      }
    }

    // Perform real-time validation for specific fields
    if (name === 'password' && value) {
      const passwordErrors = validatePassword(value);
      setFieldErrors(prev => ({
        ...prev,
        password: passwordErrors
      }));
    } else if (name === 'password' && !value) {
      // Clear password errors when field is empty (for edit mode where password is optional)
      setFieldErrors(prev => ({
        ...prev,
        password: []
      }));
    }

    if (name === 'email') {
      const emailErrors = validateEmail(value);
      setFieldErrors(prev => ({
        ...prev,
        email: emailErrors
      }));
    }

    if (name === 'mobile') {
      const mobileErrors = validateMobile(value);
      setFieldErrors(prev => ({
        ...prev,
        mobile: mobileErrors
      }));
    }

    // Check for form changes after any input change (for Edit User modal)
    if (currentUser && originalFormData) {
      // Use a timeout to ensure state updates are processed
      setTimeout(() => {
        const currentFormData = formData;
        // Update the current field value in the comparison
        const updatedFormData = { ...currentFormData, [name]: value };
        const hasChanges = checkForFormChanges(updatedFormData, originalFormData);
        setHasFormChanges(hasChanges);
      }, 0);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Check if user has admin role for creating users
    if (!currentUser && !userIsAdmin) {
      setError("You don't have permission to add users. Only administrators can add users.");
      return;
    }

    // Validate all fields before submission
    const isValid = validateAllFields();
    if (!isValid) {
      // Field errors are already set by validateAllFields, don't set a global error
      return;
    }

    setIsLoading(true);
    setError(null); // Clear any global errors

    try {
      if (currentUser) {
        // Update existing user
        const apiData: Record<string, any> = {
          user_name: formData.name,
          email: formData.email,
          mobile_number: formData.mobile,
          is_active: formData.status === 'active'
        };

        // Send the role string directly instead of looking up a role ID
        apiData.role = formData.role;

        // Only include password if changed
        if (formData.password) {
          apiData.password = formData.password;
        }

        // Role-specific fields - instead of manual mapping, use the helper function
        const updateData = mapDisplayUserToApiUpdate({
          name: formData.name,
          email: formData.email,
          password: formData.password || undefined,
          role: formData.role,
          status: formData.status,
          mobile: formData.mobile,
          storeTypeId: formData.storeTypeId,
          managerId: formData.managerId
        }, managers);

        // Copy the properties from updateData to apiData
        Object.assign(apiData, {
          store_type_id: updateData.store_type_id,
          manager_id: updateData.manager_id
        });

        console.log('Sending user update data:', JSON.stringify(apiData, null, 2));

        // Make API call to update user
        const updatedUserResponse = await apiClient.put<UserResponse>(`/api/v1/users/${currentUser.id}`, apiData);

        console.log('API response:', updatedUserResponse);

        if (updatedUserResponse) {
          let updatedUser: UserDisplay;
          try {
            updatedUser = mapApiUserToDisplayUser(updatedUserResponse);
          } catch (mappingError) {
            console.error('Error mapping updated user:', mappingError);
            // Create a fallback user object with form data
            updatedUser = {
              ...currentUser,
              name: formData.name,
              email: formData.email,
              role: formData.role,
              status: formData.status,
              mobile: formData.mobile,
              storeType: apiData.store_type_id === 1 ? 'Cranbourne' :
                (apiData.store_type_id === 2 ? 'Sale' : 'N/A'),
              storeTypeId: apiData.store_type_id,
              managerId: apiData.manager_id
            };
          }

          // Update users array with the updated user
          setUsers(users.map(user => user.id === updatedUser.id ? updatedUser : user));

          // Show success toast
          toast.showToast(`User "${updatedUser.name}" has been updated successfully!`, { type: 'success' });

          // Reset form and close modal
          resetFormData();
          setShowEditModal(false);
        }
      } else {
        // Create new user
        const createData = mapDisplayUserToApiCreate({
          name: formData.name,
          email: formData.email,
          password: formData.password,
          role: formData.role,
          mobile: formData.mobile,
          storeTypeId: formData.storeTypeId,
          managerId: formData.managerId
        }, managers);

        // Create API data using the helper function
        const apiData: Record<string, any> = {
          user_name: createData.user_name,
          email: createData.email,
          password: createData.password,
          mobile_number: createData.mobile_number,
          role: createData.role,
          store_type_id: createData.store_type_id,
          manager_id: createData.manager_id
        };

        console.log('Sending user data:', JSON.stringify(apiData, null, 2));

        // Make API call to create user
        const newUserResponse = await apiClient.post<UserResponse>('/api/v1/users/', apiData);

        console.log('API response:', newUserResponse);

        if (newUserResponse) {
          let newUser: UserDisplay;
          try {
            newUser = mapApiUserToDisplayUser(newUserResponse);
          } catch (mappingError) {
            console.error('Error mapping new user:', mappingError);
            throw new Error('Error creating user: Failed to process server response');
          }

          // Add the new user to the users array
          setUsers([...users, newUser]);

          // Update total users count to ensure pagination appears
          setTotalUsers(prevTotal => prevTotal + 1);

          // Show success toast
          toast.showToast(`User "${newUser.name}" has been created successfully!`, { type: 'success' });

          // Reset form and close modal
          resetFormData();
          setShowAddModal(false);
        }
      }
    } catch (error) {
      console.error('Error submitting form:', error);

      // Log the complete error object for debugging
      console.log('Complete error object:', JSON.stringify(error, null, 2));

      let errorMessage = 'Failed to save user. Please try again.';

      // Cast error to ApiError for type safety
      const apiError = error as ApiError;

      // Try to extract error message from API response
      if (apiError.response) {
        console.log('Response status:', apiError.response.status);
        console.log('Response headers:', apiError.response.headers);
        console.log('Response data:', apiError.response.data);

        if (apiError.response.data) {
          // Handle validation errors (422 status)
          if (apiError.response.status === 422 && typeof apiError.response.data === 'object') {
            const validationErrors = [];

            // Check for structured validation errors
            if (apiError.response.data.detail && Array.isArray(apiError.response.data.detail)) {
              // FastAPI validation error format
              for (const detail of apiError.response.data.detail) {
                if (detail.msg && detail.loc) {
                  const field = Array.isArray(detail.loc) ? detail.loc.join('.') : detail.loc;
                  validationErrors.push(`${field}: ${detail.msg}`);
                } else if (detail.msg) {
                  validationErrors.push(detail.msg);
                }
              }
            }
            // Check for specific validation error patterns
            else if (apiError.response.data.msg) {
              validationErrors.push(apiError.response.data.msg);
            }
            // Check for password validation errors
            else if (apiError.response.data.password) {
              validationErrors.push(`Password: ${apiError.response.data.password}`);
            }
            // Check for email validation errors
            else if (apiError.response.data.email) {
              validationErrors.push(`Email: ${apiError.response.data.email}`);
            }
            // Check for mobile number validation errors
            else if (apiError.response.data.mobile_number) {
              validationErrors.push(`Mobile Number: ${apiError.response.data.mobile_number}`);
            }
            // Generic field errors
            else {
              Object.entries(apiError.response.data).forEach(([field, message]) => {
                if (field !== 'type' && field !== 'ctx' && message) {
                  if (Array.isArray(message)) {
                    validationErrors.push(`${field}: ${message.join(', ')}`);
                  } else {
                    validationErrors.push(`${field}: ${message}`);
                  }
                }
              });
            }

            if (validationErrors.length > 0) {
              errorMessage = validationErrors.join('\n');
            }
          }
          // Handle other structured error responses
          else if (typeof apiError.response.data === 'object') {
            if (apiError.response.data.detail) {
              errorMessage = apiError.response.data.detail;
            } else if (apiError.response.data.message) {
              errorMessage = apiError.response.data.message;
            } else {
              // Convert the error object to a readable string
              try {
                const errorDetails = Object.entries(apiError.response.data)
                  .filter(([key]) => !['type', 'ctx'].includes(key)) // Exclude technical fields
                  .map(([key, value]) => {
                    if (Array.isArray(value)) {
                      return `${key}: ${value.join(', ')}`;
                    }
                    return `${key}: ${value}`;
                  })
                  .join('\n');
                errorMessage = errorDetails || errorMessage;
              } catch (e) {
                console.error('Error parsing error details:', e);
              }
            }
          } else if (typeof apiError.response.data === 'string') {
            errorMessage = apiError.response.data;
          }
        }

        // Handle specific HTTP status codes
        if (apiError.response.status === 409) {
          errorMessage = 'Email address is already registered. Please use a different email address.';
        } else if (apiError.response.status === 400) {
          errorMessage = errorMessage || 'Invalid data provided. Please check your input and try again.';
        }
      } else if (apiError.message && apiError.message !== '[object Object]') {
        errorMessage = apiError.message;
      }

      // Add user-friendly formatting for common validation messages
      errorMessage = errorMessage
        .replace(/value_error\./g, '')
        .replace(/Password must contain at least one uppercase letter/g,
          'Password must contain at least one uppercase letter (A-Z)')
        .replace(/Invalid mobile number format/g,
          'Mobile number must be in Australian format: +61xxxxxxxx or 0xxxxxxxxx')
        .replace(/email_address/g, 'email address')
        .replace(/mobile_number/g, 'mobile number')
        .replace(/user_name/g, 'user name');

      setError(errorMessage);
      console.log('Detailed error:', error);

      // Don't close the modal if there's an error so user can see the error and fix their input
      // The form will remain open with their values still populated
    } finally {
      setIsLoading(false);
    }
  };

  // Compute total pages based on totalUsers and itemsPerPage
  const totalPages = Math.ceil(totalUsers / itemsPerPage);

  // Update pagination handlers
  const handlePageChange = (pageNumber: number) => {
    if (pageNumber !== currentPage) {
      console.log(`Changing page from ${currentPage} to ${pageNumber}`);
      setCurrentPage(pageNumber);
      // We'll fetch data in the useEffect
    }
  };

  const handleItemsPerPageChange = (newItemsPerPage: number) => {
    if (newItemsPerPage !== itemsPerPage) {
      console.log(`Changing items per page from ${itemsPerPage} to ${newItemsPerPage}`);
      setItemsPerPage(newItemsPerPage);
      setCurrentPage(1); // Reset to first page when changing items per page
      // We'll fetch data in the useEffect
    }
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;

    // Store cursor position before state update
    const cursorPosition = e.target.selectionStart;

    // Update the immediate search query (for UI display)
    setSearchQuery(newValue);

    // Restore focus and cursor position after state update
    requestAnimationFrame(() => {
      if (searchInputRef.current) {
        searchInputRef.current.focus();
        if (cursorPosition !== null) {
          searchInputRef.current.setSelectionRange(cursorPosition, cursorPosition);
        }
      }
    });
  };

  // Helper function to render user row
  const renderUserRow = (user: UserDisplay): React.ReactElement | null => {
    // Check for valid user data
    if (!user || !user.id) {
      console.error('Invalid user data:', user);
      return null;
    }

    try {
      // Map API response fields to display fields with correct property names
      const name = user.name || user.user_name || 'N/A'; // Try both name and user_name
      const email = user.email || 'N/A';
      const mobile = user.mobile || user.mobile_number || 'N/A'; // Try both mobile and mobile_number
      const role = user.role || 'staff';
      const roleName = role.charAt(0).toUpperCase() + role.slice(1);

      // Properly extract manager information
      let manager = user.manager || 'N/A';

      // Correctly map store_type
      let storeType = 'N/A';
      if (user.store_type && user.store_type.name) {
        storeType = user.store_type.name;
      } else if (user.storeType) {
        storeType = user.storeType; // Fallback to storeType if store_type.name is not available
      }

      const isActive = user.is_active !== undefined ? user.is_active : (user.status === 'active');

      // Check if this is the current user's own account
      const isOwnAccount = !!(currentAuthUser && currentAuthUser.id === user.id);

      return (
        <GridRow key={user.id}>
          <div title={name}>{name}</div>
          <div title={mobile}>{mobile}</div>
          <div title={email}>{email}</div>
          <div>
            <span style={{
              display: 'inline-block',
              padding: '0.25rem 0.75rem',
              borderRadius: '12px',
              fontSize: '0.85rem',
              fontWeight: '500',
              backgroundColor:
                role === 'admin' ? '#e6f7ff' :
                  role === 'manager' ? '#f6ffed' :
                    '#fff7e6',
              color:
                role === 'admin' ? '#096dd9' :
                  role === 'manager' ? '#52c41a' :
                    '#fa8c16'
            }}>
              {roleName}
            </span>
          </div>
          <div title={manager}>{manager}</div>
          <div title={storeType}>{storeType}</div>
          <div style={{ gap: '6px', display: 'flex', justifyContent: 'center' }}>
            <ActionButton onClick={() => handleEditUser(user)}>
              <img src={UserEditIcon} alt="Edit" width="22" height="22" />
            </ActionButton>
            <ActionButton
              onClick={() => handleDeleteUser(user.id)}
              disabled={isOwnAccount}
              title={isOwnAccount ? "You cannot delete your own account" : "Delete user"}
              style={{
                opacity: isOwnAccount ? 0.5 : 1,
                cursor: isOwnAccount ? 'not-allowed' : 'pointer'
              }}
            >
              <img src={userDeleteIcon} alt="Delete" width="22" height="22" />
            </ActionButton>
          </div>
        </GridRow>
      );
    } catch (error) {
      console.error('Error rendering user row:', error, user);
      return null;
    }
  };

  // Helper function to render empty state
  const renderEmptyState = () => {
    return (
      <EmptyStateContainer>
        <EmptyStateIcon>
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
          </svg>
        </EmptyStateIcon>
        <EmptyStateTitle>No Users Found</EmptyStateTitle>
        <EmptyStateDescription>
          {debouncedSearchQuery
            ? "No users match your search criteria. Try a different search term."
            : "There are no users in the system. Add a user to get started."
          }
        </EmptyStateDescription>
        {!debouncedSearchQuery && (
          <EmptyStateButton onClick={() => handleAddUser()}>
            Add User
          </EmptyStateButton>
        )}
      </EmptyStateContainer>
    );
  };

  // Render grid content with consistent loading pattern
  const renderGridContent = () => {
    if (isLoading) {
      return (
        <LoadingRow>
          <LoadingContainer>
            <LoadingSpinner size="lg" />
            <LoadingText>Loading users</LoadingText>
          </LoadingContainer>
        </LoadingRow>
      );
    }

    if (users && users.length > 0) {
      return users.map(user => renderUserRow(user)).filter(Boolean);
    }

    return renderEmptyState();
  };

  // Add these missing handler functions inside the UsersPage component, before the return statement
  const handleToggleUserStatus = (userId: string, active: boolean) => {
    // Show confirmation dialog for status change
    if (window.confirm(`Are you sure you want to ${active ? 'activate' : 'deactivate'} this user?`)) {
      // This would typically call an API to update the user status
      console.log(`Toggling status for user ${userId} to ${active ? 'active' : 'inactive'}`);

      // Update local state to reflect the change
      setUsers(prevUsers =>
        prevUsers.map(user =>
          user.id === userId ? { ...user, is_active: active } : user
        )
      );
    }
  };

  const handleResetPassword = (userId: string) => {
    // Show confirmation dialog for password reset
    if (window.confirm('Are you sure you want to reset this user\'s password?')) {
      // This would typically call an API to reset the password
      console.log(`Resetting password for user ${userId}`);
      // You would then show a success message or error based on API response
    }
  };

  // Helper function to determine if store type field should be disabled
  const isStoreTypeFieldDisabled = (role: string): boolean => {
    // Disable store type field for staff role since it's auto-populated from manager
    return isLoading || role === 'staff';
  };

  // Add a direct method to force update manager's store type
  const updateManagerStoreType = (managerId: string | null) => {
    if (!managerId || formData.role !== 'staff') return;

    // Find the manager
    const selectedManager = managers.find(m => m.id === managerId);
    if (!selectedManager) {
      console.warn(`Could not find manager with ID ${managerId}`);
      return;
    }

    console.log("Selected manager full object:", JSON.stringify(selectedManager, null, 2));

    // Try different ways to get store type id
    let storeTypeId: number | null = null;

    // First check for store_type object (new API format)
    if (selectedManager.store_type && selectedManager.store_type.id !== undefined) {
      storeTypeId = selectedManager.store_type.id;
      console.log(`Found store type ID ${storeTypeId} in store_type object`);
    }
    // Fallback to store_type_id if available (legacy format)
    else if (selectedManager.store_type_id !== undefined) {
      storeTypeId = selectedManager.store_type_id;
      console.log(`Found store type ID ${storeTypeId} from store_type_id property`);
    }

    // Only update if the store type is different from current value to prevent unnecessary rerenders
    if (storeTypeId !== null && storeTypeId !== formData.storeTypeId) {
      console.log(`Direct update: Setting store type to ${storeTypeId} from manager ${selectedManager.user_name}`);
      setFormData(prev => ({
        ...prev,
        storeTypeId: storeTypeId
      }));
    } else if (storeTypeId === null) {
      console.warn(`Manager ${selectedManager.user_name} has no store type information in any format`);
    } else {
      console.log(`Skipping update as store type ID ${storeTypeId} is already set`);
    }
  };

  // Helper function to determine if manager field should be disabled
  const isManagerFieldDisabled = (role: string): boolean => {
    // Only disable manager field for manager role, not for admin or staff
    return isLoading || role === 'manager';
  };

  // Helper function to check if the user being edited is the current logged-in user
  const isEditingOwnAccount = (): boolean => {
    return !!(currentUser && currentAuthUser && currentAuthUser.id === currentUser.id);
  };

  // Helper function to determine if email field should be disabled
  const isEmailFieldDisabled = (): boolean => {
    return isLoading || isEditingOwnAccount();
  };

  // Helper function to determine if password field should be disabled
  const isPasswordFieldDisabled = (): boolean => {
    return isLoading || isEditingOwnAccount();
  };

  // Helper function to determine if role field should be disabled
  const isRoleFieldDisabled = (): boolean => {
    return isLoading || isEditingOwnAccount();
  };

  // Helper function to determine if status field should be disabled
  const isStatusFieldDisabled = (): boolean => {
    return isLoading || isEditingOwnAccount();
  };

  // Helper function to determine if store type field should be disabled for own account
  const isStoreTypeFieldDisabledForOwnAccount = (): boolean => {
    return isLoading || isEditingOwnAccount() || isStoreTypeFieldDisabled(formData.role);
  };

  // Helper function to determine if manager field should be disabled for own account
  const isManagerFieldDisabledForOwnAccount = (): boolean => {
    return isLoading || isEditingOwnAccount() || isManagerFieldDisabled(formData.role);
  };

  // Debug useEffect to track form data changes
  useEffect(() => {
    console.log('Form data changed:', {
      name: formData.name,
      email: formData.email,
      password: formData.password ? '*'.repeat(formData.password.length) : '',
      role: formData.role,
      mobile: formData.mobile,
      currentUser: currentUser?.id || 'null',
      showAddModal,
      showEditModal
    });
  }, [formData, currentUser, showAddModal, showEditModal]);

  // First useEffect for initial load
  useEffect(() => {
    // Prevent fetch if not authenticated
    if (!isAuthenticated) {
      navigate('/login');
      return;
    }

    // Load data on initial render
    fetchUsers();

    // Only depend on authentication state to prevent unnecessary rerenders
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isAuthenticated]);

  // Second useEffect to handle pagination and search changes
  useEffect(() => {
    // Skip on initial render - the first useEffect handles that
    if (!dataLoaded) return;

    // Fetch data when pagination or search parameters change
    fetchUsers();

    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentPage, itemsPerPage, debouncedSearchQuery]);

  return (
    <Layout>
      <PageContainer>
        {/* Custom header with back button and title on a single line */}
        <CustomHeader>
          <BackButtonComponent onClick={handleBack} compact={true} />
          <PageTitle>Users Management</PageTitle>
        </CustomHeader>

        {/* Error message if present */}
        {error && (
          <div style={{
            color: '#dc3545',
            backgroundColor: '#fff5f5',
            padding: '10px 15px',
            borderRadius: '6px',
            marginBottom: '15px',
            border: '1px solid #ffccd1',
            display: 'flex',
            alignItems: 'center'
          }}>
            <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg" style={{ marginRight: '10px' }}>
              <path d="M8 5V9M8 11.01L8.01 10.999M15 8C15 11.866 11.866 15 8 15C4.13401 15 1 11.866 1 8C1 4.13401 4.13401 1 8 1C11.866 1 15 4.13401 15 8Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
            </svg>
            {error}
          </div>
        )}

        {/* Search and Add User in one line */}
        <SearchActionContainer>
          <StyledSearchInput>
            <SearchInputComponent
              ref={searchInputRef}
              placeholder="Search user"
              value={searchQuery}
              onChange={handleSearchChange}
              disabled={false}
            />
          </StyledSearchInput>
          {userIsAdmin && (
            <AddButton onClick={() => handleAddUser()}>
              <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <line x1="12" y1="5" x2="12" y2="19"></line>
                <line x1="5" y1="12" x2="19" y2="12"></line>
              </svg>
              Add User
            </AddButton>
          )}
        </SearchActionContainer>

        <GridContainer>
          <GridHeader>
            <div>Name</div>
            <div>Mobile</div>
            <div>Email</div>
            <div>Role</div>
            <div>Manager</div>
            <div>Store Type</div>
            <div>Actions</div>
          </GridHeader>

          {renderGridContent()}
        </GridContainer>

        {!isLoading && totalUsers > 0 && (
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={handlePageChange}
            totalItems={totalUsers}
            itemsPerPage={itemsPerPage}
            onItemsPerPageChange={handleItemsPerPageChange}
            itemsPerPageOptions={[10, 20, 50]}
            showItemsPerPage={true}
          />
        )}

        {/* Add User Modal */}
        {showAddModal && (
          <ModalOverlay onClick={() => {
            resetFormData();
            setShowAddModal(false);
          }}>
            <ModalContent onClick={e => e.stopPropagation()}>
              <ModalTitle>Add New User</ModalTitle>
              {error && (
                <ErrorMessage>
                  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M8 5V9M8 11.01L8.01 10.999M15 8C15 11.866 11.866 15 8 15C4.13401 15 1 11.866 1 8C1 4.13401 4.13401 1 8 1C11.866 1 15 4.13401 15 8Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                  </svg>
                  <span>{error}</span>
                </ErrorMessage>
              )}
              <form key="add-user-form" onSubmit={handleSubmit}>
                <FormGrid>
                  <FormGroup>
                    <FormLabel>
                      Name<span className="required">*</span>
                    </FormLabel>
                    <FormInput
                      type="text"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      required
                      disabled={isLoading}
                      placeholder="Enter name"
                    />
                  </FormGroup>
                  <FormGroup>
                    <FormLabel>
                      Mobile Number<span className="required">*</span>
                    </FormLabel>
                    <FormInput
                      type="text"
                      name="mobile"
                      value={formData.mobile}
                      onChange={handleInputChange}
                      required
                      disabled={isLoading}
                      placeholder="Enter mobile number"
                      onKeyPress={handleMobileKeyPress}
                    />
                    <div style={{ color: '#666', fontSize: '0.8rem', marginTop: '0.25rem' }}>
                      Format: +61xxxxxxxx or 0xxxxxxxxx
                    </div>
                    {renderFieldErrors('mobile')}
                  </FormGroup>
                  <FormGroup>
                    <FormLabel>
                      Email<span className="required">*</span>
                    </FormLabel>
                    <FormInput
                      type="email"
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      required
                      disabled={isLoading}
                      placeholder="Enter email address"
                      autoComplete="new-email"
                    />
                    {renderFieldErrors('email')}
                  </FormGroup>
                  <FormGroup>
                    <FormLabel>
                      Password<span className="required">*</span>
                    </FormLabel>
                    <FormInput
                      type="password"
                      name="password"
                      value={formData.password}
                      onChange={handleInputChange}
                      required
                      disabled={isLoading}
                      placeholder="At least 8 characters"
                      autoComplete="new-password"
                    />
                    {renderFieldErrors('password')}
                  </FormGroup>
                  <FormGroup>
                    <FormLabel>
                      Role<span className="required">*</span>
                    </FormLabel>
                    <FormSelect
                      name="role"
                      value={formData.role}
                      onChange={handleInputChange}
                      required
                      disabled={isLoading}
                    >
                      <option value="">Select a role</option>
                      {roles.map(role => (
                        <option key={role.value} value={role.value}>
                          {role.label}
                        </option>
                      ))}
                    </FormSelect>
                  </FormGroup>
                  <FormGroup>
                    <FormLabel>
                      Store Type
                      {formData.role === 'manager' && <span className="required">*</span>}
                    </FormLabel>
                    <FormSelect
                      name="storeTypeId"
                      value={formData.storeTypeId || ''}
                      onChange={handleInputChange}
                      required={formData.role === 'manager'}
                      disabled={isStoreTypeFieldDisabledForOwnAccount() || loadingManagersAndStoreTypes}
                    >
                      <option value="">Select a store type</option>
                      {loadingManagersAndStoreTypes && (
                        <option value="" disabled>Loading store types</option>
                      )}
                      {!loadingManagersAndStoreTypes && storeTypes.length === 0 && (
                        <option value="" disabled>No store types available</option>
                      )}
                      {!loadingManagersAndStoreTypes && storeTypes.map(storeType => (
                        <option key={storeType.id} value={storeType.id}>
                          {storeType.name}
                        </option>
                      ))}
                    </FormSelect>
                    {formData.role === 'admin' && (
                      <div style={{ color: '#666', fontSize: '0.8rem', marginTop: '0.25rem' }}>
                        Store type is optional for Admin users.
                      </div>
                    )}
                    {formData.role === 'staff' && formData.managerId && (
                      <div style={{ color: '#4CAF50', fontSize: '0.8rem', marginTop: '0.25rem' }}>
                        Store type is auto-populated from selected Manager.
                        {formData.storeTypeId && (
                          <span style={{ fontWeight: 'bold' }}>
                            {' '}{storeTypes.find(st => st.id === formData.storeTypeId)?.name || `ID: ${formData.storeTypeId}`}
                          </span>
                        )}
                        {!formData.storeTypeId && (
                          <span style={{ color: 'red', fontWeight: 'bold' }}>
                            {' '}No store type found for this manager!
                          </span>
                        )}
                      </div>
                    )}
                    {formData.role === 'staff' && !formData.managerId && (
                      <div style={{ color: '#FFA500', fontSize: '0.8rem', marginTop: '0.25rem' }}>
                        Store type will be auto-populated once Manager is selected.
                      </div>
                    )}
                  </FormGroup>
                  <FormGroup>
                    <FormLabel>
                      Manager
                      {formData.role === 'staff' && <span className="required">*</span>}
                    </FormLabel>
                    <FormSelect
                      name="managerId"
                      value={formData.role === 'manager' ? '' : (formData.managerId !== null ? formData.managerId : '')}
                      onChange={(e) => {
                        // First call the regular handler
                        handleInputChange(e);

                        // Then immediately use the direct approach to update store type
                        if (formData.role === 'staff' && e.target.value) {
                          // Use setTimeout to ensure this runs after state is updated
                          setTimeout(() => {
                            updateManagerStoreType(e.target.value);
                          }, 10);
                        }
                      }}
                      required={formData.role === 'staff'}
                      disabled={isManagerFieldDisabledForOwnAccount() || loadingManagersAndStoreTypes}
                    >
                      <option value="">None</option>
                      {loadingManagersAndStoreTypes && (
                        <option value="" disabled>Loading managers</option>
                      )}
                      {!loadingManagersAndStoreTypes && managers.length === 0 && (
                        <option value="" disabled>No managers available</option>
                      )}
                      {!loadingManagersAndStoreTypes && managers.map(manager => (
                        <option key={manager.id} value={manager.id}>
                          {manager.user_name}
                        </option>
                      ))}
                    </FormSelect>
                    {formData.role === 'manager' && (
                      <div style={{ color: '#666', fontSize: '0.8rem', marginTop: '0.25rem' }}>
                        Manager field is set to None when role is Manager
                      </div>
                    )}
                    {formData.role === 'admin' && (
                      <div style={{ color: '#666', fontSize: '0.8rem', marginTop: '0.25rem' }}>
                        Manager is optional for Admin users
                      </div>
                    )}
                    {formData.role === 'staff' && (
                      <div style={{ color: '#666', fontSize: '0.8rem', marginTop: '0.25rem' }}>
                        Manager is required for Staff users
                      </div>
                    )}
                  </FormGroup>
                </FormGrid>

                <ButtonGroup>
                  <CancelButton
                    type="button"
                    onClick={() => {
                      resetFormData();
                      setShowAddModal(false);
                    }}
                    disabled={isLoading}
                  >
                    Cancel
                  </CancelButton>
                  <SubmitButton
                    type="submit"
                    disabled={isLoading || loadingManagersAndStoreTypes}
                  >
                    {isLoading ? 'Saving' : loadingManagersAndStoreTypes ? 'Loading' : 'Add User'}
                  </SubmitButton>
                </ButtonGroup>
              </form>
            </ModalContent>
          </ModalOverlay>
        )}

        {/* Edit User Modal */}
        {showEditModal && currentUser && (
          <ModalOverlay onClick={() => {
            resetFormData();
            setShowEditModal(false);
          }}>
            <ModalContent onClick={e => e.stopPropagation()}>
              <ModalTitle>Edit User: {currentUser.name}</ModalTitle>
              {error && (
                <ErrorMessage>
                  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M8 5V9M8 11.01L8.01 10.999M15 8C15 11.866 11.866 15 8 15C4.13401 15 1 11.866 1 8C1 4.13401 4.13401 1 8 1C11.866 1 15 4.13401 15 8Z" stroke="currentColor" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                  </svg>
                  <span>{error}</span>
                </ErrorMessage>
              )}
              <form key={`edit-user-form-${currentUser?.id || 'new'}`} onSubmit={handleSubmit}>
                <FormGrid>
                  <FormGroup>
                    <FormLabel>
                      User Name<span className="required">*</span>
                    </FormLabel>
                    <FormInput
                      type="text"
                      name="name"
                      value={formData.name}
                      onChange={handleInputChange}
                      required
                      disabled={isLoading}
                      placeholder="Enter user name"
                    />
                  </FormGroup>
                  <FormGroup>
                    <FormLabel>
                      Mobile Number<span className="required">*</span>
                    </FormLabel>
                    <FormInput
                      type="text"
                      name="mobile"
                      value={formData.mobile}
                      onChange={handleInputChange}
                      required
                      disabled={isLoading}
                      placeholder="Enter mobile number"
                      onKeyPress={handleMobileKeyPress}
                    />
                    <div style={{ color: '#666', fontSize: '0.8rem', marginTop: '0.25rem' }}>
                      Format: +61xxxxxxxx or 0xxxxxxxxx
                    </div>
                    {renderFieldErrors('mobile')}
                  </FormGroup>
                  <FormGroup>
                    <FormLabel>
                      Email<span className="required">*</span>
                    </FormLabel>
                    <FormInput
                      type="email"
                      name="email"
                      value={formData.email}
                      onChange={handleInputChange}
                      required
                      disabled={isEmailFieldDisabled()}
                      placeholder="Enter email address"
                      autoComplete="new-email"
                    />
                    {renderFieldErrors('email')}
                  </FormGroup>
                  <FormGroup>
                    <FormLabel>
                      Password (leave empty to keep current)
                    </FormLabel>
                    <FormInput
                      type="password"
                      name="password"
                      value={formData.password}
                      onChange={handleInputChange}
                      disabled={isPasswordFieldDisabled()}
                      placeholder="At least 8 characters"
                    />
                    {renderFieldErrors('password')}
                  </FormGroup>
                  <FormGroup>
                    <FormLabel>
                      Role<span className="required">*</span>
                    </FormLabel>
                    <FormSelect
                      name="role"
                      value={formData.role}
                      onChange={handleInputChange}
                      required
                      disabled={isRoleFieldDisabled()}
                    >
                      <option value="">Select a role</option>
                      {roles.map(role => (
                        <option key={role.value} value={role.value}>
                          {role.label}
                        </option>
                      ))}
                    </FormSelect>
                  </FormGroup>
                  <FormGroup>
                    <FormLabel>
                      Status<span className="required">*</span>
                    </FormLabel>
                    <FormSelect
                      name="status"
                      value={formData.status}
                      onChange={handleInputChange}
                      required
                      disabled={isStatusFieldDisabled()}
                    >
                      <option value="active">Active</option>
                      <option value="inactive">Inactive</option>
                    </FormSelect>
                  </FormGroup>
                  <FormGroup>
                    <FormLabel>
                      Store Type
                      {formData.role === 'manager' && <span className="required">*</span>}
                    </FormLabel>
                    <FormSelect
                      name="storeTypeId"
                      value={formData.storeTypeId || ''}
                      onChange={handleInputChange}
                      required={formData.role === 'manager'}
                      disabled={isStoreTypeFieldDisabledForOwnAccount() || loadingManagersAndStoreTypes}
                    >
                      <option value="">Select a store type</option>
                      {loadingManagersAndStoreTypes && (
                        <option value="" disabled>Loading store types</option>
                      )}
                      {!loadingManagersAndStoreTypes && storeTypes.length === 0 && (
                        <option value="" disabled>No store types available</option>
                      )}
                      {!loadingManagersAndStoreTypes && storeTypes.map(storeType => (
                        <option key={storeType.id} value={storeType.id}>
                          {storeType.name}
                        </option>
                      ))}
                    </FormSelect>
                    {formData.role === 'admin' && (
                      <div style={{ color: '#666', fontSize: '0.8rem', marginTop: '0.25rem' }}>
                        Store type is optional for Admin users.
                      </div>
                    )}
                    {formData.role === 'staff' && formData.managerId && (
                      <div style={{ color: '#4CAF50', fontSize: '0.8rem', marginTop: '0.25rem' }}>
                        Store type is auto-populated from selected Manager.
                        {formData.storeTypeId && (
                          <span style={{ fontWeight: 'bold' }}>
                            {' '}{storeTypes.find(st => st.id === formData.storeTypeId)?.name || `ID: ${formData.storeTypeId}`}
                          </span>
                        )}
                        {!formData.storeTypeId && (
                          <span style={{ color: 'red', fontWeight: 'bold' }}>
                            {' '}No store type found for this manager!
                          </span>
                        )}
                      </div>
                    )}
                    {formData.role === 'staff' && !formData.managerId && (
                      <div style={{ color: '#FFA500', fontSize: '0.8rem', marginTop: '0.25rem' }}>
                        Store type will be auto-populated once Manager is selected.
                      </div>
                    )}
                  </FormGroup>
                  <FormGroup>
                    <FormLabel>
                      Manager
                      {formData.role === 'staff' && <span className="required">*</span>}
                    </FormLabel>
                    <FormSelect
                      name="managerId"
                      value={formData.role === 'manager' ? '' : (formData.managerId !== null ? formData.managerId : '')}
                      onChange={(e) => {
                        // First call the regular handler
                        handleInputChange(e);

                        // Then immediately use the direct approach to update store type
                        if (formData.role === 'staff' && e.target.value) {
                          // Use setTimeout to ensure this runs after state is updated
                          setTimeout(() => {
                            updateManagerStoreType(e.target.value);
                          }, 10);
                        }
                      }}
                      required={formData.role === 'staff'}
                      disabled={isManagerFieldDisabledForOwnAccount() || loadingManagersAndStoreTypes}
                    >
                      <option value="">None</option>
                      {loadingManagersAndStoreTypes && (
                        <option value="" disabled>Loading managers</option>
                      )}
                      {!loadingManagersAndStoreTypes && managers.length === 0 && (
                        <option value="" disabled>No managers available</option>
                      )}
                      {!loadingManagersAndStoreTypes && managers.map(manager => (
                        <option key={manager.id} value={manager.id}>
                          {manager.user_name}
                        </option>
                      ))}
                    </FormSelect>
                    {formData.role === 'manager' && (
                      <div style={{ color: '#666', fontSize: '0.8rem', marginTop: '0.25rem' }}>
                        Manager field is set to None when role is Manager
                      </div>
                    )}
                    {formData.role === 'admin' && (
                      <div style={{ color: '#666', fontSize: '0.8rem', marginTop: '0.25rem' }}>
                        Manager is optional for Admin users
                      </div>
                    )}
                    {formData.role === 'staff' && (
                      <div style={{ color: '#666', fontSize: '0.8rem', marginTop: '0.25rem' }}>
                        Manager is required for Staff users
                      </div>
                    )}
                  </FormGroup>
                </FormGrid>

                <ButtonGroup>
                  <CancelButton
                    type="button"
                    onClick={() => {
                      resetFormData();
                      setShowEditModal(false);
                    }}
                    disabled={isLoading}
                  >
                    Cancel
                  </CancelButton>
                  <SubmitButton
                    type="submit"
                    disabled={isLoading || loadingManagersAndStoreTypes || !hasFormChanges}
                  >
                    {isLoading ? 'Saving' : loadingManagersAndStoreTypes ? 'Loading' : 'Update User'}
                  </SubmitButton>
                </ButtonGroup>
              </form>
            </ModalContent>
          </ModalOverlay>
        )}

        {/* Toast notifications */}
        {toast.isVisible && (
          <Toast
            message={toast.message}
            type={toast.type}
            onClose={() => { }}
          />
        )}
      </PageContainer>
    </Layout>
  );
};

export default UsersPage;