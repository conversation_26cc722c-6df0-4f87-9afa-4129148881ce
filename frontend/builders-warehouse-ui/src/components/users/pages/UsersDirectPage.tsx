import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { useNavigate } from 'react-router-dom';
import Layout from '../../layout/Layout';
import { useAuth } from '../../../context/AuthContext';
import { fetchUsersDirectly } from '../../../services/usersApiDirect';

const Container = styled.div`
  padding: 20px;
`;

const Header = styled.h1`
  margin-bottom: 20px;
`;

const DebugPanel = styled.div`
  margin-top: 20px;
  padding: 15px;
  background-color: #f7f9fc;
  border: 1px solid #e1e5ed;
  border-radius: 6px;
  font-family: monospace;
  font-size: 12px;
  white-space: pre-wrap;
  max-height: 500px;
  overflow-y: auto;
`;

const UserTable = styled.table`
  width: 100%;
  border-collapse: collapse;
  margin-top: 20px;
  
  th, td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: left;
  }
  
  th {
    background-color: #042B41;
    color: white;
  }
  
  tr:nth-child(even) {
    background-color: #f2f2f2;
  }
`;

// Simple interface for user data
interface User {
  id: string;
  name: string;
  email: string;
  mobile: string;
  role: string;
  status: string;
  manager: string;
  storeType: string;
}

const UsersDirectPage: React.FC = () => {
  const navigate = useNavigate();
  const { isAuthenticated } = useAuth();
  const [users, setUsers] = useState<User[]>([]);
  const [totalUsers, setTotalUsers] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [debugInfo, setDebugInfo] = useState<any>(null);

  // Fetch users directly from the API
  const fetchUsers = async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const result = await fetchUsersDirectly(0, 10);
      console.log('Fetch result:', result);
      
      setUsers(result.users);
      setTotalUsers(result.total);
      setDebugInfo(result);
    } catch (err) {
      console.error('Error fetching users:', err);
      setError('Failed to load users');
    } finally {
      setIsLoading(false);
    }
  };

  // Load users on component mount
  useEffect(() => {
    if (isAuthenticated) {
      fetchUsers();
    } else {
      navigate('/login');
    }
  // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isAuthenticated]);

  return (
    <Layout>
      <Container>
        <Header>Users List (Direct API)</Header>
        
        {error && (
          <div style={{ color: 'red', marginBottom: '20px' }}>
            Error: {error}
          </div>
        )}
        
        {isLoading ? (
          <div>Loading users</div>
        ) : (
          <>
            <UserTable>
              <thead>
                <tr>
                  <th>Name</th>
                  <th>Email</th>
                  <th>Mobile</th>
                  <th>Role</th>
                  <th>Manager</th>
                  <th>Store Type</th>
                </tr>
              </thead>
              <tbody>
                {users.length > 0 ? (
                  users.map(user => (
                    <tr key={user.id}>
                      <td>{user.name}</td>
                      <td>{user.email}</td>
                      <td>{user.mobile}</td>
                      <td>{user.role}</td>
                      <td>{user.manager}</td>
                      <td>{user.storeType}</td>
                    </tr>
                  ))
                ) : (
                  <tr>
                    <td colSpan={6} style={{ textAlign: 'center' }}>
                      No users found
                    </td>
                  </tr>
                )}
              </tbody>
            </UserTable>
            
            <div style={{ marginTop: '20px' }}>
              Total Users: {totalUsers}
            </div>
            
            <DebugPanel>
              <div><strong>Debug Info:</strong></div>
              <pre>{JSON.stringify(debugInfo, null, 2)}</pre>
              <div><strong>Users State:</strong></div>
              <pre>{JSON.stringify(users, null, 2)}</pre>
            </DebugPanel>
          </>
        )}
        
        <div style={{ marginTop: '20px' }}>
          <button onClick={fetchUsers} disabled={isLoading}>
            {isLoading ? 'Loading' : 'Refresh Users'}
          </button>
        </div>
      </Container>
    </Layout>
  );
};

export default UsersDirectPage; 