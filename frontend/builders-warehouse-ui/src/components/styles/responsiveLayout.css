/* Global responsive styles */

/* Base responsive layout adjustments */
* {
  box-sizing: border-box;
}

body, html {
  margin: 0;
  padding: 0;
  overflow-x: hidden; /* Prevent horizontal scrolling */
  height: 100%;
}

#root {
  height: 100%;
}

/* Make images responsive */
img {
  max-width: 100%;
  height: auto;
}

/* Home page specific styles only - prevent affecting other pages */
body.home-page {
  overflow: hidden;
}

body.home-page .layout-content {
  padding: 0 !important;
  height: 100vh;
  overflow: hidden;
}

/* Home page tile positioning and dimensions */
.tile-card {
  position: relative;
  transition: transform 0.2s ease;
  margin: 0;
}

/* Reset default positioning for tiles */
.tile-card:nth-child(n) {
  position: static;
  top: auto;
  margin-top: 0;
}

/* Main grid layout adjustments - Apply to home page only */
.home-content .main-grid {
  display: grid;
  width: 100%;
  margin: 0;
  height: 100%;
}

/* Layout adjustments for homepage only at different screen sizes */
@media (min-width: 1400px) {
  .home-content .main-grid {
    grid-template-columns: repeat(5, 1fr);
  }
}

@media (max-width: 1200px) {
  .home-content .main-grid {
    grid-template-columns: repeat(4, 1fr);
  }
}

@media (max-width: 900px) {
  .home-content .main-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

@media (max-width: 600px) {
  .home-content .main-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

/* Remove any scrollbars on container elements */
.no-scrollbar {
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.no-scrollbar::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

/* Responsive styling for Global Nav */
@media (max-width: 920px) {
  /* Adjust search container for smaller screens */
  .global-search-input {
    width: 100%;
    max-width: 400px;
  }
  
  /* Adjust logo for smaller screens */
  .logo-image {
    width: 140px;
    height: auto;
  }
}

@media (max-width: 768px) {
  /* Further adjustments for even smaller screens */
  .global-search-input {
    max-width: 300px;
  }
  
  /* Stack elements in nav if needed */
  .responsive-stack {
    flex-direction: column;
    align-items: flex-start;
  }
  
  /* Adjust spacing for stacked elements */
  .responsive-stack > * {
    margin-bottom: 10px;
  }
}

/* Adjust main grid layout for different screen sizes */
@media (max-width: 1400px) {
  .main-grid {
    padding: 10px;
    gap: 12px;
  }
}

@media (max-width: 600px) {
  .main-grid {
    padding: 5px;
    gap: 8px;
  }
  
  /* Stack tiles vertically on very small screens */
  .tile-card {
    width: 100%;
    margin: 5px 0;
  }
}

/* Ensure sidebar is always fully visible */
@media (max-height: 600px) {
  .sidebar-container {
    overflow-y: auto;
  }
  
  .sidebar-menu-list {
    max-height: calc(100vh - 200px);
    overflow-y: auto;
  }
}

/* Ensure no content overflows on any device */
@media (max-width: 480px) {
  body.home-page {
    font-size: 14px;
  }
}

@media (max-height: 500px) {
  body.home-page {
    font-size: 12px;
  }
} 