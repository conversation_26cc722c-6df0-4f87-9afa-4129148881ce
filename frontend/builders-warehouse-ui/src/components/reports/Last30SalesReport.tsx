import React, { useState, useEffect, useRef } from 'react';
import { Bar } from 'react-chartjs-2';
import { Chart as ChartJS, CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend } from 'chart.js';
import DownloadCSVButton from './DownloadCSVButton';
import { API_URL, ENDPOINTS, AUTH_TOKEN_KEY } from '../../config';

// Register ChartJS components
ChartJS.register(CategoryScale, LinearScale, BarElement, Title, Tooltip, Legend);

interface SalesData {
  date: string;
  amount: number;
  invoiceCount: number;
}

interface Last30SalesReportProps {
  isLoading?: boolean;
}

const Last30SalesReport: React.FC<Last30SalesReportProps> = ({ isLoading: propIsLoading = false }) => {
  const [salesData, setSalesData] = useState<SalesData[]>([]);
  const [totalSales, setTotalSales] = useState(0);
  const [averageSale, setAverageSale] = useState(0);
  const [invoiceCount, setInvoiceCount] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const apiCallInProgress = useRef<boolean>(false);

  // Fetch real sales data for the last 30 days
  useEffect(() => {
    const fetchSalesData = async () => {
      // Skip if already loading or API call in progress
      if (propIsLoading || apiCallInProgress.current) {
        return;
      }
      
      setIsLoading(true);
      apiCallInProgress.current = true;
      
      try {
        // Set up date range for the last 30 days
        const endDate = new Date();
        const startDate = new Date();
        startDate.setDate(endDate.getDate() - 30);
        
        const formattedStartDate = startDate.toISOString().split('T')[0];
        const formattedEndDate = endDate.toISOString().split('T')[0];
        
        // Get auth token
        const token = localStorage.getItem(AUTH_TOKEN_KEY);
        if (!token) {
          throw new Error('Authentication token not found');
        }
        
        // Construct API endpoint with date range
        const endpoint = `${API_URL}${ENDPOINTS.INVOICES}?start_date=${formattedStartDate}&end_date=${formattedEndDate}&limit=100`;
        
        console.log('Fetching last 30 days sales data from:', endpoint);
        
        // Set a timeout to prevent the request from hanging indefinitely
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), 30000); // 30 second timeout
        
        const response = await fetch(endpoint, {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          signal: controller.signal
        });
        
        // Clear the timeout since the request completed
        clearTimeout(timeoutId);
        
        if (!response.ok) {
          throw new Error(`API request failed with status ${response.status}`);
        }
        
        const data = await response.json();
        console.log('Sales data response:', data);
        
        if (!data || !data.items || !Array.isArray(data.items)) {
          throw new Error('Invalid response format from API');
        }
        
        // Process the data to group by date
        const dateMap = new Map<string, {amount: number, count: number}>();
        let total = 0;
        let count = 0;
        
        // Group invoices by date
        data.items.forEach((invoice: any) => {
          if (!invoice.date) return;
          
          const date = invoice.date.split('T')[0];
          const amount = parseFloat(invoice.grand_total || invoice.total_amount || '0');
          
          if (isNaN(amount)) return;
          
          if (dateMap.has(date)) {
            const existing = dateMap.get(date)!;
            dateMap.set(date, {
              amount: existing.amount + amount,
              count: existing.count + 1
            });
          } else {
            dateMap.set(date, { amount, count: 1 });
          }
          
          total += amount;
          count += 1;
        });
        
        // Fill in any missing dates in the 30-day range
        const result: SalesData[] = [];
        for (let i = 0; i < 30; i++) {
          const date = new Date(startDate);
          date.setDate(startDate.getDate() + i);
          const dateString = date.toISOString().split('T')[0];
          
          const dayData = dateMap.get(dateString);
          result.push({
            date: dateString,
            amount: dayData ? dayData.amount : 0,
            invoiceCount: dayData ? dayData.count : 0
          });
        }
        
        // Sort data by date
        result.sort((a, b) => a.date.localeCompare(b.date));
        
        setSalesData(result);
        setTotalSales(total);
        setInvoiceCount(count);
        setAverageSale(count > 0 ? total / count : 0);
        setError(null);
        
      } catch (err) {
        console.error('Error fetching sales data:', err);
        setError(err instanceof Error ? err.message : 'Failed to load sales data');
        
        // If no data was loaded, provide empty data structure
        if (salesData.length === 0) {
          // Generate empty data for the last 30 days
          const emptyData: SalesData[] = [];
          const startDate = new Date();
          startDate.setDate(startDate.getDate() - 29);
          
          for (let i = 0; i < 30; i++) {
            const date = new Date(startDate);
            date.setDate(startDate.getDate() + i);
            
            emptyData.push({
              date: date.toISOString().split('T')[0],
              amount: 0,
              invoiceCount: 0
            });
          }
          
          setSalesData(emptyData);
          setTotalSales(0);
          setInvoiceCount(0);
          setAverageSale(0);
        }
      } finally {
        setIsLoading(false);
        apiCallInProgress.current = false;
      }
    };
    
    fetchSalesData();
  }, [propIsLoading]);

  // Prepare data for the chart
  const chartData = {
    labels: salesData.map(item => item.date),
    datasets: [
      {
        label: 'Daily Sales ($)',
        data: salesData.map(item => item.amount),
        backgroundColor: 'rgba(53, 162, 235, 0.5)',
        borderColor: 'rgb(53, 162, 235)',
        borderWidth: 1,
      },
    ],
  };

  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        position: 'top' as const,
      },
      title: {
        display: true,
        text: 'Last 30 Days Sales',
      },
    },
    scales: {
      y: {
        beginAtZero: true,
        title: {
          display: true,
          text: 'Amount ($)',
        },
      },
      x: {
        title: {
          display: true,
          text: 'Date',
        },
      },
    },
  };

  // Function to prepare data for CSV export
  const prepareCSVData = () => {
    return salesData.map(item => ({
      Date: item.date,
      'Sales Amount': item.amount.toFixed(2),
      'Invoice Count': item.invoiceCount,
    }));
  };

  return (
    <div className="bg-white p-6 rounded-lg shadow">
      <div className="flex justify-between items-center mb-6">
        <h3 className="text-xl font-semibold text-gray-800">Last 30 Days Sales Report</h3>
        <DownloadCSVButton 
          data={prepareCSVData()} 
          filename="last_30_days_sales.csv"
          disabled={isLoading || propIsLoading || salesData.length === 0}
        />
      </div>

      {isLoading || propIsLoading ? (
        <div className="h-80 flex items-center justify-center">
          <p className="text-gray-500">Loading sales data</p>
        </div>
      ) : error ? (
        <div className="h-80 flex items-center justify-center">
          <p className="text-red-500">{error}</p>
          <button 
            className="ml-4 px-4 py-2 bg-blue-500 text-white rounded"
            onClick={() => {
              apiCallInProgress.current = false;
              setIsLoading(true);
              window.location.reload();
            }}
          >
            Retry
          </button>
        </div>
      ) : (
        <>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div className="bg-blue-50 p-4 rounded-lg">
              <p className="text-sm text-blue-600 font-medium">Total Sales</p>
              <p className="text-2xl font-bold text-blue-800">${totalSales.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}</p>
            </div>
            <div className="bg-green-50 p-4 rounded-lg">
              <p className="text-sm text-green-600 font-medium">Invoices Generated</p>
              <p className="text-2xl font-bold text-green-800">{invoiceCount}</p>
            </div>
            <div className="bg-purple-50 p-4 rounded-lg">
              <p className="text-sm text-purple-600 font-medium">Average Sale</p>
              <p className="text-2xl font-bold text-purple-800">${averageSale.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}</p>
            </div>
          </div>

          <div className="h-80">
            <Bar data={chartData} options={chartOptions} />
          </div>
        </>
      )}
    </div>
  );
};

export default Last30SalesReport; 