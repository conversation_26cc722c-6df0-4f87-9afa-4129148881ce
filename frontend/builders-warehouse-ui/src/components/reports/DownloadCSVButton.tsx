import React from 'react';
import Button from '../common/Button';

interface DownloadCSVButtonProps {
  data: Record<string, string | number>[];
  filename: string;
  disabled?: boolean;
  label?: string;
  className?: string;
}

const DownloadCSVButton: React.FC<DownloadCSVButtonProps> = ({
  data,
  filename,
  disabled = false,
  label = 'Download CSV',
  className = '',
}) => {
  const generateCSV = () => {
    if (!data || data.length === 0) return '';

    // Get headers from the first object
    const headers = Object.keys(data[0]);
    
    // Create CSV header row
    const csvRows = [headers.join(',')];
    
    // Add data rows
    for (const row of data) {
      const values = headers.map(header => {
        const value = row[header];
        // Wrap values in quotes if they contain commas or quotes
        const escaped = String(value).replace(/"/g, '""');
        return `"${escaped}"`;
      });
      csvRows.push(values.join(','));
    }
    
    return csvRows.join('\n');
  };

  const downloadCSV = () => {
    if (disabled || !data || data.length === 0) return;
    
    const csvContent = generateCSV();
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', filename);
    link.style.visibility = 'hidden';
    
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  return (
    <Button
      onClick={downloadCSV}
      disabled={disabled}
      variant="secondary"
      className={className}
    >
      <svg 
        xmlns="http://www.w3.org/2000/svg" 
        className="h-5 w-5 mr-2" 
        fill="none" 
        viewBox="0 0 24 24" 
        stroke="currentColor"
      >
        <path 
          strokeLinecap="round" 
          strokeLinejoin="round" 
          strokeWidth={2} 
          d="M4 16v1a3 3 0 003 3h10a3 3 0 003-3v-1m-4-4l-4 4m0 0l-4-4m4 4V4" 
        />
      </svg>
      {label}
    </Button>
  );
};

export default DownloadCSVButton; 