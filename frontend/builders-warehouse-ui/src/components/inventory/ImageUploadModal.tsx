import React, { useState, useRef } from 'react';
import styled from 'styled-components';
import { useToast } from '../../hooks/useToast';
import inventoryService, { ImageUploadResult } from '../../services/inventoryService';
import { markSkusAsRecentlyUploaded } from '../common/SkuImage';

interface ImageUploadModalProps {
  onClose: () => void;
  onSuccess: () => void;
}

const Modal = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
`;

const ModalContent = styled.div`
  background-color: white;
  padding: 32px;
  border-radius: 12px;
  width: 600px;
  max-width: 90%;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
`;

const Title = styled.h2`
  font-size: 24px;
  font-weight: 700;
  color: #042B41;
  margin: 0 0 24px 0;
  text-align: center;
`;

const UploadArea = styled.div`
  border: 2px dashed #ccc;
  border-radius: 8px;
  padding: 32px;
  text-align: center;
  margin-bottom: 24px;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    border-color: #042B41;
    background-color: #f9fafb;
  }
`;

const UploadText = styled.p`
  font-size: 16px;
  color: #666;
  margin-bottom: 16px;
`;

const FileList = styled.div`
  margin-top: 16px;
  text-align: left;
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #eee;
  border-radius: 6px;
  padding: 8px;
`;

const FileItem = styled.div`
  font-size: 14px;
  padding: 6px 10px;
  margin-bottom: 4px;
  border-radius: 4px;
  background-color: #f0f5ff;
  color: #042B41;
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const RemoveButton = styled.button`
  background: none;
  border: none;
  color: #ff4d4f;
  cursor: pointer;
  font-size: 16px;
  
  &:hover {
    opacity: 0.8;
  }
`;

const ButtonGroup = styled.div`
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 16px;
`;

const Button = styled.button`
  padding: 12px 0;
  width: 180px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &.secondary {
    background-color: white;
    color: #333;
    border: 1px solid #d0d0d0;
    
    &:hover {
      background-color: #f5f5f5;
    }
  }
  
  &.primary {
    background-color: #042B41;
    color: white;
    border: 1px solid #042B41;
    
    &:hover {
      background-color: #03223A;
    }
    
    &:disabled {
      background-color: #89a2b3;
      border-color: #89a2b3;
      cursor: not-allowed;
    }
  }
`;

const FileInput = styled.input`
  display: none;
`;

const ResultContainer = styled.div`
  margin-top: 24px;
  border-top: 1px solid #eee;
  padding-top: 24px;
`;

const ResultTitle = styled.h3`
  font-size: 18px;
  font-weight: 600;
  color: #042B41;
  margin-bottom: 16px;
`;

const SuccessMessage = styled.div`
  background-color: #f6ffed;
  border: 1px solid #b7eb8f;
  color: #52c41a;
  padding: 10px 16px;
  border-radius: 6px;
  margin-bottom: 16px;
`;

const ErrorContainer = styled.div`
  max-height: 200px;
  overflow-y: auto;
  margin-top: 8px;
`;

const ErrorItem = styled.div`
  background-color: #fff2f0;
  border: 1px solid #ffccc7;
  color: #ff4d4f;
  padding: 10px 16px;
  border-radius: 6px;
  margin-bottom: 8px;
`;

const InfoText = styled.div`
  margin-top: 24px;
  font-size: 14px;
  color: #666;
  
  p {
    margin-bottom: 8px;
  }
  
  strong {
    color: #042B41;
  }
`;

const ImageUploadModal: React.FC<ImageUploadModalProps> = ({ onClose, onSuccess }) => {
  const [files, setFiles] = useState<File[]>([]);
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<ImageUploadResult | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const toast = useToast();

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const selectedFiles = Array.from(e.target.files);

      // Validate each file for image type
      const validFiles = selectedFiles.filter(file => {
        const fileType = file.type.toLowerCase();
        return fileType.startsWith('image/');
      });

      if (validFiles.length !== selectedFiles.length) {
        toast.showToast('Only image files are allowed', { type: 'error' });
      }

      // Limit to 25 files max
      const newFiles = [...files, ...validFiles].slice(0, 25);
      setFiles(newFiles);
      setResult(null); // Clear previous results

      // Reset the input to allow selecting the same file again
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }
    }
  };

  const handleRemoveFile = (index: number) => {
    const newFiles = [...files];
    newFiles.splice(index, 1);
    setFiles(newFiles);
  };

  const handleUploadClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const handleUpload = async () => {
    if (files.length === 0) return;

    try {
      setLoading(true);
      console.log('Files to upload:', files.map(f => ({ name: f.name, size: f.size, type: f.type })));

      // Create FormData for debugging
      const formData = new FormData();
      files.forEach((file) => {
        formData.append("files", file);
      });

      console.log('FormData entries:');
      Array.from(formData.entries()).forEach(([key, value]) => {
        console.log(key, value);
      });

      // Upload the files
      const result = await inventoryService.uploadSkuImages(files);
      setResult(result);

      // Clear inventory cache and refresh specific SKUs to ensure fresh data is loaded with new images
      if (result.success.length > 0) {
        inventoryService.clearInventoryCache();

        // Also refresh the specific SKUs that were uploaded
        const uploadedSkus = result.success.map(item => item.sku);
        await inventoryService.refreshInventoryForSkus(uploadedSkus);

        // Mark SKUs as recently uploaded for cache busting
        markSkusAsRecentlyUploaded(uploadedSkus);
      }

      // Show appropriate toast
      if (result.success.length > 0 && result.errors.length === 0) {
        toast.showToast(`Successfully uploaded ${result.success.length} images`, { type: 'success' });
        setTimeout(() => {
          onSuccess();
        }, 2000);
      } else if (result.success.length > 0) {
        toast.showToast(`Uploaded ${result.success.length} images with ${result.errors.length} errors`, { type: 'info' });
      } else {
        toast.showToast('Upload failed. Please check the errors and try again.', { type: 'error' });
      }
    } catch (error) {
      console.error('Error uploading images:', error);
      toast.showToast(
        error instanceof Error ? error.message : 'Error uploading images',
        { type: 'error' }
      );
    } finally {
      setLoading(false);
    }
  };

  return (
    <Modal onClick={onClose}>
      <ModalContent onClick={(e) => e.stopPropagation()}>
        <Title>Upload SKU Images</Title>

        <UploadArea onClick={handleUploadClick}>
          <UploadText>Click or drag files to this area to upload</UploadText>
          <UploadText>Support for JPG, PNG, GIF image files</UploadText>
          <FileInput
            type="file"
            ref={fileInputRef}
            onChange={handleFileChange}
            accept="image/*"
            multiple
          />
          {files.length > 0 && (
            <FileList>
              {files.map((file, index) => (
                <FileItem key={index}>
                  {file.name}
                  <RemoveButton onClick={(e) => {
                    e.stopPropagation();
                    handleRemoveFile(index);
                  }}>
                    ×
                  </RemoveButton>
                </FileItem>
              ))}
            </FileList>
          )}
        </UploadArea>

        <ButtonGroup>
          <Button className="secondary" onClick={onClose}>
            Cancel
          </Button>
          <Button
            className="primary"
            onClick={handleUpload}
            disabled={files.length === 0 || loading}
          >
            {loading ? 'Uploading' : 'Upload'}
          </Button>
        </ButtonGroup>

        {result && (
          <ResultContainer>
            <ResultTitle>Upload Results</ResultTitle>
            {result.success.length > 0 && (
              <SuccessMessage>Successfully uploaded {result.success.length} images</SuccessMessage>
            )}

            {result.errors.length > 0 && (
              <>
                <ErrorItem>Failed to upload {result.errors.length} images</ErrorItem>
                <ErrorContainer>
                  {result.errors.map((error, index) => (
                    <ErrorItem key={index}>
                      {error.file}: {error.error}
                    </ErrorItem>
                  ))}
                </ErrorContainer>
              </>
            )}
          </ResultContainer>
        )}

        <InfoText>
          <p><strong>Important:</strong> Image filenames must match SKU codes exactly (e.g., BB69033.png)</p>
          <p>Images will be automatically mapped to the corresponding SKU items in the inventory</p>
          <p>Supported formats: JPG, PNG, GIF</p>
          <p>Maximum 25 images can be uploaded at once</p>
        </InfoText>
      </ModalContent>
    </Modal>
  );
};

export default ImageUploadModal; 