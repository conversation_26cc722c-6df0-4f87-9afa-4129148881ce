import React, { useState, useEffect } from 'react';
import Table from '../common/Table';
import SearchBar from '../common/SearchBar';
import Tabs from '../common/Tabs';
import LoadingSpinner from '../ui/LoadingSpinner';
import EmptyState from '../ui/EmptyState';
import PageLayout from '../layout/PageLayout';
import inventoryService, { Inventory as ApiInventoryItem } from '../../services/inventoryService';
import styled from 'styled-components';
import { toast } from 'react-toastify';
// Import auth service if needed for login
// import { login } from '../../services/api';

const DashboardContainer = styled.div`
  padding: 0 24px;
`;

interface InventoryItem {
  id: string;
  sku: string;
  name: string;
  category: string;
  inStock: number;
  price: number;
  costPrice: number;
  supplier: string;
  location: string;
  reorderPoint: number;
  carton: string;
  unitsPerCarton: number;
  cartonLength: number;
  cartonBreadth: number;
  cartonHeight: number;
  weightPerUnit: number;
  weightPerCarton: number;
}

interface InventoryDashboardProps {
  isLoading?: boolean;
}

const InventoryDashboard: React.FC<InventoryDashboardProps> = ({ isLoading: propIsLoading = false }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [inventoryItems, setInventoryItems] = useState<InventoryItem[]>([]);
  const [filteredItems, setFilteredItems] = useState<InventoryItem[]>([]);
  const [activeTab, setActiveTab] = useState('all');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch data from API
  useEffect(() => {
    const fetchInventory = async () => {
      setLoading(true);
      setError(null);
      
      try {
        // Use the inventoryService which handles authentication automatically
        const apiInventoryItems = await inventoryService.getInventoryItems();
        
        // Transform API data to match our interface
        const transformedData: InventoryItem[] = apiInventoryItems.map(item => ({
          id: item.id.toString(),
          sku: item.sku_code,
          name: item.style_code, // Using style_code as name since there's no name field
          category: item.category || item.description || 'N/A', // Use category or description
          inStock: item.stock_on_hand || 0, // Use actual stock on hand if available
          price: item.retail_price || 0,
          costPrice: item.cost_price || 0,
          supplier: item.supplier_name || 'Unknown',
          location: item.notes || 'Not specified',
          reorderPoint: 20, // Default reorder point
          carton: item.carton || '1',
          unitsPerCarton: item.units_per_carton || 0,
          cartonLength: item.carton_length || 0,
          cartonBreadth: item.carton_breadth || 0,
          cartonHeight: item.carton_height || 0,
          weightPerUnit: item.weight_per_unit || 0,
          weightPerCarton: item.weight_per_carton || 0
        }));
        
        setInventoryItems(transformedData);
        setFilteredItems(transformedData);
      } catch (err) {
        console.error('Error fetching inventory data:', err);
        const errorMessage = 'Failed to load inventory items. The API might be unavailable.';
        setError(errorMessage);
        toast.error(errorMessage);
        
        // Set empty arrays when API fails
        setInventoryItems([]);
        setFilteredItems([]);
      } finally {
        setLoading(false);
      }
    };
    
    fetchInventory();
  }, []);

  // Search functionality
  useEffect(() => {
    if (!searchQuery.trim()) {
      setFilteredItems(inventoryItems);
      return;
    }

    const lowercasedQuery = searchQuery.toLowerCase();
    const filtered = inventoryItems.filter(item => 
      item.name.toLowerCase().includes(lowercasedQuery) ||
      item.sku.toLowerCase().includes(lowercasedQuery) ||
      item.category.toLowerCase().includes(lowercasedQuery) ||
      item.supplier.toLowerCase().includes(lowercasedQuery)
    );
    
    setFilteredItems(filtered);
  }, [searchQuery, inventoryItems]);

  // Columns for the inventory table
  const columns = [
    { header: 'SKU', accessor: 'sku' as keyof InventoryItem },
    { header: 'Name', accessor: 'name' as keyof InventoryItem },
    { header: 'Category', accessor: 'category' as keyof InventoryItem },
    { 
      header: 'In Stock', 
      accessor: 'inStock' as keyof InventoryItem,
      cell: (value: number) => (
        <span className={`${value <= 10 ? 'text-red-600 font-bold' : value <= 25 ? 'text-yellow-600' : 'text-gray-900'}`}>
          {value}
        </span>
      )
    },
    { 
      header: 'Price', 
      accessor: 'price' as keyof InventoryItem,
      cell: (value: number) => `$${value.toFixed(2)}`
    },
    { header: 'Supplier', accessor: 'supplier' as keyof InventoryItem },
    { header: 'Carton', accessor: 'carton' as keyof InventoryItem },
    { header: 'Units Per Carton', accessor: 'unitsPerCarton' as keyof InventoryItem },
    { 
      header: 'Dimensions (LxBxH)', 
      accessor: 'cartonLength' as keyof InventoryItem,
      cell: (value: number, row: InventoryItem) => 
        `${row.cartonLength} x ${row.cartonBreadth} x ${row.cartonHeight}`
    },
    { 
      header: 'Weight Per Unit', 
      accessor: 'weightPerUnit' as keyof InventoryItem,
      cell: (value: number) => `${value} kg`
    },
    { 
      header: 'Weight Per Carton', 
      accessor: 'weightPerCarton' as keyof InventoryItem,
      cell: (value: number) => `${value} kg`
    },
    { header: 'Location', accessor: 'location' as keyof InventoryItem },
    { 
      header: 'Status', 
      accessor: 'inStock' as keyof InventoryItem,
      cell: (value: number, row: InventoryItem) => (
        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
          value === 0 
            ? 'bg-red-100 text-red-800' 
            : value <= row.reorderPoint 
              ? 'bg-yellow-100 text-yellow-800' 
              : 'bg-green-100 text-green-800'
        }`}>
          {value === 0 
            ? 'Out of Stock' 
            : value <= row.reorderPoint 
              ? 'Low Stock' 
              : 'In Stock'}
        </span>
      )
    },
  ];

  const handleRefresh = () => {
    window.location.reload();
  };

  return (
    <PageLayout backButtonLabel="Back to Dashboard">
      <Tabs
        tabs={[
          {
            id: 'all',
            label: 'All Items',
            content: (
              <>
                <div className="mb-4">
                  <SearchBar 
                    value={searchQuery} 
                    onChange={setSearchQuery} 
                    placeholder="Search by name, SKU, category, or supplier"
                  />
                </div>
                
                {loading || propIsLoading ? (
                  <div className="py-12">
                    <LoadingSpinner size="lg" />
                  </div>
                ) : error ? (
                  <EmptyState
                    title="Error Loading Inventory"
                    description={error}
                    actionText="Try Again"
                    onAction={handleRefresh}
                  />
                ) : filteredItems.length === 0 ? (
                  <EmptyState
                    title="No Items Found"
                    description={searchQuery ? "No inventory items match your search criteria." : "No inventory items found. The database might be empty."}
                    actionText={searchQuery ? "Clear Search" : "Try Again"}
                    onAction={searchQuery ? () => setSearchQuery('') : handleRefresh}
                  />
                ) : (
                  <Table<InventoryItem>
                    columns={columns}
                    data={filteredItems}
                    onRowClick={(item: InventoryItem) => console.log('Clicked item:', item)}
                  />
                )}
              </>
            ),
          },
        ]}
        activeTab={activeTab}
        onTabChange={setActiveTab}
      />
    </PageLayout>
  );
};

export default InventoryDashboard; 