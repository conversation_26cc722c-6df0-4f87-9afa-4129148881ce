import React, { useState, useEffect, useRef } from 'react';
import styled from 'styled-components';
import Layout from '../../layout/Layout';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import Pagination from '../../common/Pagination';
import SearchBar from '../../common/SearchBar';
import inventoryService, { Inventory, InventoryFilter, InventoryUpdate, SkuImageInfo } from '../../../services/inventoryService';
import { useError } from '../../common/ErrorHandler';
import withErrorHandling, { WithErrorHandlingProps } from '../../../utils/withErrorHandling';
import notesIcon from '../../../assets/notesIcon.png';
import backButtonIcon from '../../../assets/backButton.png';
import customerAddIcon from '../../../assets/customerAddIcon.png';
import supplierBulkUploadIcon from '../../../assets/supplierBulkUploadIcon.png';
import imageUploadIcon from '../../../assets/supplierBulkUploadIcon.png';
import { InventoryItem } from '../../../types/inventory';
import { useToast } from '../../../hooks/useToast';
import { BackButtonComponent } from '../../ui/DesignSystem';
import BulkUploadModal from '../BulkUploadModal';
import ImageUploadModal from '../ImageUploadModal';
import PageLoadingSpinner from '../../ui/PageLoadingSpinner';
import { Toast } from '../../common/Toast';
import LoadingSpinner from '../../ui/LoadingSpinner';

const PageWrapper = styled.div`
  background: white;
  margin: 0;
  min-height: calc(100vh - 64px);
  display: flex;
  flex-direction: column;
  padding: 0 32px;
`;

const ContentContainer = styled.div`
  padding: 0 0 32px 0;
  flex: 1;
`;

const TopSection = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
`;

const TitleSection = styled.div`
  display: flex;
  align-items: center;
  gap: 10px;
`;

const BackButton = styled(Link)`
  display: flex;
  align-items: center;
  text-decoration: none;
  color: #042B41;
  font-weight: 500;
  
  img {
    width: 24px;
    height: 24px;
    margin-right: 8px;
  }
`;

const PageContainer = styled.div`
  padding: 0rem;
`;

const PageTitle = styled.h1`
  color: #042B41;
  font-size: 32px;
  font-weight: 800;
  margin: 0;
  display: inline-flex;
  align-items: center;
`;

const HeaderContainer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
`;

const TableContainer = styled.div`
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin-bottom: 1.5rem;
`;

const AddButton = styled.button`
  display: flex;
  align-items: center;
  gap: 10px;
  background-color: #042B41;
  color: white;
  border: none;
  border-radius: 10px;
  padding: 12px 20px;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  
  &:hover {
    background-color: #0A3D5A;
  }
  
  img {
    width: 18px;
    height: 18px;
  }
`;

const SearchContainer = styled.div`
  position: relative;
  width: 250px;
`;

const SearchInput = styled.input`
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #E5E7EB;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  height: 36px;
  box-sizing: border-box;
  
  &::placeholder {
    color: #9CA3AF;
  }
`;

const SearchIcon = styled.span`
  position: absolute;
  right: 10px;
  top: 50%;
  transform: translateY(-50%);
  color: #9CA3AF;
`;

const FilterInfo = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 1rem;
  font-size: 14px;
  color: #666;
`;

const FilterTag = styled.div`
  display: inline-flex;
  align-items: center;
  background-color: #f0f5ff;
  color: #042B41;
  border: 1px solid #d0e0ff;
  border-radius: 4px;
  padding: 0.3rem 0.6rem;
  margin-left: 0.5rem;
  font-size: 13px;
`;

const ClearFilterButton = styled.button`
  background: none;
  border: none;
  cursor: pointer;
  margin-left: 6px;
  padding: 2px;
  color: #666;
  display: flex;
  align-items: center;
  justify-content: center;
  
  &:hover {
    color: #333;
  }
`;

const Table = styled.table`
  width: 100%;
  border-collapse: collapse;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  border: 1px solid #e0e0e0;
`;

const CompactHeader = styled.th`
  padding: 6px 8px !important;
  font-size: 12px;
  font-weight: 500;
  text-align: center !important;
  width: auto !important;
  white-space: nowrap;
  height: 32px;
  vertical-align: middle;
  border-right: 1px solid #0A3D5A;
  border-left: 1px solid #0A3D5A;
  
  &:last-child {
    border-right: none;
  }
`;

const TableHeader = styled.thead`
  background-color: #042B41;
  color: white;
  font-family: 'Avenir', sans-serif;
  
  th {
    padding: 8px 10px;
    text-align: left;
    font-weight: 500;
    border-right: 1px solid #0A3D5A;
    border-left: 1px solid #0A3D5A;
    height: 40px;
    vertical-align: middle;
    font-size: 13px;
    
    &:last-child {
      border-right: none;
    }
  }
`;

const TableBody = styled.tbody`
  tr {
    border-bottom: 1px solid #e0e0e0;
    height: 44px;

    &:last-child {
      border-bottom: none;
    }

    &:hover {
      background-color: #f9fafb;
    }
  }

  td {
    padding: 8px 10px;
    color: #333;
    border: 1px solid #e0e0e0;
    text-align: left;
    vertical-align: middle;
    font-size: 13px;
  }
`;

const NotesIcon = styled.img<{ hasNotes?: boolean }>`
  width: 18px;
  height: 18px;
  cursor: pointer;
  margin: 0 auto;
  display: block;
  padding: 3px;
  filter: ${props => props.hasNotes ? 'invert(13%) sepia(95%) saturate(5984%) hue-rotate(358deg) brightness(96%) contrast(115%)' : 'none'};
  
  &:hover {
    opacity: 0.8;
  }
`;

const Modal = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
`;

const ModalContent = styled.div`
  background-color: white;
  padding: 24px;
  border-radius: 8px;
  width: 500px;
  max-width: 90%;
  display: flex;
  flex-direction: column;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
`;

const ModalTitle = styled.h2`
  font-size: 20px;
  margin: 0 0 20px 0;
  text-align: center;
  color: #333;
  font-weight: 600;
`;

const TextArea = styled.textarea`
  width: 100%;
  min-height: 120px;
  padding: 12px;
  border: 1px solid #d0d0d0;
  border-radius: 4px;
  margin-bottom: 24px;
  font-family: inherit;
  font-size: 16px;
  resize: none;
`;

const ButtonGroup = styled.div`
  display: flex;
  justify-content: center;
  gap: 16px;
`;

const Button = styled.button`
  padding: 10px 0;
  width: 180px;
  border-radius: 4px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &.secondary {
    background-color: white;
    color: #333;
    border: 1px solid #d0d0d0;
    
    &:hover {
      background-color: #f5f5f5;
    }
  }
  
  &.primary {
    background-color: #042B41;
    color: white;
    border: 1px solid #042B41;
    
    &:hover {
      background-color: #03223A;
    }
  }
`;

const BulkUploadButton = styled.button`
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: #042B41;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  height: 36px;
  
  &:hover {
    background-color: #0A3D5A;
  }
  
  img {
    width: 16px;
    height: 16px;
  }
`;

const ButtonsContainer = styled.div`
  display: flex;
  gap: 10px;
`;

const SearchActionsContainer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
`;

// Remove skeleton components and replace with consistent loading
const LoadingRow = styled.tr`
  td {
    text-align: center !important;
    padding: 40px 20px !important;
    border: none !important;
  }
`;

const LoadingContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 12px;
`;

const LoadingText = styled.p`
  margin: 0;
  font-size: 14px;
  color: #6B7280;
  font-weight: 500;
`;

const ActionButtonsContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 6px;
`;

const SkuImage = styled.img`
  width: 40px;
  height: 40px;
  object-fit: contain;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
  background-color: #f5f5f5;
`;

const PlaceholderImage = styled.div`
  width: 40px;
  height: 40px;
  border-radius: 4px;
  border: 1px solid #e0e0e0;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  color: #999;
  text-align: center;
`;

const ErrorContainer = styled.div`
  background-color: #fff2f0;
  border: 1px solid #ffccc7;
  color: #ff4d4f;
  padding: 16px;
  border-radius: 6px;
  margin-bottom: 16px;
`;

interface InventoryPageProps { }

interface InventoryWithImage extends Inventory {
  imageInfo?: SkuImageInfo;
}

const InventoryPage: React.FC<InventoryPageProps & WithErrorHandlingProps> = ({ handleApiCall }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [inventory, setInventory] = useState<InventoryWithImage[]>([]);
  const [totalItems, setTotalItems] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [filter, setFilter] = useState<InventoryFilter>({
    skip: 0,
    limit: 10,
  });
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [noteText, setNoteText] = useState('');
  const [selectedItem, setSelectedItem] = useState<Inventory | null>(null);
  const [isBulkUploadModalOpen, setIsBulkUploadModalOpen] = useState(false);
  const [isImageUploadModalOpen, setIsImageUploadModalOpen] = useState(false);

  // State to track which field to search in
  const [searchField, setSearchField] = useState<'all' | 'sku_code' | 'style_code' | 'supplier_name'>('all');
  const location = useLocation();
  const navigate = useNavigate();

  // Add a ref to track API calls in progress
  const apiCallInProgress = useRef<boolean>(false);

  const toast = useToast();

  // Check URL parameters for search field and search query
  useEffect(() => {
    const searchParams = new URLSearchParams(location.search);
    const searchField = searchParams.get('searchField');
    const searchValue = searchParams.get('search');

    // Update search field
    if (searchField === 'sku_code' || searchField === 'style_code' || searchField === 'supplier_name') {
      setSearchField(searchField);
    } else {
      setSearchField('all');
    }

    // Update search query if it exists in URL
    if (searchValue && searchValue !== searchQuery) {
      setSearchQuery(searchValue);
    }
  }, [location.search, searchQuery]);

  // Extract fetchInventory function so it can be reused
  const fetchInventory = async () => {
    // Skip if an API call is already in progress
    if (apiCallInProgress.current) {
      console.log('API call already in progress, skipping duplicate request');
      return;
    }

    apiCallInProgress.current = true;
    setLoading(true);
    setError(null);

    try {
      // Use real API call with pagination
      const response = await inventoryService.getInventory({
        page: currentPage,
        limit: itemsPerPage,
        search: searchQuery || undefined
      });

      // Get image info for each item
      const itemsWithImages = await Promise.all(
        response.items.map(async (item) => {
          try {
            const imageInfo = await inventoryService.getSkuImageInfo(item.sku_code);
            return { ...item, imageInfo };
          } catch (error) {
            console.error(`Error fetching image info for SKU ${item.sku_code}:`, error);
            return item;
          }
        })
      );

      setInventory(itemsWithImages);
      setTotalItems(response.total);
    } catch (error) {
      console.error('Error fetching inventory:', error);
      setError('Failed to load inventory data. Please try again.');
      // Set empty inventory when API fails
      setInventory([]);
      setTotalItems(0);
    } finally {
      setLoading(false);
      apiCallInProgress.current = false;
    }
  };

  useEffect(() => {
    fetchInventory();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [currentPage, itemsPerPage, searchQuery]);

  // Reset to first page when search query changes
  useEffect(() => {
    setCurrentPage(1);
  }, [searchQuery]);

  const handlePageChange = (pageNumber: number) => {
    setCurrentPage(pageNumber);
  };

  const handleItemsPerPageChange = (newItemsPerPage: number) => {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1); // Reset to first page when changing items per page
  };

  const handleNoteIconClick = (item: Inventory, e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent row click event
    setSelectedItem(item);
    setNoteText(item.notes || '');
    setIsModalOpen(true);
  };

  const handleSaveNote = async () => {
    if (!selectedItem) return;

    try {
      setLoading(true);

      // Call API to update note
      const updatedItem = await inventoryService.updateInventory(
        selectedItem.id,
        { notes: noteText }
      );

      // Update the item in the local state
      setInventory(prev =>
        prev.map(item =>
          item.id === updatedItem.id
            ? updatedItem
            : item
        )
      );

      // Show success message
      toast.showToast('Notes saved successfully!', { type: 'success' });
      setIsModalOpen(false);
      setSelectedItem(null);
    } catch (err) {
      console.error('Error updating note:', err);
      toast.showToast('Failed to save note. Please try again.', { type: 'error' });
    } finally {
      setLoading(false);
    }
  };

  const handleCloseModal = () => {
    setIsModalOpen(false);
    setSelectedItem(null);
  };

  const handleClearSearch = () => {
    setSearchQuery('');
    setCurrentPage(1);
  };

  const handleBack = () => {
    navigate('/home');
  };

  const handleAddInventory = () => {
    // Implementation for adding inventory items
    console.log('Add inventory item');
  };

  const handleBulkUpload = () => {
    setIsBulkUploadModalOpen(true);
  };

  const handleBulkUploadSuccess = () => {
    // Refresh inventory data
    fetchInventory();
    setIsBulkUploadModalOpen(false);
  };

  const handleImageUpload = () => {
    setIsImageUploadModalOpen(true);
  };

  const handleImageUploadSuccess = () => {
    // Refresh inventory data
    fetchInventory();
    setIsImageUploadModalOpen(false);
  };

  // Helper function to parse carton dimensions
  const parseCartonDimensions = (dimensionsStr: string | null): { length: number, breadth: number, height: number } => {
    if (!dimensionsStr) {
      return { length: 0, breadth: 0, height: 0 };
    }

    // Try to parse dimensions in format "LxBxH" or "L x B x H" 
    // (e.g., "40x30x25 cm" or "40 x 30 x 25 cm")
    const dimensionsRegex = /(\d+)\s*x\s*(\d+)\s*x\s*(\d+)/i;
    const match = dimensionsStr.match(dimensionsRegex);

    if (match && match.length >= 4) {
      return {
        length: parseInt(match[1], 10) || 0,
        breadth: parseInt(match[2], 10) || 0,
        height: parseInt(match[3], 10) || 0
      };
    }

    return { length: 0, breadth: 0, height: 0 };
  };

  const handleEditNotes = (item: Inventory) => {
    setSelectedItem(item);
    setNoteText(item.notes || '');
    setIsModalOpen(true);
  };

  // Remove the renderSkeletonRows function and replace with consistent loading
  const renderTableContent = () => {
    if (loading) {
      return (
        <LoadingRow>
          <td colSpan={13}>
            <LoadingContainer>
              <LoadingSpinner size="lg" />
              <LoadingText>Loading inventory</LoadingText>
            </LoadingContainer>
          </td>
        </LoadingRow>
      );
    }

    if (inventory.length === 0) {
      return (
        <tr>
          <td colSpan={13} style={{ textAlign: 'center', padding: '20px' }}>
            No inventory items found.
          </td>
        </tr>
      );
    }

    return inventory.map(item => {
      // Parse dimensions for display
      const dimensions = parseCartonDimensions(item.carton_dimensions);
      return (
        <tr key={item.id}>
          <td>{item.sku_code}</td>
          <td>{item.style_code}</td>
          <td>{item.supplier_name}</td>
          <td style={{ textAlign: 'right' }}>{item.carton}</td>
          <td style={{ textAlign: 'right' }}>{item.units_per_carton}</td>
          <td style={{ textAlign: 'right' }}>{dimensions.length}</td>
          <td style={{ textAlign: 'right' }}>{dimensions.breadth}</td>
          <td style={{ textAlign: 'right' }}>{dimensions.height}</td>
          <td style={{ textAlign: 'right' }}>{item.weight_per_unit}</td>
          <td style={{ textAlign: 'right' }}>{item.weight_per_carton}</td>
          <td style={{ textAlign: 'right' }}>{item.units_per_pallet}</td>
          <td style={{ textAlign: 'right' }}>{item.pallet_weight}</td>
          <td>
            <ActionButtonsContainer>
              <NotesIcon
                src={notesIcon}
                alt="Add/Edit Note"
                hasNotes={!!item.notes && item.notes.length > 0}
                onClick={(e) => handleEditNotes(item)}
              />
            </ActionButtonsContainer>
          </td>
        </tr>
      );
    });
  };

  return (
    <Layout>
      <PageWrapper>
        <ContentContainer>
          <TopSection>
            <TitleSection>
              <BackButtonComponent onClick={handleBack} compact={true} />
              <PageTitle>Inventory</PageTitle>
            </TitleSection>
          </TopSection>

          <SearchActionsContainer>
            <SearchContainer>
              <SearchInput
                type="text"
                placeholder="Search inventory"
                value={searchQuery}
                onChange={(e: React.ChangeEvent<HTMLInputElement>) => setSearchQuery(e.target.value)}
              />
            </SearchContainer>
            <ButtonsContainer>
              <BulkUploadButton onClick={handleImageUpload}>
                <img src={imageUploadIcon} alt="Upload" />
                Upload Images
              </BulkUploadButton>
              <BulkUploadButton onClick={handleBulkUpload}>
                <img src={supplierBulkUploadIcon} alt="Upload" />
                Bulk Upload
              </BulkUploadButton>
            </ButtonsContainer>
          </SearchActionsContainer>

          {error ? (
            <ErrorContainer>
              {error}
            </ErrorContainer>
          ) : (
            <>
              <TableContainer>
                <Table>
                  <TableHeader>
                    <tr>
                      <th>SKU Code</th>
                      <th>Style Code</th>
                      <th>Supplier Name</th>
                      <th style={{ textAlign: 'right' }}>Carton</th>
                      <th style={{ textAlign: 'right' }}>Units Per Carton</th>
                      <th colSpan={3} style={{ textAlign: 'center' }}>Carton Dimensions</th>
                      <th style={{ textAlign: 'right' }}>Weight Per Unit</th>
                      <th style={{ textAlign: 'right' }}>Weight Per Carton</th>
                      <th style={{ textAlign: 'right' }}>No. of Units Per Pallet</th>
                      <th style={{ textAlign: 'right' }}>Pallet Weight</th>
                      <th>Notes</th>
                    </tr>
                    <tr>
                      <th colSpan={5}></th>
                      <CompactHeader>Length</CompactHeader>
                      <CompactHeader>Breadth</CompactHeader>
                      <CompactHeader>Height</CompactHeader>
                      <th colSpan={5}></th>
                    </tr>
                  </TableHeader>
                  <TableBody>
                    {renderTableContent()}
                  </TableBody>
                </Table>
              </TableContainer>

              {!loading && (
                <Pagination
                  currentPage={currentPage}
                  totalPages={Math.ceil(totalItems / itemsPerPage)}
                  onPageChange={handlePageChange}
                  totalItems={totalItems}
                  itemsPerPage={itemsPerPage}
                  onItemsPerPageChange={handleItemsPerPageChange}
                  itemsPerPageOptions={[10, 20, 50]}
                  showItemsPerPage={true}
                />
              )}
            </>
          )}

          {isModalOpen && (
            <Modal onClick={handleCloseModal}>
              <ModalContent onClick={(e) => e.stopPropagation()}>
                <ModalTitle>
                  {selectedItem ? `Notes for ${selectedItem.sku_code}` : 'Notes'}
                </ModalTitle>
                <TextArea
                  value={noteText}
                  onChange={(e) => setNoteText(e.target.value)}
                  placeholder="Enter your note here"
                  autoFocus
                />
                <ButtonGroup>
                  <Button className="secondary" onClick={handleCloseModal}>Close</Button>
                  <Button className="primary" onClick={handleSaveNote} disabled={loading}>
                    {loading ? 'Saving' : 'Save'}
                  </Button>
                </ButtonGroup>
              </ModalContent>
            </Modal>
          )}
        </ContentContainer>
      </PageWrapper>

      {/* Bulk Upload Modal */}
      {isBulkUploadModalOpen && (
        <BulkUploadModal
          onClose={() => setIsBulkUploadModalOpen(false)}
          onSuccess={handleBulkUploadSuccess}
        />
      )}

      {/* Image Upload Modal */}
      {isImageUploadModalOpen && (
        <ImageUploadModal
          onClose={() => setIsImageUploadModalOpen(false)}
          onSuccess={handleImageUploadSuccess}
        />
      )}

      {/* Toast notification */}
      {toast.isVisible && (
        <Toast
          message={toast.message}
          type={toast.type}
          onClose={() => { }}
        />
      )}
    </Layout>
  );
};

export default withErrorHandling(InventoryPage);