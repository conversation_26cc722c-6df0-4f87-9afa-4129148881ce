import React, { useState, useRef, useEffect } from 'react';
import styled from 'styled-components';
import inventoryService from '../../services/inventoryService';
import supplierService, { Supplier } from '../../services/supplierService';
import { useToast } from '../../hooks/useToast';
import * as ExcelJS from 'exceljs';
import { AUTH_TOKEN_KEY, API_URL } from '../../config';

// Interface for API validation errors
interface ValidationError {
  type: string;
  loc: string[];
  msg: string;
  input: any;
  url?: string;
}

const Modal = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
`;

const ModalContent = styled.div`
  background-color: white;
  padding: 32px;
  border-radius: 12px;
  width: 600px;
  max-width: 90%;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
`;

const Title = styled.h2`
  font-size: 24px;
  font-weight: 700;
  color: #042B41;
  margin: 0 0 24px 0;
  text-align: center;
`;

const UploadArea = styled.div`
  border: 2px dashed #ccc;
  border-radius: 8px;
  padding: 32px;
  text-align: center;
  margin-bottom: 24px;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    border-color: #042B41;
    background-color: #f9fafb;
  }
`;

const UploadText = styled.p`
  font-size: 16px;
  color: #666;
  margin-bottom: 16px;
`;

const FileName = styled.div`
  font-size: 15px;
  font-weight: 500;
  color: #042B41;
  background-color: #f0f5ff;
  padding: 10px 16px;
  border-radius: 6px;
  margin-top: 16px;
  display: inline-block;
`;

const ButtonGroup = styled.div`
  display: flex;
  justify-content: center;
  gap: 16px;
  margin-top: 16px;
`;

const Button = styled.button`
  padding: 12px 0;
  width: 180px;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &.secondary {
    background-color: white;
    color: #333;
    border: 1px solid #d0d0d0;
    
    &:hover {
      background-color: #f5f5f5;
    }
  }
  
  &.primary {
    background-color: #042B41;
    color: white;
    border: 1px solid #042B41;
    
    &:hover {
      background-color: #03223A;
    }
    
    &:disabled {
      background-color: #89a2b3;
      border-color: #89a2b3;
      cursor: not-allowed;
    }
  }
`;

const FileInput = styled.input`
  display: none;
`;

const ResultContainer = styled.div`
  margin-top: 24px;
  border-top: 1px solid #eee;
  padding-top: 24px;
`;

const ResultTitle = styled.h3`
  font-size: 18px;
  font-weight: 600;
  color: #042B41;
  margin: 0 0 16px 0;
`;

const SuccessMessage = styled.p`
  color: #22c55e;
  font-weight: 500;
  font-size: 16px;
  margin-bottom: 8px;
`;

const ErrorContainer = styled.div`
  max-height: 200px;
  overflow-y: auto;
  background-color: #fef2f2;
  border: 1px solid #fee2e2;
  border-radius: 6px;
  padding: 12px;
  margin-top: 12px;
`;

const ErrorItem = styled.p`
  color: #ef4444;
  font-size: 14px;
  margin: 4px 0;
`;

const DownloadTemplateLink = styled.a`
  color: #042B41;
  text-decoration: underline;
  font-size: 14px;
  display: block;
  margin-top: 16px;
  text-align: center;
  cursor: pointer;
  
  &:hover {
    color: #03223A;
  }
`;

const InfoText = styled.div`
  font-size: 14px;
  color: #666;
  margin-top: 24px;
  padding: 12px;
  background-color: #f8fafc;
  border-radius: 6px;
  border-left: 4px solid #042B41;
`;

interface BulkUploadModalProps {
  onClose: () => void;
  onSuccess: () => void;
}

const BulkUploadModal: React.FC<BulkUploadModalProps> = ({ onClose, onSuccess }) => {
  const [file, setFile] = useState<File | null>(null);
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<{ success: number; errors: number; error_details: string[] } | null>(null);
  const [suppliers, setSuppliers] = useState<Supplier[]>([]);
  const [loadingSuppliers, setLoadingSuppliers] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const toast = useToast();

  // Fetch suppliers on component mount
  useEffect(() => {
    const fetchSuppliers = async () => {
      try {
        setLoadingSuppliers(true);
        const response = await supplierService.getSuppliers({ skip: 0, limit: 100 }); // Get first 100 suppliers
        setSuppliers(response.items || []);
      } catch (error) {
        console.error('Error fetching suppliers:', error);
        toast.showToast('Failed to load suppliers', { type: 'error' });
      } finally {
        setLoadingSuppliers(false);
      }
    };

    fetchSuppliers();
  }, [toast]);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      const selectedFile = e.target.files[0];
      const fileExtension = selectedFile.name.split('.').pop()?.toLowerCase();
      
      if (fileExtension === 'csv' || fileExtension === 'xlsx' || fileExtension === 'xls') {
        setFile(selectedFile);
        setResult(null); // Clear previous results
      } else {
        toast.showToast('Please select a valid CSV or Excel file', { type: 'error' });
      }
    }
  };

  const handleUploadClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const handleUpload = async () => {
    if (!file) return;

    try {
      setLoading(true);
      console.log('Uploading file:', file.name, 'Size:', file.size, 'Type:', file.type);
      
      // Create a FormData object
      const formData = new FormData();
      formData.append('file', file);
      
      // Debug: check what's in the FormData
      console.log('FormData entries:', Array.from(formData.entries()).map(entry => ({
        key: entry[0],
        value: entry[1] instanceof File ? `File: ${entry[1].name}` : entry[1]
      })));
      
      // Get the auth token
      const token = localStorage.getItem(AUTH_TOKEN_KEY);
      if (!token) {
        throw new Error('Authentication required. Please log in again.');
      }
      
      console.log(`Making direct fetch request to: ${API_URL}/api/v1/inventory/upload-file`);
      console.log('With token starting with:', token.substring(0, 15) + '...');
      
      // Use native fetch API directly
      const response = await fetch(`${API_URL}/api/v1/inventory/upload-file`, {
        method: 'POST',
        body: formData,
        headers: {
          'Authorization': `Bearer ${token}`,
          // Important: Do NOT set Content-Type header for multipart/form-data
          // The browser will set it automatically with the correct boundary
        }
      });
      
      // Check if the response is OK
      if (!response.ok) {
        const contentType = response.headers.get('content-type');
        console.error('Upload error response headers:', Object.fromEntries(response.headers.entries()));
        
        let errorMessage = `Server error: ${response.status} ${response.statusText}`;
        
        try {
          if (contentType && contentType.includes('application/json')) {
            const errorData = await response.json();
            console.error('Upload error response body:', errorData);
            
            if (errorData.detail) {
              if (Array.isArray(errorData.detail)) {
                errorMessage = errorData.detail.map((err: ValidationError) => 
                  `${err.msg} at ${err.loc.join('.')}`
                ).join(', ');
              } else {
                errorMessage = errorData.detail;
              }
            }
          } else {
            const text = await response.text();
            console.error('Upload error response text:', text);
          }
        } catch (parseError) {
          console.error('Error parsing error response:', parseError);
        }
        
        throw new Error(errorMessage);
      }
      
      // Parse the response
      const result = await response.json();
      setResult(result);
      
      if (result.success > 0 && result.errors === 0) {
        toast.showToast(`Successfully imported ${result.success} inventory items`, { type: 'success' });
        setTimeout(() => {
          onSuccess();
        }, 2000);
      } else if (result.success > 0) {
        toast.showToast(`Imported ${result.success} items with ${result.errors} errors`, { type: 'info' });
      } else {
        toast.showToast('Import failed. Please check the errors and try again.', { type: 'error' });
      }
    } catch (error) {
      console.error('Error uploading file:', error);
      if (error instanceof Error) {
        toast.showToast(`Upload failed: ${error.message}`, { type: 'error' });
      } else {
        toast.showToast('Failed to upload file. Please try again.', { type: 'error' });
      }
    } finally {
      setLoading(false);
    }
  };

  const downloadTemplate = async () => {
    // Create template with supplier dropdown using ExcelJS
    if (suppliers.length === 0) {
      toast.showToast('Please wait for suppliers to load before downloading template', { type: 'warning' });
      return;
    }

    try {
      // Create a new workbook and worksheet
      const workbook = new ExcelJS.Workbook();
      const worksheet = workbook.addWorksheet('Inventory Template');
      
      // Define headers
      const headers = [
        'SKU Code', 'Description', 'Style Code', 'Supplier Name', 'Carton', 
        'Units per carton', 'Carton Dimensions', 'Weight per unit', 
        'Weight per carton', 'No. of units per pallet', 'Pallet weight'
      ];
      
      // Add headers to the first row
      worksheet.addRow(headers);
      
      // Style the header row
      const headerRow = worksheet.getRow(1);
      headerRow.font = { bold: true };
      headerRow.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFE0E0E0' }
      };
      
      // Set column widths
      worksheet.columns = [
        { width: 15 }, // SKU Code
        { width: 25 }, // Description
        { width: 15 }, // Style Code
        { width: 20 }, // Supplier Name
        { width: 10 }, // Carton
        { width: 15 }, // Units per carton
        { width: 20 }, // Carton Dimensions
        { width: 15 }, // Weight per unit
        { width: 15 }, // Weight per carton
        { width: 20 }, // No. of units per pallet
        { width: 15 }  // Pallet weight
      ];
      
      // Create supplier names list for dropdown
      const supplierNames = suppliers.map(s => s.name || s.supplier_name).filter(Boolean);
      
      // Add data validation for Supplier Name column (column D, index 4)
      // Apply validation to rows 2-1000 to allow for plenty of data
      for (let row = 2; row <= 1000; row++) {
        worksheet.getCell(`D${row}`).dataValidation = {
          type: 'list',
          allowBlank: false,
          formulae: [`"${supplierNames.join(',')}"`],
          showErrorMessage: true,
          errorStyle: 'error',
          errorTitle: 'Invalid Supplier',
          error: 'Please select a supplier from the dropdown list.'
        };
      }
      
      // Add some sample rows with instructions
      worksheet.addRow([
        'SAMPLE001',
        'Sample Product Description',
        'STYLE001',
        supplierNames[0] || 'Select from dropdown',
        '1',
        '12',
        '40x30x25 cm',
        '0.5',
        '6.0',
        '144',
        '72.0'
      ]);
      
      // Style the sample row differently
      const sampleRow = worksheet.getRow(2);
      sampleRow.fill = {
        type: 'pattern',
        pattern: 'solid',
        fgColor: { argb: 'FFF0F8FF' }
      };
      sampleRow.font = { italic: true };
      
      // Add a note about the sample row
      worksheet.addRow([]);
      worksheet.addRow(['NOTE: The blue row above is a sample. Delete it and add your actual data.']);
      const noteRow = worksheet.getRow(4);
      noteRow.font = { color: { argb: 'FF666666' }, italic: true };
      
      // Generate Excel file buffer
      const buffer = await workbook.xlsx.writeBuffer();
      
      // Create blob and download
      const blob = new Blob([buffer], { 
        type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' 
      });
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', 'inventory_template.xlsx');
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);
      
      // Show success toast
      toast.showToast('Excel template downloaded! Supplier names are available in dropdown.', { type: 'success' });
    } catch (error) {
      console.error('Error creating Excel template:', error);
      toast.showToast('Failed to create Excel template. Please try again.', { type: 'error' });
    }
  };

  return (
    <Modal onClick={onClose}>
      <ModalContent onClick={(e) => e.stopPropagation()}>
        <Title>Bulk Upload Inventory</Title>
        
        <UploadArea onClick={handleUploadClick}>
          <UploadText>Click or drag file to this area to upload</UploadText>
          <UploadText>Support for CSV, XLS, XLSX files</UploadText>
          <FileInput 
            type="file" 
            ref={fileInputRef}
            onChange={handleFileChange}
            accept=".csv,.xlsx,.xls"
          />
          {file && <FileName>{file.name}</FileName>}
        </UploadArea>
        
        <ButtonGroup>
          <Button className="secondary" onClick={onClose}>
            Cancel
          </Button>
          <Button 
            className="primary" 
            onClick={handleUpload}
            disabled={!file || loading}
          >
            {loading ? 'Uploading' : 'Upload'}
          </Button>
        </ButtonGroup>
        
        <DownloadTemplateLink onClick={downloadTemplate}>
          Download Excel Template (with Supplier Dropdown)
        </DownloadTemplateLink>
        
        {result && (
          <ResultContainer>
            <ResultTitle>Upload Results</ResultTitle>
            {result.success > 0 && (
              <SuccessMessage>Successfully imported {result.success} inventory items</SuccessMessage>
            )}
            
            {result.errors > 0 && (
              <>
                <ErrorItem>Failed to import {result.errors} items</ErrorItem>
                <ErrorContainer>
                  {result.error_details.map((error, index) => (
                    <ErrorItem key={index}>{error}</ErrorItem>
                  ))}
                </ErrorContainer>
              </>
            )}
          </ResultContainer>
        )}
        
        <InfoText>
          <p><strong>Required Fields:</strong> SKU Code, Style Code, Supplier Name, Units per carton, Weight per unit, Weight per carton</p>
          <p>Carton Dimensions should be in format: LxWxH cm (e.g. 40x30x25 cm)</p>
          <p><strong>Supplier Name:</strong> Download the Excel template for a dropdown list of available suppliers. The system will automatically map supplier names to IDs.</p>
          <p><strong>File Format:</strong> Use the Excel template (.xlsx) for best experience with dropdown validation, or upload CSV/Excel files with exact supplier names.</p>
        </InfoText>
      </ModalContent>
    </Modal>
  );
};

export default BulkUploadModal; 