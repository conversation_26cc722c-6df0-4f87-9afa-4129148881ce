.purchase-order-details {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px 0;
}

.purchase-order-details .ant-card-head-title {
  font-size: 18px;
  font-weight: 600;
}

.purchase-order-details .ant-descriptions-title {
  font-size: 16px;
  margin-bottom: 16px;
}

.purchase-order-details .ant-descriptions-item-label {
  font-weight: 500;
}

/* Style for the ship to customer toggle */
.ship-to-customer-toggle {
  display: flex;
  align-items: center;
}

.ship-to-customer-toggle .anticon-info-circle {
  cursor: pointer;
  transition: color 0.3s;
}

.ship-to-customer-toggle .anticon-info-circle:hover {
  color: #40a9ff;
}

/* Highlight ship to customer when enabled */
.purchase-order-details .ship-to-customer-enabled {
  color: #52c41a;
  font-weight: 600;
}

/* Style for warning text in modal */
.purchase-order-details .warning-text {
  color: #ff4d4f;
  font-weight: 600;
}

/* Table styles */
.purchase-order-details .ant-table-wrapper {
  margin-top: 20px;
}

.purchase-order-details .ant-table-thead > tr > th {
  background-color: #f7f7f7;
  font-weight: 600;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .purchase-order-details {
    padding: 10px;
  }
  
  .purchase-order-details .ant-descriptions-item {
    padding: 8px 12px;
  }
} 