import React, { useState, useEffect } from 'react';
import { Card, Row, Col, Descriptions, Table, Divider, notification, Space } from 'antd';
import { useParams } from 'react-router-dom';
import axios from 'axios';
import { API_BASE_URL } from '../../config';
import ShipToCustomerToggle from '../common/ShipToCustomerToggle';
import './PurchaseOrderDetails.css';

const PurchaseOrderDetails = () => {
  const { poNumber } = useParams();
  const [poDetails, setPoDetails] = useState(null);
  const [loading, setLoading] = useState(false);

  // Fetch PO details on mount and when poNumber changes
  useEffect(() => {
    if (poNumber) {
      fetchPoDetails();
    }
  }, [poNumber]);

  // Fetch PO details from the API
  const fetchPoDetails = async () => {
    try {
      setLoading(true);
      const response = await axios.get(`${API_BASE_URL}/api/v1/inbound-delivery/po/${poNumber}`);
      
      if (response.data && response.data.data && response.data.data.length > 0) {
        setPoDetails(response.data.data[0]); // Take the first item
      } else {
        notification.warning({
          message: 'No Data Found',
          description: `No details found for PO ${poNumber}`,
          duration: 5
        });
      }
    } catch (error) {
      console.error('Error fetching PO details:', error);
      notification.error({
        message: 'Error',
        description: 'Failed to load purchase order details. Please try again.',
        duration: 5
      });
    } finally {
      setLoading(false);
    }
  };

  // Handle ship to customer status change
  const handleShipStatusChange = (newStatus) => {
    // Update local state if needed
    if (poDetails) {
      setPoDetails({
        ...poDetails,
        ship_to_customer: newStatus
      });
    }
  };

  // Define columns for the items table
  const columns = [
    {
      title: 'SKU',
      dataIndex: 'sku',
      key: 'sku',
    },
    {
      title: 'Description',
      dataIndex: 'description',
      key: 'description',
    },
    {
      title: 'Quantity Ordered',
      dataIndex: 'quantity_ordered',
      key: 'quantity_ordered',
    },
    {
      title: 'Quantity Received',
      dataIndex: 'quantity_received',
      key: 'quantity_received',
    },
    {
      title: 'Balance',
      key: 'balance',
      render: (_, record) => record.quantity_ordered - record.quantity_received
    }
  ];

  // Format date for display
  const formatDate = (dateString) => {
    if (!dateString) return 'N/A';
    const date = new Date(dateString);
    return date.toLocaleDateString();
  };

  // Generate item data for the table
  const getItemsData = () => {
    if (!poDetails || !poDetails.items) return [];
    
    if (Array.isArray(poDetails.items)) {
      return poDetails.items;
    }
    
    if (poDetails.items.items && Array.isArray(poDetails.items.items)) {
      return poDetails.items.items;
    }
    
    // If no array found, create a single item from the data we have
    return [{
      key: '1',
      sku: poDetails.sku || 'N/A',
      description: poDetails.items.description || 'No description',
      quantity_ordered: poDetails.quantity_ordered || 0,
      quantity_received: poDetails.quantity_received || 0
    }];
  };

  return (
    <div className="purchase-order-details">
      <Card 
        title={`Purchase Order Details: ${poNumber}`} 
        loading={loading}
        extra={
          <Space>
            {poDetails && (
              <ShipToCustomerToggle 
                poNumber={poNumber} 
                initialValue={poDetails.ship_to_customer} 
                onStatusChange={handleShipStatusChange}
              />
            )}
          </Space>
        }
      >
        {poDetails && (
          <>
            <Descriptions title="Order Information" bordered>
              <Descriptions.Item label="PO Number">{poDetails.po_number}</Descriptions.Item>
              <Descriptions.Item label="Supplier">{poDetails.supplier_name}</Descriptions.Item>
              <Descriptions.Item label="Status">{poDetails.status}</Descriptions.Item>
              <Descriptions.Item label="Order Date">{formatDate(poDetails.po_date)}</Descriptions.Item>
              <Descriptions.Item label="Expected Delivery">{formatDate(poDetails.new_expected_date)}</Descriptions.Item>
              <Descriptions.Item label="Invoice Number">{poDetails.linked_invoice || 'N/A'}</Descriptions.Item>
              <Descriptions.Item label="Ship to Customer">
                {poDetails.ship_to_customer ? 'Yes' : 'No'}
              </Descriptions.Item>
            </Descriptions>

            <Divider />

            <Table 
              columns={columns} 
              dataSource={getItemsData()}
              rowKey={(record) => record.id || record.sku || Math.random().toString()}
              pagination={false}
            />
          </>
        )}
      </Card>
    </div>
  );
};

export default PurchaseOrderDetails; 