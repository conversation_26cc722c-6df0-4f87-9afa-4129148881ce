import React, { useState, useEffect, useCallback, useMemo, useRef } from 'react';
import styled from 'styled-components';
import Layout from '../../layout/Layout';
import { useNavigate } from 'react-router-dom';
import DateRangePicker from '../../common/DateRangePicker';
import Pagination from '../../common/Pagination';
import EditPOForm from '../EditPOForm';
import PageLoadingSpinner from '../../ui/PageLoadingSpinner';
import poManagementNotesIcon from '../../../assets/invoicesNotesIcon.png';
import invoicesMailIcon from '../../../assets/invoicesMailIcon.png';
import customerAddIcon from '../../../assets/customerAddIcon.png';
import purchaseOrderService, {
  PurchaseOrder,
  PurchaseOrderStatus,
  PurchaseOrderFilter,
  EmailStatusUpdate,
  PurchaseOrderDetail
} from '../../../services/purchaseOrderService';
import { format, parseISO } from 'date-fns';
import { useToast } from '../../../hooks/useToast';
import { Toast } from '../../common/Toast';
import { useStoreFilter } from '../../../hooks/useStoreFilter';
import LoadingSpinner from '../../ui/LoadingSpinner';

const PageContainer = styled.div`
  width: 100%;
  max-width: 100%;
  padding: 0 32px 32px 32px;
`;

const StyledBackButton = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  border: none;
  color: #042B41;
  font-size: 1.25rem;
  cursor: pointer;
  padding: 6px;
  border-radius: 4px;
  transition: background-color 0.2s;
  
  &:hover {
    background-color: rgba(4, 43, 65, 0.05);
  }
  
  svg {
    stroke-width: 2px;
  }
`;

const PageTitle = styled.h1`
  color: #042B41;
  font-size: 1.25rem;
  font-weight: 700;
  margin: 0;
`;

const FilterContainer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
`;

const SearchDateContainer = styled.div`
  display: flex;
  gap: 16px;
  align-items: center;
`;

const SearchContainer = styled.div`
  position: relative;
  width: 250px;
`;

const SearchIconWrapper = styled.div`
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  color: #9CA3AF;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: none;
`;

const SearchInput = styled.input`
  width: 100%;
  padding: 10px 10px 10px 36px;
  border: 1px solid #E5E7EB;
  border-radius: 8px;
  font-size: 14px;
  
  &::placeholder {
    color: #9CA3AF;
  }
`;

const CreatePOButton = styled.button`
  display: flex;
  align-items: center;
  gap: 10px;
  background-color: #042B41;
  color: white;
  border: none;
  border-radius: 10px;
  padding: 12px 20px;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  
  &:hover {
    background-color: #0A3D5A;
  }
  
  img {
    width: 18px;
    height: 18px;
  }
`;

const POList = styled.div`
  display: flex;
  flex-direction: column;
  gap: 16px;
  width: 100%;
`;

const POCard = styled.div`
  background-color: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  overflow: hidden;
  width: 100%;
  border: 1px solid #e5e7eb;
  margin-bottom: 16px;
  
  &:last-child {
    margin-bottom: 0;
  }
`;

const POCardHeader = styled.div`
  display: grid;
  grid-template-columns: 150px minmax(0, 1fr) 140px 120px;
  align-items: center;
  width: 100%;
  padding: 20px 0;
  gap: 16px;
  
  & > * {
    padding: 0 20px;
  }
  
  & > *:not(:last-child) {
    border-right: 1px solid #f0f0f0;
  }
  
  & > *:last-child {
    border-right: none;
    padding-right: 24px;
  }
`;

const PONumber = styled.h2`
  font-size: 18px;
  font-weight: 600;
  color: #111827;
  margin: 0;
`;

const PODetailsContainer = styled.div`
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
  align-items: center;
  width: 100%;
`;

const PODetailItem = styled.div`
  min-width: 0;
  display: flex;
  flex-direction: column;
`;

const PODetailLabel = styled.div`
  font-size: 13px;
  color: #6B7280;
  margin-bottom: 6px;
  white-space: nowrap;
  font-weight: 500;
`;

const PODetailValue = styled.div`
  font-size: 15px;
  color: #111827;
  font-weight: 600;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
`;

const StatusContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  text-align: left;
  justify-content: center;
  width: 100%;
  gap: 8px;
  padding: 8px 0;
`;

const StatusBadge = styled.div<{ status: 'delivered' | 'backorder' | 'new' | 'unknown' }>`
  padding: 8px 20px;
  border-radius: 20px;
  font-size: 13px;
  font-weight: 600;
  display: inline-block;
  text-align: center;
  min-width: 90px;
  width: fit-content;
  
  ${props => {
    switch (props.status) {
      case 'delivered':
        return `
          background-color: #D1FAE5;
          color: #065F46;
        `;
      case 'backorder':
        return `
          background-color: #FEF3C7;
          color: #92400E;
        `;
      case 'new':
        return `
          background-color: #E5E7EB;
          color: #4B5563;
        `;
      case 'unknown':
        return `
          background-color: #E5E7EB;
          color: #4B5563;
        `;
      default:
        return '';
    }
  }}
`;

const ToggleButtonContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  padding: 8px 0;
`;

const ToggleButton = styled.button`
  background: #042B41;
  border: none;
  cursor: pointer;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-size: 13px;
  font-weight: 600;
  padding: 10px 16px;
  transition: all 0.2s;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  width: auto;
  height: auto;
  min-width: 90px;
  
  &:hover {
    background-color: #053c59;
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.3);
  }
  
  svg {
    width: 16px;
    height: 16px;
  }
`;

const EmailContainer = styled.div.withConfig({
  shouldForwardProp: (prop) => prop !== 'sent',
}) <{ sent: boolean }>`
  display: flex;
  align-items: center;
  font-weight: 600;
  
  ${props => props.sent ? `
    color: #111827;
  ` : `
    color: #6B7280;
  `}
`;

const EmailIconWrapper = styled.span`
  margin-right: 8px;
  display: flex;
  align-items: center;
`;

const PaginationContainer = styled.div`
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-top: 32px;
  gap: 8px;
`;

const POCardContent = styled.div<{ expanded: boolean }>`
  display: ${props => props.expanded ? 'block' : 'none'};
  padding: 0 0 16px 0;
  border-top: 1px solid #e0e0e0;
  background-color: white;
`;

const DetailsTable = styled.table`
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
  margin: 16px 0;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
`;

const TableHeader = styled.thead`
  background-color: #042B41;
  color: white;
  
  th {
    padding: 12px 16px;
    text-align: left;
    font-weight: 500;
    
    &:last-child {
      text-align: center;
    }
  }
`;

const TableBody = styled.tbody`
  tr {
    border-bottom: 1px solid #e0e0e0;
    
    &:last-child {
      border-bottom: none;
    }
  }
  
  td {
    padding: 12px 16px;
    color: #111827;
    
    &:last-child {
      text-align: center;
    }
  }
`;

const NoteIcon = styled.div<{ hasNotes: boolean }>`
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
  
  img {
    width: 20px;
    height: 20px;
    filter: ${props => props.hasNotes ? 'invert(16%) sepia(99%) saturate(7404%) hue-rotate(359deg) brightness(97%) contrast(118%)' : 'none'};
  }
`;

// Modal components for Notes
const ModalOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
`;

const ModalContent = styled.div`
  background-color: white;
  border-radius: 8px;
  width: 100%;
  max-width: 500px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 20px;
`;

const ModalTitle = styled.h2`
  font-size: 1.5rem;
  color: #333;
  margin: 0 0 20px 0;
  text-align: center;
`;

const NotesTextarea = styled.textarea`
  width: 100%;
  height: 150px;
  padding: 12px 15px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  font-size: 1rem;
  line-height: 1.5;
  margin-bottom: 20px;
  resize: none;
  
  &:focus {
    outline: none;
    border-color: #042B41;
  }
`;

const ButtonContainer = styled.div`
  display: flex;
  justify-content: space-between;
  gap: 16px;
`;

const ModalButton = styled.button`
  padding: 10px 20px;
  border-radius: 6px;
  font-weight: 500;
  font-size: 0.9rem;
  cursor: pointer;
  flex: 1;
`;

const CloseButton = styled(ModalButton)`
  background-color: white;
  color: #333;
  border: 1px solid #e0e0e0;
  
  &:hover {
    background-color: #f5f5f5;
  }
`;

const SaveButton = styled(ModalButton)`
  background-color: #042B41;
  color: white;
  border: none;
  
  &:hover {
    background-color: #031f30;
  }
`;

// Add empty state components
const EmptyStateContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 1rem;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  text-align: center;
  margin: 2rem 0;
`;

const EmptyStateIcon = styled.div`
  width: 60px;
  height: 60px;
  background-color: #f3f4f6;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
  
  svg {
    width: 30px;
    height: 30px;
    color: #9ca3af;
  }
`;

const EmptyStateTitle = styled.h3`
  font-size: 1.2rem;
  color: #111827;
  margin-bottom: 0.5rem;
`;

const EmptyStateDescription = styled.p`
  font-size: 0.9rem;
  color: #6b7280;
  max-width: 300px;
  margin-bottom: 1.5rem;
`;

const EmptyStateButton = styled.button`
  background-color: #042B41;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 0.6rem 1.2rem;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  
  &:hover {
    background-color: #0A3D5A;
  }
`;

// Add type for purchase order with optional po_number
interface ExtendedPurchaseOrder extends PurchaseOrder {
  po_number?: string;
}

const LoadingText = styled.p`
  margin: 0;
  font-size: 14px;
  color: #6B7280;
  font-weight: 500;
`;

const POManagementPage: React.FC = () => {
  const navigate = useNavigate();
  const storeFilterHook = useStoreFilter();
  // Memoize storeFilter to prevent unnecessary re-renders
  const storeFilter = useMemo(() => storeFilterHook, [JSON.stringify(storeFilterHook)]);

  const [searchTerm, setSearchTerm] = useState('');
  const [dateRange, setDateRange] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [purchaseOrders, setPurchaseOrders] = useState<ExtendedPurchaseOrder[]>([]);
  const [totalItems, setTotalItems] = useState(0);
  const [loading, setLoading] = useState(false);

  // Expanded PO state
  const [expandedPOId, setExpandedPOId] = useState<number | null>(null);
  const [expandedPODetails, setExpandedPODetails] = useState<PurchaseOrder | null>(null);
  const [loadingDetails, setLoadingDetails] = useState(false);

  // State for modals and UI
  const [showDetailNotesModal, setShowDetailNotesModal] = useState(false);
  const [currentDetailNotes, setCurrentDetailNotes] = useState('');
  const [currentDetailId, setCurrentDetailId] = useState<number | null>(null);
  const [editingDeliveryDate, setEditingDeliveryDate] = useState<number | null>(null);
  const [deliveryDateValue, setDeliveryDateValue] = useState<string>('');

  // Filter state
  const [filters, setFilters] = useState<PurchaseOrderFilter>({
    skip: 0,
    limit: itemsPerPage
  });

  const toast = useToast();

  // Ref to prevent multiple simultaneous API calls
  const isFetchingRef = useRef(false);

  // Debounce search term to prevent too many API calls
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState(searchTerm);

  // Memoize filter values to prevent unnecessary re-renders
  const filterValues = useMemo(() => ({
    start_date: filters.start_date,
    end_date: filters.end_date
  }), [filters.start_date, filters.end_date]);

  useEffect(() => {
    const timeoutId = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
    }, 500);

    return () => clearTimeout(timeoutId);
  }, [searchTerm]);

  // Memoize the fetchPOs function to prevent infinite re-renders
  const fetchPOs = useCallback(async () => {
    // Prevent multiple simultaneous calls
    if (isFetchingRef.current) {
      return;
    }

    isFetchingRef.current = true;
    setLoading(true);
    try {
      // Set up parameters for the request
      const params: PurchaseOrderFilter = {
        skip: (currentPage - 1) * itemsPerPage,
        limit: itemsPerPage,
        supplier_name: debouncedSearchTerm || undefined,
        start_date: filterValues.start_date,
        end_date: filterValues.end_date
      };

      // Fetch purchase orders with store filtering
      const response = await purchaseOrderService.getPurchaseOrders(params, storeFilter);

      if (response) {
        // Use the correct property names from the API response
        setPurchaseOrders(response.items || []);
        setTotalItems(response.total || 0);
      }
    } catch (err) {
      console.error('Error fetching purchase orders:', err);
      // On error, reset to empty state
      setPurchaseOrders([]);
      setTotalItems(0);
    } finally {
      setLoading(false);
      isFetchingRef.current = false;
    }
  }, [currentPage, itemsPerPage, debouncedSearchTerm, filterValues]);

  // Load purchase orders when component mounts or when fetchPOs dependencies change
  useEffect(() => {
    fetchPOs();
  }, [fetchPOs]);

  // Reset to first page when search term or items per page changes
  useEffect(() => {
    setCurrentPage(1);
  }, [debouncedSearchTerm, itemsPerPage]);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  const handleDateRangeChange = (newDateRange: string) => {
    setDateRange(newDateRange);

    if (newDateRange) {
      const [startStr, endStr] = newDateRange.split(' - ');

      // Parse from DD/MM/YYYY format (updated to match DateRangePicker format)
      try {
        const [startDay, startMonth, startYear] = startStr.split('/').map(Number);
        const [endDay, endMonth, endYear] = endStr.split('/').map(Number);

        const startDate = new Date(startYear, startMonth - 1, startDay);
        const endDate = new Date(endYear, endMonth - 1, endDay);

        // Validate date range (30 days)
        const diffTime = Math.abs(endDate.getTime() - startDate.getTime());
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

        if (diffDays > 30) {
          // If range is more than 30 days, adjust start date to be 30 days before end date
          startDate.setDate(endDate.getDate() - 30);
        }

        // Format dates for backend (YYYY-MM-DD)
        const formattedStartDate = formatDateForBackend(startDate);
        const formattedEndDate = formatDateForBackend(endDate);

        console.log(`PO Management - Setting date range: ${formattedStartDate} to ${formattedEndDate}`);

        // Only update filters if the dates have actually changed
        setFilters(prevFilters => {
          if (prevFilters.start_date === formattedStartDate && prevFilters.end_date === formattedEndDate) {
            return prevFilters; // No change, return same object to prevent re-render
          }
          return {
            ...prevFilters,
            start_date: formattedStartDate,
            end_date: formattedEndDate
          };
        });
      } catch (err) {
        console.error("Error parsing date range:", err);
      }
    } else {
      // Clear date filters when no date range is selected
      setFilters(prevFilters => {
        if (!prevFilters.start_date && !prevFilters.end_date) {
          return prevFilters; // No change, return same object to prevent re-render
        }
        return {
          ...prevFilters,
          start_date: undefined,
          end_date: undefined
        };
      });
    }
  };

  // Format date as YYYY-MM-DD for backend
  const formatDateForBackend = (date: Date): string => {
    return date.toISOString().split('T')[0];
  };

  const formatDate = (dateStr: string): string => {
    try {
      return format(parseISO(dateStr), 'dd/MM/yyyy');
    } catch (error) {
      return dateStr;
    }
  };

  const getStatusDisplay = (status: PurchaseOrderStatus): string => {
    switch (status) {
      case PurchaseOrderStatus.PENDING:
        return 'New';
      case PurchaseOrderStatus.ORDERED:
        return 'Back Order';
      case PurchaseOrderStatus.DELIVERED:
        return 'Delivered';
      case PurchaseOrderStatus.CANCELLED:
        return 'Cancelled';
      default:
        return 'New';
    }
  };

  const getStatusBadgeClass = (status: PurchaseOrderStatus): 'delivered' | 'backorder' | 'new' | 'unknown' => {
    switch (status) {
      case PurchaseOrderStatus.DELIVERED:
        return 'delivered';
      case PurchaseOrderStatus.ORDERED:
        return 'backorder';
      case PurchaseOrderStatus.PENDING:
        return 'new';
      default:
        return 'unknown';
    }
  };

  const handlePageChange = (page: number) => setCurrentPage(page);

  const handleItemsPerPageChange = (newItemsPerPage: number) => {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1); // Reset to first page when changing items per page
  };

  const toggleExpand = async (id: number) => {
    if (expandedPOId === id) {
      // If already expanded, collapse it
      setExpandedPOId(null);
      setExpandedPODetails(null);
    } else {
      // Otherwise, expand and fetch details
      setExpandedPOId(id);
      setLoadingDetails(true);
      try {
        const details = await purchaseOrderService.getPurchaseOrderById(id);

        // Ensure the details array exists and process item fields
        let poDetails: PurchaseOrderDetail[] = [];

        if (Array.isArray(details.details)) {
          poDetails = details.details;
        } else if (details.details && typeof details.details === 'object') {
          // If details is an object that might have an 'items' property (from JSONB in backend)
          const detailsObj = details.details as any;
          if (Array.isArray(detailsObj.items)) {
            poDetails = detailsObj.items;
          }
        }

        const poWithDetails = {
          ...details,
          details: poDetails
        };

        setExpandedPODetails(poWithDetails);
      } catch (error) {
        console.error(`Error fetching details for PO #${id}:`, error);
        // Don't reset expandedPOId here, but show a message
        toast.showToast(`Failed to load details for PO #${id}. Using basic info instead.`, { type: 'error' });
      } finally {
        setLoadingDetails(false);
      }
    }
  };

  const handleSendEmail = async (poId: number) => {
    try {
      const po = purchaseOrders.find(p => p.id === poId);
      if (!po) return;

      // Check if email has already been sent
      if (po.email_sent || po.email_status === 'SENT' || po.email_status === 'ALREADY_SENT') {
        toast.showToast('Email has already been sent to the supplier.', { type: 'info' });
        return;
      }

      // Set loading state in the UI
      setPurchaseOrders(prevPOs =>
        prevPOs.map(p =>
          p.id === poId
            ? { ...p, isEmailSending: true }
            : p
        )
      );

      try {
        // Call the email sending service
        const response = await purchaseOrderService.sendPurchaseOrderEmail(poId);

        if (response.success) {
          // Update local state to reflect email has been sent
          setPurchaseOrders(prevPOs =>
            prevPOs.map(p =>
              p.id === poId
                ? { 
                    ...p, 
                    email_sent: true, 
                    email_status: 'SENT', 
                    isEmailSending: false 
                  }
                : p
            )
          );

          toast.showToast(
            `Email sent to supplier successfully! (${response.supplier_email})`, 
            { type: 'success' }
          );
        } else if (response.already_sent || response.email_status === 'ALREADY_SENT') {
          // Handle already sent case
          setPurchaseOrders(prevPOs =>
            prevPOs.map(p =>
              p.id === poId
                ? { 
                    ...p, 
                    email_sent: true, 
                    email_status: 'ALREADY_SENT', 
                    isEmailSending: false 
                  }
                : p
            )
          );

          toast.showToast('Email has already been sent to the supplier.', { type: 'info' });
        } else {
          throw new Error(response.message || 'Failed to send email');
        }
      } catch (err: any) {
        console.error('Error sending email:', err);

        // Reset the UI on error
        setPurchaseOrders(prevPOs =>
          prevPOs.map(p =>
            p.id === poId
              ? { ...p, isEmailSending: false }
              : p
          )
        );

        // Handle different error scenarios
        if (err.response?.status === 400 && err.response?.data?.detail?.includes('email address not available')) {
          toast.showToast('Supplier email address not found. Please update supplier details.', { type: 'error' });
        } else if (err.response?.status === 404) {
          toast.showToast('Purchase order or supplier not found.', { type: 'error' });
        } else {
          toast.showToast(
            err.response?.data?.detail || err.message || 'Failed to send email. Please try again.', 
            { type: 'error' }
          );
        }
      }
    } catch (err) {
      console.error('Error in send email process:', err);
      toast.showToast('Failed to process email. Please try again.', { type: 'error' });
    }
  };

  const handleBack = () => {
    navigate('/home');
  };

  // Helper function to render empty state
  const renderEmptyState = () => {
    return (
      <EmptyStateContainer>
        <EmptyStateIcon>
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
        </EmptyStateIcon>
        <EmptyStateTitle>No Purchase Orders Found</EmptyStateTitle>
        <EmptyStateDescription>
          There are no purchase orders matching your criteria. Create a new purchase order to get started.
        </EmptyStateDescription>
        <EmptyStateButton onClick={() => navigate('/po-management/create')}>
          Create Purchase Order
        </EmptyStateButton>
      </EmptyStateContainer>
    );
  };

  // Render PO content with consistent loading pattern
  const renderPOContent = () => {
    if (loading && purchaseOrders.length === 0) {
      return <PageLoadingSpinner message="Loading purchase orders" />;
    }

    if (loading) {
      return (
        <LoadingContainer>
          <LoadingSpinner size="lg" />
          <LoadingText>Loading purchase orders</LoadingText>
        </LoadingContainer>
      );
    }

    if (purchaseOrders.length === 0) {
      return renderEmptyState();
    }

    return (
      <POList>
        {purchaseOrders.map(po => (
          <POCard key={po.id}>
            <POCardHeader>
              <PONumber>{po.po_number || `PO-${po.id}`}</PONumber>
              <PODetailsContainer>
                <PODetailItem>
                  <PODetailLabel>Supplier Name</PODetailLabel>
                  <PODetailValue>{po.supplier_name}</PODetailValue>
                </PODetailItem>
                <PODetailItem>
                  <PODetailLabel>Issue Date</PODetailLabel>
                  <PODetailValue>{formatDate(po.date || po.created_at)}</PODetailValue>
                </PODetailItem>
                <PODetailItem>
                  <PODetailLabel>Email To Supplier:</PODetailLabel>
                  <EmailContainer sent={po.email_sent || po.email_status === 'SENT' || po.email_status === 'ALREADY_SENT'}>
                    {po.email_sent || po.email_status === 'SENT' || po.email_status === 'ALREADY_SENT' ? (
                      'The Email has been sent'
                    ) : po.isEmailSending ? (
                      <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                        <div style={{
                          width: '16px',
                          height: '16px',
                          border: '2px solid #f3f3f3',
                          borderTop: '2px solid #042B41',
                          borderRadius: '50%',
                          animation: 'spin 1s linear infinite'
                        }}></div>
                        Sending...
                      </div>
                    ) : (
                      <EmailIconWrapper onClick={() => handleSendEmail(po.id)}>
                        <img
                          src={invoicesMailIcon}
                          alt="Send Email"
                          style={{ width: '20px', height: '20px', cursor: 'pointer' }}
                        />
                      </EmailIconWrapper>
                    )}
                  </EmailContainer>
                </PODetailItem>
              </PODetailsContainer>
              <StatusContainer>
                <PODetailLabel>Status:</PODetailLabel>
                <StatusBadge status={getStatusBadgeClass(po.status)}>
                  {getStatusDisplay(po.status)}
                </StatusBadge>
              </StatusContainer>
              <ToggleButtonContainer>
                <ToggleButton onClick={() => toggleExpand(po.id)}>
                  {expandedPOId === po.id ? (
                    <>
                      Hide
                      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M5 15L12 9L19 15" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                      </svg>
                    </>
                  ) : (
                    <>
                      View
                      <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M19 9L12 15L5 9" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
                      </svg>
                    </>
                  )}
                </ToggleButton>
              </ToggleButtonContainer>
            </POCardHeader>

            {expandedPOId === po.id && (
              <POCardContent expanded={true}>
                {loadingDetails ? (
                  <div style={{ padding: '20px', textAlign: 'center' }}>
                    <p>Loading order details</p>
                  </div>
                ) : (
                  <DetailsTable>
                    <TableHeader>
                      <tr>
                        <th>SKU</th>
                        <th>Description</th>
                        <th>Quantity Ordered</th>
                        <th>Quantity received</th>
                        <th>Expected Delivery Date For Backorder</th>
                        <th>Total</th>
                        <th>Notes</th>
                      </tr>
                    </TableHeader>
                    <TableBody>
                      {expandedPODetails && expandedPODetails.details && expandedPODetails.details.length > 0 ? (
                        expandedPODetails.details.map((detail, index) => {
                          const quantity = detail.quantity_ordered !== undefined ? detail.quantity_ordered : detail.quantity;
                          const totalPrice = detail.total || detail.total_price ||
                            (detail.unit_price && quantity ? detail.unit_price * quantity : null);
                          const formattedTotal = totalPrice !== null && !isNaN(Number(totalPrice)) ?
                            `$${Number(totalPrice).toFixed(2)}` : '-';
                          const hasNotes = Boolean(detail.notes && detail.notes.trim().length > 0);

                          return (
                            <tr key={detail.id || index}>
                              <td>{detail.sku}</td>
                              <td>{detail.description}</td>
                              <td>{quantity || 0}</td>
                              <td>{detail.quantity_received || 0}</td>
                              <td>
                                {editingDeliveryDate === parseId(detail.id) ? (
                                  <div style={{ display: 'flex', alignItems: 'center', gap: '8px' }}>
                                    <input
                                      type="date"
                                      value={deliveryDateValue}
                                      onChange={(e) => setDeliveryDateValue(e.target.value)}
                                      style={{
                                        padding: '4px 8px',
                                        border: '1px solid #d1d5db',
                                        borderRadius: '4px',
                                        fontSize: '14px'
                                      }}
                                    />
                                    <button
                                      onClick={() => {
                                        const numId = parseId(detail.id);
                                        if (numId !== undefined) handleSaveDeliveryDate(numId);
                                      }}
                                      style={{
                                        padding: '4px 8px',
                                        background: '#042B41',
                                        color: 'white',
                                        border: 'none',
                                        borderRadius: '4px',
                                        cursor: 'pointer'
                                      }}
                                    >
                                      Save
                                    </button>
                                  </div>
                                ) : (
                                  <div
                                    onClick={() => {
                                      const numId = parseId(detail.id);
                                      if (numId !== undefined) {
                                        setEditingDeliveryDate(numId);
                                        setDeliveryDateValue(detail.expected_delivery_date || '');
                                      }
                                    }}
                                    style={{ cursor: 'pointer' }}
                                  >
                                    {detail.expected_delivery_date ? formatDate(detail.expected_delivery_date) : '-'}
                                  </div>
                                )}
                              </td>
                              <td>{formattedTotal}</td>
                              <td>
                                <NoteIcon
                                  hasNotes={hasNotes}
                                  onClick={() => {
                                    const numId = parseId(detail.id);
                                    if (numId !== undefined) handleViewDetailNotes(numId);
                                  }}
                                >
                                  <img src={poManagementNotesIcon} alt="Notes" />
                                </NoteIcon>
                              </td>
                            </tr>
                          );
                        })
                      ) : (
                        <tr>
                          <td colSpan={7} style={{ textAlign: 'center', padding: '20px' }}>
                            No items found for this purchase order.
                          </td>
                        </tr>
                      )}
                    </TableBody>
                  </DetailsTable>
                )}
              </POCardContent>
            )}
          </POCard>
        ))}
      </POList>
    );
  };

  const handleViewDetailNotes = (detailId?: number | string) => {
    if (!detailId) return;

    // Convert string ID to number if needed
    const numId = typeof detailId === 'string' ? parseInt(detailId, 10) : detailId;

    const detail = expandedPODetails?.details.find(d => {
      // Convert both IDs to strings for comparison
      const dId = d.id?.toString();
      const targetId = numId.toString();
      return dId === targetId;
    });

    if (detail) {
      setCurrentDetailId(numId);
      setCurrentDetailNotes(detail.notes || '');
      setShowDetailNotesModal(true);
    }
  };

  const handleSaveDetailNotes = async () => {
    if (!currentDetailId) return;

    try {
      // Get the purchase order ID for this detail
      const poId = expandedPODetails?.id;

      if (!poId) {
        toast.showToast('Could not determine the purchase order for this detail.', { type: 'error' });
        return;
      }

      // First check if the detail exists on the backend
      try {
        // Use the correct endpoint with purchase order ID
        await purchaseOrderService.updatePurchaseOrderDetailNotes(
          poId,
          currentDetailId,
          { notes: currentDetailNotes }
        );

        // Update local state
        if (expandedPODetails) {
          const updatedDetails = expandedPODetails.details.map(detail =>
            detail.id === currentDetailId
              ? { ...detail, notes: currentDetailNotes }
              : detail
          );

          setExpandedPODetails({
            ...expandedPODetails,
            details: updatedDetails
          });
        }

        setShowDetailNotesModal(false);
        toast.showToast('Detail notes saved successfully!', { type: 'success' });
      } catch (err) {
        console.error('Error updating detail notes:', err);
        toast.showToast('Failed to save detail notes. Please try again.', { type: 'error' });
      }
    } catch (err) {
      console.error('Error in handleSaveDetailNotes:', err);
      toast.showToast('Failed to process detail notes. Please try again.', { type: 'error' });
    }
  };

  // Add a function to handle saving the delivery date
  const handleSaveDeliveryDate = async (detailId: number | string) => {
    if (!expandedPODetails?.id) return;

    // Convert string ID to number if needed
    const numId = typeof detailId === 'string' ? parseInt(detailId, 10) : detailId;

    try {
      await purchaseOrderService.updatePurchaseOrderDetail(
        numId,
        { expected_delivery_date: deliveryDateValue }
      );

      // Update local state
      if (expandedPODetails) {
        const updatedDetails = expandedPODetails.details.map(detail => {
          // Convert both IDs to strings for comparison
          const dId = detail.id?.toString();
          const targetId = numId.toString();

          return dId === targetId
            ? { ...detail, expected_delivery_date: deliveryDateValue }
            : detail;
        });

        setExpandedPODetails({
          ...expandedPODetails,
          details: updatedDetails
        });
      }

      setEditingDeliveryDate(null);
      toast.showToast('Delivery date updated successfully!', { type: 'success' });
    } catch (err) {
      console.error('Error updating delivery date:', err);
      toast.showToast('Failed to update delivery date. Please try again.', { type: 'error' });
    }
  };

  // Helper function to parse IDs
  const parseId = (id: string | number | undefined): number | undefined => {
    if (id === undefined) return undefined;
    if (typeof id === 'number') return id;
    return parseInt(id, 10);
  };

  // Remove skeleton components and add consistent loading components
  const LoadingContainer = styled.div`
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 12px;
    padding: 40px 20px;
  `;

  return (
    <Layout>
      <PageContainer>
        <div style={{ display: 'flex', alignItems: 'center', gap: '10px', marginBottom: '20px' }}>
          <StyledBackButton onClick={handleBack}>
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M19 12H5M12 19l-7-7 7-7" />
            </svg>
          </StyledBackButton>
          <PageTitle>Purchase Order Management</PageTitle>
        </div>

        <FilterContainer>
          <SearchDateContainer>
            <SearchContainer>
              <SearchIconWrapper>
                <svg
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <circle cx="11" cy="11" r="8" stroke="currentColor" strokeWidth="2" />
                  <line x1="21" y1="21" x2="16.65" y2="16.65" stroke="currentColor" strokeWidth="2" strokeLinecap="round" />
                </svg>
              </SearchIconWrapper>
              <SearchInput
                type="text"
                placeholder="Search PO Number or Supplier"
                value={searchTerm}
                onChange={handleSearchChange}
              />
            </SearchContainer>
            <DateRangePicker
              value={dateRange}
              onChange={handleDateRangeChange}
            />
          </SearchDateContainer>

          <CreatePOButton onClick={() => navigate('/po-management/create')}>
            <img src={customerAddIcon} alt="Add" />
            Create Purchase Order
          </CreatePOButton>
        </FilterContainer>

        {renderPOContent()}

        {!loading && purchaseOrders.length > 0 && (
          <PaginationContainer>
            <Pagination
              currentPage={currentPage}
              totalPages={Math.max(1, Math.ceil(totalItems / itemsPerPage))}
              onPageChange={handlePageChange}
              totalItems={totalItems}
              itemsPerPage={itemsPerPage}
              onItemsPerPageChange={handleItemsPerPageChange}
              itemsPerPageOptions={[10, 20, 50]}
              showItemsPerPage={true}
            />
          </PaginationContainer>
        )}

        {showDetailNotesModal && (
          <ModalOverlay>
            <ModalContent>
              <ModalTitle>Item Notes</ModalTitle>
              <NotesTextarea
                value={currentDetailNotes}
                onChange={e => setCurrentDetailNotes(e.target.value)}
                placeholder="Enter notes for this item..."
              />
              <ButtonContainer>
                <CloseButton onClick={() => setShowDetailNotesModal(false)}>
                  Cancel
                </CloseButton>
                <SaveButton onClick={handleSaveDetailNotes}>
                  Save Notes
                </SaveButton>
              </ButtonContainer>
            </ModalContent>
          </ModalOverlay>
        )}

        {toast.isVisible && (
          <Toast
            message={toast.message}
            type={toast.type}
            onClose={() => { }}
          />
        )}
      </PageContainer>
    </Layout>
  );
};

export default POManagementPage;