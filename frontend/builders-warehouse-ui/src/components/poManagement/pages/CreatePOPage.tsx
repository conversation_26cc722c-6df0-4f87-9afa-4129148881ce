import React, { useState, useEffect, useCallback } from 'react';
import styled from 'styled-components';
import Layout from '../../layout/Layout';
import { Link, useNavigate } from 'react-router-dom';
import poManagementNotesIcon from '../../../assets/invoicesNotesIcon.png';
import { useAppDispatch, useAppSelector } from '../../../store/hooks';
import { createPurchaseOrder } from '../../../store/slices/purchaseOrderSlice';
import { PurchaseOrderItem, CreatePurchaseOrderRequest } from '../../../store/types';
import { getInvoices, InvoiceResponse } from '../../../services/invoiceService';
import supplierService, { Supplier } from '../../../services/supplierService';
import { v4 as uuidv4 } from 'uuid';
import inventoryService, { Inventory } from '../../../services/inventoryService';
import { BackLinkComponent } from '../../ui/DesignSystem';
import PageLoadingSpinner from '../../ui/PageLoadingSpinner';
import { useToast } from '../../../hooks/useToast';
import { Toast as ToastComponent } from '../../common/Toast';
import { useStoreFilter, getDefaultStoreType } from '../../../hooks/useStoreFilter';
import SkuImage, { markSkusAsRecentlyUploaded } from '../../common/SkuImage';

const PageContainer = styled.div`
  width: 100%;
  max-width: 100%;
`;

const PageHeader = styled.div`
  padding: 1.5rem 2rem;
`;

// New back button styling based on EditInvoiceForm
const StyledBackButton = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  border: none;
  color: #042B41;
  font-size: 1.25rem;
  cursor: pointer;
  padding: 6px;
  border-radius: 4px;
  transition: background-color 0.2s;
  
  &:hover {
    background-color: rgba(4, 43, 65, 0.05);
  }
  
  svg {
    stroke-width: 2px;
  }
`;

// New styling for breadcrumb navigation
const BreadcrumbContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 0;
`;

const BreadcrumbItem = styled.span`
  font-size: 1.1rem;
  color: #6B7280;
`;

const BreadcrumbSeparator = styled.span`
  margin: 0 0.5rem;
  color: #6B7280;
`;

const BreadcrumbLink = styled(Link)`
  color: #042B41;
  text-decoration: none;
  font-size: 1.1rem;
  font-weight: 500;
  
  &:hover {
    color: #0A3D5A;
    text-decoration: underline;
  }
`;

const PageTitle = styled.span`
  font-weight: 700;
  color: #111827;
  font-size: 1.25rem;
`;

const HeaderRow = styled.div`
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 24px;
`;

const FormGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr 1fr 1fr 1fr;
  gap: 1rem;
  margin-bottom: 2rem;
`;

const FormGroup = styled.div`
  display: flex;
  flex-direction: column;
`;

const FormLabel = styled.label`
  font-size: 14px;
  color: #374151;
  margin-bottom: 0.5rem;
  font-weight: 500;
  
  .required {
    color: #EF4444;
    margin-left: 4px;
  }
`;

const FormInput = styled.input`
  padding: 0.75rem 1rem;
  border: 1px solid #E5E7EB;
  border-radius: 6px;
  font-size: 14px;
  color: #111827;
  
  &:focus {
    outline: none;
    border-color: #6B7280;
    box-shadow: 0 0 0 1px #6B7280;
  }
  
  &:disabled {
    background-color: #F9FAFB;
    cursor: not-allowed;
  }
`;

const FormSelect = styled.select`
  padding: 0.75rem 1rem;
  border: 1px solid #E5E7EB;
  border-radius: 6px;
  font-size: 14px;
  color: #111827;
  background-color: white;
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%236B7280' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M6 9l6 6 6-6'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 0.75rem center;
  cursor: pointer;
  
  &:focus {
    outline: none;
    border-color: #6B7280;
    box-shadow: 0 0 0 1px #6B7280;
  }
`;

const DateInputWrapper = styled.div`
  position: relative;
  
  .calendar-icon {
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    pointer-events: none;
  }
`;

const TableContainer = styled.div`
  margin-bottom: 2rem;
  overflow: visible;
`;

const Table = styled.table`
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 1rem;
  table-layout: fixed;
  overflow: visible;
`;

const TableHeader = styled.thead`
  background-color: #042B41;
  color: white;
  
  th {
    text-align: left;
    padding: 0.75rem 1rem;
    font-weight: 500;
    font-size: 14px;
    border-right: 1px solid rgba(255, 255, 255, 0.1);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    
    &:last-child {
      border-right: none;
      width: 50px;
    }
  }
`;

const TableBody = styled.tbody`
  tr {
    &:nth-child(even) {
      background-color: #F9FAFB;
    }
    
    &:hover {
      background-color: #F3F4F6;
    }
  }
  
  td {
    padding: 0.75rem 1rem;
    font-size: 14px;
    color: #111827;
    border: 1px solid #E5E7EB;
    vertical-align: middle;
    position: relative;
    
    &:last-child {
      text-align: center;
    }
    
    &:first-child {
      overflow: visible;
    }
  }
  
  input, select {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #E5E7EB;
    border-radius: 4px;
    font-size: 14px;
    background-color: transparent;
  }
  
  .numeric-input {
    text-align: right;
  }
`;

const AddButtonContainer = styled.div`
  display: flex;
  justify-content: flex-end;
  margin-top: 10px;
`;

const AddButton = styled.button`
  background-color: #042B41;
  color: white;
  border: none;
  width: 32px;
  height: 32px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 18px;
  margin-top: 10px;
  
  &:hover {
    background-color: #031F30;
  }
  
  &::before {
    content: '+';
  }
`;

const RemoveButton = styled.button`
  background-color: #042B41;
  color: white;
  border: none;
  width: 32px;
  height: 32px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 18px;
  font-weight: bold;
  margin: 0 auto;
  
  &:hover {
    background-color: #031F30;
  }
  
  &::before {
    content: '-';
  }
`;

const NotesIcon = styled.div`
  cursor: pointer;
  display: flex;
  justify-content: center;
  width: 100%;
  
  img {
    width: 20px;
    height: 20px;
  }
`;

const SummaryContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  margin-bottom: 2rem;
`;

const PaymentTermsContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 1rem;
  width: 400px;
  margin-bottom: 1.5rem;
  
  label {
    margin-bottom: 0;
    white-space: nowrap;
  }
  
  input {
    flex: 1;
  }
`;

const CostsContainer = styled.div`
  flex: 1;
  max-width: 300px;
`;

const CostItem = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
  font-size: 14px;
  color: #374151;
  width: 400px;
  
  &:last-child {
    margin-top: 1rem;
    padding-top: 0.75rem;
    border-top: 1px solid #E5E7EB;
    font-weight: 600;
  }
  
  .amount {
    font-weight: 500;
    background-color: #f9fafb;
    padding: 0.5rem;
    min-width: 120px;
    text-align: right;
    border-radius: 4px;
  }
`;

const ButtonContainer = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
`;

const CancelButton = styled.button`
  padding: 0.75rem 1.5rem;
  background-color: white;
  border: 1px solid #D1D5DB;
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  cursor: pointer;
  
  &:hover {
    background-color: #F9FAFB;
  }
`;

const SubmitButton = styled.button`
  padding: 10px 24px;
  background-color: #042B41;
  color: white;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  
  &:hover {
    background-color: #031F30;
  }
  
  &:disabled {
    background-color: #9CA3AF;
    cursor: not-allowed;
  }
`;

// Modal components for Notes
const ModalOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
`;

const ModalContent = styled.div`
  background-color: white;
  border-radius: 8px;
  width: 100%;
  max-width: 500px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 20px;
`;

const ModalTitle = styled.h2`
  font-size: 1.5rem;
  color: #333;
  margin: 0 0 20px 0;
  text-align: center;
`;

const NotesTextarea = styled.textarea`
  width: 100%;
  height: 150px;
  padding: 12px 15px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  font-size: 1rem;
  line-height: 1.5;
  margin-bottom: 20px;
  resize: none;
  
  &:focus {
    outline: none;
    border-color: #042B41;
  }
`;

const ModalButtonContainer = styled.div`
  display: flex;
  justify-content: space-between;
  gap: 16px;
`;

const ModalButton = styled.button`
  padding: 10px 20px;
  border-radius: 6px;
  font-weight: 500;
  font-size: 0.9rem;
  cursor: pointer;
  flex: 1;
`;

const CloseButton = styled(ModalButton)`
  background-color: white;
  color: #333;
  border: 1px solid #e0e0e0;
  
  &:hover {
    background-color: #f5f5f5;
  }
`;

const SaveButton = styled(ModalButton)`
  background-color: #042B41;
  color: white;
  border: none;
  
  &:hover {
    background-color: #031f30;
  }
`;

const ReadOnlyInput = styled.div`
  padding: 0.75rem 1rem;
  border: 1px solid #E5E7EB;
  border-radius: 6px;
  font-size: 14px;
  color: #6B7280;
  background-color: #F9FAFB;
  cursor: not-allowed;
  display: flex;
  align-items: center;
`;

const StyledFormSelect = styled(FormSelect)`
  appearance: none;
  padding-right: 30px;
  background-position: right 15px center;
  background-size: 12px;
  cursor: pointer;
  transition: border-color 0.2s;
  
  &:hover {
    border-color: #9CA3AF;
  }
  
  &:focus {
    border-color: #2563EB;
    box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.1);
  }
`;

const CurrencyInputWrapper = styled.div`
  position: relative;
  display: flex;
  align-items: center;
  background-color: #f9fafb;
  border-radius: 4px;
  
  span {
    position: absolute;
    left: 0.5rem;
    color: #6B7280;
  }
  
  input {
    padding-left: 1.5rem;
    text-align: right;
    background-color: transparent;
    border: none;
    width: 100%;
    
    &:focus {
      outline: none;
    }
  }
`;

// SKU Select component - searchable with images
const SKUSelectWrapper = styled.div`
  position: relative;
  width: 100%;
`;

const SKUSearchInput = styled(FormInput)`
  width: 100%;
`;

const SKUDropdown = styled.div<{ isOpen: boolean }>`
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #D1D5DB;
  border-top: none;
  border-radius: 0 0 0.25rem 0.25rem;
  max-height: 300px;
  overflow-y: auto;
  z-index: 1000;
  display: ${props => props.isOpen ? 'block' : 'none'};
  width: 100%;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
`;

const SKUOption = styled.div`
  padding: 0.375rem;
  cursor: pointer;
  font-size: 0.875rem;
  background-color: white;
  border-bottom: 1px solid #E5E7EB;
  display: flex;
  align-items: center;
  gap: 8px;
  
  &:hover {
    background-color: #F3F4F6;
  }
  
  &:last-child {
    border-bottom: none;
  }
`;

const SKUOptionContent = styled.div`
  flex: 1;
`;

const SelectedSKUContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
`;

// Add refresh button styles
const RefreshButton = styled.button`
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 2px 4px;
  margin-left: 8px;
  border-radius: 3px;
  font-size: 12px;
  opacity: 0.8;
  
  &:hover {
    opacity: 1;
    background-color: rgba(255, 255, 255, 0.1);
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const HeaderWithRefresh = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
`;

interface POItem {
  id: string;
  sku: string;
  description: string;
  quantityOrdered: string | number;
  expectedDeliveryDate: string;
  total: number;
  notes: string;
}

// Add skeleton loading components
const SkeletonLoader = styled.div`
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: 6px;
  height: 42px;
  width: 100%;
  
  @keyframes shimmer {
    0% {
      background-position: 200% 0;
    }
    100% {
      background-position: -200% 0;
    }
  }
`;

const ButtonSkeleton = styled(SkeletonLoader)`
  height: 44px;
  width: 100%;
  display: inline-block;
`;

const CreatePOPage: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { loading, error } = useAppSelector(state => state.purchaseOrders);
  const toast = useToast();
  const storeFilter = useStoreFilter();

  const [storeType, setStoreType] = useState('');
  const [invoiceId, setInvoiceId] = useState('');
  const [invoices, setInvoices] = useState<InvoiceResponse[]>([]);
  const [issueDate, setIssueDate] = useState(new Date().toISOString().split('T')[0]);
  const [supplier, setSupplier] = useState('');
  const [supplierId, setSupplierId] = useState<string>('');
  const [suppliers, setSuppliers] = useState<Supplier[]>([]);
  const [supplierLoading, setSupplierLoading] = useState<boolean>(false);
  const [supplierError, setSupplierError] = useState<string | null>(null);
  const [paymentTerms, setPaymentTerms] = useState('Net 30');

  const [items, setItems] = useState<POItem[]>([
    {
      id: uuidv4(),
      sku: '',
      description: '',
      quantityOrdered: '',
      expectedDeliveryDate: '',
      total: 0,
      notes: ''
    }
  ]);

  const [showNotesModal, setShowNotesModal] = useState(false);
  const [currentNotes, setCurrentNotes] = useState('');
  const [currentItemId, setCurrentItemId] = useState('');

  const [totalAmount, setTotalAmount] = useState(0);
  const [gstAmount, setGstAmount] = useState(0);
  const [totalPayable, setTotalPayable] = useState(0);

  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState('');

  const [inventoryItems, setInventoryItems] = useState<Inventory[]>([]);
  const [isLoadingInventory, setIsLoadingInventory] = useState(false);
  const [lastInventoryRefresh, setLastInventoryRefresh] = useState<number>(Date.now());

  // State for searchable SKU dropdowns
  const [skuSearchTerms, setSKUSearchTerms] = useState<{ [key: string]: string }>({});
  const [openSKUDropdowns, setOpenSKUDropdowns] = useState<{ [key: string]: boolean }>({});

  const calculateTotals = () => {
    const total = items.reduce((sum, item) => {
      // Handle item.total being potentially undefined or string
      const itemTotal = typeof item.total === 'number' ? item.total : 0;
      return sum + itemTotal;
    }, 0);

    const gst = total * 0.1;
    const payable = total + gst;

    setTotalAmount(total);
    setGstAmount(gst);
    setTotalPayable(payable);
  };

  useEffect(() => {
    const fetchInvoices = async () => {
      try {
        const response = await getInvoices(1, 100, {});
        setInvoices(response.items || []);
      } catch (error) {
        console.error('Error fetching invoices:', error);
        toast.showToast('Failed to load invoices', { type: 'error' });
      }
    };

    const fetchSuppliers = async () => {
      try {
        setSupplierLoading(true);
        setSupplierError(null);

        // Use a smaller limit and proper error handling
        const response = await supplierService.getSuppliers({ skip: 0, limit: 50 });

        // Check if response has the expected structure
        if (response && (response.items || response.data)) {
          const supplierList = response.items || response.data || [];
          console.log('Fetched suppliers:', supplierList);
          setSuppliers(supplierList);
        } else {
          console.warn('Unexpected supplier response format:', response);
          setSuppliers([]);
          setSupplierError('Invalid response format from suppliers API');
        }
      } catch (error) {
        console.error('Error fetching suppliers:', error);
        setSupplierError('Failed to load suppliers');
        setSuppliers([]);
        toast.showToast('Failed to load suppliers', { type: 'error' });
      } finally {
        setSupplierLoading(false);
      }
    };

    const fetchInventory = async () => {
      try {
        setIsLoadingInventory(true);
        // Use getAllInventory to get all inventory items with images
        const response = await inventoryService.getAllInventory();
        console.log('Fetched inventory items:', response);
        setInventoryItems(response || []);
      } catch (error) {
        console.error("Error fetching inventory:", error);
        // Don't show error toast for inventory as it's not critical for PO creation
        setInventoryItems([]);
      } finally {
        setIsLoadingInventory(false);
      }
    };

    fetchInvoices();
    fetchSuppliers();
    fetchInventory();

    // Set default store type for staff/manager users
    const defaultStore = getDefaultStoreType(storeFilter);
    if (defaultStore) {
      setStoreType(defaultStore.name);
    }
  }, [storeFilter]);

  useEffect(() => {
    calculateTotals();
  }, [items]);

  // Function to fetch inventory items with force refresh option
  const fetchInventoryItems = useCallback(async (force: boolean = false) => {
    setIsLoadingInventory(true);
    try {
      // Clear cache if force refreshing to ensure fresh data
      if (force) {
        inventoryService.clearInventoryCache();
      }

      const response = await inventoryService.getAllInventory();
      console.log('Fetched inventory items:', response);
      setInventoryItems(response || []);

      // Update refresh timestamp when force refreshing
      if (force) {
        setLastInventoryRefresh(Date.now());
        console.log('Inventory data refreshed with latest images');
      }
    } catch (error) {
      console.error('Error fetching inventory items:', error);
      setInventoryItems([]);
    } finally {
      setIsLoadingInventory(false);
    }
  }, []);

  // Auto-refresh inventory data every 30 seconds to pick up newly uploaded images
  useEffect(() => {
    const interval = setInterval(() => {
      // Only auto-refresh if page is visible and not currently loading
      if (!document.hidden && !isLoadingInventory) {
        console.log('Auto-refreshing inventory data...');
        fetchInventoryItems(true);
      }
    }, 30000); // 30 seconds

    return () => clearInterval(interval);
  }, [fetchInventoryItems]);

  // Refresh inventory when page becomes visible (user returns from other tabs/windows)
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!document.hidden && !isLoadingInventory) {
        console.log('Page became visible, refreshing inventory data...');
        fetchInventoryItems(true);
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('focus', handleVisibilityChange);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('focus', handleVisibilityChange);
    };
  }, [fetchInventoryItems]);

  // Filter inventory items based on search term for specific row
  const getFilteredInventoryItems = (itemId: string) => {
    const searchTerm = skuSearchTerms[itemId] || '';
    if (!searchTerm) return inventoryItems.slice(0, 10); // Show first 10 if no search

    return inventoryItems.filter(item =>
      item.sku_code?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.description?.toLowerCase().includes(searchTerm.toLowerCase())
    ).slice(0, 10); // Limit to 10 results
  };

  // Handle SKU selection from dropdown
  const handleSKUSelect = (itemId: string, inventoryItem: any) => {
    // Update the item with selected SKU
    setItems(prevItems =>
      prevItems.map(item =>
        item.id === itemId ? {
          ...item,
          sku: inventoryItem.sku_code,
          description: inventoryItem.style_code || inventoryItem.description || '',
        } : item
      )
    );

    // Update search term and close dropdown
    setSKUSearchTerms(prev => ({
      ...prev,
      [itemId]: inventoryItem.sku_code
    }));
    setOpenSKUDropdowns(prev => ({
      ...prev,
      [itemId]: false
    }));

    console.log(`Selected inventory item: ${JSON.stringify(inventoryItem)}`);
  };

  const handleAddItem = () => {
    setItems([...items, {
      id: uuidv4(),
      sku: '',
      description: '',
      quantityOrdered: '',
      expectedDeliveryDate: '',
      total: 0,
      notes: ''
    }]);
  };

  const handleDeleteItem = (id: string) => {
    if (items.length > 1) {
      setItems(items.filter(item => item.id !== id));
    }
  };

  const handleItemChange = (id: string, field: keyof POItem, value: any) => {
    setItems(prevItems =>
      prevItems.map(item =>
        item.id === id ? { ...item, [field]: value } : item
      )
    );
  };

  const handleViewNotes = (itemId: string) => {
    const item = items.find(i => i.id === itemId);
    if (item) {
      setCurrentNotes(item.notes);
      setCurrentItemId(itemId);
      setShowNotesModal(true);
    }
  };

  const handleSaveNotes = () => {
    const updatedItems = items.map(item => {
      if (item.id === currentItemId) {
        return { ...item, notes: currentNotes };
      }
      return item;
    });

    setItems(updatedItems);
    setShowNotesModal(false);
    toast.showToast('Notes saved successfully!', { type: 'success' });
  };

  // Update the handleSupplierChange function to fetch supplier details
  const handleSupplierChange = async (e: React.ChangeEvent<HTMLSelectElement>) => {
    const selectedSupplierId = e.target.value;
    setSupplierId(selectedSupplierId);

    console.log(`Selected supplier ID: ${selectedSupplierId}`);

    if (selectedSupplierId) {
      try {
        // First try to find the supplier in the local list to avoid unnecessary API call
        const selectedSupplier = suppliers.find(s => s.id.toString() === selectedSupplierId);
        if (selectedSupplier) {
          setSupplier(selectedSupplier.supplier_name || selectedSupplier.name || '');
          console.log(`Selected supplier from local data:`, selectedSupplier);
          return;
        }

        // If not found locally, fetch from API
        const supplierData = await supplierService.getSupplierData(selectedSupplierId);
        if (supplierData) {
          setSupplier(supplierData.supplier_name || supplierData.name || '');
          console.log(`Selected supplier from API:`, supplierData);
        }
      } catch (error) {
        console.error(`Error fetching supplier details:`, error);
        toast.showToast('Failed to load supplier details', { type: 'error' });

        // Fallback to using the local data if API fails
        const selectedSupplier = suppliers.find(s => s.id.toString() === selectedSupplierId);
        if (selectedSupplier) {
          setSupplier(selectedSupplier.supplier_name || selectedSupplier.name || '');
        }
      }
    } else {
      setSupplier('');
    }
  };

  const handleStoreTypeChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setStoreType(e.target.value);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!supplierId) {
      toast.showToast('Please select a supplier', { type: 'error' });
      return;
    }

    if (items.some(item => !item.sku)) {
      toast.showToast('Please select a SKU for all items', { type: 'error' });
      return;
    }

    // Convert supplierId to number
    const supplierIdNumber = parseInt(supplierId, 10);

    // Create request object with all fields, including optional ones
    const newPurchaseOrder: CreatePurchaseOrderRequest = {
      // Required fields - convert supplierId to a number
      supplier_id: supplierIdNumber,
      issue_date: issueDate,
      status: 'draft',

      // Optional fields that might not exist in the database schema
      supplier_name: supplier || undefined,
      store_type: storeType || undefined,
      invoice_id: invoiceId || undefined,
      payment_terms: paymentTerms || undefined,

      // Details array
      details: items.map(item => ({
        sku: item.sku,
        description: item.description,
        quantity_ordered: parseInt(item.quantityOrdered as string) || 0, // Convert string to number
        quantity_received: 0,
        expected_delivery_date: item.expectedDeliveryDate || undefined,
        total: item.total || 0,
        notes: item.notes || ''
      }))
    };

    // Log request for debugging
    console.log('Submitting purchase order:', JSON.stringify(newPurchaseOrder, null, 2));

    try {
      await dispatch(createPurchaseOrder(newPurchaseOrder)).unwrap();
      navigate('/po-management');
    } catch (error) {
      console.error('Error creating purchase order:', error);
      toast.showToast('Failed to create purchase order. Please try again.', { type: 'error' });
    }
  };

  const handleCancel = () => {
    navigate('/po-management');
  };

  const formatCurrency = (value: number) => {
    return value.toFixed(2);
  };

  // Manual refresh function
  const refreshInventoryData = () => {
    if (!isLoadingInventory) {
      console.log('Manual inventory refresh triggered');
      fetchInventoryItems(true);
    }
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (!target.closest('.sku-dropdown-container')) {
        setOpenSKUDropdowns({});
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  return (
    <Layout>
      <PageContainer>
        <div style={{ display: 'flex', alignItems: 'center', gap: '10px', marginBottom: '24px', padding: '0 32px' }}>
          <StyledBackButton onClick={() => navigate('/po-management')}>
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M19 12H5M12 19l-7-7 7-7" />
            </svg>
          </StyledBackButton>
          <BreadcrumbContainer>
            <BreadcrumbLink to="/po-management">
              PO Management
            </BreadcrumbLink>
            <BreadcrumbSeparator>&gt;</BreadcrumbSeparator>
            <PageTitle>Create PO</PageTitle>
          </BreadcrumbContainer>
        </div>

        <div style={{ padding: '0 32px' }}>
          <form onSubmit={handleSubmit}>
            <FormGrid>
              <FormGroup>
                <FormLabel>Store Type<span className="required">*</span></FormLabel>
                <StyledFormSelect
                  value={storeType}
                  onChange={handleStoreTypeChange}
                  required
                  disabled={storeFilter.isStoreRestricted}
                  style={{
                    backgroundColor: storeFilter.isStoreRestricted ? '#F9FAFB' : 'white',
                    cursor: storeFilter.isStoreRestricted ? 'not-allowed' : 'pointer'
                  }}
                >
                  <option value="">Select a store type</option>
                  <option value="Cranbourne">Cranbourne</option>
                  <option value="Sale">Sale</option>
                </StyledFormSelect>
                {storeFilter.isStoreRestricted && (
                  <div style={{ fontSize: '12px', color: '#6B7280', marginTop: '4px' }}>
                    Store type is auto-selected based on your role
                  </div>
                )}
              </FormGroup>

              <FormGroup>
                <FormLabel>Link to Invoice</FormLabel>
                <StyledFormSelect
                  value={invoiceId}
                  onChange={(e) => setInvoiceId(e.target.value)}
                >
                  <option value="">Select Invoice</option>
                  {invoices.map((invoice) => (
                    <option key={invoice.id} value={invoice.id}>
                      {invoice.invoice_number?.formatted_number || invoice.invoice_no}
                    </option>
                  ))}
                </StyledFormSelect>
              </FormGroup>

              <FormGroup>
                <FormLabel>Date of Issued<span className="required">*</span></FormLabel>
                <FormInput
                  type="date"
                  value={issueDate}
                  onChange={(e) => setIssueDate(e.target.value)}
                  required
                />
              </FormGroup>

              <FormGroup>
                <FormLabel>Supplier<span className="required">*</span></FormLabel>
                {supplierLoading ? (
                  <SkeletonLoader />
                ) : (
                  <StyledFormSelect
                    value={supplierId}
                    onChange={handleSupplierChange}
                    required
                    disabled={supplierLoading}
                  >
                    <option value="">Select Supplier</option>
                    {suppliers.map((s) => (
                      <option key={s.id} value={s.id}>
                        {s.supplier_name || s.name || `Supplier ${s.id}`}
                      </option>
                    ))}
                  </StyledFormSelect>
                )}
                {supplierError && <div style={{ color: '#EF4444', fontSize: '12px', marginTop: '4px' }}>{supplierError}</div>}
              </FormGroup>
            </FormGrid>

            <TableContainer>
              <Table>
                <TableHeader>
                  <tr>
                    <th style={{ width: '15%' }}>
                      <HeaderWithRefresh>
                        SKU
                        <RefreshButton
                          type="button"
                          onClick={refreshInventoryData}
                          disabled={isLoadingInventory}
                          title="Refresh inventory data to pick up newly uploaded images"
                        >
                          {isLoadingInventory ? '↻' : '↻'}
                        </RefreshButton>
                      </HeaderWithRefresh>
                    </th>
                    <th style={{ width: '25%' }}>Description</th>
                    <th style={{ width: '10%' }}>Quantity Ordered</th>
                    <th style={{ width: '20%' }}>Expected Delivery Date for Backorder</th>
                    <th style={{ width: '15%' }}>Total</th>
                    <th style={{ width: '10%' }}>Notes</th>
                    <th style={{ width: '5%' }}></th>
                  </tr>
                </TableHeader>
                <TableBody>
                  {items.map((item) => (
                    <tr key={item.id}>
                      <td>
                        <SKUSelectWrapper className="sku-dropdown-container">
                          {item.sku && inventoryItems.find(inv => inv.sku_code === item.sku) ? (
                            <SelectedSKUContainer>
                              <SkuImage
                                imagePath={inventoryItems.find(inv => inv.sku_code === item.sku)?.image_path}
                                alt="SKU"
                                width={32}
                                height={32}
                                enableModal={true}
                                showBlankWhenNoImage={true}
                                key={`${item.sku}-${lastInventoryRefresh}`}
                              />
                              <SKUSearchInput
                                type="text"
                                value={skuSearchTerms[item.id] || item.sku}
                                onChange={(e) => {
                                  setSKUSearchTerms(prev => ({
                                    ...prev,
                                    [item.id]: e.target.value
                                  }));
                                  setOpenSKUDropdowns(prev => ({
                                    ...prev,
                                    [item.id]: true
                                  }));
                                }}
                                onFocus={() => setOpenSKUDropdowns(prev => ({
                                  ...prev,
                                  [item.id]: true
                                }))}
                                placeholder="Search SKU"
                                style={{ flex: 1 }}
                              />
                            </SelectedSKUContainer>
                          ) : (
                            <SKUSearchInput
                              type="text"
                              value={skuSearchTerms[item.id] || item.sku}
                              onChange={(e) => {
                                setSKUSearchTerms(prev => ({
                                  ...prev,
                                  [item.id]: e.target.value
                                }));
                                setOpenSKUDropdowns(prev => ({
                                  ...prev,
                                  [item.id]: true
                                }));
                              }}
                              onFocus={() => setOpenSKUDropdowns(prev => ({
                                ...prev,
                                [item.id]: true
                              }))}
                              placeholder="Search SKU"
                            />
                          )}
                          <SKUDropdown isOpen={openSKUDropdowns[item.id] && getFilteredInventoryItems(item.id).length > 0}>
                            {getFilteredInventoryItems(item.id).map(invItem => (
                              <SKUOption
                                key={invItem.id}
                                onClick={() => handleSKUSelect(item.id, invItem)}
                              >
                                <SkuImage
                                  imagePath={invItem.image_path}
                                  alt="SKU"
                                  width={40}
                                  height={40}
                                  enableModal={true}
                                  showBlankWhenNoImage={true}
                                  key={`${invItem.id}-${lastInventoryRefresh}`}
                                />
                                <SKUOptionContent>
                                  <div style={{ fontWeight: '500' }}>{invItem.sku_code}</div>
                                  {invItem.description && (
                                    <div style={{ fontSize: '0.75rem', color: '#6B7280', marginTop: '2px' }}>
                                      {invItem.description}
                                    </div>
                                  )}
                                </SKUOptionContent>
                              </SKUOption>
                            ))}
                          </SKUDropdown>
                        </SKUSelectWrapper>
                      </td>
                      <td>
                        <FormInput
                          type="text"
                          value={item.description}
                          onChange={(e) => handleItemChange(item.id, 'description', e.target.value)}
                          required
                        />
                      </td>
                      <td>
                        <FormInput
                          type="number"
                          className="numeric-input"
                          value={item.quantityOrdered || ''}
                          onChange={(e) => handleItemChange(item.id, 'quantityOrdered', e.target.value)}
                          min="0"
                          required
                        />
                      </td>
                      <td>
                        <FormInput
                          type="date"
                          value={item.expectedDeliveryDate}
                          onChange={(e) => handleItemChange(item.id, 'expectedDeliveryDate', e.target.value)}
                        />
                      </td>
                      <td>
                        <CurrencyInputWrapper>
                          <span>$</span>
                          <FormInput
                            type="number"
                            className="numeric-input"
                            value={item.total || ''}
                            onChange={(e) => handleItemChange(item.id, 'total', Number(e.target.value))}
                            step="0.01"
                            min="0"
                            required
                          />
                        </CurrencyInputWrapper>
                      </td>
                      <td>
                        <NotesIcon onClick={() => handleViewNotes(item.id)}>
                          <img src={poManagementNotesIcon} alt="Notes" />
                        </NotesIcon>
                      </td>
                      <td>
                        {items.length > 1 && (
                          <RemoveButton
                            type="button"
                            onClick={() => handleDeleteItem(item.id)}
                          />
                        )}
                      </td>
                    </tr>
                  ))}
                </TableBody>
              </Table>

              <AddButtonContainer>
                <AddButton onClick={handleAddItem} type="button" />
              </AddButtonContainer>
            </TableContainer>

            <SummaryContainer>
              <PaymentTermsContainer>
                <FormLabel>Payment Terms:</FormLabel>
                <FormInput
                  type="text"
                  value={paymentTerms}
                  onChange={(e) => setPaymentTerms(e.target.value)}
                />
              </PaymentTermsContainer>

              <CostItem>
                <span>Total amount:</span>
                <div className="amount">
                  <CurrencyInputWrapper>
                    <span>$</span>
                    <input
                      type="number"
                      value={totalAmount || ''}
                      onChange={(e) => setTotalAmount(Number(e.target.value))}
                      step="0.01"
                      min="0"
                    />
                  </CurrencyInputWrapper>
                </div>
              </CostItem>
              <CostItem>
                <span>GST (10% of total amount):</span>
                <div className="amount">
                  <CurrencyInputWrapper>
                    <span>$</span>
                    <input
                      type="number"
                      value={gstAmount || ''}
                      onChange={(e) => {
                        setGstAmount(Number(e.target.value));
                        setTotalPayable(totalAmount + Number(e.target.value));
                      }}
                      step="0.01"
                      min="0"
                    />
                  </CurrencyInputWrapper>
                </div>
              </CostItem>
              <CostItem>
                <span>Total Payable:</span>
                <div className="amount">
                  <CurrencyInputWrapper>
                    <span>$</span>
                    <input
                      type="number"
                      value={totalPayable || ''}
                      onChange={(e) => setTotalPayable(Number(e.target.value))}
                      step="0.01"
                      min="0"
                    />
                  </CurrencyInputWrapper>
                </div>
              </CostItem>
            </SummaryContainer>

            <ButtonContainer>
              <CancelButton type="button" onClick={handleCancel}>
                Cancel
              </CancelButton>
              <SubmitButton type="submit" disabled={loading}>
                {loading ? (
                  <div style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                    <ButtonSkeleton style={{ width: '80%', maxWidth: '120px' }} />
                  </div>
                ) : 'Submit'}
              </SubmitButton>
            </ButtonContainer>
          </form>
        </div>

        {showNotesModal && (
          <ModalOverlay>
            <ModalContent>
              <ModalTitle>Item Notes</ModalTitle>
              <NotesTextarea
                value={currentNotes}
                onChange={(e) => setCurrentNotes(e.target.value)}
                placeholder="Enter notes for this item"
              />
              <ModalButtonContainer>
                <CloseButton onClick={() => setShowNotesModal(false)}>Cancel</CloseButton>
                <SaveButton onClick={handleSaveNotes}>Save Notes</SaveButton>
              </ModalButtonContainer>
            </ModalContent>
          </ModalOverlay>
        )}

        {toast.isVisible && (
          <ToastComponent
            message={toast.message}
            type={toast.type}
            onClose={() => { }}
          />
        )}
      </PageContainer>
    </Layout>
  );
};

export default CreatePOPage; 