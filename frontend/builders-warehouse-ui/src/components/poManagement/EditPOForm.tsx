import React, { useState } from 'react';
import styled from 'styled-components';
import { PurchaseOrder } from '../../services/purchaseOrderService';

const FormContainer = styled.div`
  padding: 20px;
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
`;

const FormTitle = styled.h2`
  margin-bottom: 20px;
  color: #333;
`;

const FormGroup = styled.div`
  margin-bottom: 15px;
`;

const Label = styled.label`
  display: block;
  margin-bottom: 5px;
  color: #666;
`;

const Input = styled.input`
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
`;

const ButtonGroup = styled.div`
  display: flex;
  gap: 10px;
  margin-top: 20px;
`;

const Button = styled.button`
  padding: 8px 16px;
  border-radius: 4px;
  border: none;
  cursor: pointer;
  font-size: 14px;
`;

const SaveButton = styled(Button)`
  background-color: #042B41;
  color: white;
`;

const CancelButton = styled(Button)`
  background-color: #f5f5f5;
  color: #333;
`;

interface EditPOFormProps {
  onCancel: () => void;
  onSubmit?: (data: any) => void;
  initialData?: any;
  po?: PurchaseOrder;
  onSave?: (data: any) => void;
}

interface FormData {
  supplierName: string;
  issueDate: string;
  status: string;
  notes: string;
}

const EditPOForm: React.FC<EditPOFormProps> = ({ onCancel, onSubmit, initialData, po, onSave }) => {
  const data = po || initialData || {};
  
  const [formData, setFormData] = useState<FormData>({
    supplierName: data.supplier_name || '',
    issueDate: data.issue_date || '',
    status: data.status || 'new',
    notes: data.notes || ''
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (onSave) {
      onSave(formData);
    } else if (onSubmit) {
      onSubmit(formData);
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev: FormData) => ({
      ...prev,
      [name]: value
    }));
  };

  return (
    <FormContainer>
      <FormTitle>Edit Purchase Order</FormTitle>
      <form onSubmit={handleSubmit}>
        <FormGroup>
          <Label>Supplier Name</Label>
          <Input
            type="text"
            name="supplierName"
            value={formData.supplierName}
            onChange={handleChange}
          />
        </FormGroup>
        <FormGroup>
          <Label>Issue Date</Label>
          <Input
            type="date"
            name="issueDate"
            value={formData.issueDate}
            onChange={handleChange}
          />
        </FormGroup>
        <FormGroup>
          <Label>Status</Label>
          <Input
            type="text"
            name="status"
            value={formData.status}
            onChange={handleChange}
          />
        </FormGroup>
        <FormGroup>
          <Label>Notes</Label>
          <Input
            type="text"
            name="notes"
            value={formData.notes}
            onChange={handleChange}
          />
        </FormGroup>
        <ButtonGroup>
          <SaveButton type="submit">Save</SaveButton>
          <CancelButton type="button" onClick={onCancel}>Cancel</CancelButton>
        </ButtonGroup>
      </form>
    </FormContainer>
  );
};

export default EditPOForm; 