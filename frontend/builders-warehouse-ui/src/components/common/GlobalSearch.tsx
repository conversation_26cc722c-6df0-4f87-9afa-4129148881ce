import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import globalSearchService, { GlobalSearchResult } from '../../services/globalSearchService';

const SearchContainer = styled.div`
  position: relative;
  flex: 1;
  max-width: 657px;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 10px;
`;

const SearchInput = styled.input`
  width: 657px;
  height: 50px;
  padding: 5px 10px 5px 40px;
  border-radius: 10px;
  border: 1px solid #304C5733;
  font-size: 1rem;
  background-color: #FFFFFF;
  box-shadow: 0px 0px 8px 0px #00000024;
  color: #111827;
  transition: border 0.2s, box-shadow 0.2s;

  &::placeholder {
    color: #9CA3AF;
    font-weight: 400;
  }

  &:focus {
    outline: none;
    border-color: #042B41;
    box-shadow: 0px 0px 8px 0px #00000035;
    background-color: #fff;
  }
`;

const SearchIcon = styled.div`
  position: absolute;
  left: 1.1rem;
  top: 50%;
  transform: translateY(-50%);
  color: #6B7280;
  display: flex;
  align-items: center;

  svg {
    width: 20px;
    height: 20px;
  }
`;

const SearchResults = styled.div<{ isVisible: boolean }>`
  position: absolute;
  top: calc(100% + 4px);
  left: 0;
  right: 0;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  max-height: 400px;
  overflow-y: auto;
  z-index: 1000;
  display: ${props => props.isVisible ? 'block' : 'none'};
`;

const ResultGroup = styled.div`
  padding: 0.5rem 0;

  &:not(:last-child) {
    border-bottom: 1px solid #E5E7EB;
  }
`;

const GroupTitle = styled.div`
  padding: 0.5rem 1rem;
  font-size: 0.8rem;
  font-weight: 500;
  color: #6B7280;
  text-transform: uppercase;
`;

const ResultItem = styled.div`
  padding: 0.75rem 1rem;
  cursor: pointer;
  display: flex;
  align-items: center;

  &:hover {
    background-color: #F9FAFB;
  }
`;

const ResultIcon = styled.div`
  width: 24px;
  height: 24px;
  margin-right: 12px;
  display: flex;
  align-items: center;
  justify-content: center;

  img {
    width: 20px;
    height: 20px;
  }
`;

const ResultInfo = styled.div`
  flex: 1;
`;

const ResultTitle = styled.div`
  font-size: 0.9rem;
  font-weight: 500;
  color: #111827;
`;

const ResultSubtitle = styled.div`
  font-size: 0.8rem;
  color: #6B7280;
  margin-top: 2px;
`;

const EmptyResults = styled.div`
  padding: 1.5rem;
  text-align: center;
  color: #6B7280;
`;

const LoadingIndicator = styled.div`
  padding: 1.5rem;
  text-align: center;
  color: #6B7280;
  display: flex;
  align-items: center;
  justify-content: center;

  &::after {
    content: '';
    width: 20px;
    height: 20px;
    border: 2px solid #E5E7EB;
    border-top: 2px solid #3B82F6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-left: 8px;
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }
`;

// We're using the GlobalSearchResult interface from our service

interface GlobalSearchProps {
  className?: string;
}

const GlobalSearch: React.FC<GlobalSearchProps> = ({ className }) => {
  const [searchQuery, setSearchQuery] = useState('');
  const [isResultsVisible, setIsResultsVisible] = useState(false);
  const [searchResults, setSearchResults] = useState<GlobalSearchResult[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const searchContainerRef = useRef<HTMLDivElement>(null);
  const navigate = useNavigate();

  useEffect(() => {
    // Search logic
    if (searchQuery.trim() === '') {
      setSearchResults([]);
      return;
    }

    // Debounce the search to avoid too many API calls
    const timer = setTimeout(async () => {
      setIsLoading(true);
      try {
        const results = await globalSearchService.globalSearch(searchQuery, {
          limit: 5 // Limit results per category
        });
        setSearchResults(results);
      } catch (error) {
        console.error('Error performing global search:', error);
        setSearchResults([]);
      } finally {
        setIsLoading(false);
      }
    }, 300); // 300ms debounce

    return () => clearTimeout(timer);
  }, [searchQuery]);

  useEffect(() => {
    // Handle clicks outside search container
    const handleClickOutside = (event: MouseEvent) => {
      if (searchContainerRef.current && !searchContainerRef.current.contains(event.target as Node)) {
        setIsResultsVisible(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
    setIsResultsVisible(true);
  };

  const handleResultClick = (result: GlobalSearchResult) => {
    // Navigate to the route with search parameter if available
    if (result.searchParam) {
      let url = `${result.route}?search=${encodeURIComponent(result.searchParam)}`;

      // Add searchField parameter if available
      if (result.searchField) {
        url += `&searchField=${encodeURIComponent(result.searchField)}`;
      }

      navigate(url);
    } else {
      navigate(result.route);
    }
    setIsResultsVisible(false);
    setSearchQuery('');
  };

  // Group results by category
  const groupedResults = searchResults.reduce((acc, result) => {
    if (!acc[result.category]) {
      acc[result.category] = [];
    }
    acc[result.category].push(result);
    return acc;
  }, {} as Record<string, GlobalSearchResult[]>);

  return (
    <SearchContainer ref={searchContainerRef} className={className}>
      <SearchIcon>
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
        </svg>
      </SearchIcon>
      <SearchInput
        type="text"
        value={searchQuery}
        onChange={handleSearchChange}
        placeholder="Search Anything"
        className="global-search-input"
      />
      <SearchResults isVisible={isResultsVisible && (searchResults.length > 0 || isLoading)}>
        {isLoading ? (
          <LoadingIndicator>Searching</LoadingIndicator>
        ) : (
          Object.entries(groupedResults).map(([category, results]) => (
            <ResultGroup key={category}>
              <GroupTitle>{category}</GroupTitle>
              {results.map(result => (
                <ResultItem key={result.id} onClick={() => handleResultClick(result)}>
                  <ResultIcon>
                    {result.icon ? (
                      <img src={result.icon} alt={result.title} />
                    ) : (
                      <span>🔍</span>
                    )}
                  </ResultIcon>
                  <ResultInfo>
                    <ResultTitle>{result.title}</ResultTitle>
                    {result.subtitle && (
                      <ResultSubtitle>{result.subtitle}</ResultSubtitle>
                    )}
                  </ResultInfo>
                </ResultItem>
              ))}
            </ResultGroup>
          ))
        )}
      </SearchResults>

      {isResultsVisible && searchQuery && !isLoading && searchResults.length === 0 && (
        <SearchResults isVisible={true}>
          <EmptyResults>No results found for "{searchQuery}"</EmptyResults>
        </SearchResults>
      )}
    </SearchContainer>
  );
};

export default GlobalSearch;