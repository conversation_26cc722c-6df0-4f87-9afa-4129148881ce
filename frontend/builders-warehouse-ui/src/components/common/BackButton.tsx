import React from 'react';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';

const BackButtonContainer = styled.div`
  width: 100%;
  position: relative;
  border-top: 1px solid #E5E7EB;
  border-bottom: 1px solid #E5E7EB;
  background-color: white;
`;

const ButtonWrapper = styled.div`
  max-width: 100%;
  margin: 0 auto;
  padding: 0 24px;
`;

const Button = styled.button`
  display: flex;
  align-items: center;
  gap: 8px;
  background: none;
  border: none;
  cursor: pointer;
  padding: 16px 0;
  color: #042B41;
  font-weight: 500;
  font-size: 14px;
  
  &:hover {
    opacity: 0.8;
  }
  
  svg {
    width: 20px;
    height: 20px;
  }
`;

interface BackButtonProps {
  label?: string;
}

const BackButton: React.FC<BackButtonProps> = ({ label = 'Back' }) => {
  const navigate = useNavigate();

  return (
    <BackButtonContainer>
      <ButtonWrapper>
        <Button onClick={() => navigate(-1)}>
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={2} stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" d="M10.5 19.5L3 12m0 0l7.5-7.5M3 12h18" />
          </svg>
          {label}
        </Button>
      </ButtonWrapper>
    </BackButtonContainer>
  );
};

export default BackButton; 