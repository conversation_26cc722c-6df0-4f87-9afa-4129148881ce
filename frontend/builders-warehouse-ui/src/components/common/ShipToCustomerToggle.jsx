import React, { useState, useEffect } from 'react';
import { Switch, Button, notification, Modal, Tooltip, Tag } from 'antd';
import { InfoCircleFilled, LockFilled } from '@ant-design/icons';
import axios from 'axios';
import { API_BASE_URL } from '../../config';

/**
 * Ship To Customer Toggle Component
 * 
 * This component shows a toggle switch to enable/disable ship_to_customer for a PO
 * with proper warnings and toast messages.
 * 
 * Once ship_to_customer is enabled, it cannot be disabled.
 */
const ShipToCustomerToggle = ({ poNumber, initialValue = false, onStatusChange }) => {
  const [shipToCustomer, setShipToCustomer] = useState(initialValue);
  const [loading, setLoading] = useState(false);
  const [canDisable, setCanDisable] = useState(true);
  const [confirmVisible, setConfirmVisible] = useState(false);
  const [newValue, setNewValue] = useState(false);

  // Fetch the current status on component mount
  useEffect(() => {
    checkShipStatus();
  }, [poNumber]);

  // Check the ship status from the backend
  const checkShipStatus = async () => {
    try {
      setLoading(true);
      const response = await axios.get(`${API_BASE_URL}/api/v1/inbound-delivery/ship-status/${poNumber}`);
      
      // Update state with server values
      setShipToCustomer(response.data.ship_to_customer);
      setCanDisable(!response.data.ship_to_customer); // Can't disable if already true
      
      // Show toast if needed
      if (response.data.toast && response.data.toast.show) {
        notification[response.data.toast.type || 'info']({
          message: response.data.toast.title || 'Ship to Customer Status',
          description: response.data.toast.message,
          duration: 5
        });
      }
    } catch (error) {
      console.error('Error checking ship status:', error);
      notification.error({
        message: 'Error',
        description: 'Failed to check shipping status. Please try again.',
        duration: 3
      });
    } finally {
      setLoading(false);
    }
  };

  // Update the ship_to_customer status
  const updateShipStatus = async (value) => {
    try {
      setLoading(true);
      const response = await axios.patch(
        `${API_BASE_URL}/api/v1/inbound-delivery/ship/${poNumber}?ship_to_customer=${value}`,
        {}
      );
      
      // Handle response and toast
      if (response.data.success) {
        setShipToCustomer(response.data.ship_to_customer);
        
        // Show toast if provided
        if (response.data.toast && response.data.toast.show) {
          notification[response.data.toast.type || 'success']({
            message: response.data.toast.title || 'Success',
            description: response.data.toast.message,
            duration: 5
          });
        }
        
        // Update parent component if needed
        if (onStatusChange) {
          onStatusChange(response.data.ship_to_customer);
        }
        
        // Update canDisable based on new value
        setCanDisable(!response.data.ship_to_customer);
      } else {
        // Handle error
        notification.warning({
          message: 'Warning',
          description: response.data.message || 'Failed to update ship to customer status.',
          duration: 5
        });
        
        // Show toast if provided
        if (response.data.toast && response.data.toast.show) {
          notification[response.data.toast.type || 'warning']({
            message: response.data.toast.title || 'Warning',
            description: response.data.toast.message,
            duration: 5
          });
        }
        
        // Reset toggle to previous value
        setShipToCustomer(response.data.ship_to_customer);
      }
    } catch (error) {
      console.error('Error updating ship status:', error);
      
      // Show error notification
      notification.error({
        message: 'Error',
        description: error.response?.data?.detail || 'Failed to update shipping status. Please try again.',
        duration: 5
      });
      
      // Reset toggle to previous value
      setShipToCustomer(initialValue);
    } finally {
      setLoading(false);
      setConfirmVisible(false);
    }
  };

  // Handle toggle change
  const handleToggleChange = (checked) => {
    // If trying to disable when already enabled, prevent it
    if (shipToCustomer && !checked) {
      notification.warning({
        message: 'Cannot Disable',
        description: 'Once "Ship to Customer" is enabled, it cannot be disabled.',
        duration: 5
      });
      return;
    }
    
    // If enabling (must be enabling at this point), show confirmation modal
    if (checked) {
      setNewValue(checked);
      setConfirmVisible(true);
    }
  };

  // Handle confirmation modal
  const handleConfirm = () => {
    updateShipStatus(newValue);
  };

  // Handle cancel confirmation
  const handleCancel = () => {
    setConfirmVisible(false);
    // Reset toggle to previous value
    setNewValue(shipToCustomer);
  };

  return (
    <div className="ship-to-customer-toggle">
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <Switch 
          checked={shipToCustomer}
          onChange={handleToggleChange}
          loading={loading}
          disabled={loading || (shipToCustomer && !canDisable)}
        />
        <span style={{ marginLeft: '8px' }}>Ship to Customer</span>
        
        {shipToCustomer && !canDisable && (
          <>
            <Tag color="blue" style={{ marginLeft: '8px' }}>
              <LockFilled style={{ marginRight: '4px' }} />
              Locked
            </Tag>
            <Tooltip title="Once enabled, 'Ship to Customer' cannot be disabled">
              <InfoCircleFilled style={{ marginLeft: '6px', color: '#1890ff' }} />
            </Tooltip>
          </>
        )}
      </div>

      <Modal
        title="Confirm Ship to Customer"
        open={confirmVisible}
        onOk={handleConfirm}
        onCancel={handleCancel}
        confirmLoading={loading}
        okText="Enable"
        cancelText="Cancel"
      >
        <p>Are you sure you want to enable ship to customer for this delivery?</p>
        <p style={{ fontWeight: 'bold', color: '#ff4d4f' }}>
          Warning: Once enabled, this action cannot be undone.
        </p>
      </Modal>
    </div>
  );
};

export default ShipToCustomerToggle; 