import React from 'react';
import styled from 'styled-components';

type StatusType = 'active' | 'inactive' | 'pending' | 'completed' | 'draft' | 'cancelled';

const getStatusColor = (status: StatusType): { bg: string; text: string } => {
  switch (status.toLowerCase() as StatusType) {
    case 'active':
      return { bg: '#E6F4EA', text: '#137333' };
    case 'inactive':
      return { bg: '#F3F4F6', text: '#6B7280' };
    case 'pending':
      return { bg: '#FFF8E6', text: '#B45309' };
    case 'completed':
      return { bg: '#E6F4EA', text: '#137333' };
    case 'draft':
      return { bg: '#EFF6FF', text: '#1E40AF' };
    case 'cancelled':
      return { bg: '#FEE2E2', text: '#B91C1C' };
    default:
      return { bg: '#F3F4F6', text: '#6B7280' };
  }
};

const Badge = styled.span<{ statusType: StatusType }>`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 9999px;
  font-size: 0.75rem;
  font-weight: 500;
  padding: 0.25rem 0.75rem;
  text-transform: capitalize;
  background-color: ${props => getStatusColor(props.statusType).bg};
  color: ${props => getStatusColor(props.statusType).text};
`;

interface StatusBadgeProps {
  status: string;
  className?: string;
}

const StatusBadge: React.FC<StatusBadgeProps> = ({ status, className }) => {
  const statusType = status.toLowerCase() as StatusType;
  
  return (
    <Badge statusType={statusType} className={className}>
      {status}
    </Badge>
  );
};

export default StatusBadge; 