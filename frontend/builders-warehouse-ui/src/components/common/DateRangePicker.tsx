import React, { useState, useRef, useEffect } from 'react';
import styled from 'styled-components';
import { format, parseISO } from 'date-fns';

const DateRangeContainer = styled.div`
  position: relative;
  display: flex;
  align-items: center;
  width: 245px;
  height: 42px;
  padding: 10px 15px;
  border: 1px solid #E5E7EB;
  border-radius: 10px;
  font-size: 14px;
  color: #6B7280;
  cursor: pointer;
  transition: all 0.3s ease-out;
  background: rgba(255, 255, 255, 1);
  
  &:hover {
    border-color: #D1D5DB;
  }
`;

const IconWrapper = styled.span`
  margin-right: 12px;
  color: #9CA3AF;
  display: flex;
  align-items: center;
`;

const CalendarDropdown = styled.div<{ isOpen: boolean }>`
  display: ${props => props.isOpen ? 'block' : 'none'};
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: rgba(255, 255, 255, 1);
  border: 1px solid #E5E7EB;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  margin-top: 4px;
  padding: 16px;
  width: 380px;
  max-height: 500px;
  overflow-y: auto;
  opacity: ${props => props.isOpen ? 1 : 0};
  transform: ${props => props.isOpen ? 'translateY(0)' : 'translateY(-10px)'};
  transition: opacity 300ms ease-out, transform 300ms ease-out;
`;

const CalendarHeader = styled.div`
  display: flex;
  justify-content: space-between;
  margin-bottom: 16px;
`;

const MonthYearDisplay = styled.div`
  font-weight: 500;
  font-size: 16px;
  color: #042B41;
`;

const NavigationButton = styled.button`
  background: none;
  border: none;
  cursor: pointer;
  color: #042B41;
  font-size: 18px;
  padding: 0 8px;
  
  &:hover {
    color: #0A3D5A;
  }
`;

const CalendarGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  gap: 4px;
`;

const DayOfWeek = styled.div`
  text-align: center;
  font-size: 12px;
  font-weight: 500;
  color: #6B7280;
  padding: 8px 0;
`;

const DayCell = styled.div<{ 
  isCurrentMonth?: boolean; 
  isToday?: boolean; 
  isSelected?: boolean;
  isInRange?: boolean;
  isSelectable?: boolean;
}>`
  text-align: center;
  padding: 8px 0;
  border-radius: 6px;
  cursor: ${props => props.isSelectable !== false ? 'pointer' : 'default'};
  font-weight: ${props => props.isToday ? '600' : '400'};
  color: ${props => {
    if (!props.isCurrentMonth) return '#D1D5DB';
    if (props.isSelected) return 'white';
    return '#333';
  }};
  background-color: ${props => {
    if (props.isSelected) return '#042B41';
    if (props.isInRange) return '#EBF5FF';
    return 'transparent';
  }};
  
  &:hover {
    background-color: ${props => {
      if (props.isSelected) return '#042B41';
      if (props.isSelectable !== false) return '#F3F4F6';
      return 'transparent';
    }};
  }
`;

const PresetContainer = styled.div`
  margin-top: 16px;
  margin-bottom: 8px;
  display: flex;
  flex-direction: column;
  gap: 8px;
`;

const PresetTitle = styled.div`
  font-size: 14px;
  color: #6B7280;
  margin-bottom: 4px;
  font-weight: 500;
`;

const PresetButtonsRow = styled.div`
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
  max-height: 120px;
  overflow-y: auto;
`;

const PresetButton = styled.button<{ isActive?: boolean }>`
  padding: 8px 12px;
  background-color: ${props => props.isActive ? '#EBF5FF' : '#F9FAFB'};
  border: 1px solid ${props => props.isActive ? '#93C5FD' : '#E5E7EB'};
  border-radius: 6px;
  font-size: 12px;
  color: ${props => props.isActive ? '#1E40AF' : '#4B5563'};
  cursor: pointer;
  white-space: nowrap;
  transition: all 0.2s ease;
  font-weight: ${props => props.isActive ? '600' : '400'};
  
  &:hover {
    background-color: ${props => props.isActive ? '#DBEAFE' : '#F3F4F6'};
    border-color: ${props => props.isActive ? '#60A5FA' : '#D1D5DB'};
    transform: translateY(-1px);
  }

  &:active {
    transform: translateY(0);
  }
`;

const ActionButtons = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  margin-top: 16px;
  padding-top: 12px;
  border-top: 1px solid #E5E7EB;
  position: sticky;
  bottom: 0;
  background: white;
  z-index: 10;
`;

const Button = styled.button<{ primary?: boolean }>`
  padding: 10px 20px;
  background-color: ${props => props.primary ? '#042B41' : 'white'};
  color: ${props => props.primary ? 'white' : '#4B5563'};
  border: 1px solid ${props => props.primary ? '#042B41' : '#E5E7EB'};
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 80px;
  
  &:hover {
    background-color: ${props => props.primary ? '#0A3D5A' : '#F9FAFB'};
    transform: translateY(-1px);
  }

  &:active {
    transform: translateY(0);
  }
`;

interface DateRangePickerProps {
  value: string;
  onChange: (range: string) => void;
}

const DateRangePicker: React.FC<DateRangePickerProps> = ({ value, onChange }) => {
  const [isOpen, setIsOpen] = useState(false);
  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [startDate, setStartDate] = useState<Date | null>(null);
  const [endDate, setEndDate] = useState<Date | null>(null);
  const [hoverDate, setHoverDate] = useState<Date | null>(null);
  const [activePreset, setActivePreset] = useState<string | null>(null);
  const [selectionState, setSelectionState] = useState<'start' | 'end'>('start');
  const containerRef = useRef<HTMLDivElement>(null);
  
  // Initialize with default values - today to 30 days in the future
  useEffect(() => {
    if (!value) {
      // Get fresh date object to ensure it's today
      const today = new Date();
      // Future date: 30 days from today
      const futureDate = new Date(today);
      futureDate.setDate(today.getDate() + 30);
      
      // Format dates in DD/MM/YYYY format for display
      const formattedStart = formatDateForDisplay(today);
      const formattedEnd = formatDateForDisplay(futureDate);
      
      onChange(`${formattedStart} - ${formattedEnd}`);
    }
  }, []);
  
  // Parse the value to get start and end dates
  useEffect(() => {
    if (value) {
      const [startStr, endStr] = value.split(' - ');
      
      if (startStr && endStr) {
        const parsedStart = parseDateFromDisplay(startStr);
        const parsedEnd = parseDateFromDisplay(endStr);
        
        if (parsedStart) {
          setStartDate(parsedStart);
          setCurrentMonth(new Date(parsedStart));
        }
        
        if (parsedEnd) {
          setEndDate(parsedEnd);
        }
      }
    }
  }, [value]);
  
  // Handle click outside to close the dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);
  
  // Format date as DD/MM/YYYY for display
  const formatDateForDisplay = (date: Date): string => {
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = date.getFullYear();
    return `${day}/${month}/${year}`;
  };
  
  // Format date as YYYY-MM-DD for backend
  const formatDateForBackend = (date: Date): string => {
    return date.toISOString().split('T')[0];
  };
  
  // Parse date from DD/MM/YYYY format
  const parseDateFromDisplay = (dateString: string): Date | null => {
    if (!dateString) return null;
    
    try {
      const [day, month, year] = dateString.split('/').map(Number);
      
      // Validate date parts
      if (isNaN(day) || isNaN(month) || isNaN(year) || 
          day < 1 || day > 31 || month < 1 || month > 12 || year < 1000) {
        console.error('Invalid date format:', dateString);
        return null;
      }
      
      const date = new Date(year, month - 1, day);
      
      // Check if date is valid (e.g., not Feb 30)
      if (date.getFullYear() !== year || date.getMonth() !== month - 1 || date.getDate() !== day) {
        console.error('Invalid date values:', dateString);
        return null;
      }
      
      return date;
    } catch (error) {
      console.error('Error parsing date:', error);
      return null;
    }
  };
  
  // Generate calendar days for current month
  const generateCalendarDays = () => {
    const year = currentMonth.getFullYear();
    const month = currentMonth.getMonth();
    
    // Get the first day of the month
    const firstDayOfMonth = new Date(year, month, 1);
    
    // Get the day of the week for the first day (0 for Sunday, 1 for Monday, etc.)
    const firstDayOfWeek = firstDayOfMonth.getDay();
    
    // Adjust for Monday as first day of week (0 becomes 6, 1 becomes 0, etc.)
    const firstDayIndex = firstDayOfWeek === 0 ? 6 : firstDayOfWeek - 1;
    
    // Get the last day of the month
    const lastDayOfMonth = new Date(year, month + 1, 0);
    
    // Create array for days from previous month to fill in first week
    const previousMonthDays = [];
    const previousMonthLastDay = new Date(year, month, 0).getDate();
    
    for (let i = 0; i < firstDayIndex; i++) {
      const day = previousMonthLastDay - firstDayIndex + i + 1;
      previousMonthDays.push(new Date(year, month - 1, day));
    }
    
    // Create array for days in current month
    const currentMonthDays = [];
    for (let day = 1; day <= lastDayOfMonth.getDate(); day++) {
      currentMonthDays.push(new Date(year, month, day));
    }
    
    // Create array for days from next month to fill in last week
    const nextMonthDays = [];
    const totalDaysShown = Math.ceil((firstDayIndex + lastDayOfMonth.getDate()) / 7) * 7;
    const nextMonthDaysCount = totalDaysShown - (previousMonthDays.length + currentMonthDays.length);
    
    for (let day = 1; day <= nextMonthDaysCount; day++) {
      nextMonthDays.push(new Date(year, month + 1, day));
    }
    
    return [...previousMonthDays, ...currentMonthDays, ...nextMonthDays];
  };
  
  // Check if date is today
  const isToday = (date: Date): boolean => {
    const today = new Date();
    return date.getDate() === today.getDate() &&
           date.getMonth() === today.getMonth() &&
           date.getFullYear() === today.getFullYear();
  };
  
  // Check if date is in current month
  const isCurrentMonth = (date: Date): boolean => {
    return date.getMonth() === currentMonth.getMonth() &&
           date.getFullYear() === currentMonth.getFullYear();
  };
  
  // Check if date is selected (start or end date)
  const isSelected = (date: Date): boolean => {
    if (!startDate && !endDate) return false;
    
    const dateTime = date.getTime();
    const startDateTime = startDate ? startDate.getTime() : null;
    const endDateTime = endDate ? endDate.getTime() : null;
    
    return (startDateTime === dateTime) || (endDateTime === dateTime);
  };
  
  // Check if date is in selected range
  const isInRange = (date: Date): boolean => {
    if (!startDate || !endDate) {
      if (startDate && hoverDate) {
        const dateTime = date.getTime();
        const startDateTime = startDate.getTime();
        const hoverDateTime = hoverDate.getTime();
        
        return (
          (dateTime > startDateTime && dateTime < hoverDateTime) ||
          (dateTime < startDateTime && dateTime > hoverDateTime)
        );
      }
      return false;
    }
    
    const dateTime = date.getTime();
    const startDateTime = startDate.getTime();
    const endDateTime = endDate.getTime();
    
    return (dateTime > startDateTime && dateTime < endDateTime) ||
           (dateTime < startDateTime && dateTime > endDateTime);
  };
  
  // Handle month navigation
  const handlePreviousMonth = () => {
    setCurrentMonth(prevMonth => {
      const newMonth = new Date(prevMonth);
      newMonth.setMonth(newMonth.getMonth() - 1);
      return newMonth;
    });
  };
  
  const handleNextMonth = () => {
    setCurrentMonth(prevMonth => {
      const newMonth = new Date(prevMonth);
      newMonth.setMonth(newMonth.getMonth() + 1);
      return newMonth;
    });
  };
  
  // Handle date selection
  const handleDateClick = (date: Date) => {
    // If we're selecting the start date...
    if (selectionState === 'start') {
      setStartDate(date);
      setEndDate(null);
      setSelectionState('end');
      setActivePreset(null);
      console.log('Selected start date:', formatDateForDisplay(date));
    }
    // If we're selecting the end date...
    else {
      // Ensure end date is not before start date
      if (startDate && date < startDate) {
        // If user clicks a date before start date, swap them
        setEndDate(startDate);
        setStartDate(date);
      } else {
        setEndDate(date);
      }
      
      // Reset to start date selection for next time
      setSelectionState('start');
      console.log('Selected end date:', formatDateForDisplay(date));
      
      // Auto apply after selecting both dates
      if (startDate) {
        // Short delay to allow UI to update before applying
        setTimeout(() => {
          const finalStartDate = date < startDate ? date : startDate;
          const finalEndDate = date < startDate ? startDate : date;
          
          const formattedStart = formatDateForDisplay(finalStartDate);
          const formattedEnd = formatDateForDisplay(finalEndDate);
          const dateRangeString = `${formattedStart} - ${formattedEnd}`;
          
          onChange(dateRangeString);
          setIsOpen(false);
          console.log('Auto-applied date range:', dateRangeString);
        }, 100);
      }
    }
  };
  
  // Handle date hover for range preview
  const handleDateHover = (date: Date) => {
    if (selectionState === 'end' && startDate) {
      setHoverDate(date);
    }
  };
  
  // Apply preset date ranges
  const applyPreset = (preset: string) => {
    const now = new Date();
    let start: Date;
    let end: Date = new Date(now);
    
    switch (preset) {
      case 'today':
        start = new Date(now);
        end = new Date(now);
        break;
      case 'yesterday':
        start = new Date(now);
        start.setDate(now.getDate() - 1);
        end = new Date(start);
        break;
      case 'thisWeek':
        start = new Date(now);
        // Get to Monday of current week
        const dayOfWeek = now.getDay() || 7; // Convert Sunday (0) to 7
        start.setDate(now.getDate() - dayOfWeek + 1);
        end = new Date(now); // End is today
        break;
      case 'lastWeek':
        start = new Date(now);
        // Get to Monday of last week
        const lastWeekDay = now.getDay() || 7;
        start.setDate(now.getDate() - lastWeekDay + 1 - 7);
        end = new Date(start);
        end.setDate(start.getDate() + 6);
        break;
      case 'thisMonth':
        start = new Date(now.getFullYear(), now.getMonth(), 1);
        end = new Date(now); // Today
        break;
      case 'lastMonth':
        start = new Date(now.getFullYear(), now.getMonth() - 1, 1);
        end = new Date(now.getFullYear(), now.getMonth(), 0);
        break;
      case 'last30Days':
        start = new Date(now);
        start.setDate(now.getDate() - 30);
        end = new Date(now);
        break;
      case 'next30Days':
        start = new Date(now);
        end = new Date(now);
        end.setDate(now.getDate() + 30);
        break;
      case 'last60Days':
        start = new Date(now);
        start.setDate(now.getDate() - 60);
        end = new Date(now);
        break;
      case 'lastQuarter':
        const currentMonth = now.getMonth();
        const currentQuarter = Math.floor(currentMonth / 3);
        const firstMonthOfQuarter = currentQuarter * 3;
        const firstMonthOfLastQuarter = firstMonthOfQuarter - 3;
        const yearOfLastQuarter = firstMonthOfLastQuarter < 0 
          ? now.getFullYear() - 1 
          : now.getFullYear();
        const adjustedMonth = firstMonthOfLastQuarter < 0 
          ? firstMonthOfLastQuarter + 12 
          : firstMonthOfLastQuarter;
        
        start = new Date(yearOfLastQuarter, adjustedMonth, 1);
        end = new Date(yearOfLastQuarter, adjustedMonth + 3, 0);
        break;
      default:
        console.warn('Unknown preset:', preset);
        return;
    }
    
    // Update the state immediately
    setStartDate(start);
    setEndDate(end);
    setCurrentMonth(new Date(start));
    setActivePreset(preset);
    setSelectionState('start');
    
    // Format dates for display and immediately apply
    const formattedStart = formatDateForDisplay(start);
    const formattedEnd = formatDateForDisplay(end);
    const dateRangeString = `${formattedStart} - ${formattedEnd}`;
    
    console.log(`Applied preset "${preset}":`, dateRangeString, { start, end });
    
    // Call the onChange handler with the formatted date range
    onChange(dateRangeString);
    
    // Close the dropdown after a short delay to show the selection
    setTimeout(() => {
      setIsOpen(false);
    }, 150);
  };
  
  // Apply the selected date range
  const applyDateRange = () => {
    if (!startDate || !endDate) {
      console.warn('Cannot apply date range: Both start and end dates must be selected');
      
      // If we don't have both dates, set default values
      const today = new Date();
      const futureDate = new Date(today);
      futureDate.setDate(today.getDate() + 30);
      
      setStartDate(today);
      setEndDate(futureDate);
      
      const formattedStart = formatDateForDisplay(today);
      const formattedEnd = formatDateForDisplay(futureDate);
      const defaultRange = `${formattedStart} - ${formattedEnd}`;
      
      onChange(defaultRange);
      setIsOpen(false);
      
      console.log('Applied default date range:', defaultRange);
      return;
    }
    
    // Ensure dates are in correct order
    const finalStartDate = startDate < endDate ? startDate : endDate;
    const finalEndDate = startDate < endDate ? endDate : startDate;
    
    const formattedStart = formatDateForDisplay(finalStartDate);
    const formattedEnd = formatDateForDisplay(finalEndDate);
    
    // Format the date string in DD/MM/YYYY - DD/MM/YYYY format
    const dateRangeString = `${formattedStart} - ${formattedEnd}`;
    
    console.log('Applied custom date range:', dateRangeString, { 
      start: finalStartDate, 
      end: finalEndDate 
    });
    
    // Call the onChange handler with the formatted date range
    onChange(dateRangeString);
    
    // Close the dropdown
    setIsOpen(false);
  };
  
  // Reset the date range
  const resetDateRange = () => {
    setStartDate(null);
    setEndDate(null);
    setActivePreset(null);
    setSelectionState('start');
  };
  
  // Format the month and year display
  const formatMonthYear = (date: Date) => {
    return format(date, 'MMMM yyyy');
  };
  
  // Get formatted display for the selected range
  const getDisplayValue = () => {
    if (value) {
      return value;
    }
    
    if (startDate && endDate) {
      return `${formatDateForDisplay(startDate)} - ${formatDateForDisplay(endDate)}`;
    }
    
    return 'Select Date Range';
  };
  
  return (
    <DateRangeContainer ref={containerRef} onClick={() => setIsOpen(!isOpen)}>
      <IconWrapper>
        📅
      </IconWrapper>
      {getDisplayValue()}
      
      <CalendarDropdown isOpen={isOpen} onClick={e => e.stopPropagation()}>
        <CalendarHeader>
          <NavigationButton onClick={handlePreviousMonth}>❮</NavigationButton>
          <MonthYearDisplay>{formatMonthYear(currentMonth)}</MonthYearDisplay>
          <NavigationButton onClick={handleNextMonth}>❯</NavigationButton>
        </CalendarHeader>
        
        <CalendarGrid>
          <DayOfWeek>Mo</DayOfWeek>
          <DayOfWeek>Tu</DayOfWeek>
          <DayOfWeek>We</DayOfWeek>
          <DayOfWeek>Th</DayOfWeek>
          <DayOfWeek>Fr</DayOfWeek>
          <DayOfWeek>Sa</DayOfWeek>
          <DayOfWeek>Su</DayOfWeek>
          
          {generateCalendarDays().map((date, index) => (
            <DayCell 
              key={index}
              isCurrentMonth={isCurrentMonth(date)}
              isToday={isToday(date)}
              isSelected={isSelected(date)}
              isInRange={isInRange(date)}
              onClick={() => handleDateClick(date)}
              onMouseEnter={() => handleDateHover(date)}
            >
              {date.getDate()}
            </DayCell>
          ))}
        </CalendarGrid>
        
        <PresetContainer>
          <PresetTitle>Presets</PresetTitle>
          <PresetButtonsRow>
            <PresetButton 
              isActive={activePreset === 'today'} 
              onClick={(e) => {
                e.stopPropagation();
                applyPreset('today');
              }}
            >
              Today
            </PresetButton>
            <PresetButton 
              isActive={activePreset === 'yesterday'} 
              onClick={(e) => {
                e.stopPropagation();
                applyPreset('yesterday');
              }}
            >
              Yesterday
            </PresetButton>
            <PresetButton 
              isActive={activePreset === 'last30Days'} 
              onClick={(e) => {
                e.stopPropagation();
                applyPreset('last30Days');
              }}
            >
              Last 30 Days
            </PresetButton>
            <PresetButton 
              isActive={activePreset === 'next30Days'} 
              onClick={(e) => {
                e.stopPropagation();
                applyPreset('next30Days');
              }}
            >
              Next 30 Days
            </PresetButton>
            <PresetButton 
              isActive={activePreset === 'last60Days'} 
              onClick={(e) => {
                e.stopPropagation();
                applyPreset('last60Days');
              }}
            >
              Last 60 Days
            </PresetButton>
            <PresetButton 
              isActive={activePreset === 'thisWeek'} 
              onClick={(e) => {
                e.stopPropagation();
                applyPreset('thisWeek');
              }}
            >
              This Week
            </PresetButton>
            <PresetButton 
              isActive={activePreset === 'lastWeek'} 
              onClick={(e) => {
                e.stopPropagation();
                applyPreset('lastWeek');
              }}
            >
              Last Week
            </PresetButton>
            <PresetButton 
              isActive={activePreset === 'thisMonth'} 
              onClick={(e) => {
                e.stopPropagation();
                applyPreset('thisMonth');
              }}
            >
              This Month
            </PresetButton>
            <PresetButton 
              isActive={activePreset === 'lastMonth'} 
              onClick={(e) => {
                e.stopPropagation();
                applyPreset('lastMonth');
              }}
            >
              Last Month
            </PresetButton>
            <PresetButton 
              isActive={activePreset === 'lastQuarter'} 
              onClick={(e) => {
                e.stopPropagation();
                applyPreset('lastQuarter');
              }}
            >
              Last Quarter
            </PresetButton>
          </PresetButtonsRow>
        </PresetContainer>
        
        <ActionButtons>
          <Button onClick={(e) => {
            e.stopPropagation();
            resetDateRange();
          }}>
            Reset
          </Button>
          <Button primary onClick={(e) => {
            e.stopPropagation();
            applyDateRange();
          }}>
            Apply
          </Button>
        </ActionButtons>
      </CalendarDropdown>
    </DateRangeContainer>
  );
};

export default DateRangePicker; 