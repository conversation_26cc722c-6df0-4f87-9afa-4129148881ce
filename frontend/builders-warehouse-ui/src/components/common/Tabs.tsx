import React from 'react';
import styled from 'styled-components';

interface Tab {
  id: string;
  label: string;
  content: React.ReactNode;
}

interface TabsProps {
  tabs: Tab[];
  activeTab: string;
  onTabChange: (tabId: string) => void;
}

const TabsContainer = styled.div`
  display: flex;
  flex-direction: column;
  width: 100%;
`;

const TabList = styled.div`
  display: flex;
  border-bottom: 1px solid #E5E7EB;
  margin-bottom: 1rem;
`;

const TabButton = styled.button<{ isActive: boolean }>`
  padding: 0.75rem 1rem;
  background: none;
  border: none;
  border-bottom: 2px solid ${props => props.isActive ? '#3B82F6' : 'transparent'};
  color: ${props => props.isActive ? '#3B82F6' : '#6B7280'};
  font-weight: ${props => props.isActive ? '600' : '500'};
  cursor: pointer;
  transition: all 0.2s;
  
  &:hover {
    color: #3B82F6;
  }
`;

const TabContent = styled.div`
  padding: 1rem;
`;

const Tabs: React.FC<TabsProps> = ({ tabs, activeTab, onTabChange }) => {
  return (
    <TabsContainer>
      <TabList>
        {tabs.map(tab => (
          <TabButton
            key={tab.id}
            isActive={activeTab === tab.id}
            onClick={() => onTabChange(tab.id)}
          >
            {tab.label}
          </TabButton>
        ))}
      </TabList>
      <TabContent>
        {tabs.find(tab => tab.id === activeTab)?.content}
      </TabContent>
    </TabsContainer>
  );
};

export default Tabs; 