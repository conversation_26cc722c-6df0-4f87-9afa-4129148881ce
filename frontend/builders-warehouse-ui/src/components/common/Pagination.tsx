import React from 'react';
import styled from 'styled-components';

// Styled components for pagination
const PaginationContainer = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1.5rem 0;
  width: 100%;
`;

const PaginationControls = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

const PageButton = styled.button<{ $active?: boolean }>`
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 2.5rem;
  height: 2.5rem;
  border: 1px solid ${props => props.$active ? '#042B41' : '#E5E7EB'};
  background-color: ${props => props.$active ? '#042B41' : 'white'};
  color: ${props => props.$active ? 'white' : '#374151'};
  border-radius: 0.25rem;
  font-size: 0.875rem;
  font-weight: ${props => props.$active ? '600' : '400'};
  cursor: pointer;
  padding: 0 0.625rem;
  transition: all 0.2s ease;
  
  &:hover {
    background-color: ${props => props.$active ? '#042B41' : '#F9FAFB'};
    border-color: ${props => props.$active ? '#042B41' : '#D1D5DB'};
  }
  
  &:disabled {
    cursor: not-allowed;
    opacity: 0.5;
    background-color: #F9FAFB;
    border-color: #E5E7EB;
    color: #9CA3AF;
  }
`;

const NavButton = styled(PageButton)`
  min-width: auto;
  padding: 0 0.75rem;
  display: flex;
  align-items: center;
  font-weight: 500;
`;

const PageInfo = styled.div`
  font-size: 0.875rem;
  color: #6B7280;
  display: flex;
  align-items: center;
`;

const ResultsPerPageContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;
`;

const ResultsPerPageLabel = styled.span`
  font-size: 0.875rem;
  color: #6B7280;
`;

const ResultsPerPageSelect = styled.select`
  padding: 0.375rem 0.75rem;
  border: 1px solid #E5E7EB;
  border-radius: 0.25rem;
  font-size: 0.875rem;
  color: #374151;
  background-color: white;
  cursor: pointer;
  
  &:focus {
    outline: none;
    border-color: #4B5563;
  }
`;

// Props interface
interface PaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  totalItems?: number;
  itemsPerPage?: number;
  onItemsPerPageChange?: (itemsPerPage: number) => void;
  itemsPerPageOptions?: number[];
  showItemsPerPage?: boolean;
}

/**
 * Reusable pagination component that handles navigation between pages
 * Designed to match the provided design with Previous/Next navigation
 */
const Pagination: React.FC<PaginationProps> = ({
  currentPage,
  totalPages,
  onPageChange,
  totalItems,
  itemsPerPage = 10,
  onItemsPerPageChange,
  itemsPerPageOptions = [10, 20, 50],
  showItemsPerPage = true
}) => {
  // Generate the page buttons to display
  const getPageNumbers = () => {
    if (totalPages <= 5) {
      // Show all pages if 5 or fewer
      return Array.from({ length: totalPages }, (_, i) => i + 1);
    }
    
    // Complex logic for more pages
    const pageNumbers = [];
    
    // Always show first page
    pageNumbers.push(1);
    
    if (currentPage > 3) {
      pageNumbers.push('...');
    }
    
    // Current page and adjacent pages
    const start = Math.max(2, currentPage - 1);
    const end = Math.min(totalPages - 1, currentPage + 1);
    
    for (let i = start; i <= end; i++) {
      pageNumbers.push(i);
    }
    
    if (currentPage < totalPages - 2) {
      pageNumbers.push('...');
    }
    
    // Always show last page if more than 1
    if (totalPages > 1) {
      pageNumbers.push(totalPages);
    }
    
    return pageNumbers;
  };

  const handleItemsPerPageChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    if (onItemsPerPageChange) {
      onItemsPerPageChange(Number(e.target.value));
      // Reset to page 1 when changing items per page
      onPageChange(1);
    }
  };

  const pageNumbers = getPageNumbers();
  
  // Calculate item range for display
  const startItem = totalItems ? Math.min((currentPage - 1) * itemsPerPage + 1, totalItems) : 0;
  const endItem = totalItems ? Math.min(currentPage * itemsPerPage, totalItems) : 0;

  return (
    <PaginationContainer>
      {/* Results per page dropdown */}
      {showItemsPerPage && onItemsPerPageChange && (
        <ResultsPerPageContainer>
          <ResultsPerPageLabel>Show results:</ResultsPerPageLabel>
          <ResultsPerPageSelect
            value={itemsPerPage}
            onChange={handleItemsPerPageChange}
          >
            {itemsPerPageOptions.map(option => (
              <option key={option} value={option}>
                {option}
              </option>
            ))}
          </ResultsPerPageSelect>
        </ResultsPerPageContainer>
      )}
      
      {/* Page info - showing x of y results */}
      {totalItems !== undefined && (
        <PageInfo>
          Showing {startItem} to {endItem} of {totalItems} results
        </PageInfo>
      )}
      
      {/* Pagination controls */}
      <PaginationControls>
        {/* Previous button */}
        <NavButton
          onClick={() => onPageChange(currentPage - 1)}
          disabled={currentPage === 1}
        >
          Previous
        </NavButton>
        
        {/* Page numbers */}
        {pageNumbers.map((page, index) => {
          if (page === '...') {
            return <span key={`ellipsis-${index}`}>...</span>;
          }
          
          return (
            <PageButton
              key={`page-${page}`}
              $active={page === currentPage}
              onClick={() => onPageChange(Number(page))}
            >
              {page}
            </PageButton>
          );
        })}
        
        {/* Next button */}
        <NavButton
          onClick={() => onPageChange(currentPage + 1)}
          disabled={currentPage === totalPages || totalPages === 0}
        >
          Next
        </NavButton>
      </PaginationControls>
    </PaginationContainer>
  );
};

export default Pagination; 