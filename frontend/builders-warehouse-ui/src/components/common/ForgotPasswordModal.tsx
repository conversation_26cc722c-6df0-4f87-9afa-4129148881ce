import React, { useState } from 'react';
import styled from 'styled-components';
import logoSvg from '../../assets/Builder-Logo.png';
import bgImage from '../../assets/signup.jpg';
import { passwordResetService } from '../../services/passwordResetService';

// Modal overlay with same background as login container
const ModalOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  min-height: 100vh;
  background: url(${bgImage}) no-repeat center center;
  background-size: cover;
  padding-left: 50px;
  z-index: 1000;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background-color: rgba(0, 0, 0, 0.5);
  }
`;

// Modal content styled exactly like the login form with identical positioning
const ModalContent = styled.div`
  background: white;
  padding: 2.5rem;
  border-radius: 40px;
  border: none;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.25);
  width: 100%;
  max-width: 500px;
  position: relative;
  z-index: 1;
  margin: 0 auto;
`;

const Logo = styled.div`
  text-align: center;
  margin-bottom: 30px;
  
  img {
    width: 250px;
  }
`;

const WelcomeTitle = styled.h1`
  font-size: 1.75rem;
  color: #333;
  margin-bottom: 15px;
  text-align: center;
  font-weight: 600;
`;

const Title = styled.h2`
  font-size: 1.25rem;
  color: #666;
  margin-bottom: 30px;
  text-align: center;
  font-weight: 400;
`;

const FormGroup = styled.div`
  margin-bottom: 1.5rem;
  
  label {
    display: block;
    margin-bottom: 0.6rem;
    font-weight: 500;
    color: #333;
  }
  
  input {
    width: 100%;
    padding: 0.85rem;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    font-size: 0.95rem;
    transition: border-color 0.2s;
    
    &:focus {
      outline: none;
      border-color: #042B41;
      box-shadow: 0 0 0 2px rgba(4, 43, 65, 0.1);
    }
  }
`;

const ResetButton = styled.button`
  width: 100%;
  padding: 0.85rem;
  background-color: #042B41;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 0.95rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  
  &:hover {
    background-color: #031f30;
  }
  
  &:disabled {
    background-color: #ccc;
    cursor: not-allowed;
  }
`;

const BackToLogin = styled.div`
  text-align: center;
  margin-top: 1.75rem;
  
  button {
    color: #042B41;
    text-decoration: none;
    font-weight: 500;
    cursor: pointer;
    background: none;
    border: none;
    font-size: inherit;
    
    &:hover {
      text-decoration: underline;
    }
  }
`;

const ErrorMessage = styled.div`
  color: #dc3545;
  margin-bottom: 1.5rem;
  padding: 0.75rem 1rem;
  background-color: #fff5f5;
  border: 1px solid #ffccd1;
  border-radius: 6px;
  font-size: 0.9rem;
`;

const SuccessMessage = styled.div`
  color: #28a745;
  margin-bottom: 1.5rem;
  padding: 0.75rem 1rem;
  background-color: #f8fff9;
  border: 1px solid #c3e6cb;
  border-radius: 6px;
  font-size: 0.9rem;
`;

interface ForgotPasswordModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const ForgotPasswordModal: React.FC<ForgotPasswordModalProps> = ({ isOpen, onClose }) => {
  const [email, setEmail] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!email) {
      setError('Please enter your email address.');
      return;
    }

    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      setError('Please enter a valid email address.');
      return;
    }
    
    setIsLoading(true);
    setError('');
    setSuccess('');
    
    try {
      // Call the backend API
      const response = await passwordResetService.requestPasswordReset(email);
      
      if (response.success) {
        setSuccess(response.message);
      setEmail('');
      
        // Auto close modal after 5 seconds on success
      setTimeout(() => {
        onClose();
        setSuccess('');
        }, 5000);
      } else {
        setError(response.message || 'Failed to send reset link. Please try again.');
      }
      
    } catch (error: any) {
      console.error('Forgot password error:', error);
      
      // Handle specific error messages from the backend
      if (error.message) {
        setError(error.message);
      } else {
      setError('Failed to send reset link. Please try again.');
      }
    } finally {
      setIsLoading(false);
    }
  };

  const handleClose = () => {
    setEmail('');
    setError('');
    setSuccess('');
    onClose();
  };

  const handleOverlayClick = (e: React.MouseEvent) => {
    // Only close if clicking on the overlay itself, not the modal content
    if (e.target === e.currentTarget) {
      handleClose();
    }
  };

  if (!isOpen) return null;

  return (
    <ModalOverlay onClick={handleOverlayClick}>
      <ModalContent onClick={(e) => e.stopPropagation()}>
        <Logo>
          <img src={logoSvg} alt="Builders Warehouse Australia" />
        </Logo>
        <WelcomeTitle>Welcome to Builders Warehouse</WelcomeTitle>
        <Title>Reset Your Password</Title>
        
        {error && <ErrorMessage>{error}</ErrorMessage>}
        {success && <SuccessMessage>{success}</SuccessMessage>}
        
        <form onSubmit={handleSubmit}>
          <FormGroup>
            <label htmlFor="email">Email ID</label>
            <input
              type="email"
              id="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="Enter Your Registered email id"
              disabled={isLoading}
            />
          </FormGroup>
          
          <ResetButton type="submit" disabled={isLoading}>
            {isLoading ? "Sending Reset Link..." : "Send Reset Link"}
          </ResetButton>
        </form>
        
        <BackToLogin>
          <button onClick={handleClose}>Back to Login</button>
        </BackToLogin>
      </ModalContent>
    </ModalOverlay>
  );
};

export default ForgotPasswordModal; 