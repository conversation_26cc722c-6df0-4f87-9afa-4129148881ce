import React, { useState, useEffect } from 'react';
import styled from 'styled-components';

interface ToastProps {
  message: string;
  type?: 'success' | 'error' | 'warning' | 'info';
  duration?: number;
  onClose?: () => void;
}

interface ToastContainerProps {
  type: string;
  visible: boolean;
}

const ToastContainer = styled.div<ToastContainerProps>`
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 12px 20px;
  min-width: 300px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  z-index: 1000;
  opacity: ${(props) => (props.visible ? 1 : 0)};
  transform: translateY(${(props) => (props.visible ? '0' : '-20px')});
  transition: opacity 0.3s, transform 0.3s;
  
  /* Different background colors based on type */
  background-color: ${(props) => {
    switch (props.type) {
      case 'success':
        return '#4CAF50';
      case 'error':
        return '#F44336';
      case 'warning':
        return '#FF9800';
      case 'info':
      default:
        return '#2196F3';
    }
  }};
  color: white;
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  color: white;
  margin-left: 10px;
  cursor: pointer;
  padding: 2px 6px;
  font-size: 16px;
  opacity: 0.6;
  
  &:hover {
    opacity: 1;
  }
`;

export const Toast: React.FC<ToastProps> = ({
  message,
  type = 'info',
  duration = 5000,
  onClose,
}) => {
  const [visible, setVisible] = useState(true);

  useEffect(() => {
    const timer = setTimeout(() => {
      setVisible(false);
      setTimeout(() => {
        if (onClose) onClose();
      }, 300); // Wait for fade out animation before calling onClose
    }, duration);

    return () => clearTimeout(timer);
  }, [duration, onClose]);

  const handleClose = () => {
    setVisible(false);
    setTimeout(() => {
      if (onClose) onClose();
    }, 300);
  };

  return (
    <ToastContainer type={type} visible={visible}>
      {message}
      <CloseButton onClick={handleClose}>×</CloseButton>
    </ToastContainer>
  );
};

export default Toast; 