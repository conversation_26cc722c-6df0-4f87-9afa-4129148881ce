import React, { createContext, useContext, useState, useCallback } from 'react';
import styled from 'styled-components';

// Define the error notification component
const ErrorNotification = styled.div<{ visible: boolean }>`
  position: fixed;
  top: 20px;
  right: 20px;
  background-color: #FEE2E2;
  color: #B91C1C;
  border: 1px solid #F87171;
  border-radius: 6px;
  padding: 16px 20px;
  max-width: 400px;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  z-index: 1000;
  transition: transform 0.3s ease, opacity 0.3s ease;
  transform: ${props => props.visible ? 'translateY(0)' : 'translateY(-20px)'};
  opacity: ${props => props.visible ? '1' : '0'};
  pointer-events: ${props => props.visible ? 'auto' : 'none'};
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
`;

const ErrorMessage = styled.div`
  flex: 1;
  margin-right: 12px;
  font-size: 14px;
`;

const ErrorTitle = styled.div`
  font-weight: 600;
  margin-bottom: 4px;
  font-size: 16px;
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  color: #B91C1C;
  font-size: 20px;
  cursor: pointer;
  padding: 0;
  line-height: 1;
`;

// Define the context type
interface ErrorContextType {
  showError: (message: string, title?: string) => void;
  clearError: () => void;
}

// Create the context
const ErrorContext = createContext<ErrorContextType | undefined>(undefined);

// Create the provider component
export const ErrorProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [error, setError] = useState<{ message: string; title?: string } | null>(null);
  const [visible, setVisible] = useState(false);

  // Show error notification
  const showError = useCallback((message: string, title?: string) => {
    setError({ message, title: title || 'Error' });
    setVisible(true);
    
    // Auto-hide after 5 seconds
    setTimeout(() => {
      setVisible(false);
    }, 5000);
  }, []);

  // Clear error notification
  const clearError = useCallback(() => {
    setVisible(false);
    setTimeout(() => setError(null), 300); // Wait for animation to complete
  }, []);

  return (
    <ErrorContext.Provider value={{ showError, clearError }}>
      {children}
      
      {error && (
        <ErrorNotification visible={visible}>
          <ErrorMessage>
            <ErrorTitle>{error.title}</ErrorTitle>
            {error.message}
          </ErrorMessage>
          <CloseButton onClick={clearError}>×</CloseButton>
        </ErrorNotification>
      )}
    </ErrorContext.Provider>
  );
};

// Create a hook to use the error context
export const useError = () => {
  const context = useContext(ErrorContext);
  if (context === undefined) {
    throw new Error('useError must be used within an ErrorProvider');
  }
  return context;
};
