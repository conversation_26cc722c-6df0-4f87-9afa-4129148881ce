import React, { useEffect, useState } from 'react';
import styled from 'styled-components';
import { useLocation, useNavigate } from 'react-router-dom';

interface SearchBarProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  autoApplyUrlParams?: boolean; // Whether to automatically apply search params from URL
  updateUrlOnSearch?: boolean; // Whether to update the URL when the search value changes
  onSearch?: (value: string) => void; // Optional callback when search is triggered
}

const SearchContainer = styled.div`
  position: relative;
  width: 100%;
`;

const SearchInput = styled.input`
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  border: 1px solid #E5E7EB;
  border-radius: 0.375rem;
  font-size: 1rem;
  color: #111827;
  transition: all 0.2s;

  &:focus {
    outline: none;
    border-color: #3B82F6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  &::placeholder {
    color: #9CA3AF;
  }
`;

const SearchIcon = styled.div`
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: #9CA3AF;
`;

const SearchBar: React.FC<SearchBarProps> = ({
  value,
  onChange,
  placeholder = 'Search',
  className,
  autoApplyUrlParams = true,
  updateUrlOnSearch = false,
  onSearch
}) => {
  const location = useLocation();
  const navigate = useNavigate();
  const [internalValue, setInternalValue] = useState(value);

  // Extract search parameter from URL and apply it if autoApplyUrlParams is true
  useEffect(() => {
    if (autoApplyUrlParams) {
      const searchParams = new URLSearchParams(location.search);
      const searchValue = searchParams.get('search');

      if (searchValue) {
        // Always update the internal value
        setInternalValue(searchValue);

        // Only call onChange if the value is different to avoid infinite loops
        if (searchValue !== value) {
          onChange(searchValue);

          // If onSearch is provided, call it with the search value from URL
          if (onSearch) {
            onSearch(searchValue);
          }
        } else {
          // Even if the value is the same, we should trigger the search
          // This ensures the search is performed when navigating directly to a URL with search params
          if (onSearch) {
            onSearch(searchValue);
          }
        }
      }
    }
  }, [location.search, onChange, value, autoApplyUrlParams, onSearch]);

  // Update internal value when external value changes
  useEffect(() => {
    setInternalValue(value);
  }, [value]);

  // Handle input change
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setInternalValue(newValue);
    onChange(newValue);

    // If updateUrlOnSearch is true, update the URL with the search parameter
    if (updateUrlOnSearch) {
      const currentParams = new URLSearchParams(location.search);

      if (newValue) {
        currentParams.set('search', newValue);
      } else {
        currentParams.delete('search');
      }

      const newSearch = currentParams.toString();
      const newPath = `${location.pathname}${newSearch ? `?${newSearch}` : ''}`;

      navigate(newPath, { replace: true });
    }
  };

  // Handle key press (Enter)
  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter' && onSearch) {
      onSearch(internalValue);
    }
  };

  return (
    <SearchContainer className={className}>
      <SearchIcon>
        <svg
          width="16"
          height="16"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        >
          <circle cx="11" cy="11" r="8" />
          <line x1="21" y1="21" x2="16.65" y2="16.65" />
        </svg>
      </SearchIcon>
      <SearchInput
        type="text"
        value={internalValue}
        onChange={handleInputChange}
        onKeyPress={handleKeyPress}
        placeholder={placeholder}
      />
    </SearchContainer>
  );
};

export default SearchBar;