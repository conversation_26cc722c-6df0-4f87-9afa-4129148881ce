import React, { ButtonHTMLAttributes } from 'react';
import styled, { css } from 'styled-components';

export type ButtonVariant = 'primary' | 'secondary' | 'outline' | 'danger' | 'success';
export type ButtonSize = 'small' | 'medium' | 'large' | 'sm';

const getVariantStyles = (variant: ButtonVariant) => {
  switch (variant) {
    case 'primary':
      return css`
        background-color: #042B41;
        color: white;
        border: none;
        
        &:hover {
          background-color: #0A3D5A;
        }
      `;
    case 'secondary':
      return css`
        background-color: #4A6572;
        color: white;
        border: none;
        
        &:hover {
          background-color: #5A7A8A;
        }
      `;
    case 'outline':
      return css`
        background-color: transparent;
        color: #042B41;
        border: 1px solid #042B41;
        
        &:hover {
          background-color: #F0F5F9;
        }
      `;
    case 'danger':
      return css`
        background-color: #DC3545;
        color: white;
        border: none;
        
        &:hover {
          background-color: #C82333;
        }
      `;
    case 'success':
      return css`
        background-color: #28A745;
        color: white;
        border: none;
        
        &:hover {
          background-color: #218838;
        }
      `;
    default:
      return '';
  }
};

const getSizeStyles = (size: ButtonSize) => {
  switch (size) {
    case 'small':
    case 'sm':
      return css`
        padding: 6px 12px;
        font-size: 0.875rem;
      `;
    case 'medium':
      return css`
        padding: 8px 16px;
        font-size: 1rem;
      `;
    case 'large':
      return css`
        padding: 10px 20px;
        font-size: 1.125rem;
      `;
    default:
      return '';
  }
};

const StyledButton = styled.button<{
  variant: ButtonVariant;
  size: ButtonSize;
  fullWidth: boolean;
}>`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  width: ${props => props.fullWidth ? '100%' : 'auto'};
  
  ${props => getVariantStyles(props.variant)}
  ${props => getSizeStyles(props.size)}
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
  
  .icon {
    margin-right: ${props => props.children ? '8px' : '0'};
  }
`;

export interface ButtonProps extends ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: ButtonVariant;
  size?: ButtonSize;
  fullWidth?: boolean;
  icon?: React.ReactNode;
  isLoading?: boolean;
}

const Button: React.FC<ButtonProps> = ({
  variant = 'primary',
  size = 'medium',
  fullWidth = false,
  icon,
  children,
  isLoading = false,
  ...rest
}) => {
  return (
    <StyledButton
      variant={variant}
      size={size}
      fullWidth={fullWidth}
      {...rest}
    >
      {icon && <span className="icon">{icon}</span>}
      {isLoading ? 'Loading' : children}
    </StyledButton>
  );
};

export default Button; 