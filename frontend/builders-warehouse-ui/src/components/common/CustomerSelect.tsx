import React, { useState, useEffect, useRef } from 'react';
import styled from 'styled-components';
import customerService, { Customer } from '../../services/customerService';

const SelectContainer = styled.div`
  position: relative;
  width: 100%;
`;

const SelectInput = styled.input`
  width: 100%;
  padding: 0.625rem;
  border: 1px solid #D1D5DB;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  
  &:focus {
    outline: none;
    border-color: #042B41;
    box-shadow: 0 0 0 2px rgba(4, 43, 65, 0.1);
  }
`;

const DropdownList = styled.ul<{ isOpen: boolean }>`
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  max-height: 200px;
  overflow-y: auto;
  background: white;
  border: 1px solid #D1D5DB;
  border-top: none;
  border-radius: 0 0 0.375rem 0.375rem;
  margin: 0;
  padding: 0;
  list-style: none;
  z-index: 1000;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  display: ${props => props.isOpen ? 'block' : 'none'};
`;

const DropdownItem = styled.li`
  padding: 0.5rem;
  cursor: pointer;
  font-size: 0.875rem;
  
  &:hover {
    background-color: #F3F4F6;
  }
`;

const LoadingMessage = styled.div`
  padding: 0.5rem;
  color: #6B7280;
  font-style: italic;
  font-size: 0.875rem;
`;

interface CustomerSelectProps {
  value: string;
  onChange: (customerId: string, customerName: string) => void;
  name: string;
  placeholder?: string;
  required?: boolean;
}

const CustomerSelect: React.FC<CustomerSelectProps> = ({
  value,
  onChange,
  name,
  placeholder = 'Search Customer',
  required = false
}) => {
  const [searchTerm, setSearchTerm] = useState('');
  const [isOpen, setIsOpen] = useState(false);
  const [customers, setCustomers] = useState<Customer[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedCustomer, setSelectedCustomer] = useState<Customer | null>(null);
  const containerRef = useRef<HTMLDivElement>(null);
  const searchTimeoutRef = useRef<ReturnType<typeof setTimeout>>(null);
  
  // Load the selected customer when value changes
  useEffect(() => {
    if (value && (!selectedCustomer || value !== selectedCustomer.id)) {
      const loadCustomer = async () => {
        try {
          const customer = await customerService.getCustomerById(value);
          if (customer && customer.name) {
            setSelectedCustomer(customer);
            setSearchTerm(customer.name);
          }
        } catch (error) {
          console.error('Error loading customer:', error);
          setSelectedCustomer(null);
          setSearchTerm('');
        }
      };
      
      loadCustomer();
    } else if (!value) {
      setSelectedCustomer(null);
      setSearchTerm('');
    }
  }, [value]);
  
  // Handle outside clicks
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  // Search for customers when search term changes
  useEffect(() => {
    // Clear any existing timeout
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    const searchCustomers = async () => {
      if (!isOpen) return;
      
      setIsLoading(true);
      try {
        const result = await customerService.getCustomers(1, 10, searchTerm);
        setCustomers(result.items || []);
      } catch (error) {
        console.error('Error searching customers:', error);
        setCustomers([]);
      } finally {
        setIsLoading(false);
      }
    };
    
    // Set new timeout
    searchTimeoutRef.current = setTimeout(searchCustomers, 300);
    
    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, [searchTerm, isOpen]);
  
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchTerm(value);
    setIsOpen(true);
    
    // Clear selection if user is typing
    if (selectedCustomer && value !== selectedCustomer.name) {
      setSelectedCustomer(null);
      onChange('', ''); // Clear the parent's value
    }
  };
  
  const handleSelect = (customer: Customer) => {
    setSelectedCustomer(customer);
    setSearchTerm(customer.name);
    setIsOpen(false);
    onChange(customer.id, customer.name);
  };
  
  const handleFocus = () => {
    setIsOpen(true);
    // Trigger search immediately on focus if there's a search term
    if (searchTerm.length > 0) {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
      searchTimeoutRef.current = setTimeout(async () => {
        setIsLoading(true);
        try {
          const result = await customerService.getCustomers(1, 10, searchTerm);
          setCustomers(result.items || []);
        } catch (error) {
          console.error('Error searching customers:', error);
          setCustomers([]);
        } finally {
          setIsLoading(false);
        }
      }, 0);
    }
  };
  
  return (
    <SelectContainer ref={containerRef}>
      <SelectInput
        type="text"
        name={name}
        value={searchTerm}
        onChange={handleInputChange}
        onFocus={handleFocus}
        placeholder={placeholder}
        required={required}
        autoComplete="off"
      />
      
      <DropdownList isOpen={isOpen}>
        {isLoading ? (
          <LoadingMessage>Loading customers...</LoadingMessage>
        ) : customers.length > 0 ? (
          customers.map((customer) => (
            <DropdownItem
              key={customer.id}
              onClick={() => handleSelect(customer)}
            >
              {customer.name}
            </DropdownItem>
          ))
        ) : searchTerm.length > 0 ? (
          <LoadingMessage>No customers found</LoadingMessage>
        ) : null}
      </DropdownList>
    </SelectContainer>
  );
};

export default CustomerSelect; 