import React, { ReactNode } from 'react';
import styled from 'styled-components';

const TableContainer = styled.div`
  width: 100%;
  overflow-x: auto;
`;

const StyledTable = styled.table`
  width: 100%;
  border-collapse: collapse;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  overflow: hidden;
`;

const TableHeader = styled.thead`
  background-color: white;
  color: #042B41;
  border-bottom: 2px solid #e0e0e0;
  
  th {
    padding: 16px;
    text-align: left;
    font-weight: 600;
    white-space: nowrap;
    border-right: 1px solid #e0e0e0;
    
    &:last-child {
      border-right: none;
    }
  }
`;

const TableRow = styled.tr<{ clickable?: boolean }>`
  background-color: white;
  border-bottom: 1px solid #e0e0e0;
  cursor: ${props => props.clickable ? 'pointer' : 'default'};
  
  &:last-child {
    border-bottom: none;
  }
  
  &:hover {
    background-color: ${props => props.clickable ? '#f9fafb' : 'white'};
  }
  
  td {
    padding: 16px;
    color: #333;
    border-right: 1px solid #e0e0e0;
    
    &:last-child {
      border-right: none;
    }
  }
`;

const EmptyState = styled.div`
  padding: 48px 24px;
  text-align: center;
  color: #6B7280;
  
  svg {
    display: block;
    margin: 0 auto 16px;
    color: #D1D5DB;
  }
  
  h3 {
    margin: 0 0 8px;
    font-size: 16px;
    font-weight: 500;
  }
  
  p {
    margin: 0;
    font-size: 14px;
  }
`;

interface Column<T> {
  header: string;
  accessor: keyof T;
  cell?: (value: any, row: T) => ReactNode;
  width?: string;
}

interface DataTableProps<T> {
  columns: Column<T>[];
  data: T[];
  emptyMessage?: string;
  className?: string;
  onRowClick?: (row: T) => void;
}

function DataTable<T extends Record<string, any>>({
  columns,
  data,
  emptyMessage = 'No data found',
  className,
  onRowClick,
}: DataTableProps<T>): React.ReactElement {
  if (data.length === 0) {
    return (
      <TableContainer className={className}>
        <StyledTable>
          <TableHeader>
            <tr>
              {columns.map((column, index) => (
                <th key={index} style={column.width ? { width: column.width } : undefined}>
                  {column.header}
                </th>
              ))}
            </tr>
          </TableHeader>
          <tbody>
            <TableRow>
              <td colSpan={columns.length}>
                <EmptyState>
                  <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M9 5H7C5.89543 5 5 5.89543 5 7V19C5 20.1046 5.89543 21 7 21H17C18.1046 21 19 20.1046 19 19V7C19 5.89543 18.1046 5 17 5H15M9 5C9 6.10457 9.89543 7 11 7H13C14.1046 7 15 6.10457 15 5M9 5C9 3.89543 9.89543 3 11 3H13C14.1046 3 15 3.89543 15 5M12 12H15M12 16H15M9 12H9.01M9 16H9.01" stroke="currentColor" strokeWidth="2" strokeLinecap="round"/>
                  </svg>
                  <h3>No results found</h3>
                  <p>{emptyMessage}</p>
                </EmptyState>
              </td>
            </TableRow>
          </tbody>
        </StyledTable>
      </TableContainer>
    );
  }

  return (
    <TableContainer className={className}>
      <StyledTable>
        <TableHeader>
          <tr>
            {columns.map((column, index) => (
              <th key={index} style={column.width ? { width: column.width } : undefined}>
                {column.header}
              </th>
            ))}
          </tr>
        </TableHeader>
        <tbody>
          {data.map((row, rowIndex) => (
            <TableRow 
              key={rowIndex}
              onClick={onRowClick ? () => onRowClick(row) : undefined}
              clickable={!!onRowClick}
            >
              {columns.map((column, colIndex) => (
                <td key={colIndex}>
                  {column.cell 
                    ? column.cell(row[column.accessor], row)
                    : row[column.accessor]}
                </td>
              ))}
            </TableRow>
          ))}
        </tbody>
      </StyledTable>
    </TableContainer>
  );
}

export default DataTable; 