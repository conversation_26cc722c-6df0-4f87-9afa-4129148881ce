import React, { useState } from 'react';
import styled from 'styled-components';
import { API_URL } from '../../config';
import skuDefaultImage from '../../assets/sku.png';

// Track recently uploaded SKUs to force cache busting
const recentlyUploadedSkus = new Map<string, number>();
const RECENT_UPLOAD_DURATION = 60000; // 1 minute

interface SkuImageProps {
    imagePath?: string;
    alt?: string;
    width?: number;
    height?: number;
    className?: string;
    onClick?: (e: React.MouseEvent) => void;
    enableModal?: boolean;
    showBlankWhenNoImage?: boolean; // New prop to control blank display
}

const StyledImage = styled.img<{ width?: number; height?: number }>`
  width: ${props => props.width || 40}px;
  height: ${props => props.height || 40}px;
  object-fit: cover;
  border-radius: 4px;
  cursor: pointer;
  border: 1px solid #E5E7EB;
  flex-shrink: 0;
  
  &:hover {
    border-color: #042B41;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
`;

const ModalOverlay = styled.div<{ isOpen: boolean }>`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.8);
  display: ${props => props.isOpen ? 'flex' : 'none'};
  align-items: center;
  justify-content: center;
  z-index: 10000;
`;

const ModalContent = styled.div`
  position: relative;
  max-width: 90vw;
  max-height: 90vh;
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
`;

const ModalImage = styled.img`
  max-width: 100%;
  max-height: 70vh;
  object-fit: contain;
  border-radius: 4px;
`;

const ModalCloseButton = styled.button`
  position: absolute;
  top: 10px;
  right: 10px;
  background: #042B41;
  color: white;
  border: none;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  
  &:hover {
    background: #031F30;
  }
`;

const BlankPlaceholder = styled.div<{ width?: number; height?: number }>`
  width: ${props => props.width || 40}px;
  height: ${props => props.height || 40}px;
  border: 1px solid #E5E7EB;
  border-radius: 4px;
  flex-shrink: 0;
  background-color: #F9FAFB;
`;

const SkuImage: React.FC<SkuImageProps> = ({
    imagePath,
    alt = "SKU Image",
    width = 40,
    height = 40,
    className,
    onClick,
    enableModal = true,
    showBlankWhenNoImage = false,
}) => {
    const [isModalOpen, setIsModalOpen] = useState(false);
    const [imageError, setImageError] = useState(false);

    // Extract SKU from image path for cache busting
    const extractSkuFromPath = (path?: string): string | null => {
        if (!path) return null;

        // Extract filename from path
        const filename = path.split('/').pop();
        if (!filename) return null;

        // Remove extension to get SKU
        const sku = filename.split('.')[0];
        return sku;
    };

    // Check if SKU was recently uploaded
    const isRecentlyUploaded = (sku: string): boolean => {
        const uploadTime = recentlyUploadedSkus.get(sku);
        if (!uploadTime) return false;

        const now = Date.now();
        if (now - uploadTime > RECENT_UPLOAD_DURATION) {
            recentlyUploadedSkus.delete(sku);
            return false;
        }

        return true;
    };

    // Construct full image URL
    const getImageUrl = (path?: string): string => {
        if (!path || path.trim() === '' || imageError) {
            return skuDefaultImage;
        }

        let baseUrl: string;

        // If path already starts with http or https, use it as is
        if (path.startsWith('http://') || path.startsWith('https://')) {
            baseUrl = path;
        } else if (path.startsWith('/')) {
            // If path starts with /, use it with the API_URL
            const apiUrl = API_URL || window.location.origin;
            baseUrl = `${apiUrl}${path}`;
        } else {
            // Otherwise, assume it's a relative path and construct full URL
            const apiUrl = API_URL || window.location.origin;
            baseUrl = `${apiUrl}/${path}`;
        }

        // Add cache-busting parameter for recently uploaded SKUs
        const sku = extractSkuFromPath(path);
        if (sku && isRecentlyUploaded(sku)) {
            const separator = baseUrl.includes('?') ? '&' : '?';
            return `${baseUrl}${separator}t=${Date.now()}`;
        }

        return baseUrl;
    };

    // Check if we should show image or leave blank
    const shouldShowImage = (path?: string): boolean => {
        return Boolean(path && path.trim() !== '' && !imageError);
    };

    const handleImageClick = (e: React.MouseEvent) => {
        e.stopPropagation();

        if (onClick) {
            onClick(e);
        } else if (enableModal) {
            setIsModalOpen(true);
        }
    };

    const handleModalClose = () => {
        setIsModalOpen(false);
    };

    const handleImageError = () => {
        setImageError(true);
    };

    const imageUrl = getImageUrl(imagePath);
    const hasImage = shouldShowImage(imagePath);

    // If showBlankWhenNoImage is true and there's no image, show blank placeholder
    if (showBlankWhenNoImage && !hasImage) {
        return (
            <BlankPlaceholder
                width={width}
                height={height}
                className={className}
            />
        );
    }

    return (
        <>
            <StyledImage
                src={imageUrl}
                alt={alt}
                width={width}
                height={height}
                className={className}
                onClick={handleImageClick}
                onError={handleImageError}
            />

            {enableModal && (
                <ModalOverlay isOpen={isModalOpen} onClick={handleModalClose}>
                    <ModalContent onClick={(e) => e.stopPropagation()}>
                        <ModalCloseButton onClick={handleModalClose}>×</ModalCloseButton>
                        <ModalImage
                            src={imageUrl}
                            alt={alt}
                            onError={handleImageError}
                        />
                    </ModalContent>
                </ModalOverlay>
            )}
        </>
    );
};

// Function to mark SKUs as recently uploaded (for cache busting)
export const markSkusAsRecentlyUploaded = (skuCodes: string[]) => {
    const now = Date.now();
    skuCodes.forEach(sku => {
        recentlyUploadedSkus.set(sku, now);
    });
    console.log(`Marked ${skuCodes.length} SKUs as recently uploaded for cache busting`);
};

export default SkuImage; 