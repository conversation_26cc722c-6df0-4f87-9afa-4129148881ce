import React, { memo } from 'react';
import styled from 'styled-components';
import { Customer } from '../../services/customerService';
import customerEditIcon from '../../assets/customerEditIcon.png';
import customerHistoryIcon from '../../assets/customerHistoryIcon.png';
import LoadingSpinner from '../ui/LoadingSpinner';

const TableContainer = styled.div`
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  min-height: 300px;
`;

const Table = styled.table`
  width: 100%;
  border-collapse: collapse;
  background-color: white;
  border: 1px solid #e0e0e0;
`;

const TableHeader = styled.thead`
  background-color: #042B41;
  color: white;
  
  th {
    padding: 10px 12px;
    text-align: left;
    font-weight: 500;
    font-size: 14px;
    border: 1px solid #0A3D5A;
    
    &:last-child {
      text-align: center;
    }
  }
`;

const TableBody = styled.tbody`
  tr {
    background-color: white;
    border-bottom: 1px solid #e0e0e0;
    height: 48px;
    
    &:last-child {
      border-bottom: none;
    }
    
    &:hover {
      background-color: #f9fafb;
    }
  }
  
  td {
    padding: 0 12px;
    color: #333;
    font-size: 14px;
    vertical-align: middle;
    border: 1px solid #e0e0e0;
    
    &:last-child {
      text-align: center;
    }
  }
`;

const MessageRow = styled.tr`
  td {
    text-align: center !important;
    padding: 20px !important;
    color: #6B7280 !important;
    font-size: 14px !important;
  }
`;

const LoadingRow = styled.tr`
  td {
    text-align: center !important;
    padding: 40px 20px !important;
    border: none !important;
  }
`;

const LoadingContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 12px;
`;

const LoadingText = styled.p`
  margin: 0;
  font-size: 14px;
  color: #6B7280;
  font-weight: 500;
`;

const ActionsCell = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
`;

const ActionButton = styled.button`
  background: none;
  border: none;
  cursor: pointer;
  padding: 3px;
  display: flex;
  align-items: center;
  justify-content: center;
  
  img {
    width: 18px;
    height: 18px;
  }
  
  &:hover {
    opacity: 0.8;
  }
`;

interface CustomerTableProps {
  customers: Customer[];
  onEdit: (customerId: string) => void;
  onViewHistory: (customerId: string) => void;
  loading?: boolean;
}

const CustomerTableComponent: React.FC<CustomerTableProps> = ({ 
  customers, 
  onEdit, 
  onViewHistory,
  loading = false 
}) => {
  const renderTableContent = () => {
    if (loading) {
      return (
        <LoadingRow>
          <td colSpan={7}>
            <LoadingContainer>
              <LoadingSpinner size="lg" />
              <LoadingText>Loading customers</LoadingText>
            </LoadingContainer>
          </td>
        </LoadingRow>
      );
    }

    if (customers.length === 0) {
      return (
        <MessageRow>
          <td colSpan={7}>
            No customers found. Add a new customer to get started.
          </td>
        </MessageRow>
      );
    }

    return customers.map((customer) => (
      <tr key={customer.id}>
        <td>{customer.name}</td>
        <td>{customer.contactPerson || ''}</td>
        <td>{customer.email || ''}</td>
        <td>{customer.phone || ''}</td>
        <td>{customer.billingAddress || ''}</td>
        <td>{customer.priceList || ''}</td>
        <td>
          <ActionsCell>
            <ActionButton onClick={() => onEdit(customer.id)} title="Edit Customer">
              <img src={customerEditIcon} alt="Edit" />
            </ActionButton>
            <ActionButton onClick={() => onViewHistory(customer.id)} title="View History">
              <img src={customerHistoryIcon} alt="History" />
            </ActionButton>
          </ActionsCell>
        </td>
      </tr>
    ));
  };

  return (
    <TableContainer>
      <Table>
        <TableHeader>
          <tr>
            <th>Company Name</th>
            <th>Contact Person</th>
            <th>Email ID</th>
            <th>Phone Number</th>
            <th>Billing Address</th>
            <th>Applicable Price List</th>
            <th>Action</th>
          </tr>
        </TableHeader>
        <TableBody>
          {renderTableContent()}
        </TableBody>
      </Table>
    </TableContainer>
  );
};

// Memoization comparison function
const areEqual = (prevProps: CustomerTableProps, nextProps: CustomerTableProps) => {
  return (
    prevProps.customers.length === nextProps.customers.length &&
    prevProps.loading === nextProps.loading &&
    prevProps.customers.every((customer, index) => 
      customer.id === nextProps.customers[index]?.id
    )
  );
};

const CustomerTable = memo(CustomerTableComponent, areEqual);

export default CustomerTable; 