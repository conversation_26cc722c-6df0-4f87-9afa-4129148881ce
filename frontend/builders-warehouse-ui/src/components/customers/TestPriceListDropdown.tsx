import React, { useState, useEffect } from 'react';
import priceListService from '../../services/priceListService';

// This is a test component just to verify price list functionality
const TestPriceListDropdown: React.FC = () => {
  const [priceLists, setPriceLists] = useState<any[]>([]);
  const [selectedPriceList, setSelectedPriceList] = useState('');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchPriceLists = async () => {
      try {
        setLoading(true);
        // This should now fetch actual price lists with valid UUIDs
        const lists = await priceListService.fetchPriceListsForDropdown();
        console.log('Fetched price lists:', lists);
        setPriceLists(lists);
      } catch (error) {
        console.error('Error fetching price lists:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchPriceLists();
  }, []);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Selected price list ID:', selectedPriceList);
    
    // Check if selected ID is valid UUID format
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i;
    const isValidUuid = uuidRegex.test(selectedPriceList);
    console.log('Is valid UUID?', isValidUuid);
    
    // Check if selected ID exists in our price lists
    const foundList = priceLists.find(list => list.id === selectedPriceList);
    console.log('Found in priceLists:', foundList);
  };

  return (
    <div style={{ maxWidth: '500px', margin: '50px auto', padding: '20px', border: '1px solid #ddd', borderRadius: '8px' }}>
      <h2>Price List Dropdown Test</h2>
      <p>This component tests the price list dropdown functionality with real UUIDs.</p>
      
      {loading ? (
        <p>Loading price lists</p>
      ) : (
        <form onSubmit={handleSubmit}>
          <div style={{ marginBottom: '20px' }}>
            <label htmlFor="priceList" style={{ display: 'block', marginBottom: '8px', fontWeight: 'bold' }}>
              Price List
            </label>
            <select
              id="priceList"
              value={selectedPriceList}
              onChange={(e) => setSelectedPriceList(e.target.value)}
              style={{ width: '100%', padding: '10px', borderRadius: '4px', border: '1px solid #ccc' }}
            >
              <option value="">Select a price list</option>
              {priceLists.map(list => (
                <option key={list.id} value={list.id}>
                  {list.name} ({list.id})
                </option>
              ))}
            </select>
          </div>
          
          <button 
            type="submit" 
            style={{ padding: '10px 20px', background: '#4a90e2', color: 'white', border: 'none', borderRadius: '4px', cursor: 'pointer' }}
          >
            Submit
          </button>
        </form>
      )}
      
      <div style={{ marginTop: '20px', padding: '15px', background: '#f7f7f7', borderRadius: '4px' }}>
        <h3>Price Lists from API</h3>
        <pre style={{ overflowX: 'auto' }}>
          {JSON.stringify(priceLists, null, 2)}
        </pre>
        
        <h3>Selected Price List</h3>
        <p>
          <strong>ID:</strong> {selectedPriceList || 'None'}
        </p>
        <p>
          <strong>Is Valid UUID:</strong> {selectedPriceList && /^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i.test(selectedPriceList) ? 'Yes' : 'No'}
        </p>
      </div>
    </div>
  );
};

export default TestPriceListDropdown; 