import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { useNavigate, Link, useParams, useLocation } from 'react-router-dom';
import styled from 'styled-components';
import Layout from '../../layout/Layout';
import Pagination from '../../common/Pagination';
import SearchBar from '../../common/SearchBar';
import customerEditIcon from '../../../assets/customerEditIcon.png';
import customerHistoryIcon from '../../../assets/customerHistoryIcon.png';
import customerAddIcon from '../../../assets/customerAddIcon.png';
import printIcon from '../../../assets/printIcon.png';
import notesIcon from '../../../assets/notesIcon.png';
import builderLogo from '../../../assets/Builder-Logo.png';
import CustomerTable from '../CustomerTable';
import AddCustomerModal from '../AddCustomerModal';
import { Toast } from '../../common/Toast';
import customerService, { Customer, Customer as CustomerType, CustomerFormData } from '../../../services/customerService';
import { AUTH_TOKEN_KEY, API_URL, ENDPOINTS } from '../../../config';
import {
  PageContainer,
  PageHeaderComponent,
  SearchContainer,
  SearchField,
  BackButtonComponent,
  SearchIconWrapper as DesignSystemSearchIconWrapper,
  SearchInputComponent
} from '../../ui/DesignSystem';
import backButtonIcon from '../../../assets/backButton.png';
import PageLoadingSpinner from '../../ui/PageLoadingSpinner';

const HeaderContainer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 2rem;
`;

const CustomAddButton = styled.button`
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: #042B41;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: bold;
  cursor: pointer;

  &:hover {
    background-color: #0A3D5A;
  }

  img {
    width: 16px;
    height: 16px;
  }
`;

const PageTitle = styled.h1`
  color: #042B41;
  font-size: 2.3rem;
  font-weight: 800;
  margin: 0;
  font-family: 'Avenir', sans-serif;
`;

const CustomSearchContainer = styled.div`
  position: relative;
  width: 100%;
  max-width: 24rem;
  margin-bottom: 0;
`;

const CustomSearchIconWrapper = styled.div`
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  color: #9CA3AF;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: none;
`;

const CustomSearchField = styled.input`
  width: 100%;
  padding: 8px 12px 8px 2.5rem;
  border: 1px solid #E5E7EB;
  border-radius: 0.5rem;
  font-size: 14px;
  color: #374151;
  background-color: white;

  &::placeholder {
    color: #9CA3AF;
  }

  &:focus {
    outline: none;
    border-color: #042B41;
    box-shadow: 0 0 0 2px rgba(4, 43, 65, 0.1);
  }
`;

const TopSection = styled.div`
  display: flex;
  flex-direction: column;
  margin-bottom: 0.75rem;
`;

const TitleSection = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 0.75rem;
`;

const SearchActionSection = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  margin-bottom: 0;
`;

const BackButton = styled(Link)`
  display: flex;
  align-items: center;
  text-decoration: none;
  color: #042B41;
  font-weight: 500;

  img {
    width: 24px;
    height: 24px;
    margin-right: 8px;
  }
`;

const ActionsContainer = styled.div`
  display: flex;
  gap: 12px;
  margin-left: 12px;
`;

const BreadcrumbContainer = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 1.5rem;
  color: #6B7280;
  font-size: 28px;
  font-weight: 600;

  span {
    margin: 0 8px;
  }
`;

const BreadcrumbLink = styled.a`
  color: #9CA3AF;
  text-decoration: none;
  font-weight: 500;
  cursor: pointer;

  &:hover {
    color: #6B7280;
  }
`;

const BreadcrumbCurrent = styled.span`
  color: #042B41;
`;

const PageHeader = styled.div`
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-bottom: 1.5rem;
`;

const HistoryTable = styled.table`
  width: 100%;
  border-collapse: collapse;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  overflow: hidden;
  background-color: white;
`;

const TableHeader = styled.thead`
  background-color: #042B41;
  color: white;

  th {
    padding: 16px;
    text-align: left;
    font-weight: 500;
  }
`;

const TableBody = styled.tbody`
  tr {
    background-color: white;
    border-bottom: 1px solid #e0e0e0;

    &:last-child {
      border-bottom: none;
    }

    &:hover {
      background-color: #f9fafb;
    }
  }

  td {
    padding: 16px;
    color: #333;
  }
`;

const IconButton = styled.button`
  background: none;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4px;

  img {
    width: 20px;
    height: 20px;
  }

  &:hover {
    opacity: 0.8;
  }
`;

const PaginationWrapper = styled.div`
  margin-top: 0;
`;

const ActionsContainerCell = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
`;

const LoaderOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(255, 255, 255, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  pointer-events: none; /* Allows interactions with elements beneath the overlay */
`;

// Interface for customer history data
interface CustomerHistory {
  id: string;
  type: 'invoice' | 'quote';
  number: string;
  storeType: string;
  date: string;
  total: string;
  hasNotes: boolean;
  invoiceId?: string; // Add the invoiceId property as optional
}

// Custom styled back button without top spacing
const CustomBackButton = styled.button`
  display: flex;
  align-items: center;
  gap: 12px;
  background-color: transparent;
  color: #042B41;
  border: none;
  padding: 16px 20px;
  font-size: 18px;
  font-weight: 500;
  cursor: pointer;
  transition: color 0.2s ease;
  margin-bottom: 24px;

  &:hover {
    color: #031f30;
  }

  &:active {
    color: #021624;
  }

  svg {
    width: 22px;
    height: 22px;
    stroke: currentColor;
    transition: transform 0.2s ease;
  }

  &:hover svg {
    transform: translateX(-3px);
  }
`;

const PageWrapper = styled.div`
  background: white;
  margin: 0;
  min-height: calc(100vh - 64px);
  display: flex;
  flex-direction: column;
  padding: 0 24px;
`;

const ContentContainer = styled.div`
  padding: 0;
  flex: 1;
`;

const TableContainer = styled.div`
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin-bottom: 0;
`;

// Add debounce utility function
const useDebounce = (value: string, delay: number): string => {
  const [debouncedValue, setDebouncedValue] = useState(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
};

// Add skeleton loading components
const SkeletonRow = styled.tr`
  td {
    position: relative;
    overflow: hidden;
    
    &::after {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
      background-size: 200% 100%;
      animation: shimmer 1.5s infinite;
    }
  }
  
  @keyframes shimmer {
    0% {
      background-position: -200% 0;
    }
    100% {
      background-position: 200% 0;
    }
  }
`;

const SkeletonCell = styled.td`
  height: 24px;
  padding: 16px !important;
`;

// Add a function to render skeleton rows for customer list
const renderCustomerSkeletonRows = (count = 5) => {
  return (
    <tr>
      <td colSpan={7} style={{ padding: 0 }}>
        <div style={{ minHeight: '300px', display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
          <PageLoadingSpinner message="Loading customers" />
        </div>
      </td>
    </tr>
  );
};

// Add a function to render skeleton rows for customer history
const renderHistorySkeletonRows = (count = 5) => {
  return (
    <tr>
      <td colSpan={6} style={{ padding: 0 }}>
        <div style={{ minHeight: '300px', display: 'flex', justifyContent: 'center', alignItems: 'center' }}>
          <PageLoadingSpinner message="Loading history" />
        </div>
      </td>
    </tr>
  );
};

// Main component
const CustomersPage: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { customerId } = useParams<{ customerId: string }>();
  
  const [customers, setCustomers] = useState<CustomerType[]>([]);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalCustomers, setTotalCustomers] = useState(0);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCustomer, setSelectedCustomer] = useState<CustomerType | null>(null);
  const [customerHistory, setCustomerHistory] = useState<CustomerHistory[]>([]);
  const [showAddModal, setShowAddModal] = useState(false);
  const [showEditModal, setShowEditModal] = useState(false);
  const [loading, setLoading] = useState(false);
  const [historyLoading, setHistoryLoading] = useState(false);
  const [toastMessage, setToastMessage] = useState<string | null>(null);
  const [toastType, setToastType] = useState<'success' | 'error' | 'info' | 'warning'>('info');
  const [isViewingHistory, setIsViewingHistory] = useState(!!customerId);
  // Keep error state but only for internal tracking, not for UI display
  const [error, setError] = useState<string | null>(null);
  const [fetchInProgress, setFetchInProgress] = useState(false);
  const [initialLoadCompleted, setInitialLoadCompleted] = useState(false);

  // Debounce search term to avoid excessive API calls
  const debouncedSearchTerm = useDebounce(searchTerm, 500);

  // Load customers from API - make fetchCustomers memoized with useCallback
  const fetchCustomers = useCallback(async (page: number = 1, perPage: number = itemsPerPage, search?: string): Promise<void> => {
    console.log(`fetchCustomers called with page=${page}, perPage=${perPage}, search=${search || 'none'}, fetchInProgress=${fetchInProgress}`);
    
    // Prevent multiple simultaneous calls
    if (fetchInProgress) {
      console.log('Fetch already in progress, skipping duplicate request');
      return;
    }
    
    setFetchInProgress(true);
    
    // Always set loading to true when fetching new data
    setLoading(true);
    
    // Don't show errors on UI, but keep the state for internal tracking
    setError(null);

    try {
      console.log('Fetching customers:', { page, perPage, search });
      // Backend already sorts by company_name by default
      const response = await customerService.getCustomers(page, perPage, search);

      console.log('Customers response:', response);
      console.log('Response items length:', response.items?.length || 0);
      console.log('Response total:', response.total);
      console.log('Response page:', response.page);

      // Check if we got a valid response with items
      if (response && Array.isArray(response.items)) {
        console.log(`Setting customers state with ${response.items.length} items for page ${page}`);
        setCustomers(response.items);
        setTotalCustomers(response.total || response.items.length);
        setInitialLoadCompleted(true);
        console.log('State updated successfully');
      } else {
        console.error('Invalid response format:', response);
        // Set empty data on invalid response
        setCustomers([]);
        setTotalCustomers(0);
        // Log error but don't show to user
        console.error('Received invalid data format from the server');
      }

    } catch (error) {
      console.error('Failed to fetch customers:', error);

      // Only log the error, don't show on UI
      if (error instanceof Error) {
        console.error(`Error details: ${error.message}`);
      }

      // Set empty data on error
      setCustomers([]);
      setTotalCustomers(0);
    } finally {
      setLoading(false);
      setFetchInProgress(false);
    }
  }, [itemsPerPage]); // Removed fetchInProgress and initialLoadCompleted from dependencies

  // Add useEffect for initial data load
  useEffect(() => {
    if (!isViewingHistory && !initialLoadCompleted) {
      console.log('Initial data load');
      fetchCustomers(1, itemsPerPage);
    }
  }, [itemsPerPage, isViewingHistory, initialLoadCompleted]); // Removed fetchCustomers from dependencies

  // Add useEffect to react to changes in debouncedSearchTerm
  useEffect(() => {
    // Only fetch if debounced search term has changed and initial load is complete
    if (initialLoadCompleted && !isViewingHistory) {
      // Reset to first page when search term changes
      setCurrentPage(1);
      // Fetch customers with the search term
      fetchCustomers(1, itemsPerPage, debouncedSearchTerm || undefined);
    }
  }, [debouncedSearchTerm, initialLoadCompleted, itemsPerPage, isViewingHistory]); // Removed fetchCustomers from dependencies

  // Add useEffect to handle pagination changes
  useEffect(() => {
    // Only fetch if initial load is complete and we're not viewing history
    if (initialLoadCompleted && !isViewingHistory && currentPage > 1) {
      // Fetch customers for the new page
      fetchCustomers(currentPage, itemsPerPage, debouncedSearchTerm || undefined);
    }
  }, [currentPage]); // Only depend on currentPage to avoid loops

  // Fetch customer details and history - memoize this function too
  const fetchCustomerDetails = useCallback(async (id: string) => {
    if (fetchInProgress) {
      console.log('A fetch operation is already in progress');
      return;
    }

    setFetchInProgress(true);
    // Only set loading if we're not in history view (to prevent multiple loaders)
    if (!isViewingHistory) {
      setLoading(true);
    }

    try {
      // First, try to get the customer details directly from the API
      console.log(`Fetching customer details for ID: ${id}`);
      
      try {
        // Fetch customer details directly
        const token = localStorage.getItem(AUTH_TOKEN_KEY);
        const endpoint = `${API_URL}${ENDPOINTS.CUSTOMERS}/${id}`;
        
        console.log('Fetching customer data from API:', endpoint);
        
        const response = await fetch(endpoint, {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            'Authorization': token ? `Bearer ${token}` : ''
          },
          credentials: 'include',
          mode: 'cors'
        });
        
        if (!response.ok) {
          throw new Error(`Failed to fetch customer: ${response.status}`);
        }
        
        const data = await response.json();
        console.log('Customer data from API:', data);
        
        // Map the API response to our frontend model
        const customer: Customer = {
          id: data.id || id,
          name: data.company_name || 'Unknown',
          contactPerson: data.contact_person || '',
          email: data.email || '',
          phone: data.phone || '',
          billingAddress: data.billing_address || '',
          billingSuburb: data.billing_suburb || '',
          billingPostcode: data.billing_postcode || '',
          isAccountCustomer: data.is_account || false,
          priceList: data.price_list ? data.price_list.name : null,
          price_list_id: data.price_list_id || '',
          status: 'active',
          createdDate: data.created_at || '',
          lastOrderDate: null
        };
        
        console.log('Mapped customer from API:', customer);
        setSelectedCustomer(customer);
      } catch (error) {
        console.error('Error fetching customer in fetchCustomerDetails:', error);
        // Fall back to using the service if direct API call fails
        try {
          const customer = await customerService.getCustomerById(id);
          setSelectedCustomer(customer);
        } catch (fallbackError) {
          console.error('Even fallback customer fetch failed:', fallbackError);
          throw new Error('Failed to fetch customer data');
        }
      }

      // Fetch customer history from API only if we're in history view
      if (isViewingHistory) {
        await fetchCustomerHistoryData(id);
      }
    } catch (error) {
      console.error('Failed to fetch customer details:', error);
      // Show error toast to the user
      setToastMessage("Error fetching customer history. Please try again.");
      setToastType('error');

      if (selectedCustomer === null) {
        // Handle the case where we don't have any customer data
        navigate('/customers');
      }
    } finally {
      if (!isViewingHistory) {
        setLoading(false);
      }
      setFetchInProgress(false);
    }
  }, [navigate, selectedCustomer, isViewingHistory]); // Added isViewingHistory to dependencies

  // Separate function to fetch customer history data
  const fetchCustomerHistoryData = useCallback(async (customerId: string) => {
    try {
      console.log(`Fetching invoice history for customer ID: ${customerId}`);

      // First, get the customer's invoices using the history endpoint
      const historyResponse = await customerService.getCustomerHistory(customerId, 1, 50);
      console.log('Customer history response:', historyResponse);

      // Safety checks for the response
      if (!historyResponse) {
        console.error('Received null or undefined response from history service');
        setCustomerHistory([]);
        return;
      }

      if (!historyResponse.items || !Array.isArray(historyResponse.items)) {
        console.error('History response missing items array:', historyResponse);
        setCustomerHistory([]);
        return;
      }

      if (historyResponse.items && historyResponse.items.length > 0) {
        // Log the raw history items to inspect the structure
        console.log(`Raw history items (${historyResponse.items.length} items):`, 
          historyResponse.items.length > 3 
            ? JSON.stringify(historyResponse.items.slice(0, 3), null, 2) + '... (truncated)'
            : JSON.stringify(historyResponse.items, null, 2));

        // First, copy the items to avoid modifying the original array
        const safeItems = [...historyResponse.items];
        
        // Filter out any null or invalid items
        const validItems = safeItems.filter(item => item && typeof item === 'object');
        
        // Get invoice details if they're not complete
        let invoicesWithDetails = validItems;
        
        try {
          const needsDetails = validItems.some(item => !item.total_amount && !item.amount);
          
          if (needsDetails) {
            console.log('Some invoices need additional details, fetching them...');
            // This would be a placeholder for fetching additional details if needed
            // For now, we'll just use what we have
          }
        } catch (detailsError) {
          console.error('Error checking or fetching invoice details:', detailsError);
        }

        // Transform the history data for UI format
        const transformedHistory = invoicesWithDetails.map((item: any) => {
          try {
            // Log each item during transformation
            console.log('Transforming item:', item);

            // Safely extract properties with fallbacks
            return {
              id: item.id || `hist-${Math.random().toString(36).substring(2, 11)}`,
              type: item.type || (item.invoice_no || item.invoice_number ? 'invoice' : 'quote'),
              // Check all possible field names for invoice number
              number: item.number || item.invoice_number || item.invoice_no || 'N/A',
              storeType: item.storeType || item.store_type || '',
              date: item.date || (
                item.created_at
                  ? new Date(item.created_at).toLocaleDateString()
                  : new Date().toLocaleDateString()
              ),
              total: item.total || `$${(item.amount || item.total_amount || 0).toFixed(2)}`,
              hasNotes: !!item.has_notes,
              invoiceId: item.invoice_id || item.id
            };
          } catch (itemError) {
            console.error('Error transforming history item:', itemError, item);
            // Return a placeholder for problematic items
            return {
              id: `error-${Math.random().toString(36).substring(2, 11)}`,
              type: 'invoice',
              number: 'Error',
              storeType: '',
              date: new Date().toLocaleDateString(),
              total: '$0.00',
              hasNotes: false,
              invoiceId: ''
            };
          }
        });

        console.log('Transformed history data:', transformedHistory);
        setCustomerHistory(transformedHistory);
      } else {
        // If no items were returned, set empty array
        console.log('No history items found for customer');
        setCustomerHistory([]);
      }
    } catch (error) {
      console.error('Failed to fetch customer history:', error);
      // Set empty history array if API fails
      setCustomerHistory([]);
      // Show error toast
      setToastMessage("Error fetching customer history. Please try again.");
      setToastType('error');
    } finally {
      // Clear the history loading state
      setHistoryLoading(false);
    }
  }, []);

  // Handle customer history view
  useEffect(() => {
    if (isViewingHistory && customerId) {
      console.log('useEffect triggered for history view with customerId:', customerId);
      fetchCustomerDetails(customerId);
    }
  }, [isViewingHistory, customerId, fetchCustomerDetails]);

  const handlePageChange = (pageNumber: number) => {
    console.log(`Page change requested: from ${currentPage} to ${pageNumber}`);
    console.log(`Current fetchInProgress state: ${fetchInProgress}`);
    console.log(`Current initialLoadCompleted state: ${initialLoadCompleted}`);
    
    if (pageNumber !== currentPage) {
      console.log(`Changing page from ${currentPage} to ${pageNumber}`);
      
      // Update the current page immediately
      setCurrentPage(pageNumber);
      
      // Fetch the data for the new page
      console.log(`Calling fetchCustomers for page ${pageNumber}`);
      fetchCustomers(pageNumber, itemsPerPage, debouncedSearchTerm || undefined);
    }
  };

  const handleItemsPerPageChange = (newItemsPerPage: number) => {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1);
    // Explicitly fetch customers with the new page size
    fetchCustomers(1, newItemsPerPage, debouncedSearchTerm || undefined);
  };

  const handleSearch = (value: string) => {
    setSearchTerm(value);
    // No need to set loading to true here, the CustomerTable component
    // will handle loading state internally and prevent flickering
  };

  // Memoize the filtered customers to prevent unnecessary re-renders
  const displayedCustomers = useMemo(() => {
    // The API now handles the filtering, so we just return the customers as is
    return customers;
  }, [customers]);

  const handleAddCustomer = () => {
    // Reset selectedCustomer to avoid edit modal showing instead of add modal
    setSelectedCustomer(null);
    setShowAddModal(true);
  };

  const handleEditCustomer = async (customerId: string) => {
    console.log(`Editing customer with ID: ${customerId}`);
    setLoading(true);

    try {
      // Make sure we're using the correct ID format
      console.log('Customer ID type:', typeof customerId);

      // Fetch the customer data directly from the API
      // Use the curl command format that works
      const token = localStorage.getItem(AUTH_TOKEN_KEY);
      // Remove the trailing slash as it might be causing the 404 error
      const endpoint = `${API_URL}${ENDPOINTS.CUSTOMERS}/${customerId}`;

      console.log('Making direct API request to:', endpoint);

      const response = await fetch(endpoint, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'Authorization': token ? `Bearer ${token}` : ''
        },
        credentials: 'include',
        mode: 'cors'
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch customer: ${response.status}`);
      }

      const data = await response.json();
      console.log("Retrieved customer data from API:", data);

      // Map the API response to our frontend model
      const customer: Customer = {
        id: data.id || customerId,
        name: data.company_name || '',
        contactPerson: data.contact_person || '',
        email: data.email || '',
        phone: data.phone || '',
        billingAddress: data.billing_address || '',
        billingSuburb: data.billing_suburb || '',
        billingPostcode: data.billing_postcode || '',
        isAccountCustomer: data.is_account || false,
        priceList: data.price_list ? data.price_list.name : null,
        price_list_id: data.price_list_id || '',
        status: 'active',
        createdDate: data.created_at || '',
        lastOrderDate: null
      };

      console.log("Mapped customer for editing:", customer);

      // Set the selected customer and open the modal
      setSelectedCustomer(customer);
      setShowAddModal(true);
    } catch (error) {
      console.error("Error fetching customer details:", error);
      // Show error toast to the user
      setToastMessage("Error fetching customer details. Please try again.");
      setToastType('error');

      // Even if there's an error, we still want to open the modal with empty data
      // Create a basic customer object with just the ID
      const basicCustomer: Customer = {
        id: customerId,
        name: '',
        contactPerson: '',
        email: '',
        phone: '',
        billingAddress: '',
        billingSuburb: null,
        billingPostcode: null,
        isAccountCustomer: false,
        priceList: null,
        price_list_id: null,
        status: 'active',
        createdDate: '',
        lastOrderDate: null
      };

      setSelectedCustomer(basicCustomer);
      setShowAddModal(true);
    } finally {
      setLoading(false);
    }
  };

  const handleViewHistory = async (customerId: string) => {
    console.log(`Viewing history for customer ID: ${customerId}`);
    
    // Skip if a fetch is already in progress
    if (fetchInProgress || historyLoading) {
      console.log('Fetch already in progress, skipping duplicate request');
      return;
    }
    
    setFetchInProgress(true);
    setHistoryLoading(true);

    try {
      // Fetch the customer data directly from the API to ensure we have the latest data
      const token = localStorage.getItem(AUTH_TOKEN_KEY);
      const endpoint = `${API_URL}${ENDPOINTS.CUSTOMERS}/${customerId}`;

      console.log('Making direct API request to fetch customer for history:', endpoint);

      const response = await fetch(endpoint, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'Authorization': token ? `Bearer ${token}` : ''
        },
        credentials: 'include',
        mode: 'cors'
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch customer: ${response.status}`);
      }

      // Get the raw response text first for debugging
      const responseText = await response.text();
      console.log("Customer API raw response:", responseText);

      // Parse the response text into JSON
      let data;
      try {
        data = JSON.parse(responseText);
      } catch (parseError) {
        console.error('Error parsing JSON response:', parseError);
        throw new Error("Failed to parse customer data response");
      }

      console.log("Retrieved customer data for history:", data);

      // Ensure we have the company_name from the API response
      if (!data.company_name) {
        console.error("API response missing company_name:", data);
        throw new Error("Customer data is incomplete. Missing company name.");
      }

      // Map the API response to our frontend model
      const customer: Customer = {
        id: data.id || customerId,
        name: data.company_name, // Ensure this is not empty
        contactPerson: data.contact_person || '',
        email: data.email || '',
        phone: data.phone || '',
        billingAddress: data.billing_address || '',
        billingSuburb: data.billing_suburb || '',
        billingPostcode: data.billing_postcode || '',
        isAccountCustomer: data.is_account || false,
        priceList: data.price_list ? data.price_list.name : null,
        price_list_id: data.price_list_id || '',
        status: 'active',
        createdDate: data.created_at || '',
        lastOrderDate: null
      };

      console.log("Mapped customer for history:", customer);
      console.log("Customer name from API:", customer.name);

      // Set the selected customer
      setSelectedCustomer(customer);

      // Set the viewing history flag
      setIsViewingHistory(true);

      // Navigate first to show the loading state in history view
      navigate(`/customers/${customerId}/history`);

      // Now fetch the customer's invoice history
      await fetchCustomerHistoryData(customerId);

    } catch (error) {
      console.error("Error fetching customer details for history:", error);
      setToastMessage("Error fetching customer details. Please try again.");
      setToastType('error');
      navigate('/customers'); // Return to customers list on error
    } finally {
      setHistoryLoading(false);
      setFetchInProgress(false);
    }
  };

  const handleCreateCustomer = async (customerData: CustomerFormData) => {
    setLoading(true);
    try {
      console.log("Creating customer with data:", customerData);
      await customerService.createCustomer(customerData);
      setShowAddModal(false);

      // Refresh the customer list after successful creation
      await fetchCustomers(1, itemsPerPage); // Reset to first page to show the new customer
      setCurrentPage(1);

      setToastMessage('Customer created successfully!');
      setToastType('success');
    } catch (error) {
      console.error('Failed to create customer:', error);
      // Show error toast to the user
      const errorMsg = error instanceof Error ? error.message : 'Unknown error';
      setToastMessage(`Failed to create customer: ${errorMsg}`);
      setToastType('error');
      // Still close the modal to avoid confusing the user
      setShowAddModal(false);
    } finally {
      setLoading(false);
    }
  };

  const handleUpdateCustomer = async (customerData: Partial<CustomerFormData>) => {
    if (!selectedCustomer) {
      console.error('No customer selected for update');
      setToastMessage('Error: No customer selected for update');
      setToastType('error');
      return;
    }

    const customerId = selectedCustomer.id;
    console.log(`Updating customer with ID: ${customerId}`);
    console.log('Update data:', customerData);

    setLoading(true);
    try {
      // Create the API payload directly
      const apiPayload = {
        company_name: customerData.name || '',
        contact_person: customerData.contactPerson || null,
        email: customerData.email || '<EMAIL>',
        phone: customerData.phone ? customerData.phone.replace(/\s+/g, '') : '',
        billing_address: customerData.billingAddress || null,
        billing_suburb: customerData.billingSuburb || null,
        billing_postcode: customerData.billingPostcode || null,
        is_account: customerData.isAccountCustomer || false,
        price_list_id: customerData.priceList || null
      };

      console.log('API payload for update:', apiPayload);

      // Make the PUT request directly to the API
      const token = localStorage.getItem(AUTH_TOKEN_KEY);
      // Remove the trailing slash as it might be causing the 404 error
      const endpoint = `${API_URL}${ENDPOINTS.CUSTOMERS}/${customerId}`;

      console.log('Making direct PUT request to:', endpoint);

      const response = await fetch(endpoint, {
        method: 'PUT',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'Authorization': token ? `Bearer ${token}` : ''
        },
        body: JSON.stringify(apiPayload),
        credentials: 'include',
        mode: 'cors'
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`Failed to update customer: ${response.status} - ${errorText}`);
      }

      const data = await response.json();
      console.log('Updated customer response:', data);

      // Close the modal - using showAddModal since that's what we're using for both add and edit
      setShowAddModal(false);

      // Refresh the customer list to show updated data
      await fetchCustomers(currentPage, itemsPerPage);

      // Show success message
      setToastMessage('Customer updated successfully!');
      setToastType('success');
    } catch (error) {
      console.error('Failed to update customer:', error);

      // Show error toast to the user
      setToastMessage(error instanceof Error ? `Failed to update customer: ${error.message}` : 'Failed to update customer');
      setToastType('error');

      // Still close the modal to avoid confusing the user
      setShowAddModal(false);

      // Refresh the customer list anyway to ensure UI is up to date
      await fetchCustomers(currentPage, itemsPerPage);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteCustomer = async (customerId: string) => {
    if (!window.confirm('Are you sure you want to delete this customer? This action cannot be undone.')) {
      return;
    }

    setLoading(true);
    try {
      await customerService.deleteCustomer(customerId);
      setShowEditModal(false);
      fetchCustomers(currentPage, itemsPerPage);
      setToastMessage('Customer deleted successfully!');
      setToastType('success');
    } catch (error) {
      console.error('Failed to delete customer:', error);
      // Don't show error toast to the user
      // Still close the modal to avoid confusing the user
      setShowEditModal(false);
    } finally {
      setLoading(false);
    }
  };

  const handleBackToCustomers = () => {
    // Reset history-related states
    setIsViewingHistory(false);
    setSelectedCustomer(null);
    setCustomerHistory([]);
    setHistoryLoading(false);
    navigate('/customers');
  };

  const handleBackToDashboard = () => {
    navigate('/home');
  };

  const handleBulkUpload = () => {
    // To be implemented - for now just show a message
    alert('Bulk upload functionality will be implemented here');
  };

  // Render function for customer list view
  const renderCustomerListView = () => {
    return (
      <PageWrapper>
        <ContentContainer>
          <TopSection>
            <TitleSection>
              <BackButton to="/dashboard">
                <img src={backButtonIcon} alt="Back" />
              </BackButton>
              <PageTitle>Customers</PageTitle>
            </TitleSection>

            <SearchActionSection>
              <CustomSearchContainer>
                <CustomSearchIconWrapper>
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="20"
                    height="20"
                    viewBox="0 0 20 20"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <circle cx="9" cy="9" r="6"></circle>
                    <line x1="13" y1="13" x2="17" y2="17"></line>
                  </svg>
                </CustomSearchIconWrapper>
                <CustomSearchField
                  type="text"
                  placeholder="Search a Company"
                  value={searchTerm}
                  onChange={(e) => handleSearch(e.target.value)}
                />
              </CustomSearchContainer>

              <ActionsContainer>
                <CustomAddButton onClick={handleAddCustomer}>
                  <img src={customerAddIcon} alt="Add" />
                  Add Customer
                </CustomAddButton>
              </ActionsContainer>
            </SearchActionSection>
          </TopSection>

          <TableContainer>
            <CustomerTable
              customers={displayedCustomers}
              onEdit={handleEditCustomer}
              onViewHistory={handleViewHistory}
              loading={loading}
            />
          </TableContainer>

          {!loading && customers.length > 0 && (
            <PaginationWrapper>
              <Pagination
                currentPage={currentPage}
                totalPages={Math.max(1, Math.ceil(totalCustomers / itemsPerPage))}
                onPageChange={handlePageChange}
                totalItems={totalCustomers}
                itemsPerPage={itemsPerPage}
                onItemsPerPageChange={handleItemsPerPageChange}
                itemsPerPageOptions={[10, 20, 50]}
                showItemsPerPage={true}
              />
            </PaginationWrapper>
          )}
        </ContentContainer>
      </PageWrapper>
    );
  };

  // Render function for customer history view
  const renderCustomerHistoryView = () => {
    if (!selectedCustomer) {
      console.error('No selected customer available for history view');
      return null;
    }

    // Show full page loading when initially loading history
    if (historyLoading) {
      return (
        <PageContainer>
          <div style={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '60vh' }}>
            <PageLoadingSpinner message="Loading customer history..." />
          </div>
        </PageContainer>
      );
    }

    // Ensure we have a valid customer name
    const customerName = selectedCustomer.name || 'Customer';

    return (
      <PageContainer>
        <BackButtonComponent onClick={handleBackToCustomers} label="Back" />

        <HeaderContainer>
          <PageTitle>{`${customerName} - History`}</PageTitle>
          <CustomAddButton onClick={() => window.print()}>
            <img src={printIcon} alt="Print" />
            Print
          </CustomAddButton>
        </HeaderContainer>

        <HistoryTable>
          <TableHeader>
            <tr>
              <th>Type</th>
              <th>Number</th>
              <th>Store Type</th>
              <th>Date</th>
              <th>Total</th>
              <th>Actions</th>
            </tr>
          </TableHeader>
          <TableBody>
            {customerHistory.length === 0 ? (
              <tr>
                <td colSpan={6} style={{ textAlign: 'center', padding: '32px' }}>
                  No history found for this customer.
                </td>
              </tr>
            ) : (
              customerHistory.map(item => (
                <tr key={item.id}>
                  <td>{item.type === 'invoice' ? 'Invoice' : 'Quote'}</td>
                  <td>{item.number}</td>
                  <td>{item.storeType}</td>
                  <td>{item.date}</td>
                  <td>{item.total}</td>
                  <td>
                    <div style={{ display: 'flex', gap: '8px' }}>
                      <button
                        className="print-button"
                        onClick={(e) => {
                          e.stopPropagation();
                          if (item.invoiceId) {
                            window.open(`${API_URL}${ENDPOINTS.INVOICES}/${item.invoiceId}/pdf`, '_blank');
                          }
                        }}
                      >
                        <img src={printIcon} alt="Print" />
                      </button>
                      {item.hasNotes && (
                        <IconButton title="View Notes">
                          <img src={notesIcon} alt="Notes" />
                        </IconButton>
                      )}
                    </div>
                  </td>
                </tr>
              ))
            )}
          </TableBody>
        </HistoryTable>
      </PageContainer>
    );
  };

  return (
    <Layout>
      {showAddModal && (
        <AddCustomerModal
          isOpen={showAddModal}
          onClose={() => setShowAddModal(false)}
          onSubmit={selectedCustomer ? undefined : handleCreateCustomer}
          onUpdate={selectedCustomer ? handleUpdateCustomer : undefined}
          customer={selectedCustomer || undefined}
          isLoading={loading}
        />
      )}
      {isViewingHistory ? renderCustomerHistoryView() : renderCustomerListView()}
      {toastMessage && (
        <Toast
          message={toastMessage}
          type={toastType}
          onClose={() => setToastMessage(null)}
        />
      )}
    </Layout>
  );
};

export default CustomersPage;