import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { Link } from 'react-router-dom';
import customerService, { Customer, CustomerFormData } from '../../services/customerService';
import { Toast } from '../common/Toast';
import priceListService, { PriceList, DEFAULT_PRICE_LISTS } from '../../services/priceListService';
import { AUTH_TOKEN_KEY, API_URL, ENDPOINTS } from '../../config';

interface AddCustomerModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSubmit?: (customerData: CustomerFormData) => Promise<void>;
  onUpdate?: (customerData: Partial<CustomerFormData>) => Promise<void>;
  onDelete?: () => Promise<void>;
  customer?: Customer;
  isLoading?: boolean;
}

const initialFormData: CustomerFormData = {
  name: '',
  contactPerson: '',
  email: '',
  phone: '',
  billingAddress: '',
  billingSuburb: '',
  billingPostcode: '',
  isAccountCustomer: false,
  priceList: ''
};

// Modal styled components
const ModalOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
`;

const ModalContent = styled.div`
  background-color: white;
  border-radius: 10px;
  width: 100%;
  max-width: 1100px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
  max-height: calc(100vh - 40px);
  display: flex;
  flex-direction: column;
`;

const ModalHeader = styled.div`
  padding: 24px 32px;
  border-bottom: 1px solid #e0e0e0;
  display: flex;
  justify-content: space-between;
  align-items: center;
`;

const ModalTitle = styled.h2`
  margin: 0;
  font-size: 24px;
  color: #042B41;
  font-weight: 600;
`;

const RedirectLink = styled(Link)`
  color: #042B41;
  text-decoration: none;
  display: flex;
  align-items: center;
  font-size: 14px;

  &:hover {
    text-decoration: underline;
  }
`;

const ModalBody = styled.div`
  padding: 32px;
  overflow-y: auto;
  flex: 1;
`;

const FormRow = styled.div`
  display: flex;
  gap: 24px;
  margin-bottom: 24px;
  flex-wrap: wrap;
`;

const FormGroup = styled.div`
  flex: 1;
  min-width: 240px;
`;

const Label = styled.label`
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  font-size: 16px;
  color: #333;

  .required {
    color: #FF0000;
  }
`;

const Input = styled.input`
  width: 100%;
  padding: 12px 14px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 16px;

  &:focus {
    outline: none;
    border-color: #042B41;
    box-shadow: 0 0 0 2px rgba(4, 43, 65, 0.1);
  }
`;

const Select = styled.select`
  width: 100%;
  padding: 12px 14px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 16px;
  appearance: none;
  background-color: white;

  &:focus {
    outline: none;
    border-color: #042B41;
    box-shadow: 0 0 0 2px rgba(4, 43, 65, 0.1);
  }
`;

const TextArea = styled.textarea`
  width: 100%;
  padding: 12px 14px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 16px;
  min-height: 120px;
  resize: vertical;

  &:focus {
    outline: none;
    border-color: #042B41;
    box-shadow: 0 0 0 2px rgba(4, 43, 65, 0.1);
  }
`;

const CheckboxContainer = styled.div`
  display: flex;
  align-items: center;
  margin-top: 34px;
`;

const CheckboxLabel = styled.label`
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 500;
  font-size: 16px;
  color: #333;
  cursor: pointer;
`;

const Checkbox = styled.input`
  width: 20px;
  height: 20px;
  margin: 0;
  cursor: pointer;
`;

const BillingSection = styled.div`
  background-color: #f8f9fa;
  padding: 24px;
  border-radius: 8px;
  margin-top: 24px;
  margin-bottom: 24px;
`;

const SectionTitle = styled.h3`
  color: #333;
  font-size: 18px;
  margin-top: 8px;
  margin-bottom: 16px;
  font-weight: 600;
  padding-bottom: 8px;
  border-bottom: 1px solid #f0f0f0;
`;

const BillingSectionTitle = styled.h3`
  font-size: 18px;
  margin: 0 0 24px 0;
  font-weight: 600;
  color: #333;
`;

const ModalFooter = styled.div`
  padding: 24px;
  display: flex;
  justify-content: center;
  gap: 24px;
  border-top: 1px solid #e0e0e0;
`;

const CancelButton = styled.button`
  padding: 12px 24px;
  background-color: white;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  width: 120px;

  &:hover {
    background-color: #f5f5f5;
  }
`;

const SubmitButton = styled.button`
  padding: 12px 24px;
  background-color: #042B41;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  width: 120px;
  position: relative;

  &:hover {
    background-color: #031f30;
  }

  &:disabled {
    opacity: 0.7;
    cursor: not-allowed;
  }
`;

const SelectWrapper = styled.div`
  position: relative;

  &::after {
    content: '';
  position: absolute;
  top: 50%;
    right: 12px;
    transform: translateY(-50%);
    width: 0;
    height: 0;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-top: 5px solid #333;
    pointer-events: none;
  }
`;

const ButtonLoader = () => (
  <div style={{
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    display: 'flex',
    alignItems: 'center',
    justifyContent: 'center'
  }}>
    <span
      style={{
        width: '12px',
        height: '12px',
        border: '2px solid white',
        borderTopColor: 'transparent',
        borderRadius: '50%',
        animation: 'spin 1s linear infinite'
      }}
    />
  </div>
);

const AddCustomerModal: React.FC<AddCustomerModalProps> = ({
  isOpen,
  onClose,
  onSubmit,
  onUpdate,
  onDelete,
  customer,
  isLoading = false
}) => {
  const [formData, setFormData] = useState<CustomerFormData>(initialFormData);
  const [originalData, setOriginalData] = useState<CustomerFormData>(initialFormData);
  const [hasChanges, setHasChanges] = useState(false);
  const [errors, setErrors] = useState<Partial<Record<keyof CustomerFormData, string>>>({});
  const [showToast, setShowToast] = useState(false);
  const [toastMessage, setToastMessage] = useState('');
  const [toastType, setToastType] = useState<'success' | 'error' | 'warning'>('success');
  const [priceLists, setPriceLists] = useState<PriceList[]>([]);

  const isEditMode = !!customer;

  // Fetch price lists when the component mounts
  useEffect(() => {
    const fetchPriceLists = async () => {
      try {
        console.log('Fetching price lists for AddCustomerModal...');

        // Use the new dedicated endpoint function instead of the general one
        const lists = await priceListService.fetchPriceListsForDropdown();
        console.log('Price lists fetched for AddCustomerModal:', lists);

        // Accept all price lists regardless of ID format
        setPriceLists(lists);

        // For debugging
        console.log('Price lists state value after setting:', lists);
      } catch (error) {
        console.error('Error fetching price lists in AddCustomerModal:', error);

        // Fall back to default price lists instead of showing nothing
        console.warn('Using default price lists as fallback');
        setPriceLists(DEFAULT_PRICE_LISTS);

        // Show error toast
        setToastMessage('Failed to load price lists. Using default options.');
        setToastType('error');
        setShowToast(true);
      }
    };

    // Always fetch price lists regardless of mode
    fetchPriceLists();
  }, []);

  // Separate useEffect to handle customer data changes
  useEffect(() => {
    // If in edit mode, populate form with customer data
    if (customer) {
      console.log('Setting form data for editing customer:', customer);
      console.log('Customer ID:', customer.id);
      console.log('Customer object type:', typeof customer);
      console.log('Customer object keys:', Object.keys(customer));

      // Use the most reliable property first with fallbacks
      const priceListIdToUse = customer.price_list_id
        || (customer.price_list ? customer.price_list.id : null)
        || '';

      console.log('Using price_list_id for edit form:', priceListIdToUse);
      console.log('Customer isAccountCustomer:', customer.isAccountCustomer);

      // Create a new form data object with all fields from the customer
      const newFormData = {
        name: customer.name || '',
        contactPerson: customer.contactPerson || '',
        email: customer.email || '',
        phone: customer.phone || '',
        billingAddress: customer.billingAddress || '',
        billingSuburb: customer.billingSuburb || '',
        billingPostcode: customer.billingPostcode || '',
        isAccountCustomer: customer.isAccountCustomer || false,
        priceList: priceListIdToUse,
      };

      console.log('Setting form data to:', newFormData);

      // Force a delay to ensure the form is updated after the modal is opened
      setTimeout(() => {
        setFormData(newFormData);
        setOriginalData(newFormData); // Store the original data
        setHasChanges(false); // Reset changes flag
        console.log('Form data set with delay:', newFormData);
      }, 100);

      // Clear any previous errors
      setErrors({});
    }
  }, [customer]);

  // Add effect to check for changes
  useEffect(() => {
    if (isEditMode) {
      const hasAnyChanges = Object.keys(formData).some(key => {
        const k = key as keyof CustomerFormData;
        return formData[k] !== originalData[k];
      });
      setHasChanges(hasAnyChanges);
    }
  }, [formData, originalData, isEditMode]);

  // Log form data changes for debugging
  useEffect(() => {
    console.log('Form data changed:', formData);
  }, [formData]);

  // Handle phone number input with formatting
  const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { value } = e.target;

    // Allow digits, +, and space characters only
    const sanitizedValue = value.replace(/[^\d\s+]/g, '');

    setFormData(prev => ({
      ...prev,
      phone: sanitizedValue
    }));

    // Clear error when field is edited
    if (errors.phone) {
      setErrors(prev => ({
        ...prev,
        phone: ''
      }));
    }
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value, type } = e.target as HTMLInputElement;

    // Debug price list selection
    if (name === 'priceList') {
      console.log(`Price list selection changed to: "${value}"`);
      console.log('Dropdown options:', priceLists);
      const selectedOption = priceLists.find(list => list.id === value);
      console.log('Selected price list:', selectedOption);
    }

    // Handle checkboxes separately
    if (type === 'checkbox') {
      const { checked } = e.target as HTMLInputElement;
      setFormData((prev) => ({
        ...prev,
        [name]: checked,
      }));
    } else {
      setFormData((prev) => ({
        ...prev,
        [name]: value,
      }));
    }

    // Clear error when field is edited
    if (errors[name as keyof CustomerFormData]) {
      setErrors((prev) => ({
        ...prev,
        [name]: '',
      }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<Record<keyof CustomerFormData, string>> = {};

    if (!formData.name.trim()) {
      newErrors.name = 'Company name is required';
    }

    if (!formData.phone.trim()) {
      newErrors.phone = 'Phone number is required';
    } else {
      // Remove spaces and check if phone number matches the required pattern
      const phonePattern = /^\+?\d{7,15}$/;
      const cleanedPhone = formData.phone.replace(/\s+/g, '');
      if (!phonePattern.test(cleanedPhone)) {
        newErrors.phone = 'Phone number must be 7-15 digits with optional + prefix';
      }
    }

    // Email validation - only validate if email is provided and not empty
    if (formData.email && formData.email.trim() && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
      newErrors.email = 'Invalid email format';
    }

    // Only show price list error if no value is selected
    if (!formData.priceList || formData.priceList === '') {
      newErrors.priceList = 'Price list is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Debug the price list value
    console.log('Price list value at submission:', formData.priceList);
    console.log('Price lists available:', priceLists);
    console.log('Form data at submission:', formData);

    // Skip price list validation for now to ensure the form can be submitted
    // We'll handle any errors from the API response

    if (validateForm()) {
      try {
        // Debug the priceList value
        console.group('Price List Validation Debug Info');
        console.log('Current priceList value:', formData.priceList);
        console.log('Value type:', typeof formData.priceList);
        console.log('Value length:', formData.priceList ? formData.priceList.length : 0);

        // Show all available price lists and check for a match
        console.log('Available price lists:');
        priceLists.forEach(list => {
          console.log(`- ${list.name} (${list.id})`);
          if (list.id === formData.priceList) {
            console.log(`  ✓ Match found by ID`);
          }
        });
        console.groupEnd();

        if (isEditMode && customer && onUpdate) {
          console.log('Updating customer with ID:', customer.id);
          console.log('Updating customer with data:', formData);

          // Ensure email is valid
          if (!formData.email || !formData.email.includes('@')) {
            formData.email = '<EMAIL>';
          }

          try {
            // Make a direct API call to update the customer
            const token = localStorage.getItem(AUTH_TOKEN_KEY);
            const customerId = customer.id;
            // Remove the trailing slash as it might be causing the 404 error
            const endpoint = `${API_URL}${ENDPOINTS.CUSTOMERS}/${customerId}`;

            // Create the API payload
            const apiPayload = {
              company_name: formData.name,
              contact_person: formData.contactPerson || null,
              email: formData.email,
              phone: formData.phone ? formData.phone.replace(/\s+/g, '') : '',
              billing_address: formData.billingAddress || null,
              billing_suburb: formData.billingSuburb || null,
              billing_postcode: formData.billingPostcode || null,
              is_account: formData.isAccountCustomer || false,
              price_list_id: formData.priceList || null
            };

            console.log('Making direct PUT request to:', endpoint);
            console.log('API payload:', apiPayload);

            const response = await fetch(endpoint, {
              method: 'PUT',
              headers: {
                'Accept': 'application/json',
                'Content-Type': 'application/json',
                'Authorization': token ? `Bearer ${token}` : ''
              },
              body: JSON.stringify(apiPayload),
              credentials: 'include',
              mode: 'cors'
            });

            if (!response.ok) {
              const errorText = await response.text();
              throw new Error(`Failed to update customer: ${response.status} - ${errorText}`);
            }

            const data = await response.json();
            console.log('Customer updated successfully:', data);

            // Call the onUpdate function to update the UI
            await onUpdate(formData);

            setToastMessage('Customer updated successfully!');
            setToastType('success');
            setShowToast(true);
            onClose();
          } catch (error) {
            console.error('Error updating customer:', error);
            const errorMsg = error instanceof Error ? error.message : 'An unknown error occurred';
            setToastMessage(`Failed to update customer: ${errorMsg}`);
            setToastType('error');
            setShowToast(true);
          }
        } else if (onSubmit) {
          console.log('Creating new customer with data:', formData);

          try {
            await onSubmit(formData);
            setFormData(initialFormData);
            setToastMessage('Customer added successfully!');
            setToastType('success');
            setShowToast(true);
            onClose();
          } catch (error: any) {
            const errorMsg = error instanceof Error ? error.message : 'An unknown error occurred';

            // Enhanced error detection for backend issues
            if (
              errorMsg.includes('greenlet_spawn') ||
              errorMsg.includes('await_only') ||
              errorMsg.includes('RuntimeError') ||
              errorMsg.includes('HTTPException')
            ) {
              console.warn('Backend async error detected. This is a known issue with the backend.');

              // Show a more user-friendly warning message
              setToastMessage('Your request was received. The customer may have been created. Please check the customer list and refresh before trying again.');
              setToastType('warning');
              setShowToast(true);

              // Close modal after a short delay to improve user experience
              setTimeout(() => {
                onClose();
              }, 5000);

              return;
            }

            // Handle other errors normally
            throw error;
          }
        }
      } catch (error) {
        console.error('Error during form submission:', error);
        const errorMsg = error instanceof Error ? error.message : 'An unknown error occurred';
        setToastMessage(isEditMode ? `Failed to update customer: ${errorMsg}` : `Failed to add customer: ${errorMsg}`);
        setToastType('error');
        setShowToast(true);
      }
    }
  };

  // Price list selection handling
  const handlePriceListChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const selectedId = e.target.value;
    console.log('Price list selected, ID:', selectedId);

    // Log the selected price list for debugging
    if (selectedId) {
      const selected = priceLists.find(list => list.id === selectedId);
      console.log('Selected price list:', selected);
    }

    setFormData(prev => ({
      ...prev,
      priceList: selectedId // Store the ID value
    }));

    // Clear error if it exists
    if (errors.priceList) {
      setErrors(prev => ({
        ...prev,
        priceList: ''
      }));
    }
  };

  // Fetch customer by ID directly from the API
  const fetchCustomerById = async (customerId: string) => {
    try {
      const token = localStorage.getItem(AUTH_TOKEN_KEY);
      const endpoint = `${API_URL}${ENDPOINTS.CUSTOMERS}/${customerId}`;
      
      const response = await fetch(endpoint, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'Authorization': token ? `Bearer ${token}` : ''
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch customer: ${response.status} - ${response.statusText}`);
      }

      const data = await response.json();
      console.log('Customer data:', data);

      // Populate form data with fetched customer data
      setFormData({
        name: data.name || '',
        contactPerson: data.contactPerson || '',
        email: data.email || '',
        phone: data.phone || '',
        billingAddress: data.billingAddress || '',
        billingSuburb: data.billingSuburb || '',
        billingPostcode: data.billingPostcode || '',
        isAccountCustomer: data.isAccountCustomer || false,
        priceList: data.price_list_id || '',
      });

      // Clear any previous errors
      setErrors({});
    } catch (error) {
      console.error('Error fetching customer:', error);
      const errorMsg = error instanceof Error ? error.message : 'An unknown error occurred';
      setToastMessage(`Failed to fetch customer: ${errorMsg}`);
      setToastType('error');
      setShowToast(true);
    }
  };

  if (!isOpen) return null;

  return (
    <>
      {showToast && (
        <Toast
          message={toastMessage}
          type={toastType}
          onClose={() => setShowToast(false)}
        />
      )}

      <ModalOverlay onClick={onClose}>
        <ModalContent onClick={(e) => e.stopPropagation()}>
          <ModalHeader>
            <ModalTitle>{isEditMode ? 'Edit Customer' : 'Add Customer'}</ModalTitle>
            {isEditMode && customer && (
              <RedirectLink to={`/customers/${customer.id}/history`}>
                Redirect to the Customer History ↗
              </RedirectLink>
            )}
          </ModalHeader>
          <ModalBody>
            <form onSubmit={handleSubmit}>
              <FormRow>
                <FormGroup>
                  <Label htmlFor="name">
                    Company Name<span className="required">*</span>
                  </Label>
                  <Input
                    type="text"
                    id="name"
                    name="name"
                    value={formData.name}
                    onChange={handleChange}
                  />
                  {errors.name && <p style={{ color: 'red', fontSize: '14px', marginTop: '4px' }}>{errors.name}</p>}
                </FormGroup>

                <FormGroup>
                  <Label htmlFor="contactPerson">
                    Contact Person
                  </Label>
                  <Input
                    type="text"
                    id="contactPerson"
                    name="contactPerson"
                    value={formData.contactPerson}
                    onChange={handleChange}
                  />
                </FormGroup>

                <FormGroup>
                  <Label htmlFor="email">
                    Email Address
                  </Label>
                  <Input
                    type="email"
                    id="email"
                    name="email"
                    value={formData.email}
                    onChange={handleChange}
                  />
                  {errors.email && <p style={{ color: 'red', fontSize: '14px', marginTop: '4px' }}>{errors.email}</p>}
                </FormGroup>

                <FormGroup>
                  <Label htmlFor="phone">
                    Phone Number<span className="required">*</span>
                  </Label>
                  <Input
                    type="text"
                    id="phone"
                    name="phone"
                    value={formData.phone}
                    onChange={handlePhoneChange}
                    placeholder="+61412345678"
                  />
                  {errors.phone && <p style={{ color: 'red', fontSize: '14px', marginTop: '4px' }}>{errors.phone}</p>}
                  <p style={{ color: '#6B7280', fontSize: '12px', marginTop: '4px' }}>Enter 7-15 digits with optional + prefix. No spaces will be saved.</p>
                </FormGroup>
              </FormRow>

              <FormRow>
                <FormGroup>
                  <Label htmlFor="priceList">
                    Applicable Price List<span className="required">*</span>
                  </Label>
                  <SelectWrapper>
                    <Select
                      id="priceList"
                      name="priceList"
                      value={formData.priceList || ''}
                      onChange={handlePriceListChange}
                    >
                      <option value="">Select a price list</option>
                      {priceLists && priceLists.length > 0 ? (
                        priceLists.map(list => (
                          <option key={list.id} value={list.id}>{list.name}</option>
                        ))
                      ) : (
                        <option value="">No price lists available</option>
                      )}
                    </Select>
                  </SelectWrapper>
                  {errors.priceList && <p style={{ color: 'red', fontSize: '14px', marginTop: '4px' }}>{errors.priceList}</p>}
                  {priceLists.length === 0 && <p style={{ color: 'orange', fontSize: '14px', marginTop: '4px' }}>Loading price lists</p>}
                </FormGroup>

                <FormGroup>
                  <CheckboxContainer>
                    <CheckboxLabel htmlFor="isAccountCustomer">
                      Account Customer
                    <Checkbox
                      type="checkbox"
                      id="isAccountCustomer"
                      name="isAccountCustomer"
                      checked={formData.isAccountCustomer || false}
                      onChange={handleChange}
                    />
                    </CheckboxLabel>
                  </CheckboxContainer>
                </FormGroup>
              </FormRow>

              <BillingSection>
                <BillingSectionTitle>Billing Information</BillingSectionTitle>

              <FormRow>
                <FormGroup>
                  <Label htmlFor="billingAddress">
                    Billing Address
                  </Label>
                  <Input
                    type="text"
                    id="billingAddress"
                    name="billingAddress"
                    value={formData.billingAddress}
                    onChange={handleChange}
                  />
                </FormGroup>
              </FormRow>

              <FormRow>
                <FormGroup>
                  <Label htmlFor="billingSuburb">
                    Billing Suburb
                  </Label>
                  <Input
                    type="text"
                    id="billingSuburb"
                    name="billingSuburb"
                    value={formData.billingSuburb}
                    onChange={handleChange}
                  />
                </FormGroup>

                <FormGroup>
                  <Label htmlFor="billingPostcode">
                    Billing Postcode
                  </Label>
                  <Input
                    type="text"
                    id="billingPostcode"
                    name="billingPostcode"
                    value={formData.billingPostcode}
                    onChange={handleChange}
                  />
                </FormGroup>
              </FormRow>
              </BillingSection>
            </form>
          </ModalBody>
              <ModalFooter>
                <CancelButton type="button" onClick={onClose} disabled={isLoading}>
                  Cancel
                </CancelButton>
                <SubmitButton 
                  type="button" 
                  onClick={handleSubmit} 
                  disabled={isLoading || (isEditMode && !hasChanges)}
                >
                  {isLoading ? <ButtonLoader /> : isEditMode ? 'Update' : 'Submit'}
                </SubmitButton>
              </ModalFooter>
        </ModalContent>
      </ModalOverlay>
    </>
  );
};

export default AddCustomerModal;