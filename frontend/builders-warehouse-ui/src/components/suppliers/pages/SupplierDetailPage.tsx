import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import styled from 'styled-components';
import Layout from '../../layout/Layout';
import { Link, useParams, useNavigate, useLocation } from 'react-router-dom';
import supplierBulkUploadIcon from '../../../assets/supplierBulkUploadIcon.png';
import { useAuth } from '../../../context/AuthContext';
import supplierService, { Supplier as ApiSupplier, PriceItem } from '../../../services/supplierService';
import { BackButtonComponent } from '../../ui/DesignSystem';
import { BackLinkComponent } from '../../ui/DesignSystem';
import { API_URL, ENDPOINTS, AUTH_TOKEN_KEY } from '../../../config';
import { useToast } from '../../../hooks/useToast';
import { Toast } from '../../common/Toast';

// Create a wrapper for form and buttons for proper layout
const ContentContainer = styled.div`
  display: flex;
  flex-direction: column;
  width: 100%;
`;

const PageContainer = styled.div`
  padding: 0.5rem 1.5rem;
  max-width: 100vw;
  width: 100%;
  display: flex;
  flex-direction: column;
  min-height: calc(100vh - 60px);
  overflow-x: hidden;
  box-sizing: border-box;
  
  @media (max-width: 768px) {
    padding: 0.5rem 1rem;
  }
`;

const TopSection = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
  flex-wrap: wrap;
  gap: 12px;
  padding: 0 0.5rem;
  
  @media (max-width: 768px) {
    flex-direction: column;
    align-items: stretch;
    padding: 0;
  }
`;

const TitleSection = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
  flex-wrap: wrap;
  min-width: 0; /* Allow shrinking */
  
  @media (max-width: 768px) {
    width: 100%;
    justify-content: flex-start;
  }
`;

const PageTitle = styled.h1`
  color: #042B41;
  font-size: clamp(20px, 4vw, 28px);
  font-weight: 700;
  margin: 0;
`;

const BulkUploadButton = styled.button`
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background-color: #042B41;
  color: white;
  border: none;
  padding: 0.625rem 1rem;
  border-radius: 0.375rem;
  font-weight: 500;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s;
  white-space: nowrap;
  min-width: 120px;
  height: 44px;
  margin-left: auto;
  
  &:hover {
    background-color: #031F30;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  &:active {
    transform: translateY(0);
  }
  
  img {
    width: 16px;
    height: 16px;
    flex-shrink: 0;
  }
  
  @media (max-width: 768px) {
    width: 100%;
    justify-content: center;
    margin-left: 0;
  }
  
  @media (max-width: 480px) {
    padding: 0.5rem 0.75rem;
    font-size: 0.8rem;
    height: 40px;
    min-width: 100px;
  }
`;

const BreadcrumbNav = styled.div`
  display: flex;
  align-items: center;
  font-size: 0.9rem;
  color: #6B7280;
  flex-wrap: wrap;
  min-width: 0;
  
  @media (max-width: 480px) {
    font-size: 0.8rem;
  }
`;

const BreadcrumbSeparator = styled.span`
  margin: 0 0.5rem;
  color: #6B7280;
  flex-shrink: 0;
`;

const SuppliersText = styled.span`
  color: #6B7280;
  text-decoration: none;
  white-space: nowrap;
  cursor: pointer;
  
  &:hover {
    text-decoration: underline;
  }
`;

const SupplierName = styled.span`
  font-weight: 600;
  color: #111827;
  word-break: break-word;
  min-width: 0;
`;

const FormContainer = styled.div`
  width: 100%;
  margin-bottom: 0.5rem;
`;

const FormRow = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 0.75rem;
  
  @media (max-width: 768px) {
    grid-template-columns: 1fr;
    gap: 0.75rem;
  }
`;

const FormField = styled.div`
  display: flex;
  flex-direction: column;
  min-width: 0;
`;

const FormLabel = styled.label<{ required?: boolean }>`
  font-size: 0.8rem;
  font-weight: 500;
  color: #111827;
  margin-bottom: 0.25rem;
  
  &::after {
    content: ${props => props.required ? '"*"' : '""'};
    color: #EF4444;
    margin-left: 2px;
  }
`;

const FormInput = styled.input`
  padding: 0.5rem;
  border: 1px solid #D1D5DB;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  width: 100%;
  min-width: 0;
  
  &:focus {
    outline: none;
    border-color: #042B41;
    box-shadow: 0 0 0 2px rgba(4, 43, 65, 0.1);
  }
`;

const TableContainer = styled.div`
  width: 100%;
  max-width: 100vw;
  margin-top: 0.5rem;
  border: 1px solid #E5E7EB;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  background-color: white;
  overflow: hidden;
  position: relative;
`;

const TableScrollWrapper = styled.div`
  overflow-x: auto;
  overflow-y: visible;
  width: 100%;
  max-width: calc(100vw - 2rem);
  max-height: 60vh;
  
  /* Custom dark scrollbar styling */
  &::-webkit-scrollbar {
    height: 16px;
  }
  
  &::-webkit-scrollbar-track {
    background: #f1f3f4;
    border-radius: 8px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: #4a5568;
    border-radius: 8px;
    border: 2px solid #f1f3f4;
  }
  
  &::-webkit-scrollbar-thumb:hover {
    background: #2d3748;
  }
  
  &::-webkit-scrollbar-corner {
    background: #f1f3f4;
  }
  
  /* Firefox scrollbar */
  scrollbar-width: auto;
  scrollbar-color: #4a5568 #f1f3f4;
`;

const Table = styled.table.withConfig({
  shouldForwardProp: (prop) => prop !== 'isStaffUser',
}) <{ isStaffUser?: boolean }>`
  width: ${props => props.isStaffUser ? '1800px' : '2600px'};
  min-width: ${props => props.isStaffUser ? '1800px' : '2600px'};
  border-collapse: collapse;
  border: none;
  table-layout: fixed;
  background-color: white;
  display: table;
`;

const TableHeader = styled.thead`
  background-color: #042B41;
  color: white;
  position: sticky;
  top: 0;
  z-index: 10;
  
  th {
    padding: 12px 8px;
    text-align: center;
    font-weight: 700;
    font-size: 11px;
    border-right: 1px solid rgba(255, 255, 255, 0.2);
    white-space: nowrap;
    vertical-align: middle;
    line-height: 1.2;
    
    &:last-child {
      border-right: none;
    }
    
    /* Increased column widths to prevent text overlapping */
    &:nth-child(1) { width: 120px; min-width: 120px; } /* Code */
    &:nth-child(2) { width: 300px; min-width: 300px; } /* Description */
    &:nth-child(3) { width: 180px; min-width: 180px; } /* Style Code */
    &:nth-child(4) { width: 160px; min-width: 160px; } /* RRP */
    &:nth-child(5) { width: 200px; min-width: 200px; } /* BWA Buy Price */
    &:nth-child(6) { width: 180px; min-width: 180px; } /* Regional Retail */
    &:nth-child(7) { width: 180px; min-width: 180px; } /* Regional Trade */
    &:nth-child(8) { width: 180px; min-width: 180px; } /* Metro Retail */
    &:nth-child(9) { width: 180px; min-width: 180px; } /* Metro Trade */
    &:nth-child(10) { width: 200px; min-width: 200px; } /* Regional Retail VIP */
    &:nth-child(11) { width: 220px; min-width: 220px; } /* Regional Trade VIP 1 */
    &:nth-child(12) { width: 220px; min-width: 220px; } /* Regional Trade VIP2 */
    &:nth-child(13) { width: 200px; min-width: 200px; } /* Metro Retail VIP */
    &:nth-child(14) { width: 220px; min-width: 220px; } /* Metro Trade VIP1 */
    &:nth-child(15) { width: 220px; min-width: 220px; } /* Metro Trade VIP2 */
    &:nth-child(16) { width: 120px; min-width: 120px; } /* Status */
  }
`;

const TableBody = styled.tbody`
  tr {
    border-bottom: 1px solid #E5E7EB;
    
    &:nth-child(even) {
      background-color: #F9FAFB;
    }
    
    &:last-child {
      border-bottom: none;
    }
    
    &:hover {
      background-color: #F3F4F6;
    }
  }
  
  td {
    padding: 10px 8px;
    font-size: 13px;
    border-right: 1px solid #E5E7EB;
    word-break: break-word;
    overflow: hidden;
    text-overflow: ellipsis;
    text-align: center;
    vertical-align: middle;
    line-height: 1.3;
    
    &:last-child {
      border-right: none;
    }
    
    &.number-cell {
      text-align: right;
      font-family: 'Courier New', monospace;
      font-weight: 500;
    }
    
    &.status-cell {
      text-align: center;
    }
    
    /* Description column - left aligned and allows text wrapping */
    &:nth-child(2) {
      text-align: left;
      white-space: normal;
      line-height: 1.4;
      word-wrap: break-word;
    }
    
    /* Code column */
    &:nth-child(1) {
      font-weight: 600;
      color: #042B41;
    }
    
    /* Style code column */
    &:nth-child(3) {
      font-family: 'Courier New', monospace;
      font-size: 12px;
    }
  }
`;

const ActionButtonsContainer = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
  margin-top: 1rem;
  padding: 0.75rem 1rem;
  position: fixed;
  bottom: 0;
  right: 0;
  left: 0;
  background-color: white;
  z-index: 10;
  box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  border-top: 1px solid #E5E7EB;
  
  @media (max-width: 768px) {
    flex-direction: column-reverse;
    gap: 0.75rem;
    padding: 1rem;
  }
`;

const CancelButton = styled.button`
  padding: 0.75rem 2rem;
  border: 1px solid #D1D5DB;
  border-radius: 0.375rem;
  background-color: white;
  color: #111827;
  font-weight: 500;
  font-size: 0.875rem;
  cursor: pointer;
  min-width: 120px;
  height: 44px;
  
  &:hover {
    background-color: #F9FAFB;
  }
  
  @media (max-width: 768px) {
    width: 100%;
  }
`;

const SaveButton = styled.button`
  padding: 0.75rem 2rem;
  border: none;
  border-radius: 0.375rem;
  background-color: #042B41;
  color: white;
  font-weight: 500;
  font-size: 0.875rem;
  cursor: pointer;
  min-width: 120px;
  height: 44px;
  
  &:hover:not(:disabled) {
    background-color: #031F30;
  }
  
  &:disabled {
    background-color: #9CA3AF;
    cursor: not-allowed;
    opacity: 0.6;
  }
  
  @media (max-width: 768px) {
    width: 100%;
  }
`;

const LoadingContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 3rem 2rem;
  
  @media (max-width: 480px) {
    padding: 2rem 1rem;
  }
`;

// Modal components
const ModalOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  padding: 1rem;
`;

const ModalContent = styled.div`
  background-color: white;
  border-radius: 8px;
  width: 100%;
  max-width: 600px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 2rem;
  max-height: 90vh;
  overflow-y: auto;
  
  @media (max-width: 768px) {
    padding: 1.5rem;
    max-height: 95vh;
  }
  
  @media (max-width: 480px) {
    padding: 1rem;
    margin: 0.5rem;
  }
`;

const ModalTitle = styled.h2`
  font-size: 1.5rem;
  color: #333;
  margin: 0 0 1rem 0;
  text-align: center;
  font-weight: 600;
  
  @media (max-width: 480px) {
    font-size: 1.25rem;
  }
`;

const ModalSubtitle = styled.p`
  font-size: 1rem;
  color: #333;
  margin: 0 0 1.5rem 0;
  text-align: center;
  
  @media (max-width: 480px) {
    font-size: 0.9rem;
    margin: 0 0 1rem 0;
  }
`;

const FileUploadArea = styled.div`
  border: 2px dashed #D1D5DB;
  border-radius: 8px;
  padding: 2.5rem;
  margin-bottom: 1.5rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #F9FAFB;
  cursor: pointer;
  transition: all 0.2s;
  
  &:hover {
    border-color: #042B41;
    background-color: #F3F4F6;
  }
  
  @media (max-width: 480px) {
    padding: 1.5rem;
    margin-bottom: 1rem;
  }
`;

const UploadIcon = styled.div`
  margin-bottom: 1rem;
  
  img {
    width: 48px;
    height: 48px;
  }
  
  @media (max-width: 480px) {
    margin-bottom: 0.75rem;
    
    img {
      width: 36px;
      height: 36px;
    }
  }
`;

const FileText = styled.p`
  font-size: 1rem;
  color: #333;
  margin: 0;
  text-align: center;
  
  @media (max-width: 480px) {
    font-size: 0.9rem;
  }
`;

const ButtonContainer = styled.div`
  display: flex;
  justify-content: space-between;
  gap: 1rem;
  
  @media (max-width: 480px) {
    flex-direction: column;
    gap: 0.75rem;
  }
`;

const CancelModalButton = styled.button`
  flex: 1;
  padding: 0.75rem;
  border: 1px solid #D1D5DB;
  border-radius: 0.375rem;
  background-color: white;
  color: #111827;
  font-weight: 500;
  font-size: 0.875rem;
  cursor: pointer;
  height: 44px;
  
  &:hover {
    background-color: #F9FAFB;
  }
  
  @media (max-width: 480px) {
    width: 100%;
    font-size: 0.8rem;
    height: 40px;
  }
`;

const SubmitModalButton = styled.button`
  flex: 1;
  padding: 0.75rem;
  border: none;
  border-radius: 0.375rem;
  background-color: #042B41;
  color: white;
  font-weight: 500;
  font-size: 0.875rem;
  cursor: pointer;
  height: 44px;
  
  &:hover {
    background-color: #031F30;
  }
  
  &:disabled {
    background-color: #9CA3AF;
    cursor: not-allowed;
    opacity: 0.6;
  }
  
  @media (max-width: 480px) {
    width: 100%;
    font-size: 0.8rem;
    height: 40px;
  }
`;

const HiddenFileInput = styled.input`
  display: none;
`;

// Loading spinner component
const LoadingSpinner = styled.div`
  display: inline-block;
  width: 2rem;
  height: 2rem;
  border: 0.25rem solid rgba(4, 43, 65, 0.3);
  border-radius: 50%;
  border-top-color: #042B41;
  animation: spin 1s ease-in-out infinite;
  margin: 2rem auto;
  
  @keyframes spin {
    to { transform: rotate(360deg); }
  }
`;

// Add a component for displaying error messages
const ErrorMessage = styled.div`
  color: #EF4444;
  background-color: #FEE2E2;
  border: 1px solid #FCA5A5;
  padding: 0.75rem 1rem;
  border-radius: 0.375rem;
  margin-bottom: 0.75rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  color: #EF4444;
  font-size: 1.25rem;
  font-weight: bold;
  cursor: pointer;
  padding: 0;
  margin-left: 1rem;
  line-height: 1;
  
  &:hover {
    opacity: 0.7;
  }
`;

// Add a component for success messages
const SuccessMessage = styled.div`
  color: #047857;
  background-color: #D1FAE5;
  border: 1px solid #A7F3D0;
  padding: 0.75rem 1rem;
  border-radius: 0.375rem;
  margin-bottom: 0.75rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  position: relative;
`;

const SuccessCloseButton = styled.button`
  background: none;
  border: none;
  color: #047857;
  font-size: 1.25rem;
  font-weight: bold;
  cursor: pointer;
  padding: 0;
  margin-left: 1rem;
  line-height: 1;
  
  &:hover {
    opacity: 0.7;
  }
`;

// Create extra padding at the bottom to prevent content from being hidden behind the fixed buttons
const BottomSpacer = styled.div`
  height: 60px;
  width: 100%;
`;

// Update content wrapper to handle proper positioning
const ContentWrapper = styled.div`
  position: relative;
  width: 100%;
  margin-bottom: 1rem;
`;

// Update the interface for product items to match our API
interface Product extends PriceItem {
  // Additional properties or UI-specific properties can be added here
}

// Add Toggle Switch Component
const ToggleSwitch = styled.label`
  position: relative;
  display: inline-block;
  width: 44px;
  height: 24px;
`;

const ToggleSlider = styled.span<{ checked: boolean }>`
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: ${props => props.checked ? '#042B41' : '#ccc'};
  transition: .4s;
  border-radius: 24px;

  &:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: ${props => props.checked ? '23px' : '3px'};
    bottom: 3px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
  }
`;

const ToggleInput = styled.input`
  opacity: 0;
  width: 0;
  height: 0;
`;

const SupplierDetailPage: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const { user } = useAuth();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const toast = useToast();
  const location = useLocation();

  // Helper function to build suppliers URL with preserved pagination state
  const buildSuppliersURL = useCallback(() => {
    const urlParams = new URLSearchParams(location.search);
    const page = urlParams.get('page');
    const limit = urlParams.get('limit');
    const search = urlParams.get('search');

    const suppliersParams = new URLSearchParams();
    if (page) suppliersParams.set('page', page);
    if (limit) suppliersParams.set('limit', limit);
    if (search) suppliersParams.set('search', search);

    return suppliersParams.toString()
      ? `/suppliers?${suppliersParams.toString()}`
      : '/suppliers';
  }, [location.search]);

  // State for supplier data
  const [supplier, setSupplier] = useState<ApiSupplier | null>(null);
  const [supplierName, setSupplierName] = useState<string>('');
  const [loading, setLoading] = useState<boolean>(true);
  const [saving, setSaving] = useState<boolean>(false);
  const [products, setProducts] = useState<Product[]>([]);
  const [showBulkUploadModal, setShowBulkUploadModal] = useState<boolean>(false);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);

  // Supplier form data
  const [email, setEmail] = useState('');
  const [phone, setPhone] = useState('');
  const [address, setAddress] = useState('');

  // Original data for change detection
  const [originalData, setOriginalData] = useState({
    supplierName: '',
    email: '',
    phone: '',
    address: '',
    products: [] as Product[]
  });

  // Check if there are any changes
  const hasChanges = useMemo(() => {
    const currentProducts = JSON.stringify(products);
    const originalProducts = JSON.stringify(originalData.products);

    return (
      supplierName !== originalData.supplierName ||
      email !== originalData.email ||
      phone !== originalData.phone ||
      address !== originalData.address ||
      currentProducts !== originalProducts
    );
  }, [supplierName, email, phone, address, products, originalData]);

  // Memoize the fetchSupplierDetails function to prevent infinite loops
  const fetchSupplierDetails = useCallback(async (supplierId: number) => {
    setLoading(true);

    try {
      console.log(`Fetching supplier details for ID: ${supplierId}`);

      // First attempt - use the proper API endpoint
      try {
        console.log(`Making API call to get supplier with ID: ${supplierId}`);
        console.log(`API URL: ${API_URL}${ENDPOINTS.SUPPLIERS}/${supplierId}`);

        const supplier = await supplierService.getSupplierById(supplierId);
        console.log('Supplier data received:', supplier);

        if (!supplier || !supplier.name) {
          throw new Error('Received incomplete supplier data from API');
        }

        // Update form state with supplier data
        setSupplierName(supplier.supplier_name || supplier.name || '');
        setPhone(supplier.phone_no || supplier.phone || '');
        setEmail(supplier.email || '');
        setAddress(supplier.address || '');

        // Handle price list items if they exist
        if (supplier.price_list && Array.isArray(supplier.price_list) && supplier.price_list.length > 0) {
          // Map the response data to ensure we use 'sku' field consistently
          const mappedPriceList = supplier.price_list.map((item: any) => ({
            ...item,
            sku: item.sku || { id: 0, code: item.code || '' }, // Handle both new and old formats
          }));
          setProducts(mappedPriceList);

          // Store original data for change detection
          setOriginalData({
            supplierName: supplier.supplier_name || supplier.name || '',
            email: supplier.email || '',
            phone: supplier.phone_no || supplier.phone || '',
            address: supplier.address || '',
            products: mappedPriceList
          });
        } else {
          // Initialize with empty array to prevent undefined errors
          setProducts([]);

          // Store original data for change detection
          setOriginalData({
            supplierName: supplier.supplier_name || supplier.name || '',
            email: supplier.email || '',
            phone: supplier.phone_no || supplier.phone || '',
            address: supplier.address || '',
            products: []
          });
        }
      } catch (apiError) {
        console.error('Error from supplier service:', apiError);

        // Try to get data directly using fetch as a fallback
        const token = localStorage.getItem(AUTH_TOKEN_KEY);

        // Check if token exists
        if (!token) {
          throw new Error('Authentication token is missing. Please log in again.');
        }

        console.log(`Making direct fetch to ${API_URL}${ENDPOINTS.SUPPLIERS}/${supplierId}`);

        const response = await fetch(`${API_URL}${ENDPOINTS.SUPPLIERS}${supplierId}`, {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          }
        });

        // Detailed error logging
        if (!response.ok) {
          console.error(`API returned status ${response.status}`);

          if (response.status === 404) {
            throw new Error(`Supplier with ID ${supplierId} not found.`);
          } else if (response.status === 401 || response.status === 403) {
            throw new Error('You do not have permission to access this supplier.');
          } else {
            // Try to get error details from response
            const errorText = await response.text();
            console.error('Error response:', errorText);

            // Extract error message if possible
            let errorMessage = `API error: ${response.status}`;
            try {
              // Try to parse JSON error response
              const errorJson = JSON.parse(errorText);
              if (errorJson.detail) {
                errorMessage = errorJson.detail;
              }
            } catch (parseError) {
              // If not JSON, use the raw text
              if (errorText) {
                errorMessage += ` - ${errorText}`;
              }
            }

            throw new Error(errorMessage);
          }
        }

        const data = await response.json();
        console.log('Data received from direct fetch:', data);

        // Update form state with supplier data
        setSupplierName(data.supplier_name || data.name || '');
        setPhone(data.phone_no || data.phone || '');
        setEmail(data.email || '');
        setAddress(data.address || '');

        // Handle price list items if they exist
        if (data.price_list && Array.isArray(data.price_list) && data.price_list.length > 0) {
          // Map the response data to ensure we use 'sku' field consistently
          const mappedPriceList = data.price_list.map((item: any) => ({
            ...item,
            sku: item.sku || { id: 0, code: item.code || '' }, // Handle both new and old formats
          }));
          setProducts(mappedPriceList);

          // Store original data for change detection
          setOriginalData({
            supplierName: data.supplier_name || data.name || '',
            email: data.email || '',
            phone: data.phone_no || data.phone || '',
            address: data.address || '',
            products: mappedPriceList
          });
        } else {
          // Initialize with empty array to prevent undefined errors
          setProducts([]);

          // Store original data for change detection
          setOriginalData({
            supplierName: data.supplier_name || data.name || '',
            email: data.email || '',
            phone: data.phone_no || data.phone || '',
            address: data.address || '',
            products: []
          });
        }
      }
    } catch (err) {
      console.error('Error in fetchSupplierDetails:', err);

      // Display a meaningful error to the user
      let errorMessage = 'Failed to load supplier details.';

      if (err instanceof Error) {
        errorMessage = err.message;
      }

      toast.showToast(errorMessage, { type: 'error' });

      // Create minimal supplier data to prevent UI crashes
      setSupplierName(supplierName || `Supplier ${supplierId}`);
      setPhone(phone || '');
      setEmail(email || '');
      setAddress(address || '');
      setProducts([]);
    } finally {
      setLoading(false);
    }
  }, [toast.showToast]); // Only depend on the showToast function, not the entire toast object

  // Fetch supplier data on component mount
  useEffect(() => {
    if (id) {
      // Validate that the ID is a valid number
      const numericId = parseInt(id, 10);

      if (isNaN(numericId)) {
        toast.showToast(`Invalid supplier ID: ${id}. The ID must be a number.`, { type: 'error' });
        setLoading(false);
        return;
      }

      fetchSupplierDetails(numericId);
    } else {
      toast.showToast('No supplier ID provided. Please select a valid supplier.', { type: 'error' });
      setLoading(false);
    }
  }, [id, fetchSupplierDetails]); // Remove toast from dependencies, use fetchSupplierDetails instead

  const handleSave = async () => {
    setSaving(true);

    try {
      if (!id) {
        toast.showToast('No supplier ID available for update.', { type: 'error' });
        return;
      }

      // Prepare supplier data for update
      const supplierData = {
        supplier_name: supplierName,
        phone_no: phone,
        email: email,
        address: address,
        price_list: products
      };

      console.log('Updating supplier with data:', supplierData);

      // Call the API to update supplier
      await supplierService.updateSupplier(parseInt(id), supplierData);

      // Update original data to reflect the saved state
      setOriginalData({
        supplierName,
        email,
        phone,
        address,
        products: [...products]
      });

      toast.showToast('Supplier information updated successfully', { type: 'success' });

      // Navigate back to suppliers page with preserved pagination state
      const suppliersUrl = buildSuppliersURL();
      navigate(suppliersUrl);

    } catch (err) {
      console.error('Error updating supplier:', err);
      if (err instanceof Error) {
        toast.showToast(err.message, { type: 'error' });
      } else {
        toast.showToast('Failed to update supplier information. Please try again.', { type: 'error' });
      }
    } finally {
      setSaving(false);
    }
  };

  const handleCancel = () => {
    // Extract the pagination parameters from the current URL
    const suppliersUrl = buildSuppliersURL();
    navigate(suppliersUrl);
  };

  const handleBulkUploadClick = () => {
    setShowBulkUploadModal(true);
  };

  const handleCloseModal = () => {
    setShowBulkUploadModal(false);
    setSelectedFile(null);
  };

  const handleFileAreaClick = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setSelectedFile(e.target.files[0]);
    }
  };

  const handleSubmitFile = () => {
    if (!selectedFile) {
      toast.showToast('Please select a file to upload.', { type: 'error' });
      return;
    }

    // Process the uploaded file (in a real app, this would send to backend)
    console.log('Uploading file:', selectedFile);
    toast.showToast('File uploaded successfully!', { type: 'success' });
    setShowBulkUploadModal(false);
    setSelectedFile(null);
  };

  const handleBuyPriceChange = (index: number, newPrice: string) => {
    const updatedProducts = [...products];
    updatedProducts[index] = { ...updatedProducts[index], bwa_buy_price_50_ex_gst: parseFloat(newPrice) || 0 };
    setProducts(updatedProducts);
  };

  const handleBack = () => {
    // Extract the pagination parameters from the current URL
    const suppliersUrl = buildSuppliersURL();
    navigate(suppliersUrl);
  };

  if (loading && products.length === 0) {
    return (
      <Layout>
        <LoadingContainer>
          <LoadingSpinner />
        </LoadingContainer>
      </Layout>
    );
  }

  return (
    <Layout>
      {/* Toast notification */}
      {toast.isVisible && (
        <Toast
          message={toast.message}
          type={toast.type}
          onClose={() => { }}
        />
      )}

      <PageContainer>
        <TopSection>
          <TitleSection>
            <BackButtonComponent onClick={handleBack} compact={true} />
            <BreadcrumbNav>
              <SuppliersText onClick={() => navigate(buildSuppliersURL())}>Suppliers Dashboard</SuppliersText>
              <BreadcrumbSeparator>&gt;</BreadcrumbSeparator>
              <SupplierName>{supplierName}</SupplierName>
            </BreadcrumbNav>
          </TitleSection>

          <BulkUploadButton onClick={handleBulkUploadClick}>
            <img src={supplierBulkUploadIcon} alt="Upload" />
            Bulk Upload
          </BulkUploadButton>
        </TopSection>

        <ContentContainer>
          <FormContainer>
            <FormRow>
              <FormField>
                <FormLabel required>Supplier Name</FormLabel>
                <FormInput
                  type="text"
                  value={supplierName}
                  onChange={(e) => setSupplierName(e.target.value)}
                />
              </FormField>

              <FormField>
                <FormLabel>Email ID</FormLabel>
                <FormInput
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                />
              </FormField>

              <FormField>
                <FormLabel required>Phone No</FormLabel>
                <FormInput
                  type="tel"
                  value={phone}
                  onChange={(e) => setPhone(e.target.value)}
                />
              </FormField>

              <FormField>
                <FormLabel>Address</FormLabel>
                <FormInput
                  type="text"
                  value={address}
                  onChange={(e) => setAddress(e.target.value)}
                />
              </FormField>
            </FormRow>
          </FormContainer>

          <ContentWrapper>
            <TableContainer>
              <TableScrollWrapper>
                <Table isStaffUser={user?.role === 'staff'}>
                  <TableHeader>
                    <tr>
                      <th>SKU CODE</th>
                      <th>Product Description</th>
                      <th>Style Code</th>
                      {user?.role !== 'staff' && <th>RRP (ex GST)</th>}
                      {user?.role !== 'staff' && <th>BWA BUY PRICE -50% (ex GST)</th>}
                      {user?.role !== 'staff' && <th>Regional Retail AUD Incl</th>}
                      {user?.role !== 'staff' && <th>Regional Trade AUD Incl</th>}
                      <th>Metro Retail AUD Incl</th>
                      <th>Metro Trade AUD Incl</th>
                      <th>Regional Retail VIP AUD Incl</th>
                      <th>Regional Trade VIP 1 AUD Incl</th>
                      <th>Regional Trade VIP2 AUD Incl</th>
                      <th>Metro Retail VIP AUD Incl</th>
                      <th>Metro Trade VIP1 AUD Incl</th>
                      <th>Metro Trade VIP2 AUD Incl</th>
                      <th>Status</th>
                    </tr>
                  </TableHeader>
                  <TableBody>
                    {products.map((product, index) => (
                      <tr key={index}>
                        <td>{product.sku?.code || ''}</td>
                        <td>{product.description}</td>
                        <td>{product.style_code || ''}</td>
                        {user?.role !== 'staff' && <td className="number-cell">{product.rrp_ex_gst || 0}</td>}
                        {user?.role !== 'staff' && (
                          <td className="number-cell">
                            {user?.role === 'admin' ? (
                              <FormInput
                                type="text"
                                value={product.bwa_buy_price_50_ex_gst?.toString() || '0'}
                                onChange={(e) => handleBuyPriceChange(index, e.target.value)}
                                style={{ width: '180px', textAlign: 'right', margin: '0', padding: '6px 8px', fontSize: '13px' }}
                              />
                            ) : (
                              product.bwa_buy_price_50_ex_gst || 0
                            )}
                          </td>
                        )}
                        {user?.role !== 'staff' && <td className="number-cell">{product.regional_retail_aud_incl || 0}</td>}
                        {user?.role !== 'staff' && <td className="number-cell">{product.regional_trade_aud_incl || 0}</td>}
                        <td className="number-cell">{product.metro_retail_aud_incl || 0}</td>
                        <td className="number-cell">{product.metro_trade_aud_incl || 0}</td>
                        <td className="number-cell">{product.regional_retail_vip_aud_incl || 0}</td>
                        <td className="number-cell">{product.regional_trade_vip1_aud_incl || 0}</td>
                        <td className="number-cell">{product.regional_trade_vip2_aud_incl || 0}</td>
                        <td className="number-cell">{product.metro_retail_vip_aud_incl || 0}</td>
                        <td className="number-cell">{product.metro_trade_vip1_aud_incl || 0}</td>
                        <td className="number-cell">{product.metro_trade_vip2_aud_incl || 0}</td>
                        <td className="status-cell">
                          <ToggleSwitch>
                            <ToggleInput
                              type="checkbox"
                              checked={product.status !== false}
                              onChange={() => {
                                const updatedProducts = [...products];
                                updatedProducts[index].status = !product.status;
                                setProducts(updatedProducts);
                              }}
                            />
                            <ToggleSlider checked={product.status !== false} />
                          </ToggleSwitch>
                        </td>
                      </tr>
                    ))}
                  </TableBody>
                </Table>
              </TableScrollWrapper>
            </TableContainer>
          </ContentWrapper>

          <BottomSpacer />
        </ContentContainer>

        <ActionButtonsContainer>
          <CancelButton onClick={handleCancel}>Cancel</CancelButton>
          <SaveButton onClick={handleSave} disabled={saving || !hasChanges}>
            {saving ? 'Saving...' : 'Save'}
          </SaveButton>
        </ActionButtonsContainer>

        {showBulkUploadModal && (
          <ModalOverlay>
            <ModalContent>
              <ModalTitle>Bulk Upload</ModalTitle>
              <ModalSubtitle>
                Upload a CSV file with product information
              </ModalSubtitle>

              <FileUploadArea onClick={handleFileAreaClick}>
                <input
                  type="file"
                  ref={fileInputRef}
                  style={{ display: 'none' }}
                  accept=".csv"
                  onChange={handleFileChange}
                />
                {selectedFile ? (
                  <FileText>Selected file: {selectedFile.name}</FileText>
                ) : (
                  <FileText>Click to select a CSV file</FileText>
                )}
              </FileUploadArea>

              <ButtonContainer>
                <CancelModalButton onClick={handleCloseModal}>Cancel</CancelModalButton>
                <SubmitModalButton
                  onClick={handleSubmitFile}
                  disabled={!selectedFile}
                >
                  Upload
                </SubmitModalButton>
              </ButtonContainer>
            </ModalContent>
          </ModalOverlay>
        )}
      </PageContainer>
    </Layout>
  );
};

export default SupplierDetailPage; 