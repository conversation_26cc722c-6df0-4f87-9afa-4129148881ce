import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import Layout from '../../layout/Layout';
import { useNavigate } from 'react-router-dom';
import backButtonIcon from '../../../assets/backButton.png';

// Sample data matching the image structure
const sampleData = [
  {
    code: 'BB69033',
    description: 'JESS Towel Ring in Brushed Brass',
    styleCode: 'BWA E1 BB69033',
    rrp: '$78.00',
    bwaBuyPrice: '$39.00',
    regionalRetailAUD: '$72.95',
    regionalTradeAUD: '$68.55',
    metroRetailAUD: '$68.65'
  },
  {
    code: 'BB69034',
    description: 'JESS Soap Dish in Brushed Brass',
    styleCode: 'BWA E1 BB69034',
    rrp: '$110.00',
    bwaBuyPrice: '$55.00',
    regionalRetailAUD: '$102.85',
    regionalTradeAUD: '$96.80',
    metroRetailAUD: '$96.80'
  },
  {
    code: 'BB69035A',
    description: 'JESS Toilet Roll Holder in Brushed Brass',
    styleCode: 'BWA E1 BB69035A',
    rrp: '$69.00',
    bwaBuyPrice: '$34.00',
    regionalRetailAUD: '$64.55',
    regionalTradeAUD: '$60.75',
    metroRetailAUD: '$60.75'
  },
  {
    code: 'BB69036',
    description: 'JESS 600mm Single Towel Rail in Brushed Brass',
    styleCode: 'BWA E1 BB69036',
    rrp: '$92.00',
    bwaBuyPrice: '$46.00',
    regionalRetailAUD: '$86.05',
    regionalTradeAUD: '$81.00',
    metroRetailAUD: '$81.00'
  },
  {
    code: 'BB69037',
    description: 'JESS Double Towel Rail 600mm in Brushed Brass',
    styleCode: 'BWA E1 BB69037',
    rrp: '$145.00',
    bwaBuyPrice: '$72.50',
    regionalRetailAUD: '$135.65',
    regionalTradeAUD: '$127.85',
    metroRetailAUD: '$127.85'
  },
  {
    code: 'BB69038',
    description: 'JESS Towel Hook in Brushed Brass',
    styleCode: 'BWA E1 BB69038',
    rrp: '$45.00',
    bwaBuyPrice: '$22.50',
    regionalRetailAUD: '$42.15',
    regionalTradeAUD: '$39.65',
    metroRetailAUD: '$39.65'
  },
  {
    code: 'BB69039',
    description: 'JESS Robe Hook in Brushed Brass',
    styleCode: 'BWA E1 BB69039',
    rrp: '$52.00',
    bwaBuyPrice: '$26.00',
    regionalRetailAUD: '$48.70',
    regionalTradeAUD: '$45.85',
    metroRetailAUD: '$45.85'
  },
  {
    code: 'BB69040',
    description: 'JESS Shower Caddy Corner in Brushed Brass',
    styleCode: 'BWA E1 BB69040',
    rrp: '$165.00',
    bwaBuyPrice: '$82.50',
    regionalRetailAUD: '$154.45',
    regionalTradeAUD: '$145.35',
    metroRetailAUD: '$145.35'
  },
  {
    code: 'BB69041',
    description: 'JESS Glass Shelf 500mm in Brushed Brass',
    styleCode: 'BWA E1 BB69041',
    rrp: '$125.00',
    bwaBuyPrice: '$62.50',
    regionalRetailAUD: '$117.05',
    regionalTradeAUD: '$110.15',
    metroRetailAUD: '$110.15'
  },
  {
    code: 'BB69042',
    description: 'JESS Mirror Demister Pad 230x300mm',
    styleCode: 'BWA E1 BB69042',
    rrp: '$85.00',
    bwaBuyPrice: '$42.50',
    regionalRetailAUD: '$79.65',
    regionalTradeAUD: '$74.95',
    metroRetailAUD: '$74.95'
  }
];

const PageContainer = styled.div`
  padding: 1rem;
  width: 100%;
  max-width: 100%;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
`;

const PageHeader = styled.div`
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 1.5rem;
  flex-wrap: wrap;
  
  @media (max-width: 768px) {
    margin-bottom: 1rem;
  }
`;

const BackButton = styled.button`
  display: flex;
  align-items: center;
  background: none;
  border: none;
  padding: 8px;
  cursor: pointer;
  color: #042B41;
  border-radius: 4px;
  
  &:hover {
    background-color: #f3f4f6;
  }
  
  @media (max-width: 480px) {
    padding: 4px;
  }
`;

const BackIcon = styled.img`
  width: 24px;
  height: 24px;
  margin-right: 8px;
  
  @media (max-width: 480px) {
    width: 20px;
    height: 20px;
    margin-right: 4px;
  }
`;

const PageTitle = styled.h1`
  color: #042B41;
  font-size: clamp(24px, 4vw, 32px);
  font-weight: 800;
  margin: 0;
  flex-shrink: 0;
`;

const TableContainer = styled.div`
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e0e0e0;
  overflow: hidden;
  flex: 1;
  min-height: 400px;
`;

const ScrollableWrapper = styled.div`
  overflow-x: auto;
  overflow-y: visible;
  width: 100%;
  
  /* Custom scrollbar styling */
  &::-webkit-scrollbar {
    height: 8px;
  }
  
  &::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
  }
  
  &::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
  }
  
  &::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
  }
  
  /* Firefox scrollbar */
  scrollbar-width: thin;
  scrollbar-color: #c1c1c1 #f1f1f1;
`;

const Table = styled.table`
  width: 100%;
  min-width: 1400px; /* Increased to ensure proper horizontal scroll */
  border-collapse: collapse;
  font-size: 14px;
  
  @media (max-width: 768px) {
    font-size: 12px;
    min-width: 1200px;
  }
`;

const TableHeader = styled.thead`
  background-color: #1F4A5C;
  position: sticky;
  top: 0;
  z-index: 10;
`;

const TableHeaderRow = styled.tr`
  border: none;
`;

const TableHeaderCell = styled.th`
  padding: 16px 12px;
  text-align: center;
  font-weight: 700;
  color: white;
  border-right: 1px solid rgba(255, 255, 255, 0.2);
  white-space: nowrap;
  min-width: 140px;
  font-size: 13px;
  line-height: 1.2;
  
  &:last-child {
    border-right: none;
  }
  
  &:first-child {
    min-width: 120px;
  }
  
  &:nth-child(2) {
    min-width: 280px;
    text-align: left;
  }
  
  &:nth-child(3) {
    min-width: 160px;
  }
  
  @media (max-width: 768px) {
    padding: 12px 8px;
    font-size: 11px;
    min-width: 120px;
    
    &:nth-child(2) {
      min-width: 220px;
    }
  }
`;

const TableBody = styled.tbody``;

const TableRow = styled.tr`
  border-bottom: 1px solid #E5E7EB;
  
  &:hover {
    background-color: #F9FAFB;
  }
  
  &:last-child {
    border-bottom: none;
  }
`;

const TableCell = styled.td`
  padding: 16px 12px;
  text-align: center;
  color: #111827;
  border-right: 1px solid #E5E7EB;
  white-space: nowrap;
  
  &:last-child {
    border-right: none;
  }
  
  &:first-child {
    font-weight: 600;
    color: #042B41;
  }
  
  &:nth-child(2) {
    text-align: left;
    max-width: 280px;
    white-space: normal;
    word-wrap: break-word;
    line-height: 1.4;
  }
  
  &:nth-child(3) {
    font-size: 12px;
    color: #6B7280;
  }
  
  @media (max-width: 768px) {
    padding: 12px 8px;
    font-size: 12px;
    
    &:nth-child(2) {
      max-width: 220px;
    }
  }
`;

const PriceCell = styled(TableCell)`
  font-weight: 600;
  
  &.rrp {
    background-color: #FEF3F2;
    color: #B91C1C;
  }
  
  &.buy-price {
    background-color: #F0FDF4;
    color: #166534;
  }
  
  &.retail {
    background-color: #EFF6FF;
    color: #1D4ED8;
  }
  
  &.trade {
    background-color: #F3F4F6;
    color: #374151;
  }
`;

const ControlsContainer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 1rem;
  gap: 16px;
  flex-wrap: wrap;
  
  @media (max-width: 768px) {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
`;

const SearchContainer = styled.div`
  position: relative;
  flex-grow: 1;
  max-width: 400px;
  min-width: 200px;
  
  @media (max-width: 768px) {
    max-width: 100%;
    order: 1;
  }
`;

const SearchInput = styled.input`
  width: 100%;
  height: 44px;
  border-radius: 8px;
  border: 1px solid #E5E7EB;
  font-size: 14px;
  padding: 8px 12px 8px 2.5rem;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236B7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z'/%3e%3c/svg%3e");
  background-position: left 0.75rem center;
  background-repeat: no-repeat;
  background-size: 1rem 1rem;

  &:focus {
    outline: none;
    border-color: #3B82F6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  &::placeholder {
    color: #9CA3AF;
  }
  
  @media (max-width: 480px) {
    font-size: 16px; /* Prevents zoom on mobile */
  }
`;

const StatsContainer = styled.div`
  display: flex;
  gap: 16px;
  align-items: center;
  font-size: 14px;
  color: #6B7280;
  flex-wrap: wrap;
  
  @media (max-width: 768px) {
    order: 2;
    justify-content: space-between;
    width: 100%;
    gap: 12px;
  }
  
  @media (max-width: 480px) {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
`;

const StatItem = styled.div`
  display: flex;
  align-items: center;
  gap: 4px;
  white-space: nowrap;
  
  strong {
    color: #111827;
  }
  
  @media (max-width: 480px) {
    font-size: 13px;
  }
`;

const ExportButton = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  background-color: #F3F4F6;
  color: #374151;
  border: 1px solid #D1D5DB;
  border-radius: 8px;
  padding: 12px 16px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  white-space: nowrap;
  height: 44px;
  min-width: 120px;
  transition: all 0.2s ease;
  
  &:hover {
    background-color: #E5E7EB;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  
  &:active {
    transform: translateY(0);
  }
  
  svg {
    width: 16px;
    height: 16px;
    flex-shrink: 0;
  }
  
  @media (max-width: 768px) {
    width: 100%;
    justify-content: center;
  }
  
  @media (max-width: 480px) {
    padding: 10px 12px;
    font-size: 13px;
    height: 40px;
    min-width: 100px;
  }
`;

const EmptyState = styled.div`
  text-align: center;
  padding: 3rem 2rem;
  color: #6B7280;
  
  h3 {
    margin: 0 0 0.5rem 0;
    color: #374151;
    font-size: 18px;
    font-weight: 600;
  }
  
  p {
    margin: 0;
    font-size: 14px;
    line-height: 1.5;
  }
  
  @media (max-width: 480px) {
    padding: 2rem 1rem;
    
    h3 {
      font-size: 16px;
    }
    
    p {
      font-size: 13px;
    }
  }
`;

const LoadingContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 3rem 2rem;
  
  @media (max-width: 480px) {
    padding: 2rem 1rem;
  }
`;

const ScrollHint = styled.div`
  text-align: center;
  padding: 8px 16px;
  background-color: #F3F4F6;
  color: #6B7280;
  font-size: 12px;
  border-top: 1px solid #E5E7EB;
  
  @media (min-width: 1400px) {
    display: none;
  }
`;

const SuppliersDashboard: React.FC = () => {
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredData, setFilteredData] = useState(sampleData);

  useEffect(() => {
    if (searchQuery.trim() === '') {
      setFilteredData(sampleData);
    } else {
      const filtered = sampleData.filter(item =>
        item.code.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        item.styleCode.toLowerCase().includes(searchQuery.toLowerCase())
      );
      setFilteredData(filtered);
    }
  }, [searchQuery]);

  const handleBackClick = () => {
    navigate('/suppliers');
  };

  const handleExport = () => {
    // Create CSV content
    const headers = ['Code', 'Description', 'Style Code', 'RRP (ex GST)', 'BWA BUY PRICE 50% (ex GST)', 'Regional Retail AUD Incl', 'Regional Trade AUD Incl', 'Metro Retail AUD Incl'];
    const csvContent = [
      headers.join(','),
      ...filteredData.map(item => [
        item.code,
        `"${item.description}"`,
        item.styleCode,
        item.rrp,
        item.bwaBuyPrice,
        item.regionalRetailAUD,
        item.regionalTradeAUD,
        item.metroRetailAUD
      ].join(','))
    ].join('\n');

    // Create download link
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `suppliers-dashboard-${new Date().toISOString().split('T')[0]}.csv`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    window.URL.revokeObjectURL(url);
  };

  return (
    <Layout>
      <PageContainer>
        <PageHeader>
          <BackButton onClick={handleBackClick}>
            <BackIcon src={backButtonIcon} alt="Back" />
          </BackButton>
          <PageTitle>Suppliers Dashboard</PageTitle>
        </PageHeader>

        <ControlsContainer>
          <SearchContainer>
            <SearchInput
              type="text"
              placeholder="Search by code, description, or style..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </SearchContainer>
          <StatsContainer>
            <StatItem>
              Showing <strong>{filteredData.length}</strong> of <strong>{sampleData.length}</strong> products
            </StatItem>
            <ExportButton onClick={handleExport}>
              <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              Export CSV
            </ExportButton>
          </StatsContainer>
        </ControlsContainer>

        <TableContainer>
          <ScrollableWrapper>
            <Table>
              <TableHeader>
                <TableHeaderRow>
                  <TableHeaderCell>Code</TableHeaderCell>
                  <TableHeaderCell>Description</TableHeaderCell>
                  <TableHeaderCell>Style Code</TableHeaderCell>
                  <TableHeaderCell>
                    RRP<br />
                    (ex GST)
                  </TableHeaderCell>
                  <TableHeaderCell>
                    BWA BUY<br />
                    PRICE 50%<br />
                    (ex GST)
                  </TableHeaderCell>
                  <TableHeaderCell>
                    Regional<br />
                    Retail AUD<br />
                    Incl
                  </TableHeaderCell>
                  <TableHeaderCell>
                    Regional<br />
                    Trade AUD<br />
                    Incl
                  </TableHeaderCell>
                  <TableHeaderCell>
                    Metro<br />
                    Retail AUD<br />
                    Incl
                  </TableHeaderCell>
                </TableHeaderRow>
              </TableHeader>
              <TableBody>
                {filteredData.length > 0 ? (
                  filteredData.map((item, index) => (
                    <TableRow key={index}>
                      <TableCell>{item.code}</TableCell>
                      <TableCell>{item.description}</TableCell>
                      <TableCell>{item.styleCode}</TableCell>
                      <PriceCell className="rrp">{item.rrp}</PriceCell>
                      <PriceCell className="buy-price">{item.bwaBuyPrice}</PriceCell>
                      <PriceCell className="retail">{item.regionalRetailAUD}</PriceCell>
                      <PriceCell className="trade">{item.regionalTradeAUD}</PriceCell>
                      <PriceCell className="retail">{item.metroRetailAUD}</PriceCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={8}>
                      <EmptyState>
                        <h3>No products found</h3>
                        <p>Try adjusting your search criteria or clear the search to see all products.</p>
                      </EmptyState>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </ScrollableWrapper>
          <ScrollHint>
            💡 Scroll horizontally to view all columns
          </ScrollHint>
        </TableContainer>
      </PageContainer>
    </Layout>
  );
};

export default SuppliersDashboard; 