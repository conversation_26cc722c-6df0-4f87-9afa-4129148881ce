import React, { useState, useEffect, useCallback, useRef } from 'react';
import styled from 'styled-components';
import Layout from '../../layout/Layout';
import { Link, useNavigate, useLocation } from 'react-router-dom';
import Pagination from '../../common/Pagination';
import supplierService, { SupplierBase } from '../../../services/supplierService';
import supplierPriceTemplate from '../../../assets/supplier-price-template.csv';
import inventoryTemplate from '../../../assets/inventory-template.csv';
import backButtonIcon from '../../../assets/backButton.png';
import customerAddIcon from '../../../assets/customerAddIcon.png';
import { useToast } from '../../../hooks/useToast';
import { Toast } from '../../common/Toast';
import LoadingSpinner from '../../ui/LoadingSpinner';
import SearchBar from '../../common/SearchBar';

// Custom styled search bar wrapper for this page
const StyledSearchBar = styled(SearchBar)`
  width: 300px;
  flex-shrink: 0;
  flex-grow: 1;
  max-width: 500px;
  
  /* Target the input inside the SearchBar */
  & input {
    height: 36px;
    border-radius: 8px;
    border: 1px solid #E5E7EB;
    font-size: 14px;
    padding: 8px 12px 8px 2.5rem;
  }
  
  /* Ensure the search icon is properly positioned */
  & > div:first-child {
    left: 1rem;
  }
`;

// Replace the StyledSearchBar with a simple custom search input
const SearchInput = styled.input`
  width: 300px;
  flex-shrink: 0;
  flex-grow: 1;
  max-width: 500px;
  height: 36px;
  border-radius: 8px;
  border: 1px solid #E5E7EB;
  font-size: 14px;
  padding: 8px 12px 8px 2.5rem;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236B7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z'/%3e%3c/svg%3e");
  background-position: left 0.75rem center;
  background-repeat: no-repeat;
  background-size: 1rem 1rem;

  &:focus {
    outline: none;
    border-color: #3B82F6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  }

  &::placeholder {
    color: #9CA3AF;
  }
`;

const PageHeader = styled.div`
  padding: 0.5rem 1rem 0;
`;

const PageTitle = styled.h1`
  color: #042B41;
  font-size: 32px;
  font-weight: 800;
  margin: 0;
`;

const PageContainer = styled.div`
  padding: 0 1rem 0.5rem;
  width: 100%;
  max-width: 100%;
`;

const SearchAddContainer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
  width: 100%;
  gap: 12px;
`;

const SearchIconWrapper = styled.div`
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  color: #9CA3AF;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: none;
`;

const TitleContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
`;

const StyledBackButton = styled.button`
  display: flex;
  align-items: center;
  background: none;
  border: none;
  padding: 0;
  cursor: pointer;
  color: #042B41;
`;

const BackIcon = styled.img`
  width: 24px;
  height: 24px;
  margin-right: 8px;
`;

const HeaderContainer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
`;

const AddSupplierButton = styled.button`
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  background-color: #042B41;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 8px 16px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  white-space: nowrap;
  height: 36px;
  min-width: 120px;
  
  &:hover {
    background-color: #0A3D5A;
  }
  
  img {
    width: 16px;
    height: 16px;
  }
`;

const GridContainer = styled.div`
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  border: 1px solid #e0e0e0;
  
  /* Add table-like borders to all cells */
  & > div:not(:first-child) {
    border-top: 1px solid #E5E7EB;
  }
`;

const GridHeader = styled.div`
  display: grid;
  grid-template-columns: 1.5fr 1fr 1fr 1fr 1fr;
  background-color: #042B41;
  color: white;
  border-bottom: 1px solid #E5E7EB;

  div {
    padding: 1rem;
    font-weight: 700;
    font-size: 14px;
    border-right: 1px solid rgba(255, 255, 255, 0.2);
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;

    &:last-child {
      border-right: none;
    }
  }
`;

const GridRow = styled.div`
  display: grid;
  grid-template-columns: 1.5fr 1fr 1fr 1fr 1fr;
  border-bottom: 1px solid #E5E7EB;

  &:last-child {
    border-bottom: none;
  }

  &:hover {
    background-color: #F9FAFB;
  }

  div {
    padding: 1rem;
    font-size: 14px;
    color: #111827;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    border-right: 1px solid #E5E7EB;
    border-bottom: 1px solid #E5E7EB;

    &:last-child {
      border-right: none;
    }
  }
`;

const SupplierNameLink = styled(Link)`
  color: #0056b3;
  text-decoration: underline;
  font-weight: 500;
  cursor: pointer;
  
  &:hover {
    color: #003d7a;
    text-decoration: underline;
  }
`;

interface Supplier {
  id: number;
  name: string;
  phone: string;
  email: string;
  address: string;
  priceList: string;
}

// Add Supplier Modal Styled Components
const ModalOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
`;

const ModalContent = styled.div`
  background-color: white;
  border-radius: 8px;
  width: 100%;
  max-width: 600px;
  padding: 2rem;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
`;

const ModalHeader = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
`;

const ModalTitle = styled.h2`
  font-size: 1.5rem;
  color: #111827;
  margin: 0;
  font-weight: 600;
`;

const TemplateLink = styled.a`
  color: #2563EB;
  font-size: 0.875rem;
  text-decoration: none;
  display: flex;
  align-items: center;

  &:hover {
    text-decoration: underline;
  }

  svg {
    margin-right: 0.5rem;
  }
`;

const FormGroup = styled.div`
  margin-bottom: 1.5rem;
`;

const FormLabel = styled.label<{ required?: boolean }>`
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: #111827;
  margin-bottom: 0.5rem;

  &::after {
    content: ${props => props.required ? '"*"' : '""'};
    color: #EF4444;
    margin-left: 2px;
  }
`;

// Add new styled component for form labels with template links
const FormLabelWithTemplate = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
`;

const FormLabelText = styled.label<{ required?: boolean }>`
  font-size: 0.875rem;
  font-weight: 500;
  color: #111827;

  &::after {
    content: ${props => props.required ? '"*"' : '""'};
    color: #EF4444;
    margin-left: 2px;
  }
`;

const FormInput = styled.input`
  width: 100%;
  padding: 0.625rem;
  border: 1px solid #D1D5DB;
  border-radius: 0.375rem;
  font-size: 0.875rem;

  &:focus {
    outline: none;
    border-color: #2563EB;
    box-shadow: 0 0 0 2px rgba(37, 99, 235, 0.1);
  }
`;

const FileUploadContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 0.625rem;
`;

const FileInputLabel = styled.label`
  flex: 1;
  border: 1px solid #D1D5DB;
  border-radius: 0.375rem;
  padding: 0.625rem;
  font-size: 0.875rem;
  color: #6B7280;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: pointer;
  background-color: #F9FAFB;
`;

const HiddenFileInput = styled.input`
  display: none;
`;

const UploadIcon = styled.span`
  color: #6B7280;
  display: inline-flex;
  align-items: center;
  justify-content: center;
`;

const ButtonContainer = styled.div`
  display: flex;
  justify-content: space-between;
  gap: 1rem;
  margin-top: 1.5rem;
`;

const CancelButton = styled.button`
  flex: 1;
  padding: 0.75rem;
  border: 1px solid #D1D5DB;
  border-radius: 0.375rem;
  background-color: white;
  color: #111827;
  font-weight: 500;
  font-size: 0.875rem;
  cursor: pointer;

  &:hover {
    background-color: #F9FAFB;
  }
`;

const SubmitButton = styled.button`
  flex: 1;
  padding: 0.75rem;
  border: none;
  border-radius: 0.375rem;
  background-color: #042B41;
  color: white;
  font-weight: 500;
  font-size: 0.875rem;
  cursor: pointer;

  &:hover {
    background-color: #031F30;
  }

  &:disabled {
    opacity: 0.7;
    cursor: not-allowed;
  }
`;

const ErrorMessage = styled.div`
  color: #EF4444;
  background-color: #FEE2E2;
  border: 1px solid #FCA5A5;
  padding: 0.75rem;
  border-radius: 0.375rem;
  margin-bottom: 1rem;
`;

const SuccessMessage = styled.div`
  color: #059669;
  background-color: #D1FAE5;
  border: 1px solid #6EE7B7;
  padding: 0.75rem;
  border-radius: 0.375rem;
  margin-bottom: 1rem;
`;

// Remove skeleton components and add consistent loading components
const LoadingRow = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  border-bottom: 1px solid #E5E7EB;
`;

const LoadingContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 12px;
`;

const LoadingText = styled.p`
  margin: 0;
  font-size: 14px;
  color: #6B7280;
  font-weight: 500;
`;

const MessageRow = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  border-bottom: 1px solid #E5E7EB;
  color: #6B7280;
  font-size: 14px;
  text-align: center;
`;

const SuppliersPage: React.FC = () => {
  const navigate = useNavigate();
  const toast = useToast();
  const location = useLocation();

  // Initialize state from URL parameters
  const urlParams = new URLSearchParams(location.search);
  const initialPage = parseInt(urlParams.get('page') || '1', 10);
  const initialLimit = parseInt(urlParams.get('limit') || '10', 10);
  const initialSearch = urlParams.get('search') || '';

  const [searchQuery, setSearchQuery] = useState(initialSearch);
  const [currentPage, setCurrentPage] = useState(initialPage);
  const [itemsPerPage, setItemsPerPage] = useState(initialLimit);

  // Add Supplier Modal State
  const [showAddModal, setShowAddModal] = useState(false);
  const [newSupplier, setNewSupplier] = useState({
    name: '',
    phone: '',
    email: '',
    address: '',
    priceList: ''
  });
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [processedPriceList, setProcessedPriceList] = useState<any[]>([]);
  const [selectedInventoryFile, setSelectedInventoryFile] = useState<File | null>(null);
  const [processedInventoryList, setProcessedInventoryList] = useState<any[]>([]);

  // API state
  const [suppliers, setSuppliers] = useState<Supplier[]>([]);
  const [loading, setLoading] = useState(false);
  const [dataLoaded, setDataLoaded] = useState(false);
  // Add lastFetchTime to track when the data was last fetched
  const [lastFetchTime, setLastFetchTime] = useState<number>(0);

  // Add totalItems state to track total items from API response
  const [totalItems, setTotalItems] = useState(0);

  // Add a ref to track API calls in progress
  const apiCallInProgress = useRef<boolean>(false);

  // Add a ref to track if this is the initial load
  const initialLoad = useRef<boolean>(true);

  // Add a ref to track user-initiated search vs. programmatic search updates
  const userSearchAction = useRef<boolean>(false);

  // Function to update URL with current pagination state
  const updateURLParams = useCallback((page: number, limit: number, search: string) => {
    const params = new URLSearchParams();
    if (page > 1) params.set('page', page.toString());
    if (limit !== 10) params.set('limit', limit.toString());
    if (search) params.set('search', search);

    const newURL = params.toString() ? `/suppliers?${params.toString()}` : '/suppliers';
    navigate(newURL, { replace: true });
  }, [navigate]);

  // Natural sorting function for supplier names
  const naturalSort = (suppliers: Supplier[]): Supplier[] => {
    return suppliers.sort((a, b) => {
      const nameA = a.name.toLowerCase();
      const nameB = b.name.toLowerCase();

      // Check if both names follow "supplier X" pattern
      const supplierRegex = /^supplier\s+(\d+)$/;
      const matchA = nameA.match(supplierRegex);
      const matchB = nameB.match(supplierRegex);

      // If both are "supplier X" format, sort numerically
      if (matchA && matchB) {
        const numA = parseInt(matchA[1]);
        const numB = parseInt(matchB[1]);
        return numA - numB;
      }

      // If only A is "supplier X" format, A comes first
      if (matchA && !matchB) {
        return -1;
      }

      // If only B is "supplier X" format, B comes first
      if (!matchA && matchB) {
        return 1;
      }

      // If neither follows the pattern, sort alphabetically
      return nameA.localeCompare(nameB);
    });
  };

  // Function to fetch suppliers from API - memoized with useCallback
  const fetchSuppliers = useCallback(async () => {
    // Skip if an API call is already in progress
    if (apiCallInProgress.current) {
      return;
    }

    apiCallInProgress.current = true;
    setLoading(true);
    try {
      // Build search parameters - ensure search query is properly formatted
      const searchParams = {
        skip: (currentPage - 1) * itemsPerPage,
        limit: itemsPerPage,
        ...(searchQuery.trim() && { search: searchQuery.trim() })
      };

      // Use 'any' type to avoid type errors with the API client
      const response: any = await supplierService.getSuppliers(searchParams);

      // Ensure data exists before mapping
      if (!response.data || !Array.isArray(response.data)) {
        setSuppliers([]);
        setDataLoaded(true);
        return;
      }

      // Convert API supplier model to component supplier model
      const mappedSuppliers: Supplier[] = response.data.map((supplier: any) => ({
        id: supplier.id,
        name: supplier.supplier_name || supplier.name || '',
        phone: supplier.phone_no || supplier.phone || '',
        email: supplier.email,
        address: supplier.address,
        priceList: supplier.price_list?.length ? 'Available' : 'Not available'
      }));

      setSuppliers(naturalSort(mappedSuppliers));
      // Set total count from API response if available
      setTotalItems(response.total || mappedSuppliers.length);
      setDataLoaded(true);
      setLastFetchTime(Date.now());
    } catch (err) {
      console.error('Error fetching suppliers:', err);
      // Don't set error messages to display to users

      // Keep the existing data if available, but still mark as loaded
      setSuppliers(prevSuppliers => prevSuppliers.length > 0 ? prevSuppliers : []);
      setDataLoaded(true);
    } finally {
      setLoading(false);
      apiCallInProgress.current = false;
    }
  }, [currentPage, itemsPerPage, searchQuery]);

  // Fetch suppliers on component mount and when dependencies change
  useEffect(() => {
    // If an API call is already in progress, don't start another one
    if (apiCallInProgress.current) return;

    // Use requestAnimationFrame to avoid issues with React 18 strict mode
    const rafId = requestAnimationFrame(() => {
      fetchSuppliers();
    });

    return () => cancelAnimationFrame(rafId);
  }, [fetchSuppliers]);

  // Simple search effect that only resets page for user-initiated searches
  useEffect(() => {
    // Skip on initial load
    if (initialLoad.current) {
      initialLoad.current = false;
      console.log('SuppliersPage: Initial load, not triggering search reset');
      return;
    }

    // Only reset page if this was a user-initiated search action
    if (userSearchAction.current) {
      console.log('SuppliersPage: User search detected, resetting to page 1');
      if (currentPage !== 1) {
        setCurrentPage(1);
      }
      userSearchAction.current = false; // Reset the flag
    }

    // Always update URL to keep it in sync (after a short delay)
    const timer = setTimeout(() => {
      updateURLParams(currentPage, itemsPerPage, searchQuery);
    }, 100);

    return () => clearTimeout(timer);
  }, [searchQuery]); // Only trigger on search query changes

  // Calculate pagination - use total from API instead of local length
  const totalPages = Math.ceil(totalItems / itemsPerPage);

  const handlePageChange = (pageNumber: number) => {
    setCurrentPage(pageNumber);
    updateURLParams(pageNumber, itemsPerPage, searchQuery);
  };

  const handleItemsPerPageChange = (newItemsPerPage: number) => {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1); // Reset to first page when changing items per page
    updateURLParams(1, newItemsPerPage, searchQuery);
  };

  const handleBack = () => {
    navigate('/home');
  };

  const handleSupplierClick = (supplierId: number) => {
    // Preserve current pagination state in the URL
    const currentParams = new URLSearchParams();
    currentParams.set('page', currentPage.toString());
    currentParams.set('limit', itemsPerPage.toString());
    if (searchQuery) {
      currentParams.set('search', searchQuery);
    }

    navigate(`/suppliers/${supplierId}?${currentParams.toString()}`);
  };

  const handleSearchChange = (newSearchQuery: string) => {
    userSearchAction.current = true; // Mark this as a user-initiated search
    setSearchQuery(newSearchQuery);
  };

  const handleAddSupplierClick = () => {
    setShowAddModal(true);
  };

  const handleCloseModal = () => {
    setShowAddModal(false);
    setNewSupplier({
      name: '',
      phone: '',
      email: '',
      address: '',
      priceList: ''
    });
    setSelectedFile(null);
    setProcessedPriceList([]);
    setSelectedInventoryFile(null);
    setProcessedInventoryList([]);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setNewSupplier(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];

    if (file) {
      setSelectedFile(file);

      // Process CSV file
      const reader = new FileReader();
      reader.onload = (event) => {
        try {
          const csvData = event.target?.result as string;

          const lines = csvData.split('\n');

          if (lines.length === 0) {
            toast.showToast('Price list file appears to be empty.', { type: 'error' });
            setSelectedFile(null);
            setProcessedPriceList([]);
            return;
          }

          const headers = lines[0].split(',').map(h => h.trim());

          // Validate required columns for price list
          const requiredColumns = ['SKU CODE', 'PRODUCT DESCRIPTION', 'STYLE CODE', 'RRP (EX GST)', 'BWA BUY PRICE -50% (EX GST)'];
          const missingColumns = requiredColumns.filter(col => !headers.includes(col));

          if (missingColumns.length > 0) {
            toast.showToast(`Invalid price list file. Missing required columns: ${missingColumns.join(', ')}`, { type: 'error' });
            setSelectedFile(null);
            setProcessedPriceList([]);
            return;
          }

          const processedData = lines.slice(1)
            .filter(line => line.trim())
            .map((line, index) => {
              const values = line.split(',').map(v => v.trim());
              const item: any = {};
              headers.forEach((header, headerIndex) => {
                item[header] = values[headerIndex] || '';
              });
              return item;
            });

          // Validate that we have at least one valid row
          if (processedData.length === 0) {
            toast.showToast('Price list file is empty or contains no valid data rows.', { type: 'error' });
            setSelectedFile(null);
            setProcessedPriceList([]);
            return;
          }

          setProcessedPriceList(processedData);
          toast.showToast(`Price list file processed successfully. ${processedData.length} items found.`, { type: 'success' });
        } catch (error) {
          console.error('Error processing price list file:', error);
          const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
          toast.showToast(`Error processing price list file: ${errorMessage}`, { type: 'error' });
          setSelectedFile(null);
          setProcessedPriceList([]);
        }
      };

      reader.onerror = (error) => {
        console.error('FileReader error for price list:', error);
        toast.showToast('Error reading price list file. Please try again.', { type: 'error' });
        setSelectedFile(null);
        setProcessedPriceList([]);
      };

      reader.readAsText(file);
    }

    // Reset the input value to allow selecting the same file again
    e.target.value = '';
  };

  const handleInventoryFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];

    if (file) {
      setSelectedInventoryFile(file);

      // Process CSV file
      const reader = new FileReader();
      reader.onload = (event) => {
        try {
          const csvData = event.target?.result as string;

          const lines = csvData.split('\n');

          if (lines.length === 0) {
            toast.showToast('Inventory file appears to be empty.', { type: 'error' });
            setSelectedInventoryFile(null);
            setProcessedInventoryList([]);
            return;
          }

          const headers = lines[0].split(',').map(h => h.trim());

          // Validate required columns
          const requiredColumns = ['SKU CODE', 'STYLE CODE'];
          const missingColumns = requiredColumns.filter(col => !headers.includes(col));

          if (missingColumns.length > 0) {
            toast.showToast(`Invalid inventory file. Missing required columns: ${missingColumns.join(', ')}`, { type: 'error' });
            setSelectedInventoryFile(null);
            setProcessedInventoryList([]);
            return;
          }

          const processedData = lines.slice(1)
            .filter(line => line.trim())
            .map((line, index) => {
              const values = line.split(',').map(v => v.trim());
              const item: any = {};
              headers.forEach((header, headerIndex) => {
                item[header] = values[headerIndex] || '';
              });
              return item;
            });

          // Validate that we have at least one valid row
          if (processedData.length === 0) {
            toast.showToast('Inventory file is empty or contains no valid data rows.', { type: 'error' });
            setSelectedInventoryFile(null);
            setProcessedInventoryList([]);
            return;
          }

          setProcessedInventoryList(processedData);
          toast.showToast(`Inventory file processed successfully. ${processedData.length} items found.`, { type: 'success' });
        } catch (error) {
          console.error('Error processing inventory file:', error);
          const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
          toast.showToast(`Error processing inventory file: ${errorMessage}`, { type: 'error' });
          setSelectedInventoryFile(null);
          setProcessedInventoryList([]);
        }
      };

      reader.onerror = (error) => {
        console.error('FileReader error for inventory:', error);
        toast.showToast('Error reading inventory file. Please try again.', { type: 'error' });
        setSelectedInventoryFile(null);
        setProcessedInventoryList([]);
      };

      reader.readAsText(file);
    }

    // Reset the input value to allow selecting the same file again
    e.target.value = '';
  };

  const handleSubmit = async () => {
    // Create a new supplier using the API
    try {
      setLoading(true);

      // Validate required fields
      if (!newSupplier.name || !newSupplier.phone || !selectedFile || !selectedInventoryFile) {
        throw new Error('Please fill all required fields including price list and inventory file');
      }

      // Cross-validation: Check SKU codes between inventory and price list
      const inventorySkuCodes = processedInventoryList.map(item => item['SKU CODE']).filter(Boolean);
      const priceListSkuCodes = processedPriceList.map(item => item['SKU CODE']).filter(Boolean);

      // Find matching and non-matching SKU codes
      const matchingSkuCodes = inventorySkuCodes.filter(sku => priceListSkuCodes.includes(sku));
      const inventoryOnlySkuCodes = inventorySkuCodes.filter(sku => !priceListSkuCodes.includes(sku));
      const priceListOnlySkuCodes = priceListSkuCodes.filter(sku => !inventorySkuCodes.includes(sku));

      // Filter processed data to only include matching SKU codes
      const validatedInventoryList = processedInventoryList.filter(item =>
        matchingSkuCodes.includes(item['SKU CODE'])
      );
      const validatedPriceList = processedPriceList.filter(item =>
        matchingSkuCodes.includes(item['SKU CODE'])
      );

      // Show validation results
      if (matchingSkuCodes.length === 0) {
        throw new Error('No matching SKU codes found between inventory and price list files. Please check your data.');
      }

      let validationMessage = `${matchingSkuCodes.length} SKU codes matched successfully.`;
      if (inventoryOnlySkuCodes.length > 0 || priceListOnlySkuCodes.length > 0) {
        const errorDetails = [];
        if (inventoryOnlySkuCodes.length > 0) {
          errorDetails.push(`${inventoryOnlySkuCodes.length} SKU codes in inventory not found in price list`);
        }
        if (priceListOnlySkuCodes.length > 0) {
          errorDetails.push(`${priceListOnlySkuCodes.length} SKU codes in price list not found in inventory`);
        }
        validationMessage += ` ${errorDetails.join(', ')}.`;
      }

      // Create supplier data object in API format
      const supplierData: SupplierBase = {
        supplier_name: newSupplier.name, // Will be mapped to 'name' in the backend
        phone_no: newSupplier.phone.replace(/[^\d+]/g, ''), // Remove all characters except digits and +
        email: newSupplier.email,
        address: newSupplier.address,
        // Map CSV columns to backend expected field names - use only validated price list
        price_list: validatedPriceList?.length ? validatedPriceList.map((item, index) => ({
          sku: {
            id: index + 1,
            code: item['SKU CODE'] || ''
          },
          description: item['PRODUCT DESCRIPTION'] || '',
          style_code: item['STYLE CODE'] || '',
          rrp_ex_gst: parseFloat(item['RRP (EX GST)']) || 0,
          bwa_buy_price_50_ex_gst: parseFloat(item['BWA BUY PRICE -50% (EX GST)']) || 0
        })) : []
      };

      const createdSupplier = await supplierService.createSupplier(supplierData);

      // Process only validated inventory items
      if (selectedInventoryFile && validatedInventoryList.length > 0) {
        try {
          // Create inventory items for each validated row in the CSV
          const inventoryPromises = validatedInventoryList.map(async (item) => {
            try {
              // Map CSV columns to inventory fields according to backend schema
              const inventoryData = {
                sku_code: item['SKU CODE'] || '',
                style_code: item['STYLE CODE'] || '',
                supplier_id: createdSupplier.id,
                carton: parseInt(item['CARTON QTY']) || 1,
                units_per_carton: parseInt(item['UNITS PER CARTON']) || 1,
                carton_dimensions: item['CARTON DIMENSIONS'] || '',
                weight_per_unit: parseFloat(item['WEIGHT PER UNIT']) || 1.0,
                weight_per_carton: parseFloat(item['WEIGHT PER CARTON']) || 1.0,
                units_per_pallet: item['UNITS PER PALLET'] ? parseInt(item['UNITS PER PALLET']) : null,
                pallet_weight: item['PALLET WEIGHT'] ? parseFloat(item['PALLET WEIGHT']) : null,
                notes: item['PRODUCT DESCRIPTION'] || ''
              };

              // Use fetch directly to call the backend API since the frontend service expects different fields
              const token = localStorage.getItem('authToken');
              const response = await fetch('http://localhost:8000/api/v1/inventory/', {
                method: 'POST',
                headers: {
                  'Content-Type': 'application/json',
                  'Authorization': `Bearer ${token}`
                },
                body: JSON.stringify(inventoryData)
              });

              if (!response.ok) {
                throw new Error(`Failed to create inventory item: ${response.statusText}`);
              }

              return await response.json();
            } catch (error) {
              console.error('Error creating inventory item:', error);
              return null;
            }
          });

          const inventoryResults = await Promise.allSettled(inventoryPromises);
          const successCount = inventoryResults.filter(result => result.status === 'fulfilled' && result.value).length;
          const errorCount = inventoryResults.filter(result => result.status === 'rejected').length;

          // Show comprehensive success message
          let successMessage = `Supplier created successfully! ${successCount} inventory items added.`;
          if (inventoryOnlySkuCodes.length > 0 || priceListOnlySkuCodes.length > 0) {
            successMessage += ` Note: ${inventoryOnlySkuCodes.length + priceListOnlySkuCodes.length} items were skipped due to mismatched SKU codes.`;
          }

          if (successCount > 0) {
            toast.showToast(successMessage, { type: 'success' });
          } else if (errorCount > 0) {
            toast.showToast(`Supplier created successfully, but failed to add ${errorCount} inventory items.`, { type: 'warning' });
          }
        } catch (error) {
          console.error('Error processing inventory file:', error);
          const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
          toast.showToast(`Error processing inventory file: ${errorMessage}`, { type: 'error' });
          setSelectedInventoryFile(null);
          setProcessedInventoryList([]);
        }
      } else {
        // Since inventory is now required, this should not happen due to validation
        // But handle the edge case where file is selected but processing failed
        toast.showToast('Supplier created successfully, but no matching inventory items found.', { type: 'warning' });
      }

      // Map the created supplier to our component format
      const newSupplierMapped: Supplier = {
        id: createdSupplier.id,
        name: createdSupplier.supplier_name || createdSupplier.name || '',
        phone: createdSupplier.phone_no || createdSupplier.phone || '',
        email: createdSupplier.email,
        address: createdSupplier.address,
        priceList: createdSupplier.price_list?.length ? 'Available' : 'Not available'
      };

      // Add to state
      setSuppliers(naturalSort([...suppliers, newSupplierMapped]));

      // Close modal and reset form
      handleCloseModal();

      // Refresh the supplier list
      fetchSuppliers();

    } catch (err) {
      console.error('Error creating supplier:', err);
      toast.showToast('Failed to create supplier. Please try again.', { type: 'error' });
    } finally {
      setLoading(false);
    }
  };

  const downloadTemplate = () => {
    const link = document.createElement('a');
    link.href = supplierPriceTemplate;
    link.download = 'supplier-price-template.csv';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const downloadInventoryTemplate = () => {
    const link = document.createElement('a');
    link.href = inventoryTemplate;
    link.download = 'inventory-template.csv';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  // Helper function to render empty state
  const renderEmptyState = () => {
    return (
      <MessageRow>
        No Suppliers  found. Add a new Supplier  to get started.
      </MessageRow>
    );
  };

  // Render grid content with consistent loading pattern
  const renderGridContent = () => {
    if (loading) {
      return (
        <LoadingRow>
          <LoadingContainer>
            <LoadingSpinner size="lg" />
            <LoadingText>Loading suppliers...</LoadingText>
          </LoadingContainer>
        </LoadingRow>
      );
    }

    if (suppliers.length === 0) {
      return renderEmptyState();
    }

    return suppliers.map(supplier => (
      <GridRow key={supplier.id}>
        <div>
          <SupplierNameLink to="#" onClick={(e) => {
            e.preventDefault();
            handleSupplierClick(supplier.id);
          }}>
            {supplier.name}
          </SupplierNameLink>
        </div>
        <div>{supplier.phone}</div>
        <div>{supplier.email}</div>
        <div>{supplier.address}</div>
        <div>{supplier.priceList}</div>
      </GridRow>
    ));
  };

  return (
    <Layout>
      {/* Toast notification */}
      {toast.isVisible && (
        <Toast
          message={toast.message}
          type={toast.type}
          onClose={() => { }}
        />
      )}

      <PageHeader>
        <HeaderContainer>
          <TitleContainer>
            <StyledBackButton onClick={handleBack}>
              <BackIcon src={backButtonIcon} alt="Back" />
            </StyledBackButton>
            <PageTitle>Suppliers</PageTitle>
          </TitleContainer>
        </HeaderContainer>
      </PageHeader>

      <PageContainer>
        <SearchAddContainer>
          <StyledSearchBar
            value={searchQuery}
            onChange={handleSearchChange}
            placeholder="Search a supplier"
            autoApplyUrlParams={false}
          />
          <div style={{ display: 'flex', gap: '12px' }}>
            <AddSupplierButton onClick={() => navigate('/suppliers-dashboard')}>
              📊 Dashboard
            </AddSupplierButton>
            <AddSupplierButton onClick={handleAddSupplierClick}>
              <img src={customerAddIcon} alt="Add" />
              Add Supplier
            </AddSupplierButton>
          </div>
        </SearchAddContainer>

        <GridContainer>
          <GridHeader>
            <div>Supplier Name</div>
            <div>Phone No.</div>
            <div>Email</div>
            <div>Address</div>
            <div>Price List</div>
          </GridHeader>

          {renderGridContent()}
        </GridContainer>

        {!loading && suppliers.length > 0 && (
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={handlePageChange}
            totalItems={totalItems}
            itemsPerPage={itemsPerPage}
            onItemsPerPageChange={handleItemsPerPageChange}
            itemsPerPageOptions={[5, 10, 25, 50]}
            showItemsPerPage={true}
          />
        )}

        {/* Add Supplier Modal */}
        {showAddModal && (
          <ModalOverlay onClick={handleCloseModal}>
            <ModalContent onClick={(e) => e.stopPropagation()}>
              <ModalHeader>
                <ModalTitle>Add Supplier</ModalTitle>
              </ModalHeader>

              <FormGroup>
                <FormLabel required>Supplier Company Name</FormLabel>
                <FormInput
                  type="text"
                  name="name"
                  value={newSupplier.name}
                  onChange={handleInputChange}
                  required
                />
              </FormGroup>

              <FormGroup>
                <FormLabel>Address</FormLabel>
                <FormInput
                  type="text"
                  name="address"
                  value={newSupplier.address}
                  onChange={handleInputChange}
                />
              </FormGroup>

              <FormGroup>
                <FormLabel required>Phone No</FormLabel>
                <FormInput
                  type="tel"
                  name="phone"
                  value={newSupplier.phone}
                  onChange={handleInputChange}
                  required
                />
              </FormGroup>

              <FormGroup>
                <FormLabel>Email ID</FormLabel>
                <FormInput
                  type="email"
                  name="email"
                  value={newSupplier.email}
                  onChange={handleInputChange}
                />
              </FormGroup>

              <FormGroup>
                <FormLabelWithTemplate>
                  <FormLabelText required>Inventory</FormLabelText>
                  <TemplateLink href="#" onClick={(e) => {
                    e.preventDefault();
                    downloadInventoryTemplate();
                  }}>
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M8 2V11M8 11L4.5 7.5M8 11L11.5 7.5M2 13H14" stroke="#2563EB" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                    </svg>
                    InventoryTemplate.csv
                  </TemplateLink>
                </FormLabelWithTemplate>
                <FileUploadContainer>
                  <FileInputLabel style={{ border: '1px solid #D1D5DB' }}>
                    <HiddenFileInput
                      type="file"
                      onChange={handleInventoryFileChange}
                      accept=".csv"
                      required
                    />
                    {selectedInventoryFile ? selectedInventoryFile.name : 'Choose CSV File'}
                  </FileInputLabel>
                  <UploadIcon>
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M3 16.5V18.75C3 19.3467 3.23705 19.919 3.65901 20.341C4.08097 20.7629 4.65326 21 5.25 21H18.75C19.3467 21 19.919 20.7629 20.341 20.341C20.7629 19.919 21 19.3467 21 18.75V16.5M16.5 12L12 7.5M12 7.5L7.5 12M12 7.5V16.5" stroke="#6B7280" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                    </svg>
                  </UploadIcon>
                </FileUploadContainer>
              </FormGroup>

              <FormGroup>
                <FormLabelWithTemplate>
                  <FormLabelText required>Price List</FormLabelText>
                  <TemplateLink href="#" onClick={(e) => {
                    e.preventDefault();
                    downloadTemplate();
                  }}>
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M8 2V11M8 11L4.5 7.5M8 11L11.5 7.5M2 13H14" stroke="#2563EB" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                    </svg>
                    PriceTemplate.csv
                  </TemplateLink>
                </FormLabelWithTemplate>
                <FileUploadContainer>
                  <FileInputLabel style={{ border: '1px solid #D1D5DB' }}>
                    <HiddenFileInput
                      type="file"
                      onChange={handleFileChange}
                      accept=".csv,.xlsx,.xls"
                      required
                    />
                    {selectedFile ? selectedFile.name : 'Choose CSV File'}
                  </FileInputLabel>
                  <UploadIcon>
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                      <path d="M3 16.5V18.75C3 19.3467 3.23705 19.919 3.65901 20.341C4.08097 20.7629 4.65326 21 5.25 21H18.75C19.3467 21 19.919 20.7629 20.341 20.341C20.7629 19.919 21 19.3467 21 18.75V16.5M16.5 12L12 7.5M12 7.5L7.5 12M12 7.5V16.5" stroke="#6B7280" strokeWidth="1.5" strokeLinecap="round" strokeLinejoin="round" />
                    </svg>
                  </UploadIcon>
                </FileUploadContainer>
              </FormGroup>

              <ButtonContainer>
                <CancelButton onClick={handleCloseModal}>
                  Cancel
                </CancelButton>
                <SubmitButton
                  onClick={handleSubmit}
                  disabled={!newSupplier.name || !newSupplier.phone || !selectedFile || !selectedInventoryFile}
                >
                  Submit
                </SubmitButton>
              </ButtonContainer>
            </ModalContent>
          </ModalOverlay>
        )}
      </PageContainer>
    </Layout>
  );
};

export default SuppliersPage;