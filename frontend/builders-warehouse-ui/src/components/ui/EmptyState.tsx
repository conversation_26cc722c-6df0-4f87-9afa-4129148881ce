import React from 'react';
import styled from 'styled-components';

interface EmptyStateProps {
  title: string;
  description?: string;
  icon?: React.ReactNode;
  action?: React.ReactNode;
  actionText?: string;
  onAction?: () => void;
}

const EmptyStateContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;
`;

const IconContainer = styled.div`
  margin-bottom: 1rem;
  color: #9CA3AF;
  
  svg {
    width: 48px;
    height: 48px;
  }
`;

const Title = styled.h3`
  font-size: 1.25rem;
  font-weight: 600;
  color: #111827;
  margin: 0 0 0.5rem;
`;

const Description = styled.p`
  color: #6B7280;
  margin: 0 0 1.5rem;
  max-width: 400px;
`;

const ActionButton = styled.button`
  background-color: #042B41;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;

  &:hover {
    background-color: #0B4D77;
  }
`;

const ActionContainer = styled.div`
  margin-top: 1.5rem;
`;

const EmptyState: React.FC<EmptyStateProps> = ({
  title,
  description,
  icon,
  action,
  actionText,
  onAction
}) => {
  return (
    <EmptyStateContainer>
      {icon && <IconContainer>{icon}</IconContainer>}
      <Title>{title}</Title>
      {description && <Description>{description}</Description>}
      {actionText && onAction && (
        <ActionContainer>
          <ActionButton onClick={onAction}>{actionText}</ActionButton>
        </ActionContainer>
      )}
      {action && <ActionContainer>{action}</ActionContainer>}
    </EmptyStateContainer>
  );
};

export default EmptyState; 