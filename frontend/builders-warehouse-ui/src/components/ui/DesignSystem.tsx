import React from 'react';
import styled from 'styled-components';
import { Link } from 'react-router-dom';

// 1. Navigation Elements

// Standard Back Arrow
export const BackArrow = styled.div`
  width: 24px;
  height: 16px;
  position: absolute;
  top: 8px;
  left: 4px;
  color: #042B41;
  transition: none;
  cursor: pointer;
  
  svg {
    width: 100%;
    height: 100%;
  }
`;

// Large Back Arrow
export const BackArrowLarge = styled.div`
  width: 32px;
  height: 32px;
  position: absolute;
  top: 114.13px;
  left: 31px;
  color: #042B41;
  cursor: pointer;
  
  svg {
    width: 100%;
    height: 100%;
  }
`;

// Back Text
export const BackText = styled.span`
  width: 39px;
  height: 25px;
  position: absolute;
  top: 117.63px;
  left: 73.21px;
  font-family: 'Avenir', sans-serif;
  font-weight: 500;
  font-size: 18px;
  line-height: 100%;
  letter-spacing: 0;
  color: #304C57;
`;

// Back Navigation Group (combines arrow and text)
export const BackNavigation = styled.div`
  display: flex;
  align-items: center;
  position: relative;
  margin-bottom: 40px;
`;

// Back Navigation Link (for router links)
export const StyledBackLink = styled(Link)`
  display: flex;
  align-items: center;
  background-color: transparent;
  border: none;
  border-radius: 8px;
  padding: 0;
  cursor: pointer;
  color: #042B41;
  font-weight: 500;
  font-size: 16px;
  text-decoration: none;
  margin-right: 1rem;
  
  &:hover {
    color: #031f30;
  }
  
  svg {
    color: #042B41;
  }
  
  &:focus {
    outline: none;
  }
`;

// Back Button - Base style
const BackButton = styled.button`
  display: flex;
  align-items: center;
  gap: 8px;
  background-color: transparent;
  color: #042B41;
  border: none;
  padding: 0;
  font-size: 18px;
  font-weight: 500;
  cursor: pointer;
  transition: color 0.2s ease;

  &:hover {
    color: #031f30;
  }

  &:active {
    color: #021624;
  }

  svg {
    width: 22px;
    height: 22px;
    stroke: currentColor;
    transition: transform 0.2s ease;
  }

  &:hover svg {
    transform: translateX(-3px);
  }
`;

// Compact Back Button
const CompactBackButton = styled(BackButton)`
  padding: 0;
  margin: 0;
`;

// Back Button Component
interface BackButtonProps {
  onClick: () => void;
  label?: string;
  compact?: boolean;
}

export const BackButtonComponent: React.FC<BackButtonProps> = ({ onClick, label = "", compact = false }) => {
  const Button = compact ? CompactBackButton : BackButton;

  return (
    <Button onClick={onClick}>
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width={compact ? "16" : "18"}
        height={compact ? "16" : "18"}
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      >
        <path d="M19 12H5M12 19l-7-7 7-7" />
      </svg>
      {label}
    </Button>
  );
};

// 2. Page Structure

// Header Section
export const HeaderSection = styled.div`
  width: 100%;
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
`;

// Page Heading (Primary)
export const PageHeadingPrimary = styled.h1`
  font-family: 'Avenir', sans-serif;
  font-weight: 800;
  font-size: 32px;
  line-height: 100%;
  color: #000000;
  margin: 0;
`;

// Page Heading (Alternate)
export const PageHeadingAlternate = styled.h1`
  width: 276px;
  height: 44px;
  font-family: 'Avenir', sans-serif;
  font-weight: 800;
  font-size: 32px;
  line-height: 100%;
  color: #000000;
  margin: 0;
`;

// 3. Interactive Elements

// Add Button
export const AddButton = styled.button`
  height: 44px;
  padding: 10px 16px;
  border-radius: 8px;
  background-color: #042B41;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  border: none;
  cursor: pointer;
  
  &:hover {
    background-color: #0A3D5A;
  }
`;

// Add Button Text
export const AddButtonText = styled.span`
  font-family: 'Avenir', sans-serif;
  font-weight: 600;
  font-size: 14px;
  line-height: 100%;
  color: #FFFFFF;
  white-space: nowrap;
`;

// 4. Search Elements

// Search Container - Updated with consistent styling
export const SearchContainer = styled.div`
  position: relative;
  width: 100%;
  max-width: 24rem;
  margin-bottom: 2rem;
`;

// Search Icon Wrapper - Ensure icon is visible
export const SearchIconWrapper = styled.div`
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: #9CA3AF;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: none;
`;

// Search Field - Add left padding for icon
export const SearchField = styled.input`
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  border: 1px solid #E5E7EB;
  border-radius: 0.5rem;
  font-size: 1rem;
  color: #374151;
  background-color: white;
  font-family: 'Avenir', sans-serif;
  
  &::placeholder {
    color: #9CA3AF;
  }
  
  &:focus {
    outline: none;
    border-color: #042B41;
    box-shadow: 0 0 0 2px rgba(4, 43, 65, 0.1);
  }
`;

// SearchInput component with icon
interface SearchInputProps {
  placeholder?: string;
  value: string;
  onChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  disabled?: boolean;
}

export const SearchInputComponent = React.forwardRef<HTMLInputElement, SearchInputProps>(
  ({ placeholder = "Search", value, onChange, disabled = false }, ref) => (
    <SearchContainer>
      <SearchIconWrapper>
        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
          <circle cx="11" cy="11" r="8"></circle>
          <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
        </svg>
      </SearchIconWrapper>
      <SearchField
        ref={ref}
        placeholder={placeholder}
        value={value}
        onChange={onChange}
        disabled={disabled}
      />
    </SearchContainer>
  )
);

SearchInputComponent.displayName = 'SearchInputComponent';

// Combined components

// BackIcon Component
export const BackIcon: React.FC = () => (
  <svg width="32" height="32" viewBox="0 0 32 32" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M20 26L10 16L20 6" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
  </svg>
);

// AddIcon Component
export const AddIcon: React.FC = () => (
  <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
    <path d="M8 1V15M1 8H15" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" />
  </svg>
);

// Back Link Component
interface BackLinkProps {
  to: string;
  label?: string;
}

export const BackLinkComponent: React.FC<BackLinkProps> = ({ to, label = "Back" }) => (
  <StyledBackLink to={to}>
    <BackIcon />
  </StyledBackLink>
);

// Add Button Component
interface AddButtonProps {
  onClick: () => void;
  label: string;
}

export const AddButtonComponent: React.FC<AddButtonProps> = ({ onClick, label }) => (
  <AddButton onClick={onClick}>
    <AddIcon />
    <AddButtonText>{label}</AddButtonText>
  </AddButton>
);

// Page Container with proper spacing
export const PageContainer = styled.div`
  width: 100%;
  max-width: 100%;
  padding: 0 32px 32px 32px;
`;

// PageHeader with all elements
interface PageHeaderProps {
  title: string;
  onBackClick?: () => void;
  backLink?: string;
  onAddClick?: () => void;
  addButtonLabel?: string;
  children?: React.ReactNode;
}

export const PageHeaderComponent: React.FC<PageHeaderProps> = ({
  title,
  onBackClick,
  backLink,
  onAddClick,
  addButtonLabel,
  children
}) => (
  <>
    {backLink ? (
      <BackLinkComponent to={backLink} />
    ) : onBackClick ? (
      <BackButtonComponent onClick={onBackClick} />
    ) : null}

    <HeaderSection>
      <PageHeadingPrimary>{title}</PageHeadingPrimary>

      {onAddClick && addButtonLabel && (
        <AddButtonComponent onClick={onAddClick} label={addButtonLabel} />
      )}

      {children}
    </HeaderSection>
  </>
);

// Create a named export for the default object
export const DesignSystemComponents = {
  BackArrow,
  BackArrowLarge,
  BackText,
  BackNavigation,
  StyledBackLink,
  BackButton,
  HeaderSection,
  PageHeadingPrimary,
  PageHeadingAlternate,
  AddButton,
  AddButtonText,
  SearchField,
  SearchContainer,
  SearchIconWrapper,
  BackIcon,
  AddIcon,
  BackLinkComponent,
  BackButtonComponent,
  AddButtonComponent,
  PageContainer,
  PageHeaderComponent,
  SearchInputComponent
};

export default DesignSystemComponents;

// Shared page layout components
export const PageWrapper = styled.div`
  background: white;
  margin: 0;
  min-height: calc(100vh - 64px);
  display: flex;
  flex-direction: column;
`;

export const ContentContainer = styled.div`
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
  padding: 24px;
`;

export const TableContainer = styled.div`
  background: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  margin-bottom: 1.5rem;
`;

export const CustomBackButton = styled.button`
  display: flex;
  align-items: center;
  gap: 12px;
  background-color: transparent;
  color: #042B41;
  border: none;
  padding: 16px 20px;
  font-size: 18px;
  font-weight: 500;
  cursor: pointer;
  transition: color 0.2s ease;
  margin-bottom: 24px;

  &:hover {
    color: #031f30;
  }

  &:active {
    color: #021624;
  }

  svg {
    width: 22px;
    height: 22px;
    stroke: currentColor;
    transition: transform 0.2s ease;
  }

  &:hover svg {
    transform: translateX(-3px);
  }
`; 