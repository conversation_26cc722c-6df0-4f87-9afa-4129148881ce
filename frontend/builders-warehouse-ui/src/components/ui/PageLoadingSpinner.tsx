import React from 'react';
import styled from 'styled-components';
import LoadingSpinner from './LoadingSpinner';

const LoadingContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  min-height: 300px;
  width: 100%;
  padding: 2rem;
`;

const LoadingText = styled.p`
  margin-top: 1rem;
  font-size: 1rem;
  color: #6B7280;
  font-weight: 500;
`;

interface PageLoadingSpinnerProps {
  message?: string;
}

const PageLoadingSpinner: React.FC<PageLoadingSpinnerProps> = ({ 
  message = 'Loading data' 
}) => {
  return (
    <LoadingContainer>
      <LoadingSpinner size="lg" />
      <LoadingText>{message}</LoadingText>
    </LoadingContainer>
  );
};

export default PageLoadingSpinner; 