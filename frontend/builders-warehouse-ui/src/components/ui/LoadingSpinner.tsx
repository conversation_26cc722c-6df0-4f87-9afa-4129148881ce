import React from 'react';
import styled, { keyframes } from 'styled-components';

const spin = keyframes`
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
`;

const SpinnerContainer = styled.div`
  display: inline-block;
  position: relative;
  width: 24px;
  height: 24px;
`;

const SpinnerCircle = styled.div`
  box-sizing: border-box;
  display: block;
  position: absolute;
  width: 24px;
  height: 24px;
  border: 3px solid #E5E7EB;
  border-radius: 50%;
  animation: ${spin} 1.2s cubic-bezier(0.5, 0, 0.5, 1) infinite;
  border-color: #3B82F6 transparent transparent transparent;
`;

interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg';
  className?: string;
}

const sizeMap = {
  sm: '16px',
  md: '24px',
  lg: '32px'
};

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({ size = 'md', className }) => {
  const spinnerSize = sizeMap[size];
  
  return (
    <SpinnerContainer
      className={className}
      style={{ width: spinnerSize, height: spinnerSize }}
    >
      <SpinnerCircle style={{ width: spinnerSize, height: spinnerSize }} />
    </SpinnerContainer>
  );
};

export default LoadingSpinner; 