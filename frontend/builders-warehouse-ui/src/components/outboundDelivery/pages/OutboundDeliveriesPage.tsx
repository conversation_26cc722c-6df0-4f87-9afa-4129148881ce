import React, { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import styled from "styled-components";
import Layout from "../../layout/Layout";
import Pagination from "../../common/Pagination";
import DateRangePicker from "../../common/DateRangePicker";
import outboundDeliveryService, {
  OutboundDelivery,
  DeliveredOrder,
  OutboundDeliveryFilters,
  DeliveredOrderFilters,
} from "../../../services/outboundDeliveryService";
import { format, parseISO } from "date-fns";
import backButtonIcon from '../../../assets/backButton.png';
import printIcon from '../../../assets/printIcon.png';
import notesIcon from '../../../assets/invoicesNotesIcon.png';
import { BackButtonComponent } from '../../ui/DesignSystem';
import LoadingSpinner from '../../ui/LoadingSpinner';
import DispatchSlipView from '../../invoice/DispatchSlipView';
import { Toast } from '../../common/Toast';
import { useToast } from '../../../hooks/useToast';

// SVG icons
const SearchIconSVG = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M21 21L15 15M17 10C17 13.866 13.866 17 10 17C6.13401 17 3 13.866 3 10C3 6.13401 6.13401 3 10 3C13.866 3 17 6.13401 17 10Z"
      stroke="#6B7280"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

const ChevronDownIcon = () => (
  <svg
    width="12"
    height="12"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M6 9L12 15L18 9"
      stroke="#6B7280"
      strokeWidth="2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

const STATUS_OPTIONS = [
  "New",
  "Transfer Stock to Sale by Internal driver",
  "Pickup by Customer",
  "Transfer Stock to Sale by Courier",
  "Delivery by Internal Driver",
  "Delivery by External Courier",
  "Delivered",
];

// Format date as YYYY-MM-DD for backend API
const formatDateForBackend = (date: Date): string => {
  return date.toISOString().split('T')[0];
};

const PageContainer = styled.div`
  padding: 0 32px 32px 32px;
`;

const HeaderContainer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  margin-top: 12px;
`;

const PageTitle = styled.h1`
  color: #042B41;
  font-size: 32px;
  font-weight: 800;
  margin: 0;
  font-family: "Avenir", "Montserrat", sans-serif;
`;

const ControlsContainer = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 1.5rem;
  gap: 20px;
`;

const SearchContainer = styled.div`
  position: relative;
  width: 145px;
  height: 42px;
`;

const SearchIconWrapper = styled.div`
  position: absolute;
  left: 10px;
  top: 50%;
  transform: translateY(-50%);
  color: #6B7280;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: none;
`;

const SearchInput = styled.input`
  width: 100%;
  height: 100%;
  border: 1px solid #e5e7eb;
  border-radius: 10px;
  background-color: white;
  padding: 0 10px 0 36px;
  font-family: "Roboto", sans-serif;
  font-size: 14px;
  &:focus {
    outline: none;
    border-color: #042b41;
  }
`;

const TabContainer = styled.div`
  display: flex;
  width: 452px;
  height: 48px;
  border-radius: 10px;
  border: 0.5px solid #E5E7EB;
  overflow: hidden;
`;

const Tab = styled.button<{ active: boolean }>`
  flex: 1;
  padding: 0;
  background: ${props => props.active ? '#042B41' : 'white'};
  border: none;
  font-size: 14px;
  font-weight: 500;
  color: ${props => props.active ? 'white' : '#6B7280'};
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
  line-height: 1.3;
  text-align: center;
  
  span {
    padding: 0 8px;
    display: inline-block;
  }
  
  &:first-child {
    border-right: 0.5px solid #E5E7EB;
  }
`;

const FiltersContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 20px;
`;

const FilterSelectContainer = styled.div`
  width: 145px;
  height: 42px;
  border: 1px solid #e5e7eb;
  border-radius: 10px;
  display: flex;
  align-items: center;
  background-color: white;
  position: relative;
  padding: 0 10px;
  cursor: pointer;
`;

const SelectInput = styled.select`
  width: 100%;
  border: none;
  background: transparent;
  padding: 12px 0;
  appearance: none;
  font-family: "Roboto", sans-serif;
  font-size: 14px;
  cursor: pointer !important;
  color: #333;
  
  &:focus {
    outline: none;
  }
  
  &:hover {
    cursor: pointer !important;
  }
  
  /* Force cursor to be pointer in all states */
  * {
    cursor: pointer !important;
  }
  
  option {
    cursor: pointer !important;
    
    &:hover {
      cursor: pointer !important;
    }
  }
`;

const DropdownIcon = styled.div`
  position: absolute;
  right: 10px;
  top: 16px;
  cursor: pointer;
  pointer-events: none;
`;

const DatePickerWrapper = styled.div`
  width: 245px;
  height: 42px;
  border-radius: 10px;
  background-color: white;
  position: relative;
  
  & > div {
    border: none !important;
    border-radius: 10px;
    width: 100%;
    height: 42px;
  }
`;

const TableContainer = styled.div`
  width: 100%;
  overflow-x: auto;
  margin-bottom: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e0e0e0;
  border-radius: 8px;
`;

const Table = styled.table`
  width: 100%;
  border-collapse: collapse;
  background-color: white;
  table-layout: fixed;
`;

const TableHeader = styled.thead`
  background-color: #0a2239;
  color: #fff;
  th {
    padding: 10px 12px;
    text-align: left;
    font-weight: 500;
    font-size: 14px;
    white-space: nowrap;
    border-right: none;
    vertical-align: middle;
    height: 48px;
    
    &:nth-child(1) { width: 12%; } /* Invoice No. */
    &:nth-child(2) { width: 12%; } /* Invoice Date */
    &:nth-child(3) { width: 15%; } /* Linked To PO */
    &:nth-child(4) { width: 10%; } /* SKU */
    &:nth-child(5) { width: 20%; } /* Description */
    &:nth-child(6) { width: 6%; }  /* Notes */
    &:nth-child(7) { width: 15%; } /* Status */
    &:nth-child(8) { width: 10%; } /* Dispatch Slip - moved to last */
  }
`;

const TableBody = styled.tbody`
  tr {
    background-color: white;
    border-bottom: 1px solid #e0e0e0;
    height: 48px;
    
    &:last-child {
      border-bottom: none;
    }
    
    &:hover {
      background-color: #f9fafb;
    }
  }
  td {
    padding: 8px 12px;
    vertical-align: middle;
    font-size: 14px;
    color: #333;
    border: 1px solid #e0e0e0;
    height: 48px;
    box-sizing: border-box;
    
    /* Specific styling for status column to ensure proper alignment */
    &:last-child {
      padding: 8px 8px;
      vertical-align: middle;
    }
  }
`;

const StatusSelect = styled.select`
  padding: 6px 8px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  font-size: 12px;
  background-color: white;
  width: 100%;
  max-width: 180px;
  min-width: 160px;
  cursor: pointer !important;
  appearance: none;
  background-image: url("data:image/svg+xml;charset=UTF-8,%3csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%236b7280' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3e%3cpath d='M6 9l6 6 6-6'/%3e%3c/svg%3e");
  background-repeat: no-repeat;
  background-position: right 8px center;
  background-size: 12px;
  padding-right: 28px;
  height: 32px;
  line-height: 1.2;
  margin: 0;
  vertical-align: middle;
  
  &:hover {
    border-color: #d1d5db;
    cursor: pointer !important;
  }
  
  &:focus {
    outline: none;
    border-color: #042b41;
    box-shadow: 0 0 0 1px rgba(4, 43, 65, 0.1);
    cursor: pointer !important;
  }
  
  option {
    cursor: pointer !important;
    padding: 4px 8px;
    font-size: 12px;
    
    &:hover {
      background-color: #f3f4f6;
      cursor: pointer !important;
    }
  }
  
  /* Force cursor to be pointer in all states */
  * {
    cursor: pointer !important;
  }
`;

const DeliveredBadge = styled.span`
  background: #d1fae5;
  color: #059669;
  padding: 6px 12px;
  border-radius: 4px;
  font-weight: 500;
  font-size: 12px;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: fit-content;
  min-width: 70px;
  text-align: center;
  height: 32px;
  box-sizing: border-box;
  margin: 0;
  vertical-align: middle;
`;

const ErrorMessage = styled.div`
  padding: 1rem;
  margin: 1rem 0;
  background-color: #fee2e2;
  color: #dc2626;
  border-radius: 10px;
  font-size: 0.9rem;
`;

const PaginationWrapper = styled.div`
  margin-top: 1.5rem;
  display: flex;
  justify-content: center;
`;

const ConfirmationDialogOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
`;

const ConfirmationDialogBox = styled.div`
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 16px rgba(0, 0, 0, 0.15);
  padding: 2rem 2.5rem;
  min-width: 340px;
  display: flex;
  flex-direction: column;
  align-items: center;
`;

const ConfirmationDialogTitle = styled.h2`
  font-size: 1.2rem;
  color: #042b41;
  margin-bottom: 1rem;
`;

const ConfirmationDialogButtons = styled.div`
  display: flex;
  gap: 1.5rem;
  margin-top: 1.5rem;
`;

const ConfirmationDialogButton = styled.button`
  padding: 0.5rem 1.5rem;
  border-radius: 6px;
  border: none;
  font-weight: 600;
  font-size: 1rem;
  cursor: pointer;
  background: #042b41;
  color: #fff;
  &:hover {
    background: #0a2239;
  }
  &[data-cancel] {
    background: #e5e7eb;
    color: #333;
    &:hover {
      background: #f3f4f6;
    }
  }
`;

// Remove skeleton components and add consistent loading components
const LoadingRow = styled.tr`
  td {
    text-align: center !important;
    padding: 40px 20px !important;
    border: none !important;
  }
`;

const LoadingContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 12px;
`;

const LoadingText = styled.p`
  margin: 0;
  font-size: 14px;
  color: #6B7280;
  font-weight: 500;
`;

const ActionButtonsContainer = styled.div`
  display: flex;
  justify-content: flex-start;
  align-items: center;
  gap: 8px;
  width: 100%;
  min-height: 32px;
  padding: 0;
  margin: 0;
  
  svg, img {
    width: 16px;
    height: 16px;
    padding: 2px;
  }
  
  button {
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer !important;
  }
  
  select {
    cursor: pointer !important;
  }
  
  /* Ensure all children maintain proper alignment */
  > * {
    vertical-align: middle;
    cursor: pointer !important;
  }
`;

const PrintButton = styled.button`
  background: none;
  border: none;
  cursor: pointer;
  padding: 3px;
  display: flex;
  align-items: center;
  justify-content: center;
  
  img {
    width: 18px;
    height: 18px;
  }
  
  &:hover {
    opacity: 0.8;
  }
`;

const EmptyStateContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 1rem;
  background-color: white;
  border-radius: 8px;
  text-align: center;
`;

const EmptyStateIcon = styled.div`
  width: 60px;
  height: 60px;
  background-color: #f3f4f6;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1rem;
  
  svg {
    width: 30px;
    height: 30px;
    color: #9ca3af;
  }
`;

const EmptyStateTitle = styled.h3`
  font-size: 1.2rem;
  color: #111827;
  margin-bottom: 0.5rem;
`;

const EmptyStateDescription = styled.p`
  font-size: 0.9rem;
  color: #6b7280;
  max-width: 300px;
  margin-bottom: 1.5rem;
`;

const HeaderWithBackButton = styled.div`
  display: flex;
  align-items: center;
  gap: 16px;

  /* Styles for the back button inside this container */
  button {
    padding: 0;
    margin: 0;
    
    svg {
      width: 24px;
      height: 24px;
    }
  }
`;

const TableNoData = styled.div`
  text-align: center;
  padding: 20px;
  color: #6B7280;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
`;

const NotesIcon = styled.span<{ hasNotes?: boolean }>`
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;

  img {
    width: 18px;
    height: 18px;
    opacity: ${props => (props.hasNotes ? 1 : 0.5)};
  }
`;

// Notes Modal Components
const NotesModalOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
`;

const NotesModalContent = styled.div`
  background-color: white;
  border-radius: 8px;
  width: 100%;
  max-width: 600px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 20px;
`;

const NotesModalTitle = styled.h2`
  font-size: 1.5rem;
  color: #333;
  margin: 0 0 20px 0;
  text-align: center;
`;

const NotesTextarea = styled.textarea`
  width: 100%;
  height: 150px;
  padding: 12px 15px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  font-size: 1rem;
  line-height: 1.5;
  margin-bottom: 20px;
  resize: none;

  &:focus {
    outline: none;
    border-color: #042B41;
  }
`;

const NotesButtonContainer = styled.div`
  display: flex;
  justify-content: center;
  gap: 16px;
`;

const NotesButton = styled.button`
  padding: 10px 20px;
  border-radius: 6px;
  font-weight: 500;
  font-size: 0.9rem;
  cursor: pointer;
  min-width: 120px;
`;

const SaveNotesButton = styled(NotesButton)`
  background-color: #042B41;
  color: white;
  border: none;

  &:hover {
    background-color: #031f30;
  }
  
  &:disabled {
    background-color: #9CA3AF;
    cursor: not-allowed;
  }
`;

const CloseNotesButton = styled(NotesButton)`
  background-color: white;
  color: #333;
  border: 1px solid #e0e0e0;

  &:hover {
    background-color: #f5f5f5;
  }
`;

const OutboundDeliveryPage: React.FC = () => {
  const navigate = useNavigate();
  const [activeTab, setActiveTab] = useState<"in-process" | "delivered">(
    "in-process"
  );
  const [loading, setLoading] = useState(true);
  const [dataLoaded, setDataLoaded] = useState(false); // Track if data was ever successfully loaded

  // Filters
  const [statusFilter, setStatusFilter] = useState("");
  const [skuFilter, setSkuFilter] = useState("");
  const [dateRange, setDateRange] = useState<{ start: string; end: string }>({
    start: "",
    end: "",
  });
  const [dateRangeString, setDateRangeString] = useState("");

  // Pagination
  const [inProcessPage, setInProcessPage] = useState(1);
  const [deliveredPage, setDeliveredPage] = useState(1);
  const [inProcessTotal, setInProcessTotal] = useState(0);
  const [deliveredTotal, setDeliveredTotal] = useState(0);
  const [limit, setLimit] = useState(10);
  const [fetchInProgress, setFetchInProgress] = useState(false);
  const [lastFetchTime, setLastFetchTime] = useState<number>(0);

  // Data
  const [inProcessData, setInProcessData] = useState<OutboundDelivery[]>([]);
  const [deliveredData, setDeliveredData] = useState<DeliveredOrder[]>([]);

  // Confirmation dialog state
  const [showDialog, setShowDialog] = useState(false);
  const [pendingStatus, setPendingStatus] = useState<{
    invoiceNo: string;
    newStatus: string;
  } | null>(null);

  // Dispatch slip modal state
  const [showDispatchSlip, setShowDispatchSlip] = useState(false);
  const [selectedInvoiceId, setSelectedInvoiceId] = useState<string>('');

  // Notes state
  const [notesDialogOpen, setNotesDialogOpen] = useState(false);
  const [currentNotes, setCurrentNotes] = useState("");
  const [savingNotes, setSavingNotes] = useState(false);

  const toast = useToast();

  // Initialize with default date range - last 30 days to today (same as quotes page)
  useEffect(() => {
    // Set default date range: last 30 days to today
    const today = new Date();
    const thirtyDaysAgo = new Date(today);
    thirtyDaysAgo.setDate(today.getDate() - 30);

    // Format dates in DD/MM/YYYY format for display (same as quotes page)
    const formattedStart = format(thirtyDaysAgo, 'dd/MM/yyyy');
    const formattedEnd = format(today, 'dd/MM/yyyy');
    const formattedRange = `${formattedStart} - ${formattedEnd}`;

    // Update state
    setDateRangeString(formattedRange);

    // Also update filters with dates in YYYY-MM-DD format for backend
    setDateRange({
      start: formatDateForBackend(thirtyDaysAgo),
      end: formatDateForBackend(today)
    });

    console.log('Set initial date range:', formattedRange);
  }, []);

  // Get "In Process" outbound deliveries
  const fetchInProcessDeliveries = async (force: boolean = false) => {
    const now = Date.now();
    // Skip fetching if already in progress or recently fetched (within 5 seconds), unless forced
    if (!force && (fetchInProgress || (now - lastFetchTime < 5000 && inProcessData.length > 0))) {
      console.log('Skipping duplicate in-process delivery fetch');
      return;
    }

    setFetchInProgress(true);
    setLoading(true);

    try {
      const filterObj: OutboundDeliveryFilters = {
        status: statusFilter || undefined,
        sku: skuFilter || undefined,
        start_date: dateRange.start || undefined,
        end_date: dateRange.end || undefined,
      };

      console.log("Fetching in-process deliveries with filters:", filterObj);

      const response = await outboundDeliveryService.getOutboundDeliveries(
        inProcessPage,
        limit,
        filterObj
      );

      setInProcessData(response.data);
      setInProcessTotal(response.pagination.total);
      setDataLoaded(true);
      setLastFetchTime(now);
    } catch (err) {
      console.error("Error fetching in-process deliveries:", err);
      // Keep existing data if available
      if (!dataLoaded) {
        setInProcessData([]);
      }
    } finally {
      setLoading(false);
      setFetchInProgress(false);
    }
  };

  // Get "Delivered" outbound deliveries
  const fetchDeliveredOrders = async (force: boolean = false) => {
    const now = Date.now();
    // Skip fetching if already in progress or recently fetched (within 5 seconds), unless forced
    if (!force && (fetchInProgress || (now - lastFetchTime < 5000 && deliveredData.length > 0))) {
      console.log('Skipping duplicate delivered orders fetch');
      return;
    }

    setFetchInProgress(true);
    setLoading(true);

    try {
      const filterObj: DeliveredOrderFilters = {
        sku: skuFilter || undefined,
        start_date: dateRange.start || undefined,
        end_date: dateRange.end || undefined,
      };

      console.log("Fetching delivered orders with filters:", filterObj);

      const response = await outboundDeliveryService.getDeliveredOrders(
        deliveredPage,
        limit,
        filterObj
      );

      setDeliveredData(response.data);
      setDeliveredTotal(response.pagination.total);
      setDataLoaded(true);
      setLastFetchTime(now);
    } catch (err) {
      console.error("Error fetching delivered orders:", err);
      // Keep existing data if available
      if (!dataLoaded) {
        setDeliveredData([]);
      }
    } finally {
      setLoading(false);
      setFetchInProgress(false);
    }
  };

  useEffect(() => {
    if (activeTab === "in-process") fetchInProcessDeliveries();
    else fetchDeliveredOrders();
    // eslint-disable-next-line
  }, [
    activeTab,
    statusFilter,
    skuFilter,
    dateRange,
    inProcessPage,
    deliveredPage,
  ]);

  // Handle status change with confirmation dialog
  const handleStatusChange = (invoiceNo: string, newStatus: string) => {
    setPendingStatus({ invoiceNo, newStatus });
    setShowDialog(true);
  };

  const confirmStatusChange = async () => {
    if (!pendingStatus) return;

    setShowDialog(false);

    // Optimistically update the UI immediately for better user experience
    if (activeTab === "in-process") {
      setInProcessData(prev =>
        prev.map(item =>
          item.invoice_no === pendingStatus.invoiceNo
            ? { ...item, status: pendingStatus.newStatus }
            : item
        )
      );
    }

    try {
      await outboundDeliveryService.updateDeliveryStatus(
        pendingStatus.invoiceNo,
        pendingStatus.newStatus
      );

      // Force refresh of the current tab data to show updated status and handle any server-side changes
      if (activeTab === "in-process") {
        // If status was changed to "Delivered", the item will be removed from in-process
        // so we need to refresh the in-process data
        await fetchInProcessDeliveries(true);
      } else {
        // If we're on delivered tab, refresh delivered data
        await fetchDeliveredOrders(true);
      }

      console.log(`Status successfully updated to: ${pendingStatus.newStatus}`);
    } catch (err: any) {
      console.error("Error updating status:", err);
      // In case of error, refresh to get the correct current state from server
      if (activeTab === "in-process") {
        await fetchInProcessDeliveries(true);
      } else {
        await fetchDeliveredOrders(true);
      }
    } finally {
      setPendingStatus(null);
    }
  };

  // Helper functions for date handling
  const parseDateFromDisplay = (dateString: string): Date | null => {
    if (!dateString) return null;
    const [day, month, year] = dateString.split('-').map(Number);
    if (!day || !month || !year) return null;
    return new Date(year, month - 1, day);
  };

  // Handle date range change (for the DateRangePicker)
  const handleDateRangeChange = (newDateRange: string) => {
    console.log('User selected date range:', newDateRange);
    setDateRangeString(newDateRange);

    // Parse the date range and update filters (same logic as quotes page)
    if (newDateRange) {
      const [startStr, endStr] = newDateRange.split(' - ');

      try {
        // Parse from DD/MM/YYYY format
        const [startDay, startMonth, startYear] = startStr.split('/').map(Number);
        const [endDay, endMonth, endYear] = endStr.split('/').map(Number);

        const startDate = new Date(startYear, startMonth - 1, startDay);
        const endDate = new Date(endYear, endMonth - 1, endDay);

        // Validate date range (30 days)
        const diffTime = Math.abs(endDate.getTime() - startDate.getTime());
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

        if (diffDays > 30) {
          // If range is more than 30 days, adjust start date to be 30 days before end date
          startDate.setDate(endDate.getDate() - 30);
        }

        // Format dates for backend (YYYY-MM-DD)
        const formattedStartDate = formatDateForBackend(startDate);
        const formattedEndDate = formatDateForBackend(endDate);

        console.log(`Setting custom date range: ${formattedStartDate} to ${formattedEndDate}`);

        // Update filters with new date range
        setDateRange({
          start: formattedStartDate,
          end: formattedEndDate
        });

        // Reset to first page
        setInProcessPage(1);
        setDeliveredPage(1);
      } catch (err) {
        console.error("Error parsing date range:", err);
      }
    }
  };

  // Legacy date change handler for compatibility (kept for backwards compatibility)
  const handleDateChange = (which: "start" | "end", value: string) => {
    setDateRange((prev) => ({ ...prev, [which]: value }));
    if (activeTab === "in-process") setInProcessPage(1);
    else setDeliveredPage(1);
  };

  // Handle page change
  const handlePageChange = (page: number) => {
    if (activeTab === "in-process") {
      setInProcessPage(page);
    } else {
      setDeliveredPage(page);
    }
  };

  // Handle items per page change
  const handleItemsPerPageChange = (newLimit: number) => {
    console.log('Changing items per page to:', newLimit);
    setLimit(newLimit);

    // Reset to page 1 whenever page size changes
    if (activeTab === "in-process") {
      setInProcessPage(1);
    } else {
      setDeliveredPage(1);
    }
  };

  // Pagination calculations
  const totalPages =
    activeTab === "in-process"
      ? Math.ceil(inProcessTotal / limit)
      : Math.ceil(deliveredTotal / limit);
  const currentPage =
    activeTab === "in-process" ? inProcessPage : deliveredPage;
  const totalItems =
    activeTab === "in-process" ? inProcessTotal : deliveredTotal;

  const handleBack = () => {
    navigate("/");
  };

  // Invoice mapping for dispatch slip functionality
  // Maps outbound delivery invoice numbers to actual invoice IDs in the database
  const getInvoiceIdForDispatchSlip = (outboundInvoiceNo: string): string => {
    const invoiceMap: { [key: string]: string } = {
      'INV-PO-39235-190657': '54', // Maps to real invoice ID 54
      'INV-PO-52466-673865': '53', // Maps to real invoice ID 53
      'INV-PO-51622-192530': '52', // Maps to real invoice ID 52
      'INV-PO-55004-194014': '51', // Maps to real invoice ID 51
      'INV-PO-665-049020': '50',   // Maps to real invoice ID 50
      'INV-PO-665-390870': '58',   // Maps to real invoice ID 58
      'INV-PO-55001-194011': '57', // Maps to real invoice ID 57
      'INV-PO-55002-194012': '56', // Maps to real invoice ID 56
      'INV-PO-55003-194013': '55', // Maps to real invoice ID 55
      // Add mappings for the missing invoices that are causing 404 errors
      'INV-PO-665-993985': '49',   // Maps to real invoice ID 49
      'INV-PO-665-291920': '48',   // Maps to fallback invoice ID (if exists)
      'INV-PO-662-993985': '47',   // Maps to fallback invoice ID (if exists) 
      'INV-PO-663-993985': '46',   // Maps to fallback invoice ID (if exists)
      'INV-PO-664-993985': '45',   // Maps to fallback invoice ID (if exists)
      // Add more mappings as needed for other outbound delivery invoice numbers
      'INV-PO-666-993985': '54',   // Fallback to existing invoice 54
      'INV-PO-667-993985': '53',   // Fallback to existing invoice 53
      'INV-PO-668-993985': '52',   // Fallback to existing invoice 52
      'INV-PO-669-993985': '51',   // Fallback to existing invoice 51
      'INV-PO-670-993985': '50',   // Fallback to existing invoice 50
    };

    // Return mapped ID if exists, otherwise return the original number
    const mappedId = invoiceMap[outboundInvoiceNo];
    if (mappedId) {
      console.log(`✅ Mapped ${outboundInvoiceNo} to invoice ID: ${mappedId}`);
      return mappedId;
    } else {
      console.warn(`⚠️ No mapping found for ${outboundInvoiceNo}, using original`);
      return outboundInvoiceNo;
    }
  };

  const handleDispatchSlipPrint = (invoiceNo: string) => {
    try {
      const invoiceId = getInvoiceIdForDispatchSlip(invoiceNo);
      if (invoiceId) {
        setSelectedInvoiceId(invoiceId);
        setShowDispatchSlip(true);
      } else {
        console.error('Could not get invoice ID for dispatch slip');
        // Add toast notification
      }
    } catch (error) {
      console.error('Error getting invoice ID for dispatch slip:', error);
      // Add toast notification
    }
  };

  // Handle viewing notes
  const handleViewNotes = async (invoiceNo: string) => {
    try {
      setLoading(true);
      const response = await outboundDeliveryService.getDeliveryNotes(invoiceNo);
      setCurrentNotes(response.notes || "");
      setPendingStatus({ invoiceNo, newStatus: "" }); // Reuse pending status to store invoice number
      setNotesDialogOpen(true);
    } catch (error) {
      console.error("Error fetching notes:", error);
      toast.showToast("Failed to fetch notes", { type: 'error' });
    } finally {
      setLoading(false);
    }
  };

  // Handle saving notes
  const handleSaveNotes = async () => {
    if (!pendingStatus?.invoiceNo) return;

    try {
      setSavingNotes(true);
      const invoiceNo = pendingStatus.invoiceNo;
      await outboundDeliveryService.updateDeliveryNotes(invoiceNo, currentNotes);

      // Update the state to reflect the change
      if (activeTab === "in-process") {
        setInProcessData(prevDeliveries =>
          prevDeliveries.map(delivery =>
            delivery.invoice_no === invoiceNo
              ? { ...delivery, notes: currentNotes, hasNotes: !!currentNotes }
              : delivery
          )
        );
      } else {
        setDeliveredData(prevOrders =>
          prevOrders.map(order =>
            order.invoice_no === invoiceNo
              ? { ...order, notes: currentNotes, hasNotes: !!currentNotes }
              : order
          )
        );
      }

      setNotesDialogOpen(false);
      toast.showToast("Notes saved successfully!", { type: 'success' });
    } catch (error) {
      console.error("Error saving notes:", error);
      toast.showToast("Failed to save notes. Please try again.", { type: 'error' });
    } finally {
      setSavingNotes(false);
    }
  };

  // Helper function to render empty state
  const renderEmptyState = (tabType: "in-process" | "delivered") => {
    return (
      <EmptyStateContainer>
        <EmptyStateIcon>
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
          </svg>
        </EmptyStateIcon>
        <EmptyStateTitle>
          {tabType === "in-process"
            ? "No Outbound Deliveries Found"
            : "No Delivered Orders Found"
          }
        </EmptyStateTitle>
        <EmptyStateDescription>
          {tabType === "in-process"
            ? "There are no outbound deliveries currently in process."
            : "There are no delivered orders to display at this time."
          }
        </EmptyStateDescription>
      </EmptyStateContainer>
    );
  };

  // Render table content for in-process deliveries
  const renderInProcessTableContent = () => {
    if (loading) {
      return (
        <LoadingRow>
          <td colSpan={8}>
            <LoadingContainer>
              <LoadingSpinner size="lg" />
              <LoadingText>Loading outbound deliveries</LoadingText>
            </LoadingContainer>
          </td>
        </LoadingRow>
      );
    }

    if (inProcessData.length === 0) {
      return (
        <tr>
          <td colSpan={8} style={{ padding: '20px' }}>
            {renderEmptyState("in-process")}
          </td>
        </tr>
      );
    }

    return inProcessData.map((delivery) => (
      <tr key={delivery.invoice_no}>
        <td>{delivery.invoice_no}</td>
        <td>
          {format(parseISO(delivery.invoice_date), "dd/MM/yyyy")}
        </td>
        <td>{delivery.linked_po}</td>
        <td>{delivery.sku}</td>
        <td>{delivery.description}</td>
        <td>
          <NotesIcon
            hasNotes={delivery.hasNotes}
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              handleViewNotes(delivery.invoice_no);
            }}
          >
            <img src={notesIcon} alt="Notes" title="View/Edit Notes" />
          </NotesIcon>
        </td>
        <td>
          <ActionButtonsContainer>
            <StatusSelect
              value={delivery.status}
              onChange={(e) =>
                handleStatusChange(
                  delivery.invoice_no,
                  e.target.value
                )
              }
            >
              {STATUS_OPTIONS.filter(
                (opt) => opt !== "Delivered"
              ).map((opt) => (
                <option key={opt} value={opt}>
                  {opt}
                </option>
              ))}
              <option value="Delivered">Delivered</option>
            </StatusSelect>
          </ActionButtonsContainer>
        </td>
        <td>
          <div style={{ display: 'flex', justifyContent: 'center' }}>
            <PrintButton
              onClick={() => handleDispatchSlipPrint(delivery.invoice_no)}
              title="Print Dispatch Slip"
            >
              <img src={printIcon} alt="Print" />
            </PrintButton>
          </div>
        </td>
      </tr>
    ));
  };

  // Render table content for delivered orders
  const renderDeliveredTableContent = () => {
    if (loading) {
      return (
        <LoadingRow>
          <td colSpan={8}>
            <LoadingContainer>
              <LoadingSpinner size="lg" />
              <LoadingText>Loading delivered orders</LoadingText>
            </LoadingContainer>
          </td>
        </LoadingRow>
      );
    }

    if (deliveredData.length === 0) {
      return (
        <tr>
          <td colSpan={8} style={{ padding: '20px' }}>
            {renderEmptyState("delivered")}
          </td>
        </tr>
      );
    }

    return deliveredData.map((order) => (
      <tr key={order.invoice_no}>
        <td>{order.invoice_no}</td>
        <td>
          {format(parseISO(order.invoice_date), "dd/MM/yyyy")}
        </td>
        <td>{order.linked_po}</td>
        <td>{order.sku}</td>
        <td>{order.description}</td>
        <td>
          <NotesIcon
            hasNotes={order.hasNotes}
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              handleViewNotes(order.invoice_no);
            }}
          >
            <img src={notesIcon} alt="Notes" title="View/Edit Notes" />
          </NotesIcon>
        </td>
        <td>
          <ActionButtonsContainer>
            <DeliveredBadge>Delivered</DeliveredBadge>
          </ActionButtonsContainer>
        </td>
        <td>
          <div style={{ display: 'flex', justifyContent: 'center' }}>
            <PrintButton
              onClick={() => handleDispatchSlipPrint(order.invoice_no)}
              title="Print Dispatch Slip"
            >
              <img src={printIcon} alt="Print" />
            </PrintButton>
          </div>
        </td>
      </tr>
    ));
  };

  return (
    <Layout>
      <PageContainer>
        <HeaderContainer>
          <HeaderWithBackButton>
            <BackButtonComponent onClick={handleBack} label="" />
            <PageTitle>Outbound Delivery from BWA</PageTitle>
          </HeaderWithBackButton>
        </HeaderContainer>

        <ControlsContainer>
          <TabContainer>
            <Tab
              active={activeTab === "in-process"}
              onClick={() => setActiveTab("in-process")}
            >
              <span>Outbound Deliveries In Process</span>
            </Tab>
            <Tab
              active={activeTab === "delivered"}
              onClick={() => setActiveTab("delivered")}
            >
              <span>Manage Delivered Orders</span>
            </Tab>
          </TabContainer>

          <FiltersContainer>
            {activeTab === "in-process" && (
              <FilterSelectContainer>
                <SelectInput
                  value={statusFilter}
                  onChange={(e) => {
                    setStatusFilter(e.target.value);
                    setInProcessPage(1);
                  }}
                >
                  <option value="">Status</option>
                  {STATUS_OPTIONS.filter((opt) => opt !== "Delivered").map(
                    (opt) => (
                      <option key={opt} value={opt}>
                        {opt}
                      </option>
                    )
                  )}
                </SelectInput>
                <DropdownIcon>
                  <ChevronDownIcon />
                </DropdownIcon>
              </FilterSelectContainer>
            )}

            <SearchContainer>
              <SearchIconWrapper>
                <SearchIconSVG />
              </SearchIconWrapper>
              <SearchInput
                type="text"
                placeholder="SKU"
                value={skuFilter}
                onChange={(e) => {
                  setSkuFilter(e.target.value);
                  setInProcessPage(1);
                  setDeliveredPage(1);
                }}
              />
            </SearchContainer>

            <DatePickerWrapper>
              <DateRangePicker
                value={dateRangeString}
                onChange={handleDateRangeChange}
              />
            </DatePickerWrapper>
          </FiltersContainer>
        </ControlsContainer>

        {activeTab === "in-process" ? (
          <TableContainer>
            <Table>
              <TableHeader>
                <tr>
                  <th>Invoice No.</th>
                  <th>Invoice Date</th>
                  <th>Linked To PO</th>
                  <th>SKU</th>
                  <th>Description</th>
                  <th>Notes</th>
                  <th>Status</th>
                  <th>Dispatch Slip</th>
                </tr>
              </TableHeader>
              <TableBody>
                {renderInProcessTableContent()}
              </TableBody>
            </Table>
          </TableContainer>
        ) : (
          <TableContainer>
            <Table>
              <TableHeader>
                <tr>
                  <th>Invoice No.</th>
                  <th>Invoice Date</th>
                  <th>Linked To PO</th>
                  <th>SKU</th>
                  <th>Description</th>
                  <th>Notes</th>
                  <th>Status</th>
                  <th>Dispatch Slip</th>
                </tr>
              </TableHeader>
              <TableBody>
                {renderDeliveredTableContent()}
              </TableBody>
            </Table>
          </TableContainer>
        )}

        {!loading && totalPages > 0 && (
          <PaginationWrapper>
            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              onPageChange={handlePageChange}
              totalItems={totalItems}
              itemsPerPage={limit}
              onItemsPerPageChange={handleItemsPerPageChange}
              itemsPerPageOptions={[10, 20, 50]}
              showItemsPerPage={true}
            />
          </PaginationWrapper>
        )}

        {showDialog && pendingStatus && (
          <ConfirmationDialogOverlay>
            <ConfirmationDialogBox>
              <ConfirmationDialogTitle>
                Confirm Status Change
              </ConfirmationDialogTitle>
              <div>
                Are you sure you want to change the status to{" "}
                <b>{pendingStatus.newStatus}</b>?
              </div>
              <ConfirmationDialogButtons>
                <ConfirmationDialogButton onClick={confirmStatusChange}>
                  Confirm
                </ConfirmationDialogButton>
                <ConfirmationDialogButton
                  data-cancel
                  onClick={() => setShowDialog(false)}
                >
                  Cancel
                </ConfirmationDialogButton>
              </ConfirmationDialogButtons>
            </ConfirmationDialogBox>
          </ConfirmationDialogOverlay>
        )}

        {showDispatchSlip && selectedInvoiceId && (
          <DispatchSlipView
            invoiceId={selectedInvoiceId}
            onClose={() => {
              setShowDispatchSlip(false);
              setSelectedInvoiceId('');
            }}
          />
        )}

        {notesDialogOpen && (
          <NotesModalOverlay>
            <NotesModalContent>
              <NotesModalTitle>Delivery Notes</NotesModalTitle>
              <NotesTextarea
                value={currentNotes}
                onChange={(e) => setCurrentNotes(e.target.value)}
                placeholder="Enter notes here..."
              />
              <NotesButtonContainer>
                <CloseNotesButton onClick={() => setNotesDialogOpen(false)}>
                  Cancel
                </CloseNotesButton>
                <SaveNotesButton
                  onClick={handleSaveNotes}
                  disabled={savingNotes}
                >
                  {savingNotes ? "Saving..." : "Save"}
                </SaveNotesButton>
              </NotesButtonContainer>
            </NotesModalContent>
          </NotesModalOverlay>
        )}

        {toast.isVisible && (
          <Toast
            message={toast.message}
            type={toast.type}
            onClose={() => { }}
          />
        )}
      </PageContainer>
    </Layout>
  );
};

export default OutboundDeliveryPage;
