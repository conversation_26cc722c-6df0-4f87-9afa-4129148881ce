import React, { useState, useEffect, useRef } from "react";
import { useNavigate } from "react-router-dom";
import styled from "styled-components";
import Layout from "../../layout/Layout";
import inboundDeliveryService, {
  CalendarDay,
  CalendarResponse,
} from "../../../services/inboundDeliveryService";
import { format, parse } from "date-fns";
import backButtonIcon from '../../../assets/backButton.png';
import { BackButtonComponent } from '../../ui/DesignSystem';
import PageLoadingSpinner from '../../ui/PageLoadingSpinner';

const PageHeader = styled.div`
  padding: 1.5rem 2rem;
`;

const HeaderContainer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
`;

const HeaderWithBackButton = styled.div`
  display: flex;
  align-items: center;
  gap: 16px;
`;

const PageTitle = styled.h1`
  color: #042B41;
  font-size: 32px;
  font-weight: 800;
  margin: 0;
`;

const DateFilterContainer = styled.div`
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
`;

const FilterDropdown = styled.div`
  position: relative;

  select {
    padding: 0.75rem 2rem 0.75rem 1rem;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
    font-size: 0.9rem;
    appearance: none;
    background-color: white;
    cursor: pointer;
    min-width: 100px;

    &:focus {
      outline: none;
      border-color: #042b41;
    }
  }

  &::after {
    content: "";
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    width: 0;
    height: 0;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-top: 5px solid #333;
    pointer-events: none;
  }
`;

const CurrentDateHeader = styled.h2`
  font-size: 1.5rem;
  margin: 1.5rem 0;
  color: #333;
  font-weight: 600;
`;

const CalendarGrid = styled.div`
  display: grid;
  grid-template-columns: repeat(7, 1fr);
  grid-gap: 1px;
  background-color: white;
  border: none;
  margin-bottom: 32px;
`;

const DayHeader = styled.div`
  background-color: #042b41;
  color: white;
  padding: 0.75rem;
  text-align: center;
  font-weight: 500;
`;

interface DayCellProps {
  isCurrentMonth: boolean;
  hasDelivery: boolean;
  deliveryColor?: string;
}

const DayCell = styled.div<DayCellProps>`
  background-color: ${(props) =>
    !props.isCurrentMonth
      ? "#f5f5f5"
      : props.hasDelivery
      ? props.deliveryColor
      : "white"};
  padding: 1rem;
  min-height: 100px;
  position: relative;
  border: 1px solid #e0e0e0;

  &:hover {
    background-color: ${(props) =>
      !props.isCurrentMonth
        ? "#f5f5f5"
        : props.hasDelivery
        ? props.deliveryColor
        : "#f9f9f9"};
  }
`;

const DayNumber = styled.span`
  font-size: 1.1rem;
  font-weight: 500;
  color: #333;
`;

const DeliveryNote = styled.div`
  margin-top: 0.5rem;
  font-size: 0.85rem;
  font-weight: 600;
  color: #333;
  cursor: pointer;
`;

const DeliveryLabel = styled.div`
  margin-top: 0.25rem;
  font-size: 0.85rem;
  color: #666;
`;

const DeliveryCount = styled.div`
  margin-top: 0.25rem;
  font-size: 0.8rem;
  color: #666;
`;

const PONumber = styled.div`
  font-weight: 600;
  margin-top: 0.25rem;
  font-size: 0.9rem;
`;

interface Delivery {
  date: number;
  month: number;
  year: number;
  count: number;
  color: string;
  poDetails?: {
    poNumber: string;
    supplierName: string;
    sku: string;
    quantityOrdered: number;
    quantityReceived: number;
    invoiceNumber: string;
    orderedBy: string;
    balanceOfOrder: number;
  };
}

// Sample data for deliveries
const sampleDeliveries: Delivery[] = [
  {
    date: 10,
    month: 3,
    year: 2025,
    count: 1,
    color: "#ffe6e6", // Pink for partial delivery (backorder)
    poDetails: {
      poNumber: "PO-660",
      supplierName: "Supplier 2",
      sku: "BB69036-30",
      quantityOrdered: 9,
      quantityReceived: 4,
      invoiceNumber: "INV-660",
      orderedBy: "Staff 1",
      balanceOfOrder: 5,
    },
  },
  {
    date: 18,
    month: 3,
    year: 2025,
    count: 1,
    color: "#e6ffea", // Green for complete delivery
    poDetails: {
      poNumber: "PO-663",
      supplierName: "Supplier 1",
      sku: "BWA V1 T/NOSIM6060",
      quantityOrdered: 10,
      quantityReceived: 10,
      invoiceNumber: "INV-663",
      orderedBy: "Staff 1",
      balanceOfOrder: 0,
    },
  },
  {
    date: 25,
    month: 3,
    year: 2025,
    count: 1,
    color: "#e6ffea", // Green for complete delivery
    poDetails: {
      poNumber: "PO-661",
      supplierName: "Supplier 3",
      sku: "BB69033",
      quantityOrdered: 3,
      quantityReceived: 3,
      invoiceNumber: "INV-661",
      orderedBy: "Staff 1",
      balanceOfOrder: 0,
    },
  },
  {
    date: 29,
    month: 3,
    year: 2025,
    count: 1,
    color: "#ffe6e6", // Pink for partial delivery (backorder)
    poDetails: {
      poNumber: "PO-662",
      supplierName: "Supplier 2",
      sku: "BB69036-30",
      quantityOrdered: 5,
      quantityReceived: 3,
      invoiceNumber: "INV-662",
      orderedBy: "Staff 1",
      balanceOfOrder: 2,
    },
  },
];

const months = [
  "January",
  "February",
  "March",
  "April",
  "May",
  "June",
  "July",
  "August",
  "September",
  "October",
  "November",
  "December",
];

const days = ["SUN", "MON", "TUE", "WED", "THUR", "FRI", "SAT"];

const getDaysInMonth = (month: number, year: number) => {
  // Month is 0-indexed in JavaScript Date
  return new Date(year, month + 1, 0).getDate();
};

const getFirstDayOfMonth = (month: number, year: number) => {
  // Month is 0-indexed in JavaScript Date
  return new Date(year, month, 1).getDay();
};

// Add a new component for calendar skeleton loading
const CalendarSkeletonGrid = styled(CalendarGrid)`
  opacity: 0.7;
`;

const SkeletonDayCell = styled(DayCell)`
  position: relative;
  overflow: hidden;
  
  &::after {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: shimmer 1.5s infinite;
  }
  
  @keyframes shimmer {
    0% {
      background-position: -200% 0;
    }
    100% {
      background-position: 200% 0;
    }
  }
`;

const InboundDeliveryPage: React.FC = () => {
  const navigate = useNavigate();

  // Get current month and year
  const currentDate = new Date();
  const [selectedMonth, setSelectedMonth] = useState(currentDate.getMonth());
  const [selectedYear, setSelectedYear] = useState(currentDate.getFullYear());
  const [calendarData, setCalendarData] = useState<CalendarResponse | null>(
    null
  );
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Add a ref to track API calls in progress
  const apiCallInProgress = useRef<boolean>(false);

  useEffect(() => {
    fetchCalendarData();
  }, [selectedMonth, selectedYear]);

  const fetchCalendarData = async () => {
    // Skip if an API call is already in progress
    if (apiCallInProgress.current) {
      console.log('API call already in progress, skipping duplicate request');
      return;
    }
    
    apiCallInProgress.current = true;
    setLoading(true);
    setError(null);
    try {
      // API months are 1-indexed, JS months are 0-indexed
      const data = await inboundDeliveryService.getCalendarHighlights(
        selectedMonth + 1,
        selectedYear
      );

      // Ensure we have a valid response with days property
      if (!data || !Array.isArray(data.days)) {
        console.error("Invalid calendar data structure:", data);
        setError("Invalid calendar data format received from server.");
        
        // Create a default calendar structure instead of setting to null
        const defaultCalendar: CalendarResponse = {
          month: selectedMonth + 1,
          year: selectedYear,
          days: []
        };
        setCalendarData(defaultCalendar);
        return;
      }

      // Map the days data to ensure correct date format
      const processedData = {
        ...data,
        days: data.days.map((day) => ({
          ...day,
          // Ensure date is in the correct format
          date:
            typeof day.date === "string"
              ? day.date
              : typeof day.date === "object" &&
                day.date &&
                "getTime" in day.date
              ? format(day.date as Date, "yyyy-MM-dd")
              : `${selectedYear}-${String(selectedMonth + 1).padStart(
                  2,
                  "0"
                )}-${String(day.date).padStart(2, "0")}`,
          po_numbers: Array.isArray(day.po_numbers) ? day.po_numbers : [],
          status: day.status || "Unknown",
        })),
      };

      setCalendarData(processedData);
    } catch (err) {
      console.error("Error fetching calendar data:", err);
      setError(
        typeof err === "object" && err !== null && "message" in err
          ? String(err.message)
          : "Failed to load calendar data. Please try again."
      );
      
      // Create a default calendar structure even when API fails
      const defaultCalendar: CalendarResponse = {
        month: selectedMonth + 1,
        year: selectedYear,
        days: []
      };
      setCalendarData(defaultCalendar);
    } finally {
      setLoading(false);
      apiCallInProgress.current = false;
    }
  };

  const handleBack = () => {
    navigate('/home');
  };

  const handleMonthChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedMonth(parseInt(e.target.value));
  };

  const handleYearChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedYear(parseInt(e.target.value));
  };

  const handleDateClick = (date: string, hasDelivery: boolean) => {
    if (hasDelivery) {
      // Navigate to detail page with date using the new API path pattern
      navigate(`/inbound-delivery/date/${date}`);
    }
  };

  const getColorForStatus = (status: string): string => {
    switch (status.toLowerCase()) {
      case "completed delivery":
        return "#D1FAE5"; // Light green for complete delivery (from Figma)
      case "partial delivery":
        return "#FEF3C7"; // Light yellow for partial/back order (from Figma)
      case "pending":
        return "#FBCFE8"; // Light pink for new/pending (from Figma)
      default:
        return "white";
    }
  };

  // Add a function to render calendar skeleton
  const renderCalendarSkeleton = () => {
    const calendarDays = [];
    
    // Add day headers
    days.forEach((day) => {
      calendarDays.push(<DayHeader key={`header-${day}`}>{day}</DayHeader>);
    });
    
    // Add skeleton cells for all days (7x6 grid = 42 cells)
    for (let i = 0; i < 42; i++) {
      calendarDays.push(
        <SkeletonDayCell
          key={`skeleton-${i}`}
          isCurrentMonth={i % 3 === 0} // Random variation
          hasDelivery={false}
        />
      );
    }
    
    return <CalendarSkeletonGrid>{calendarDays}</CalendarSkeletonGrid>;
  };

  const renderCalendar = () => {
    // Show loading indicator but keep displaying the previous calendar if available
    if (loading && !calendarData) {
      return renderCalendarSkeleton();
    }

    // Display error message but continue rendering the calendar if we have data
    // This allows for a graceful degradation UX
    
    const daysInMonth = getDaysInMonth(selectedMonth, selectedYear);
    const firstDayOfMonth = getFirstDayOfMonth(selectedMonth, selectedYear);

    // Create an array of calendar cells
    const calendarDays = [];

    // Add day headers
    days.forEach((day) => {
      calendarDays.push(<DayHeader key={`header-${day}`}>{day}</DayHeader>);
    });

    // Add empty cells for days before the first day of the month
    for (let i = 0; i < firstDayOfMonth; i++) {
      calendarDays.push(
        <DayCell
          key={`empty-${i}`}
          isCurrentMonth={false}
          hasDelivery={false}
        />
      );
    }

    // Add cells for each day of the month
    for (let day = 1; day <= daysInMonth; day++) {
      const dateStr = format(
        new Date(selectedYear, selectedMonth, day),
        "yyyy-MM-dd"
      );

      // More robust calendar day finding logic
      const calendarDay = calendarData?.days.find((d) => {
        try {
          // Try multiple date formats to handle different API responses
          let dayDate: Date;

          if (typeof d.date === "string") {
            // Try parsing the string date
            dayDate = parse(d.date, "yyyy-MM-dd", new Date());
            if (isNaN(dayDate.getTime())) {
              // If parsing fails, try with different format
              const [year, month, date] = d.date.split("-").map(Number);
              dayDate = new Date(year, month - 1, date);
            }
          } else if (
            typeof d.date === "object" &&
            d.date &&
            "getTime" in d.date
          ) {
            dayDate = d.date as Date;
          } else {
            // Fallback - construct date from day number
            dayDate = new Date(selectedYear, selectedMonth, Number(d.date));
          }

          return format(dayDate, "yyyy-MM-dd") === dateStr;
        } catch (error) {
          console.error("Error parsing date:", error, d.date);
          return false;
        }
      });

      const hasDelivery = !!calendarDay;
      const deliveryColor = hasDelivery
        ? getColorForStatus(calendarDay.status)
        : undefined;

      calendarDays.push(
        <DayCell
          key={`day-${day}`}
          isCurrentMonth={true}
          hasDelivery={hasDelivery}
          deliveryColor={deliveryColor}
          onClick={() => handleDateClick(dateStr, hasDelivery)}
          style={{ cursor: hasDelivery ? "pointer" : "default" }}
        >
          <DayNumber>{day}</DayNumber>

          {hasDelivery && (
            <>
              <DeliveryNote>
                Click to check
                <br />
                Inbound Deliveries
              </DeliveryNote>
              <DeliveryLabel>Deliveries</DeliveryLabel>
            </>
          )}
        </DayCell>
      );
    }

    // Add empty cells for the days after the last day of the month
    const totalCells = 7 * Math.ceil((firstDayOfMonth + daysInMonth) / 7);
    const remainingCells = totalCells - (firstDayOfMonth + daysInMonth);

    for (let i = 0; i < remainingCells; i++) {
      calendarDays.push(
        <DayCell
          key={`empty-end-${i}`}
          isCurrentMonth={false}
          hasDelivery={false}
        />
      );
    }

    return (
      <>
        {error && (
          <div style={{ marginBottom: '1rem', color: '#d32f2f', padding: '0.5rem', background: '#ffebee', borderRadius: '4px' }}>
            {error}
          </div>
        )}
        <CalendarGrid>{calendarDays}</CalendarGrid>
      </>
    );
  };

  return (
    <Layout>
      <PageHeader>
        <HeaderContainer>
          <HeaderWithBackButton>
            <BackButtonComponent onClick={handleBack} label="" />
            <PageTitle>Inbound Delivery to BWA Dashboard</PageTitle>
          </HeaderWithBackButton>
        </HeaderContainer>

        <DateFilterContainer>
          <FilterDropdown>
            <select value={selectedMonth} onChange={handleMonthChange}>
              {months.map((month, index) => (
                <option key={month} value={index}>
                  {month}
                </option>
              ))}
            </select>
          </FilterDropdown>

          <FilterDropdown>
            <select value={selectedYear} onChange={handleYearChange}>
              {Array.from({ length: 5 }, (_, i) => selectedYear - 2 + i).map(
                (year) => (
                  <option key={year} value={year}>
                    {year}
                  </option>
                )
              )}
            </select>
          </FilterDropdown>
        </DateFilterContainer>

        <CurrentDateHeader>
          {months[selectedMonth]}, {selectedYear}
        </CurrentDateHeader>

        {renderCalendar()}
      </PageHeader>
    </Layout>
  );
};

export default InboundDeliveryPage;
