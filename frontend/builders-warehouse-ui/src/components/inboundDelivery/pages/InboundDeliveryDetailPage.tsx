import React, { useState, useEffect, useRef } from "react";
import { useParams, useNavigate, Link } from "react-router-dom";
import styled from "styled-components";
import Layout from "../../layout/Layout";
import Pagination from "../../common/Pagination";
import InvoiceView from "../../invoice/InvoiceView";
import inboundDeliveryService, {
  InboundDelivery,
  PagedInboundDeliveryResponse,
} from "../../../services/inboundDeliveryService";
import outboundDeliveryService from "../../../services/outboundDeliveryService";
import { format, parseISO, isValid } from "date-fns";
import { BackLinkComponent } from '../../ui/DesignSystem';
import PageLoadingSpinner from '../../ui/PageLoadingSpinner';
import LoadingSpinner from '../../ui/LoadingSpinner';

const PageContainer = styled.div`
  padding: 1.5rem 2rem;
`;

const HeaderRow = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 1.5rem;
`;

const BreadcrumbContainer = styled.div`
  display: flex;
  align-items: center;
  font-size: 1.5rem;
  color: #6b7280;
`;

const BreadcrumbSeparator = styled.span`
  color: #9ca3af;
  margin: 0 8px;
`;

const SearchContainer = styled.div`
  position: relative;
  width: 250px;
  margin-bottom: 1.5rem;
`;

const SearchInput = styled.input`
  width: 100%;
  padding: 10px 16px;
  border: 1px solid #e5e7eb;
  border-radius: 8px;
  font-size: 14px;

  &::placeholder {
    color: #9ca3af;
  }
`;

const SearchIcon = styled.span`
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
`;

const TableContainer = styled.div`
  width: 100%;
  overflow-x: auto;
  margin-bottom: 1rem;
  border-radius: 8px;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
`;

const Table = styled.table`
  width: 100%;
  border-collapse: collapse;
  border-radius: 8px;
  background-color: white;
  border: 1px solid #e5e7eb;
  min-width: 1200px; /* Ensure table has a minimum width */
`;

const TableHeader = styled.thead`
  background-color: #042B41;
  color: white;

  th {
    padding: 16px;
    text-align: left;
    font-weight: 500;
    font-size: 14px;
    white-space: nowrap;
    border-right: none;
  }
`;

const TableBody = styled.tbody`
  tr {
    background-color: white;
    border-bottom: 1px solid #e5e7eb;

    &:last-child {
      border-bottom: none;
    }

    &:hover {
      background-color: #f9fafb;
    }
  }

  td {
    padding: 16px;
    vertical-align: middle;
    border-right: none;
    font-size: 14px;
    color: #333;
  }
`;

const ToggleContainer = styled.div`
  display: flex;
  justify-content: center;
`;

const ToggleSwitch = styled.label`
  position: relative;
  display: inline-block;
  width: 44px;
  height: 24px;
`;

const ToggleInput = styled.input`
  opacity: 0;
  width: 0;
  height: 0;

  &:checked + span {
    background-color: #22c55e; /* Green color when enabled */
  }

  &:checked + span:before {
    transform: translateX(20px);
  }

  &:disabled + span {
    opacity: 0.6;
  }

  &:disabled:checked + span {
    background-color: #22c55e; /* Keep green even when disabled */
  }
`;

const ToggleSlider = styled.span`
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: 0.4s;
  border-radius: 24px;

  &:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 3px;
    bottom: 3px;
    background-color: white;
    transition: 0.4s;
    border-radius: 50%;
  }

  &.disabled {
    opacity: 0.6;
    cursor: pointer;
  }
`;

const InvoiceLink = styled.a`
  color: #042b41;
  text-decoration: none;
  font-weight: 500;

  &:hover {
    text-decoration: underline;
  }
`;

const EditableCell = styled.div`
  display: flex;
  align-items: center;
  gap: 0.5rem;

  input {
    width: 80px;
    padding: 0.25rem;
    border: 1px solid #e0e0e0;
    border-radius: 4px;

    &:focus {
      outline: none;
      border-color: #042b41;
    }
  }

  button {
    background: none;
    border: none;
    color: #042b41;
    cursor: pointer;
    padding: 0.25rem;

    &:hover {
      color: #0056b3;
    }
  }
`;

const ConfirmationDialog = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
`;

const DialogContent = styled.div`
  background: white;
  padding: 2rem;
  border-radius: 8px;
  max-width: 400px;
  width: 90%;
`;

const DialogTitle = styled.h3`
  margin: 0 0 1rem 0;
  color: #333;
`;

const DialogMessage = styled.p`
  margin: 0 0 1.5rem 0;
  color: #666;
`;

const DialogActions = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: 1rem;
`;

const DialogButton = styled.button<{ secondary?: boolean }>`
  padding: 0.75rem 1.5rem;
  border-radius: 4px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  background-color: ${(props) => (props.secondary ? "white" : "#042b41")};
  color: ${(props) => (props.secondary ? "#042b41" : "white")};
  border: 1px solid ${(props) => (props.secondary ? "#e0e0e0" : "#042b41")};

  &:hover {
    background-color: ${(props) => (props.secondary ? "#f5f5f5" : "#031f2e")};
  }
`;

const ErrorMessage = styled.div`
  padding: 1rem;
  margin: 1rem 0;
  background-color: #fee2e2;
  color: #dc2626;
  border-radius: 4px;
  font-size: 0.9rem;
`;

const PaginationContainer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 1rem;
`;

const EmptyStateContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 4rem 2rem;
  background-color: white;
  border-radius: 8px;
  border: 1px solid #e5e7eb;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  margin-bottom: 1rem;
`;

const EmptyStateTitle = styled.h3`
  font-size: 1.5rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.5rem;
`;

const EmptyStateMessage = styled.p`
  font-size: 1rem;
  color: #6b7280;
  text-align: center;
  max-width: 500px;
  margin-bottom: 1.5rem;
`;

const PaginationWrapper = styled.div`
  margin-top: 1rem;
  display: flex;
  justify-content: center;
`;

// Add these styled components for the improved quantity field
const QuantityInputContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: flex-start;
`;

const QuantityInput = styled.input`
  width: 80px;
  padding: 8px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  font-size: 14px;
  margin-bottom: 4px;

  &:focus {
    outline: none;
    border-color: #042b41;
    box-shadow: 0 0 0 2px rgba(4, 43, 65, 0.1);
  }

  &:disabled {
    background-color: #f5f5f5;
    color: #9ca3af;
    cursor: not-allowed;
    border-color: #d1d5db;
  }
`;

const QuantityActionButtons = styled.div`
  display: flex;
  gap: 8px;
  margin-top: 6px;
`;

const IconButton = styled.button`
  background: none;
  border: none;
  padding: 4px;
  cursor: pointer;
  color: #6b7280;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  
  &:hover {
    background-color: #f3f4f6;
    color: #042b41;
  }
`;

// Add these styled components for the date field
const DateInputContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: flex-start;
`;

const DateInput = styled.input`
  width: 130px;
  padding: 8px;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  font-size: 14px;
  margin-bottom: 4px;

  &:focus {
    outline: none;
    border-color: #042b41;
    box-shadow: 0 0 0 2px rgba(4, 43, 65, 0.1);
  }
`;

// Remove skeleton components and replace with consistent loading
const LoadingRow = styled.tr`
  td {
    text-align: center !important;
    padding: 40px 20px !important;
    border: none !important;
  }
`;

const LoadingContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 12px;
`;

const LoadingText = styled.p`
  margin: 0;
  font-size: 14px;
  color: #6B7280;
  font-weight: 500;
`;

const InboundDeliveryDetailPage: React.FC = () => {
  const navigate = useNavigate();
  const { date_str, po_number } = useParams<{
    date_str?: string;
    po_number?: string;
  }>();
  const [deliveryData, setDeliveryData] =
    useState<PagedInboundDeliveryResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [totalItems, setTotalItems] = useState(0);
  const [selectedDate, setSelectedDate] = useState<Date | null>(null);
  const [isDateMode, setIsDateMode] = useState<boolean>(false);

  // States for editing
  const [editingQuantity, setEditingQuantity] = useState<string | null>(null);
  const [newQuantity, setNewQuantity] = useState<number>(0);
  const [editingDate, setEditingDate] = useState<string | null>(null);
  const [newDate, setNewDate] = useState<string>("");
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [confirmationType, setConfirmationType] = useState<
    "quantity" | "date" | "ship" | "warning-not-received" | "warning-quantity-mismatch" | "warning-already-shipped"
  >("quantity");
  const [toggleItem, setToggleItem] = useState<{
    poNumber: string;
    currentValue: boolean;
    sku?: string;
    description?: string;
    quantityReceived: number;
    quantityOrdered: number;
  } | null>(null);

  // Add a ref to track API calls in progress
  const apiCallInProgress = useRef<boolean>(false);

  // State for invoice modal
  const [isViewInvoiceOpen, setIsViewInvoiceOpen] = useState(false);
  const [viewInvoiceId, setViewInvoiceId] = useState<string | null>(null);

  useEffect(() => {
    // Determine if we're in date mode or PO mode
    setIsDateMode(!!date_str);

    if (date_str) {
      try {
        // Parse the date from URL parameter
        const parsedDate = new Date(date_str);
        if (isValid(parsedDate)) {
          setSelectedDate(parsedDate);
          fetchDeliveryDataByDate(parsedDate);
        } else {
          setError("Invalid date format in URL");
        }
      } catch (error) {
        console.error("Error parsing date:", error);
        setError("Error parsing date from URL");
      }
    } else if (po_number) {
      // TODO: If you need to handle PO number details fetching
      // fetchDeliveryDataByPO(po_number);
    }
  }, [date_str, po_number, currentPage, itemsPerPage, searchTerm]);

  const formatDate = (dateStr: string): string => {
    try {
      const date = parseISO(dateStr);
      return format(date, "dd/MM/yyyy");
    } catch (error) {
      return dateStr;
    }
  };

  const getMonthName = (month: number): string => {
    const months = [
      "January",
      "February",
      "March",
      "April",
      "May",
      "June",
      "July",
      "August",
      "September",
      "October",
      "November",
      "December",
    ];
    return months[month];
  };

  const fetchDeliveryDataByDate = async (date: Date) => {
    // Skip if an API call is already in progress
    if (apiCallInProgress.current) {
      console.log('API call already in progress, skipping duplicate request');
      return;
    }
    
    apiCallInProgress.current = true;
    setLoading(true);
    setError(null);
    
    try {
      const response = await inboundDeliveryService.getDeliveriesByDate(
        date,
        searchTerm || undefined,
        currentPage,
        itemsPerPage
      );
      setDeliveryData(response);
      setTotalItems(response.pagination.total);
    } catch (error) {
      console.error("Error fetching delivery data:", error);
      setError("Failed to load delivery data. Please try again.");
    } finally {
      setLoading(false);
      apiCallInProgress.current = false;
    }
  };

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
    setCurrentPage(1); // Reset to first page on new search
  };

  const handleQuantityEdit = (poNumber: string, currentValue: number) => {
    setEditingQuantity(poNumber);
    setNewQuantity(currentValue);
  };

  const handleQuantitySave = async () => {
    if (!editingQuantity) return;

    try {
      // Find the current item being edited
      const currentItem = deliveryData?.data.find(item => item.po_number === editingQuantity);
      
      // Check if the expected date is in the past and set to today if needed
      let expectedDate: string | undefined = undefined;
      if (currentItem) {
        const currentExpectedDate = new Date(currentItem.new_expected_date || currentItem.po_date);
        const today = new Date();
        today.setHours(0, 0, 0, 0); // Set to beginning of day for fair comparison
        
        if (currentExpectedDate < today) {
          // Date is in the past, set to today's date
          const todayFormatted = today.toISOString().split('T')[0]; // YYYY-MM-DD format
          expectedDate = todayFormatted;
        }
      }
      
      // Call API to update quantity
      await inboundDeliveryService.updateQuantityReceived(
        editingQuantity,
        null, // No specific SKU
        newQuantity,
        expectedDate // Now passing the expected date if needed
      );

      // Update local state
      if (deliveryData && deliveryData.data) {
        const updatedData = deliveryData.data.map((item) => {
          if (item.po_number === editingQuantity) {
            return {
              ...item,
              quantity_received: newQuantity,
              balance_order: item.quantity_ordered - newQuantity,
              status:
                newQuantity === 0
                  ? "pending"
                  : newQuantity < item.quantity_ordered
                  ? "partial"
                  : "completed",
              // Update expected date if it was changed
              new_expected_date: expectedDate || item.new_expected_date || item.po_date
            };
          }
          return item;
        });

        setDeliveryData({
          ...deliveryData,
          data: updatedData,
        });
      }

      setEditingQuantity(null);
      setShowConfirmation(false);
      
      // Show success notification
      // If you have a notification system, you can trigger it here
    } catch (error) {
      console.error("Error updating quantity:", error);
      setError("Failed to update quantity. Please try again.");
      setShowConfirmation(false);
    }
  };

  const handleDateEdit = (poNumber: string, currentValue: string) => {
    setEditingDate(poNumber);
    setNewDate(currentValue);
  };

  const handleDateSave = async () => {
    if (!editingDate) return;

    try {
      // Find the current item to get its quantity
      const currentItem = deliveryData?.data.find(item => item.po_number === editingDate);
      const currentQuantity = currentItem?.quantity_received || 0;
      
      // Call API to update date (using the same API endpoint)
      await inboundDeliveryService.updateQuantityReceived(
        editingDate,
        null, // No specific SKU
        currentQuantity, // Keep the same quantity
        newDate // Update the expected date
      );

      // Update local state
      if (deliveryData && deliveryData.data) {
        const updatedData = deliveryData.data.map((item) => {
          if (item.po_number === editingDate) {
            return {
              ...item,
              new_expected_date: newDate,
            };
          }
          return item;
        });

        setDeliveryData({
          ...deliveryData,
          data: updatedData,
        });
      }

      setEditingDate(null);
      setShowConfirmation(false);
      
      // Show success notification
      // If you have a notification system, you can trigger it here
    } catch (error) {
      console.error("Error updating date:", error);
      setError("Failed to update expected date. Please try again.");
      setShowConfirmation(false);
    }
  };

  const handleShipToCustomerToggle = async (
    poNumber: string,
    currentValue: boolean,
    quantityReceived: number,
    quantityOrdered: number,
    sku: string,
    description: string
  ) => {
    // Check if trying to disable an already enabled ship to customer
    if (currentValue === true) {
      setToggleItem({
        poNumber,
        currentValue,
        sku,
        description,
        quantityReceived,
        quantityOrdered
      });
      setConfirmationType("warning-already-shipped");
      setShowConfirmation(true);
      return;
    }

    // Check for quantity not fully received
    if (quantityReceived < quantityOrdered) {
      setToggleItem({
        poNumber,
        currentValue,
        sku,
        description,
        quantityReceived,
        quantityOrdered
      });
      setConfirmationType("warning-not-received");
      setShowConfirmation(true);
      return;
    }

    // Check for quantity received more than ordered
    if (quantityReceived > quantityOrdered) {
      setToggleItem({
        poNumber,
        currentValue,
        sku,
        description,
        quantityReceived,
        quantityOrdered
      });
      setConfirmationType("warning-quantity-mismatch");
      setShowConfirmation(true);
      return;
    }

    // Normal case - quantities match
    setToggleItem({
      poNumber,
      currentValue,
      sku,
      description,
      quantityReceived,
      quantityOrdered
    });
    setConfirmationType("ship");
    setShowConfirmation(true);
  };

  const handleShipToCustomerConfirm = async () => {
    if (!toggleItem) return;
    
    try {
      // Call API to update ship to customer status
      // The backend will automatically create an outbound delivery when ship_to_customer is set to true
      await inboundDeliveryService.updateShipToCustomer(
        toggleItem.poNumber,
        !toggleItem.currentValue
      );

      // Show success message
      if (!toggleItem.currentValue) {
        setSuccess(`Ship to customer enabled. Outbound delivery will be created automatically.`);
      }

      // Update local state
      if (deliveryData && deliveryData.data) {
        const updatedData = deliveryData.data.map((item) => {
          if (item.po_number === toggleItem.poNumber) {
            return {
              ...item,
              ship_to_customer: !toggleItem.currentValue,
            };
          }
          return item;
        });

        setDeliveryData({
          ...deliveryData,
          data: updatedData,
        });
      }

      setToggleItem(null);
      setShowConfirmation(false);
    } catch (error) {
      console.error("Error updating ship to customer status:", error);
      setError("Failed to update ship to customer status");
      setShowConfirmation(false);
    }
  };

  // Invoice modal handlers
  const handleViewInvoice = (invoiceId: string) => {
    console.log('Opening invoice view modal for ID:', invoiceId);
    
    // Close any existing modal first to ensure clean state
    if (isViewInvoiceOpen) {
      setIsViewInvoiceOpen(false);
      setViewInvoiceId(null);

      // Small delay to ensure the previous modal is fully unmounted
      setTimeout(() => {
        setViewInvoiceId(invoiceId);
        setIsViewInvoiceOpen(true);
        console.log('Invoice view modal opened with ID:', invoiceId);
      }, 100);
    } else {
      setViewInvoiceId(invoiceId);
      setIsViewInvoiceOpen(true);
      console.log('Invoice view modal opened with ID:', invoiceId);
    }
  };

  const handleCloseInvoiceView = () => {
    console.log('Closing invoice view modal');
    setIsViewInvoiceOpen(false);
    setViewInvoiceId(null);
    console.log('Invoice view modal closed');
  };

  const renderConfirmationDialog = () => {
    if (!showConfirmation) return null;

    let title = "";
    let message = "";
    let confirmAction = () => {};
    let showConfirmButton = true;
    let isQuantityMismatch = false;

    if (confirmationType === "quantity") {
      title = "Confirm Quantity Update";
      message = `Are you sure you want to update the quantity received to ${newQuantity}?`;
      confirmAction = handleQuantitySave;
    } else if (confirmationType === "date") {
      title = "Confirm Date Update";
      message = `Are you sure you want to update the expected date to ${formatDate(
        newDate
      )}?`;
      confirmAction = handleDateSave;
    } else if (confirmationType === "ship") {
      title = "Confirm Ship to Customer Toggle";
      message = `Are you sure you want to ${toggleItem?.currentValue ? "disable" : "enable"} ship to customer for this delivery?`;
      confirmAction = handleShipToCustomerConfirm;
    } else if (confirmationType === "warning-not-received") {
      title = "Incomplete Delivery";
      message = "The ordered quantity has not been fully received. Are you sure you want to enable Ship to Customer?";
      showConfirmButton = false; // Only show Cancel button
    } else if (confirmationType === "warning-quantity-mismatch") {
      title = "Confirm Quantity Mismatch";
      message = `Are you sure you ordered ${toggleItem?.quantityOrdered} but received ${toggleItem?.quantityReceived}?\nThe received quantity is more than what was ordered.`;
      // Check if this is from quantity update or ship-to-customer toggle
      if (editingQuantity) {
        // This is from quantity update
        confirmAction = handleQuantitySave;
      } else {
        // This is from ship-to-customer toggle
        confirmAction = handleShipToCustomerConfirm;
      }
      isQuantityMismatch = true;
    } else if (confirmationType === "warning-already-shipped") {
      title = "Action Cannot Be Undone";
      message = "Ship to Customer is already enabled and cannot be undone.";
      showConfirmButton = false; // Only show Close button
    }

    return (
      <ConfirmationDialog>
        <DialogContent>
          <DialogTitle style={{ 
            fontWeight: confirmationType === "warning-quantity-mismatch" ? 'bold' : 'normal' 
          }}>
            {title}
          </DialogTitle>
          <DialogMessage style={{ 
            whiteSpace: 'pre-line',
            fontWeight: isQuantityMismatch ? 'bold' : 'normal'
          }}>
            {message}
          </DialogMessage>
          <DialogActions>
            <DialogButton
              onClick={() => {
                setShowConfirmation(false);
                setEditingQuantity(null);
                setEditingDate(null);
                setToggleItem(null);
                setError(null); // Clear any error messages
              }}
              secondary
            >
              {confirmationType === "warning-already-shipped" ? "Close" : "Cancel"}
            </DialogButton>
            {showConfirmButton && (
              <DialogButton onClick={confirmAction}>Confirm</DialogButton>
            )}
          </DialogActions>
        </DialogContent>
      </ConfirmationDialog>
    );
  };

  const renderTable = () => {
    if (error) {
      return <ErrorMessage>{error}</ErrorMessage>;
    }

    const renderTableContent = () => {
      if (loading) {
        return (
          <LoadingRow>
            <td colSpan={10}>
              <LoadingContainer>
                <LoadingSpinner size="lg" />
                <LoadingText>Loading deliveries</LoadingText>
              </LoadingContainer>
            </td>
          </LoadingRow>
        );
      }

      if (!deliveryData || !deliveryData.data || deliveryData.data.length === 0) {
        return (
          <tr>
            <td colSpan={10} style={{ textAlign: 'center', padding: '2rem' }}>
              <EmptyStateTitle>No deliveries found</EmptyStateTitle>
              <EmptyStateMessage>
                {isDateMode 
                  ? selectedDate ? format(selectedDate, "dd MMMM yyyy") : "Loading"
                  : `PO: ${po_number}`}
              </EmptyStateMessage>
            </td>
          </tr>
        );
      }

      return deliveryData.data.map((item) => (
        <tr key={item.id}>
          <td>{item.po_number}</td>
          <td>{item.supplier_name}</td>
          <td>{item.sku}</td>
          <td>{item.quantity_ordered}</td>
          <td>
            <QuantityInputContainer>
              <QuantityInput
                type="number"
                defaultValue={item.quantity_received}
                min="0"
                max={item.quantity_ordered}
                disabled={item.ship_to_customer}
                onChange={(e) => {
                  // Don't allow changes if ship_to_customer is enabled
                  if (item.ship_to_customer) {
                    return;
                  }
                  
                  const newValue = parseInt(e.target.value);
                  // Only show action buttons if value has changed
                  if (newValue !== item.quantity_received) {
                    setEditingQuantity(item.po_number);
                    setNewQuantity(newValue);
                  } else {
                    setEditingQuantity(null);
                  }
                }}
              />
              {editingQuantity === item.po_number && !item.ship_to_customer && (
                <QuantityActionButtons>
                  <IconButton 
                    onClick={() => {
                      // Check if the new quantity is greater than ordered quantity
                      if (newQuantity > item.quantity_ordered) {
                        // Set up the toggle item for quantity mismatch warning
                        setToggleItem({
                          poNumber: item.po_number,
                          currentValue: false, // Not used for quantity updates
                          sku: item.sku,
                          description: item.supplier_name,
                          quantityReceived: newQuantity,
                          quantityOrdered: item.quantity_ordered
                        });
                        setConfirmationType("warning-quantity-mismatch");
                      } else {
                        setConfirmationType("quantity");
                      }
                      setShowConfirmation(true);
                    }}
                    title="Save"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M20 6L9 17l-5-5"></path>
                    </svg>
                  </IconButton>
                  <IconButton 
                    onClick={() => setEditingQuantity(null)}
                    title="Cancel"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <line x1="18" y1="6" x2="6" y2="18"></line>
                      <line x1="6" y1="6" x2="18" y2="18"></line>
                    </svg>
                  </IconButton>
                </QuantityActionButtons>
              )}
            </QuantityInputContainer>
          </td>
          <td>
            {item.linked_invoice ? (
              <InvoiceLink 
                onClick={(e) => {
                  e.preventDefault();
                  if (item.linked_invoice) {
                    handleViewInvoice(item.linked_invoice);
                  }
                }}
                style={{ cursor: 'pointer' }}
              >
                {item.linked_invoice}
              </InvoiceLink>
            ) : (
              "-"
            )}
          </td>
          <td>{item.ordered_by}</td>
          <td>{item.balance_order}</td>
          <td>
            <DateInputContainer>
              <DateInput
                type="date"
                defaultValue={item.new_expected_date || item.po_date}
                onChange={(e) => {
                  const newValue = e.target.value;
                  // Only show action buttons if value has changed
                  if (newValue !== (item.new_expected_date || item.po_date)) {
                    setEditingDate(item.po_number);
                    setNewDate(newValue);
                  } else {
                    setEditingDate(null);
                  }
                }}
              />
              {editingDate === item.po_number && (
                <QuantityActionButtons>
                  <IconButton 
                    onClick={() => {
                      setConfirmationType("date");
                      setShowConfirmation(true);
                    }}
                    title="Save"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <path d="M20 6L9 17l-5-5"></path>
                    </svg>
                  </IconButton>
                  <IconButton 
                    onClick={() => setEditingDate(null)}
                    title="Cancel"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                      <line x1="18" y1="6" x2="6" y2="18"></line>
                      <line x1="6" y1="6" x2="18" y2="18"></line>
                    </svg>
                  </IconButton>
                </QuantityActionButtons>
              )}
            </DateInputContainer>
          </td>
          <td>
            <ToggleContainer>
              <ToggleSwitch>
                <ToggleInput
                  type="checkbox"
                  checked={item.ship_to_customer}
                  onChange={() =>
                    handleShipToCustomerToggle(
                      item.po_number,
                      item.ship_to_customer,
                      item.quantity_received,
                      item.quantity_ordered,
                      item.sku,
                      item.supplier_name // Using supplier_name as description for now
                    )
                  }
                />
                <ToggleSlider className={item.ship_to_customer ? "disabled" : ""} />
              </ToggleSwitch>
            </ToggleContainer>
          </td>
        </tr>
      ));
    };

    return (
      <>
        <TableContainer>
          <Table>
            <TableHeader>
              <tr>
                <th>PO Number</th>
                <th>Supplier Name</th>
                <th>SKU</th>
                <th>Quantity Ordered</th>
                <th>Quantity Received</th>
                <th>Linked To Invoice Number</th>
                <th>Ordered By</th>
                <th>Balance Of Order</th>
                <th>New Expected Date</th>
                <th>Ship To Customer</th>
              </tr>
            </TableHeader>
            <TableBody>
              {renderTableContent()}
            </TableBody>
          </Table>
        </TableContainer>

        {success && (
          <div className="success-message" style={{ 
            padding: '10px', 
            margin: '10px 0', 
            backgroundColor: '#d4edda', 
            color: '#155724',
            borderRadius: '4px' 
          }}>
            {success}
          </div>
        )}

        {!loading && deliveryData && deliveryData.data && deliveryData.data.length > 0 && (
          <PaginationWrapper>
            <Pagination
              currentPage={currentPage}
              totalPages={Math.ceil(totalItems / itemsPerPage)}
              onPageChange={setCurrentPage}
              totalItems={totalItems}
              itemsPerPage={itemsPerPage}
              onItemsPerPageChange={(newItemsPerPage) => {
                setItemsPerPage(newItemsPerPage);
                setCurrentPage(1); // Reset to first page
              }}
              itemsPerPageOptions={[10, 25, 50, 100]}
              showItemsPerPage={true}
            />
          </PaginationWrapper>
        )}
      </>
    );
  };

  return (
    <Layout>
      <PageContainer>
        <HeaderRow>
          <BackLinkComponent to="/inbound-delivery" />
          <BreadcrumbContainer>
            <Link
              to="/inbound-delivery"
              style={{ color: "inherit", textDecoration: "none" }}
            >
              Inbound Delivery to BWA Dashboard
            </Link>
            <BreadcrumbSeparator>›</BreadcrumbSeparator>
            {isDateMode
              ? selectedDate
                ? format(selectedDate, "dd MMMM yyyy")
                : "Loading"
              : `PO: ${po_number}`}
          </BreadcrumbContainer>
        </HeaderRow>

        <SearchContainer>
          <SearchInput
            type="text"
            placeholder="Search a Supplier"
            value={searchTerm}
            onChange={handleSearchChange}
          />
          <SearchIcon>🔍</SearchIcon>
        </SearchContainer>

        {error && (
          <div className="error-message" style={{ 
            padding: '10px', 
            margin: '10px 0', 
            backgroundColor: '#f8d7da', 
            color: '#721c24',
            borderRadius: '4px' 
          }}>
            {error}
          </div>
        )}

        {renderTable()}
        {renderConfirmationDialog()}

        {/* Invoice View Modal */}
        {isViewInvoiceOpen && viewInvoiceId && (
          <InvoiceView
            invoiceId={viewInvoiceId}
            onClose={handleCloseInvoiceView}
            printMode={true}
          />
        )}
      </PageContainer>
    </Layout>
  );
};

export default InboundDeliveryDetailPage;
