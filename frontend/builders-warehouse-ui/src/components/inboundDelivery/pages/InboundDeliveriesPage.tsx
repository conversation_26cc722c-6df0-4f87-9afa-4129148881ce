import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import Layout from '../../layout/Layout';
import { useNavigate } from 'react-router-dom';
import Pagination from '../../common/Pagination';
import customerAddIcon from '../../../assets/customerAddIcon.png';
import { BackButtonComponent } from '../../ui/DesignSystem';

const PageContainer = styled.div`
  padding: 0rem;
`;

const HeaderContainer = styled.div`
  display: flex;
  flex-direction: column;
  margin-bottom: 24px;
`;

const TitleRow = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
`;

const ControlRow = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
`;

const PageTitle = styled.h1`
  color: #042B41;
  font-size: 32px;
  font-weight: 800;
  margin: 0;
`;

const AddButton = styled.button`
  display: flex;
  align-items: center;
  gap: 10px;
  background-color: #042B41;
  color: white;
  border: none;
  border-radius: 10px;
  padding: 12px 20px;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  
  &:hover {
    background-color: #0A3D5A;
  }
  
  img {
    width: 18px;
    height: 18px;
  }
`;

const SearchContainer = styled.div`
  position: relative;
  width: 250px;
`;

const SearchInput = styled.input`
  width: 100%;
  padding: 10px 10px;
  border: 1px solid #E5E7EB;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  
  &::placeholder {
    color: #9CA3AF;
  }
`;

const Table = styled.table`
  width: 100%;
  border-collapse: collapse;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border-radius: 8px;
  overflow: hidden;
`;

const TableHeader = styled.thead`
  background-color: #042B41;
  color: white;
  
  th {
    padding: 16px;
    text-align: left;
    font-weight: 500;
    font-size: 14px;
  }
`;

const TableBody = styled.tbody`
  tr {
    background-color: white;
    border-bottom: 1px solid #e0e0e0;
    
    &:last-child {
      border-bottom: none;
    }
    
    &:hover {
      background-color: #f9fafb;
    }
  }
  
  td {
    padding: 16px;
    color: #333;
    font-size: 14px;
  }
`;

const LoadingMessage = styled.div`
  text-align: center;
  padding: 2rem;
  color: #6B7280;
`;

const EmptyMessage = styled.div`
  text-align: center;
  padding: 2rem;
  color: #6B7280;
`;

interface InboundDelivery {
  id: string;
  poNumber: string;
  supplier: string;
  date: string;
  status: string;
}

const InboundDeliveriesPage: React.FC = () => {
  const navigate = useNavigate();
  const [searchQuery, setSearchQuery] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [loading, setLoading] = useState(false);
  const [deliveries, setDeliveries] = useState<InboundDelivery[]>([]);
  const [totalItems, setTotalItems] = useState(0);

  useEffect(() => {
    fetchDeliveries();
  }, [currentPage, itemsPerPage, searchQuery]);

  const fetchDeliveries = async () => {
    setLoading(true);
    try {
      // TODO: Replace with actual API call
      await new Promise(resolve => setTimeout(resolve, 1000)); // Simulate API delay
      setDeliveries([
        {
          id: '1',
          poNumber: 'PO-001',
          supplier: 'Supplier A',
          date: '2024-03-20',
          status: 'Pending'
        },
        // Add more mock data as needed
      ]);
      setTotalItems(1);
    } catch (error) {
      console.error('Error fetching deliveries:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleBack = () => {
    navigate('/home');
  };

  const handleAddDelivery = () => {
    // TODO: Implement add delivery functionality
    console.log('Add delivery clicked');
  };

  const handlePageChange = (pageNumber: number) => {
    setCurrentPage(pageNumber);
  };

  const handleItemsPerPageChange = (newItemsPerPage: number) => {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1);
  };

  return (
    <Layout>
      <PageContainer>
        <BackButtonComponent onClick={handleBack} label="Back" />
        
        <HeaderContainer>
          <TitleRow>
            <PageTitle>Inbound Deliveries</PageTitle>
          </TitleRow>
          
          <ControlRow>
            <SearchContainer>
              <SearchInput
                type="text"
                placeholder="Search"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
            </SearchContainer>
            
            <AddButton onClick={handleAddDelivery}>
              <img src={customerAddIcon} alt="Add" />
              Add Delivery
            </AddButton>
          </ControlRow>
        </HeaderContainer>

        {loading ? (
          <LoadingMessage>Loading deliveries</LoadingMessage>
        ) : (
          <>
            <Table>
              <TableHeader>
                <tr>
                  <th>PO Number</th>
                  <th>Supplier</th>
                  <th>Date</th>
                  <th>Status</th>
                </tr>
              </TableHeader>
              <TableBody>
                {deliveries.length === 0 ? (
                  <tr>
                    <td colSpan={4}>
                      <EmptyMessage>No deliveries found</EmptyMessage>
                    </td>
                  </tr>
                ) : (
                  deliveries.map((delivery) => (
                    <tr key={delivery.id}>
                      <td>{delivery.poNumber}</td>
                      <td>{delivery.supplier}</td>
                      <td>{delivery.date}</td>
                      <td>{delivery.status}</td>
                    </tr>
                  ))
                )}
              </TableBody>
            </Table>

            <Pagination
              currentPage={currentPage}
              totalPages={Math.ceil(totalItems / itemsPerPage)}
              onPageChange={handlePageChange}
              totalItems={totalItems}
              itemsPerPage={itemsPerPage}
              onItemsPerPageChange={handleItemsPerPageChange}
              itemsPerPageOptions={[10, 20, 50]}
              showItemsPerPage={true}
            />
          </>
        )}
      </PageContainer>
    </Layout>
  );
};

export default InboundDeliveriesPage; 