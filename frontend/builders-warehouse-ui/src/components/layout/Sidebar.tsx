import React from 'react';
import styled from 'styled-components';
import { Link, useLocation } from 'react-router-dom';
import { useAuth } from '../../context/AuthContext';
import { isAdmin } from '../../utils/roleUtils';
import hamburgerIcon from '../../assets/hamburger.svg';
import navDashboardIcon from '../../assets/navDashboardIcon.png';
import navCustomersIcon from '../../assets/navCustomersIcon.png';
import navInvoicesIcon from '../../assets/navInvoicesIcon.png';
import navQuotesIcon from '../../assets/navQuotesIcon.png';
import navInventoryIcon from '../../assets/navInventoryIcon.png';
import navOutboundDeliveryIcon from '../../assets/navOutboundDeliveryIcon.png';
import navSuppliersIcon from '../../assets/navSuppliersIcon.png';
import navPOManagementIcon from '../../assets/navPOManagementIcon.png';
import navInboundDeliveryIcon from '../../assets/navInboundDeliveryIcon.png';
import navReportsIcon from '../../assets/navReportsIcon.png';
import navUsersIcon from '../../assets/navUsersIcon.png';

interface SidebarProps {
  open: boolean;
  onClose: () => void;
}

const DrawerContainer = styled.div<{ open: boolean }>`
  position: fixed;
  top: 0;
  left: 0;
  height: 100vh;
  width: 260px;
  background-color: #042B41;
  color: #fff;
  z-index: 200;
  transform: translateX(${props => (props.open ? '0' : '-100%')});
  transition: transform 0.3s cubic-bezier(.4,0,.2,1);
  box-shadow: 2px 0 16px rgba(0,0,0,0.12);
  display: flex;
  flex-direction: column;
  overflow: hidden;
`;

const CloseButton = styled.button`
  background: none;
  border: none;
  color: #fff;
  font-size: 2rem;
  align-self: flex-end;
  margin: 16px 16px 0 0;
  cursor: pointer;
`;

const MenuList = styled.ul`
  list-style: none;
  padding: 0;
  margin: 0;
  margin-top: 2rem;
  flex: 1;
  overflow-y: auto;
  min-height: 0;
  
  /* Hide scrollbar but allow scrolling */
  scrollbar-width: none;
  -ms-overflow-style: none;
  &::-webkit-scrollbar {
    display: none;
  }
`;

const MenuItem = styled.li<{ isActive: boolean }>`
  margin-bottom: 0.5rem;
  
  a {
    display: flex;
    align-items: center;
    padding: 0.75rem 1.5rem;
    color: ${props => props.isActive ? '#FFFFFF' : '#F9FAFB'};
    background-color: ${props => props.isActive ? 'rgba(255, 255, 255, 0.15)' : 'transparent'};
    text-decoration: none;
    transition: all 0ms;
    font-weight: ${props => props.isActive ? '600' : '400'};
    border-radius: 6px;
    
    &:hover {
      background-color: ${props => props.isActive ? 'rgba(255, 255, 255, 0.2)' : 'rgba(255, 255, 255, 0.05)'};
      color: ${props => props.isActive ? '#FFFFFF' : '#FFFFFF'};
      animation-duration: 0ms;
    }
  }
`;

const IconWrapper = styled.span`
  margin-right: 0.75rem;
  display: flex;
  align-items: center;
  
  img {
    width: 24px;
    height: 20px;
    filter: brightness(0) invert(1);
    
    &[src*="navUsersIcon"] {
      width: 36px;
      height: 24px;
      transform: scaleX(1.2);
    }
    
    &.users-icon {
      width: 36px !important;
      height: 24px !important;
      transform: scaleX(1.3) !important;
    }
  }
`;

const MenuText = styled.span`
  display: inline;
`;

const BottomMenuSection = styled.div`
  margin-top: auto;
  padding: 1rem 1.5rem;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  flex-shrink: 0;
  min-height: 80px;
  display: flex;
  align-items: center;
`;

const LogoutButton = styled.button`
  display: flex;
  align-items: center;
  width: 100%;
  padding: 0.75rem;
  background: transparent;
  border: none;
  color: #F9FAFB;
  cursor: pointer;
  transition: all 0ms;
  font-size: 1rem;
  border-radius: 6px;
  
  &:hover {
    background-color: rgba(255, 255, 255, 0.05);
    color: #fff;
    animation-duration: 0ms;
  }
`;

const Sidebar: React.FC<SidebarProps> = ({ open, onClose }) => {
  const location = useLocation();
  const { user, logout } = useAuth();
  const userIsAdmin = isAdmin(user);

  const menuItems = [
    {
      path: '/home',
      label: 'Dashboard',
      icon: navDashboardIcon
    },
    {
      path: '/customers',
      label: 'Customers',
      icon: navCustomersIcon
    },
    {
      path: '/invoices',
      label: 'Invoices',
      icon: navInvoicesIcon
    },
    {
      path: '/quotes',
      label: 'Quotes',
      icon: navQuotesIcon
    },
    {
      path: '/inventory',
      label: 'Inventory',
      icon: navInventoryIcon
    },
    {
      path: '/outbound-delivery',
      label: 'Outbound Delivery from BWA',
      icon: navOutboundDeliveryIcon
    },
    {
      path: '/suppliers',
      label: 'Suppliers',
      icon: navSuppliersIcon
    },
    {
      path: '/po-management',
      label: 'PO Management',
      icon: navPOManagementIcon
    },
    {
      path: '/inbound-delivery',
      label: 'Inbound Delivery to BWA',
      icon: navInboundDeliveryIcon
    },
    {
      path: '/reports',
      label: 'Reports',
      icon: navReportsIcon
    },
  ];

  if (userIsAdmin) {
    menuItems.push({
      path: '/users',
      label: 'Users',
      icon: navUsersIcon
    });
  }

  const handleLogout = () => {
    logout();
  };

  return (
    <DrawerContainer open={open}>
      <CloseButton onClick={onClose} aria-label="Close navigation">&times;</CloseButton>
      <MenuList>
        {menuItems.map((item) => {
          let icon = item.icon;
          if (item.label === 'Dashboard') icon = navDashboardIcon;
          if (item.label === 'Invoices') icon = navInvoicesIcon;
          if (item.label === 'Users') icon = navUsersIcon;
          const isActive = location.pathname === item.path || 
                           (item.path !== '/home' && location.pathname.startsWith(item.path));
          return (
            <MenuItem key={item.path} isActive={isActive}>
              <Link to={item.path} onClick={onClose}>
                <IconWrapper>
                  <img 
                    src={icon} 
                    alt={item.label} 
                    className={item.label === 'Users' ? 'users-icon' : ''}
                  />
                </IconWrapper>
                <MenuText>{item.label}</MenuText>
              </Link>
            </MenuItem>
          );
        })}
      </MenuList>
      <BottomMenuSection>
        <LogoutButton onClick={handleLogout}>
          <IconWrapper>
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M16 17L21 12M21 12L16 7M21 12H9" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              <path d="M12 19C10.9391 19 9.92172 18.5786 9.17157 17.8284C8.42143 17.0783 8 16.0609 8 15V9C8 7.93913 8.42143 6.92172 9.17157 6.17157C9.92172 5.42143 10.9391 5 12 5" stroke="white" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
            </svg>
          </IconWrapper>
          <MenuText>Logout</MenuText>
        </LogoutButton>
      </BottomMenuSection>
    </DrawerContainer>
  );
};

export default Sidebar; 