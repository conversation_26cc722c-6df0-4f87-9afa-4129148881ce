import React from 'react';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import logoSvg from '../../assets/Builder-Logo.png';
import GlobalSearch from '../common/GlobalSearch';
import { useAuth } from '../../context/AuthContext';
import hamburgerIcon from '../../assets/hamburger.svg';
import userIcon from '../../assets/user.png';

interface NavbarProps {
  onHamburgerClick: () => void;
}

const NavbarContainer = styled.nav`
  height: 64px;
  background-color: white;
  border-bottom: 1px solid #E5E7EB;
  padding: 0 1.5rem;
  display: flex;
  align-items: center;
  justify-content: space-between;
  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  z-index: 10;
`;

const HamburgerButton = styled.button`
  background: none;
  border: none;
  margin-right: 1.5rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  padding: 8px;

  img {
    width: 28px;
    height: 28px;
    filter: invert(0.2);
  }
`;

const SearchContainer = styled.div`
  flex: 1;
  max-width: 657px;
  margin: 0 2rem;
`;

const UserMenu = styled.div`
  display: flex;
  align-items: center;
  background: #f5f7fa;
  border-radius: 10px;
  padding: 5px 18px;
  border: 1px solid #E5E7EB;
  width: 110px;
  height: 34px;
  gap: 10px;
`;

const UserInfo = styled.div`
  display: flex;
  align-items: center;
`;

const UserName = styled.span`
  font-weight: 500;
  color: #4F5D75;
  font-size: 14px;
`;

const UserIconWrapper = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;
  
  img {
    width: 20px;
    height: 20px;
  }
`;

const LogoSection = styled.div`
  display: flex;
  align-items: center;
  justify-content: center;

  img.logo {
    width: 179.21px;
    height: 52.71px;
    cursor: pointer;
  }
`;

const Navbar: React.FC<NavbarProps> = ({ onHamburgerClick }) => {
  const { user } = useAuth();
  const navigate = useNavigate();

  const handleLogoClick = () => {
    navigate('/home');
  };

  const getUserInitials = () => {
    if (!user || !user.user_name) return '?';

    const userName = user.user_name;
    if (userName.includes('@')) {
      // If user_name is an email, use the first letter of the part before @
      return userName.split('@')[0].charAt(0).toUpperCase();
    }

    // Otherwise use first letter of user_name
    return userName.charAt(0).toUpperCase();
  };

  // Get display name from user data
  const getDisplayName = () => {
    if (!user) return 'Loading';
    
    // If we have user_name, use it
    if (user.user_name) {
      // If it's an email, show only the part before @
      if (user.user_name.includes('@')) {
        return user.user_name.split('@')[0];
      }
      return user.user_name;
    }
    
    // Fallback to email if available
    if (user.email) {
      return user.email.split('@')[0];
    }
    
    return 'User';
  };

  return (
    <NavbarContainer>
      <LogoSection>
        <HamburgerButton onClick={onHamburgerClick} aria-label="Open navigation">
          <img src={hamburgerIcon} alt="Menu" />
        </HamburgerButton>
        <img
          src={logoSvg}
          alt="Builders Warehouse"
          className="logo logo-image"
          onClick={handleLogoClick}
        />
      </LogoSection>
      <SearchContainer className="responsive-stack">
        <GlobalSearch className="global-search-input" />
      </SearchContainer>
      <UserMenu>
        <UserInfo>
          <UserName>{getDisplayName()}</UserName>
        </UserInfo>
        <UserIconWrapper>
          <img src={userIcon} alt="User" />
        </UserIconWrapper>
      </UserMenu>
    </NavbarContainer>
  );
};

export default Navbar;