import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { useLocation } from 'react-router-dom';
import Navbar from './Navbar';
import Sidebar from './Sidebar';

const LayoutContainer = styled.div`
  display: flex;
  min-height: 100vh;
`;

const MainContent = styled.main`
  flex: 1;
  background-color: #F9FAFB;
  min-height: 100vh;
`;

const ContentArea = styled.div`
  flex: 1;
  margin-top: 64px;
  height: calc(100vh - 64px);
  overflow: hidden;
  position: relative;

  /* Hide scrollbar but allow scrolling if needed */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
  &::-webkit-scrollbar {
    display: none; /* Chrome, Safari, Opera */
  }

  @media (min-width: 768px) {
    padding: 0;
  }
  
  /* Home page specific styling only */
  &.home-content {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
    overflow: hidden;
  }
  
  /* Non-home pages get padding and can scroll */
  &:not(.home-content) {
    padding: 0.5rem;
    overflow: auto;
    
    @media (min-width: 768px) {
      padding: 1rem;
    }
  }
`;

const Backdrop = styled.div`
  position: fixed;
  top: 0; left: 0; right: 0; bottom: 0;
  background: rgba(0,0,0,0.25);
  z-index: 99;
`;

interface LayoutProps {
  children: React.ReactNode;
}

const Layout: React.FC<LayoutProps> = ({ children }) => {
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const location = useLocation();
  const isHomePage = location.pathname === '/home' || location.pathname === '/';

  useEffect(() => {
    // Add a class to the body element for the home page
    if (isHomePage) {
      document.body.classList.add('home-page');
    } else {
      document.body.classList.remove('home-page');
    }
    return () => {
      document.body.classList.remove('home-page');
    };
  }, [isHomePage]);

  const handleSidebarOpen = () => setSidebarOpen(true);
  const handleSidebarClose = () => setSidebarOpen(false);

  return (
    <LayoutContainer>
      <Sidebar open={sidebarOpen} onClose={handleSidebarClose} />
      <MainContent>
        <Navbar onHamburgerClick={handleSidebarOpen} />
        {sidebarOpen && <Backdrop onClick={handleSidebarClose} />}
        <ContentArea className={isHomePage ? 'home-content' : ''}>
          {children}
        </ContentArea>
      </MainContent>
    </LayoutContainer>
  );
};

export default Layout;