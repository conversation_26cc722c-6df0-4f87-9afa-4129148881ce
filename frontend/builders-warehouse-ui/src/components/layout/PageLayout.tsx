import React from 'react';
import styled from 'styled-components';
import BackButton from '../common/BackButton';

const PageContainer = styled.div`
  min-height: 100vh;
  background-color: #F9FAFB;
  display: flex;
  flex-direction: column;
`;

const ContentContainer = styled.div`
  flex: 1;
  padding: 24px;
  background-color: #F9FAFB;
`;

interface PageLayoutProps {
  children: React.ReactNode;
  backButtonLabel?: string;
  showBackButton?: boolean;
}

const PageLayout: React.FC<PageLayoutProps> = ({
  children,
  backButtonLabel = 'Back',
  showBackButton = true,
}) => {
  return (
    <PageContainer>
      {showBackButton && <BackButton label={backButtonLabel} />}
      <ContentContainer>
        {children}
      </ContentContainer>
    </PageContainer>
  );
};

export default PageLayout; 