import React, { useState, useEffect, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';

const SearchContainer = styled.div`
  position: relative;
  width: 300px;
`;

const SearchInput = styled.input`
  width: 100%;
  padding: 8px 40px 8px 15px;
  border-radius: 20px;
  border: 1px solid #e5e7eb;
  font-size: 14px;
  transition: all 0.2s;
  background-color: #f9fafb;
  
  &:focus {
    outline: none;
    border-color: #042B41;
    background-color: white;
    box-shadow: 0 0 0 2px rgba(4, 43, 65, 0.1);
  }
  
  &::placeholder {
    color: #9ca3af;
  }
`;

const SearchIcon = styled.div`
  position: absolute;
  right: 15px;
  top: 50%;
  transform: translateY(-50%);
  color: #9ca3af;
  display: flex;
  align-items: center;
  justify-content: center;
`;

const ResultsContainer = styled.div`
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  margin-top: 8px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  max-height: 300px;
  overflow-y: auto;
  z-index: 100;
`;

const ResultItem = styled.div`
  padding: 12px 16px;
  cursor: pointer;
  border-bottom: 1px solid #f3f4f6;
  
  &:last-child {
    border-bottom: none;
  }
  
  &:hover {
    background-color: #f9fafb;
  }
`;

const ResultTitle = styled.div`
  font-weight: 500;
  font-size: 14px;
  color: #111827;
  margin-bottom: 2px;
`;

const ResultDetails = styled.div`
  font-size: 12px;
  color: #6b7280;
`;

interface SearchResult {
  id: string;
  title: string;
  details: string;
  route: string;
}

const GlobalSearch: React.FC = () => {
  const [query, setQuery] = useState('');
  const [results, setResults] = useState<SearchResult[]>([]);
  const [showResults, setShowResults] = useState(false);
  const searchRef = useRef<HTMLDivElement>(null);
  const navigate = useNavigate();
  
  // Mock search results
  useEffect(() => {
    if (!query.trim()) {
      setResults([]);
      return;
    }
    
    // Simulate API delay
    const timer = setTimeout(() => {
      // Mock data - in a real app, this would be an API call
      const mockResults: SearchResult[] = [
        {
          id: '1',
          title: 'Invoice #INV-001',
          details: 'Customer: ABC Construction - $2,500.50',
          route: '/invoices/1',
        },
        {
          id: '2',
          title: 'XYZ Builders',
          details: 'Customer ID: 102 - 5 orders',
          route: '/customers/102',
        },
        {
          id: '3',
          title: 'Pine Lumber 2x4x8',
          details: 'SKU: LUM-001 - 250 in stock',
          route: '/inventory?search=LUM-001',
        },
      ].filter(item => 
        item.title.toLowerCase().includes(query.toLowerCase()) ||
        item.details.toLowerCase().includes(query.toLowerCase())
      );
      
      setResults(mockResults);
    }, 300);
    
    return () => clearTimeout(timer);
  }, [query]);
  
  // Handle clicks outside search component
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setShowResults(false);
      }
    };
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);
  
  const handleFocus = () => {
    if (query.trim()) {
      setShowResults(true);
    }
  };
  
  const handleResultClick = (route: string) => {
    navigate(route);
    setShowResults(false);
    setQuery('');
  };
  
  return (
    <SearchContainer ref={searchRef}>
      <SearchInput
        placeholder="Search invoices, customers, inventory"
        value={query}
        onChange={e => setQuery(e.target.value)}
        onFocus={handleFocus}
      />
      <SearchIcon>
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path d="M21 21L15 15M17 10C17 13.866 13.866 17 10 17C6.13401 17 3 13.866 3 10C3 6.13401 6.13401 3 10 3C13.866 3 17 6.13401 17 10Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
        </svg>
      </SearchIcon>
      
      {showResults && results.length > 0 && (
        <ResultsContainer>
          {results.map(result => (
            <ResultItem 
              key={result.id}
              onClick={() => handleResultClick(result.route)}
            >
              <ResultTitle>{result.title}</ResultTitle>
              <ResultDetails>{result.details}</ResultDetails>
            </ResultItem>
          ))}
        </ResultsContainer>
      )}
    </SearchContainer>
  );
};

export default GlobalSearch; 