import React, { useEffect, useState } from 'react';
import { Navigate, useLocation, useNavigate } from 'react-router-dom';
import { useError } from '../common/ErrorHandler';
import { hasRole } from '../../utils/roleUtils';
import { User } from '../../context/AuthContext';
 
interface ProtectedRouteProps {
  children: React.ReactNode;
  isAuthenticated: boolean;
  requiredRole?: string;
  userRole?: string;
  user?: User | null;
}
 
const ProtectedRoute: React.FC<ProtectedRouteProps> = ({
  children,
  isAuthenticated,
  requiredRole,
  userRole,
  user
}) => {
  const location = useLocation();
  const navigate = useNavigate();
  const { showError } = useError();
  const [isLoading, setIsLoading] = useState(true);
 
  // Check if the current path is customer-related
  const isCustomerPath = location.pathname.includes('/customers');
 
  // For debugging purposes - memoize to prevent unnecessary re-renders
  const debugInfo = React.useMemo(() => ({
      path: location.pathname,
      isAuthenticated,
      isCustomerPath,
      requiredRole,
      userRole,
      user: user ? 'present' : 'null'
  }), [location.pathname, isAuthenticated, isCustomerPath, requiredRole, userRole, user]);
 
  useEffect(() => {
    console.log("ProtectedRoute render:", debugInfo);
  }, [debugInfo]);
 
  // Handle loading state based on authentication data - use useCallback to prevent re-creation
  const updateLoadingState = React.useCallback(() => {
    if (isAuthenticated && (user || userRole)) {
      setIsLoading(false);
    } else if (!isAuthenticated) {
      // If not authenticated, don't show loading
      setIsLoading(false);
    }
  }, [isAuthenticated, user, userRole]);

  useEffect(() => {
    updateLoadingState();
  }, [updateLoadingState]);
 
  // Skip authentication check for customer paths
  if (isCustomerPath) {
    console.log("Customer path detected - bypassing authentication check");
    return <>{children}</>;
  }
 
  // Show loading state while we're waiting for authentication data
  if (isLoading && isAuthenticated) {
    return <div>Loading...</div>;
  }
 
  // If not authenticated, redirect to login
  if (!isAuthenticated) {
    console.log("Not authenticated, redirecting to login");
    return <Navigate to="/" state={{ from: location }} replace />;
  }
 
  // Check role requirements
  if (requiredRole && requiredRole !== "all") {
    // If we have the full user object, use the more robust role checking
    if (user) {
      if (!hasRole(user, requiredRole)) {
        console.log(`Role mismatch using user object: required=${requiredRole}`);
        showError("You don't have permission to access this page", "Access Denied");
        return <Navigate to="/home" replace />;
      }
    } else if (userRole !== requiredRole) {
      // Fall back to the simple string comparison if only role string is provided
      console.log(`Role mismatch: required=${requiredRole}, user has=${userRole}`);
      showError("You don't have permission to access this page", "Access Denied");
      return <Navigate to="/home" replace />;
    }
  }
 
  // Render children if authenticated and authorized
  return <>{children}</>;
};
 
export default ProtectedRoute;