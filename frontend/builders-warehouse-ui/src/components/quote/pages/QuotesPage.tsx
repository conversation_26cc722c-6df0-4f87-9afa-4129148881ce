import React, { useState, useEffect, useCallback, useRef } from 'react';
import styled from 'styled-components';
import Layout from '../../layout/Layout';
import { Link, useNavigate } from 'react-router-dom';
import Pagination from '../../common/Pagination';
import DateRangePicker from '../../common/DateRangePicker';
import quotesNotesIcon from '../../../assets/quotesNotesIcon.png';
import quotesCreateIcon from '../../../assets/quotesCreateIcon.png';
import backButtonIcon from '../../../assets/backButton.png';
import customerAddIcon from '../../../assets/customerAddIcon.png';
import quoteService, { Quote as QuoteType, QuoteFilter } from '../../../services/quoteService';
import { format, parseISO } from 'date-fns';
import { CustomBackButton } from '../../ui/DesignSystem';
import apiClient from '../../../services/apiClient';
import PageLoadingSpinner from '../../ui/PageLoadingSpinner';
import { useToast } from '../../../hooks/useToast';
import { Toast } from '../../common/Toast';
import LoadingSpinner from '../../ui/LoadingSpinner';

// Format date as YYYY-MM-DD for backend API
const formatDateForBackend = (date: Date): string => {
  return date.toISOString().split('T')[0];
};

// Extended quote service with necessary methods for our component
const extendedQuoteService = {
  ...quoteService,
  // Update the method to handle archived flag
  getQuotes: async (filters: QuoteFilter & { is_archived?: boolean }): Promise<any> => {
    // Use the archived/current API endpoints based on the tab
    if (filters.is_archived) {
      // Remove extra flags not part of the original QuoteFilter
      const { is_archived, ...standardFilters } = filters;
      return quoteService.getArchivedQuotes(standardFilters);
    } else {
      const { is_archived, ...standardFilters } = filters;
      return quoteService.getCurrentQuotes(standardFilters);
    }
  }
};

const PageContainer = styled.div`
  padding: 0 32px 32px 32px;
`;

const HeaderContainer = styled.div`
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
`;

// Back button without text, matching invoice style
const StyledBackButton = styled(CustomBackButton)`
  display: flex;
  align-items: center;
  margin: 0;
  padding: 0;
  background: none;
  border: none;
  outline: none;
`;

// Create container for back button and title
const TitleContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 10px;
`;

const BackIcon = styled.img`
  width: 24px;
  height: 24px;
  margin-right: 8px;
`;

const FiltersContainer = styled.div`
  display: flex;
  gap: 15px;
  margin-bottom: 20px;
  align-items: center;
`;

const TabContainer = styled.div`
  display: flex;
  width: 321px;
  height: 44px;
  border-radius: 10px;
  border: 0.5px solid #E5E7EB;
  overflow: hidden;
`;

const Tab = styled.button<{ active: boolean }>`
  flex: 1;
  padding: 12px;
  background: ${props => props.active ? '#042B41' : 'white'};
  border: none;
  font-size: 14px;
  font-weight: 500;
  color: ${props => props.active ? 'white' : '#6B7280'};
  cursor: pointer;
  
  &:first-child {
    border-right: 0.5px solid #E5E7EB;
  }
`;

const SearchContainer = styled.div`
  position: relative;
  width: 193px;
  height: 42px;
`;

const SearchInput = styled.input`
  width: 100%;
  height: 100%;
  padding: 10px 12px 10px 38px;
  border: 1px solid #E5E7EB;
  border-radius: 10px;
  font-size: 16px;
  font-family: 'Avenir', sans-serif;
  font-weight: 500;
  background-color: white;
  
  &::placeholder {
    color: #9CA3AF;
  }
`;

const SearchIconWrapper = styled.div`
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #9CA3AF;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: none;
  z-index: 1;
`;

const DatePickerWrapper = styled.div`
  width: 245px;
  height: 42px;
  background: rgba(255, 255, 255, 1);
  
  /* Override DateRangePicker styles for this page */
  & > div {
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 1);
  }
  
  .calendar-icon {
    width: 18px;
    height: 18px;
    margin-right: 10px;
  }
  
  span {
    opacity: 0.6;
  }
`;

const PageTitle = styled.h1`
  color: #042B41;
  font-size: 32px;
  font-weight: 800;
  margin: 0;
`;

const CreateButton = styled.button`
  display: flex;
  align-items: center;
  gap: 10px;
  background-color: #042B41;
  color: white;
  border: none;
  border-radius: 10px;
  padding: 12px 20px;
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  margin-left: auto;
  
  &:hover {
    background-color: #0A3D5A;
  }
  
  img {
    width: 18px;
    height: 18px;
  }
`;

const TableContainer = styled.div`
  width: 100%;
`;

const Table = styled.table`
  width: 100%;
  border-collapse: separate;
  border-spacing: 0;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid #e0e0e0;
`;

const TableHeader = styled.thead`
  background-color: #042B41;
  color: white;
  
  th {
    padding: 10px 12px;
    text-align: left;
    font-weight: 500;
    font-size: 14px;
    height: 48px;
    vertical-align: middle;
    border: 1px solid #0A3D5A;
    
    &:first-child {
      border-top-left-radius: 8px;
    }
    
    &:last-child {
      text-align: center;
      border-top-right-radius: 8px;
    }
  }
`;

const TableBody = styled.tbody`
  tr {
    height: 48px;
    background-color: white;
    border-bottom: 1px solid #e0e0e0;
    
    &:last-child {
      border-bottom: none;
    }
    
    &:hover {
      background-color: #f9fafb;
    }
    
    td {
      padding: 10px 12px;
      color: #374151;
      font-size: 14px;
      font-weight: 500;
      border: 1px solid #E5E7EB;
      text-align: left;
      vertical-align: middle;
      
      &:nth-child(4),
      &:nth-child(5)
      {
        text-align: right;
      }
    }
  }
`;

const DebugQuoteNumber = styled.span`
  color: #9F1239;
  font-weight: 600;
`;

const NotesIcon = styled.span<{ hasNotes?: boolean }>`
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  padding: 3px;
  
  img {
    width: 18px;
    height: 18px;
    filter: ${props => props.hasNotes ? 'invert(20%) sepia(99%) saturate(2476%) hue-rotate(343deg) brightness(94%) contrast(95%)' : 'none'};
  }
`;

const ToggleContainer = styled.div`
  display: flex;
  justify-content: center;
`;

const ToggleSwitch = styled.label`
  position: relative;
  display: inline-block;
  width: 44px;
  height: 22px;
`;

const ToggleInput = styled.input`
  opacity: 0;
  width: 0;
  height: 0;
  
  &:checked + span {
    background-color: #10B981;
  }
  
  &:checked + span:before {
    transform: translateX(22px);
  }
`;

const ToggleSlider = styled.span`
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #e0e0e0;
  transition: .3s;
  border-radius: 11px;
  
  &:before {
    position: absolute;
    content: "";
    height: 18px;
    width: 18px;
    left: 2px;
    bottom: 2px;
    background-color: white;
    transition: .3s;
    border-radius: 50%;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  }
`;

const PaginationContainer = styled.div`
  display: flex;
  justify-content: flex-end;
  margin-top: 20px;
`;

// Modal components
const ModalOverlay = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
`;

const ModalContent = styled.div`
  background-color: white;
  border-radius: 8px;
  width: 100%;
  max-width: 500px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  padding: 20px;
`;

const ModalTitle = styled.h2`
  font-size: 1.5rem;
  color: #333;
  margin: 0 0 20px 0;
  text-align: center;
`;

const NotesTextarea = styled.textarea`
  width: 100%;
  height: 150px;
  padding: 12px 15px;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  margin-bottom: 20px;
  resize: none;
  font-size: 14px;
  
  &:focus {
    outline: none;
    border-color: #042B41;
    box-shadow: 0 0 0 2px rgba(4, 43, 65, 0.1);
  }
`;

const ButtonGroup = styled.div`
  display: flex;
  justify-content: flex-end;
  gap: 12px;
`;

const Button = styled.button<{ primary?: boolean }>`
  padding: 10px 16px;
  border: ${props => props.primary ? 'none' : '1px solid #E5E7EB'};
  border-radius: 6px;
  background-color: ${props => props.primary ? '#042B41' : 'white'};
  color: ${props => props.primary ? 'white' : '#333'};
  font-weight: 500;
  font-size: 14px;
  cursor: pointer;
  
  &:hover {
    background-color: ${props => props.primary ? '#0A3D5A' : '#F9FAFB'};
  }
`;

const ConvertToggleContainer = styled.div`
  display: flex;
  justify-content: center;
  gap: 6px;
`;

const ConvertContainer = styled.div`
  display: flex;
  justify-content: center;
  gap: 6px;
`;

const ConvertButton = styled.button`
  background-color: #042B41;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 3px 8px;
  font-size: 12px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  
  &:hover {
    background-color: #0A3D5A;
  }
`;

// Add ActionButtonsContainer for consistent styling
const ActionButtonsContainer = styled.div`
  display: flex;
  justify-content: center;
  gap: 6px;
`;

// Add a RenewButton component
const RenewButton = styled.button`
  background-color: #042B41;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 3px 8px;
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  
  &:hover {
    background-color: #0A3D5A;
  }
`;

// Center the button in the modal
const CenteredButtonGroup = styled.div`
  display: flex;
  justify-content: center;
  margin-top: 16px;
`;

// Remove skeleton components and replace with consistent loading
const LoadingRow = styled.tr`
  td {
    text-align: center !important;
    padding: 40px 20px !important;
    border: none !important;
  }
`;

const LoadingContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 12px;
`;

const LoadingText = styled.p`
  margin: 0;
  font-size: 14px;
  color: #6B7280;
  font-weight: 500;
`;

interface QuoteDisplayItem {
  id: string;
  quote_no: string;  // This might come from either quote_no or quote_number in the API
  company_name: string;
  customer_id: string; 
  store_type: string;
  date: string;
  grand_total_aud: number;
  notes?: string;
  status?: string;
}

// Update the filters interface to include conversion status
interface QuoteFilters {
  is_archived?: boolean; // Keep for API compatibility
  search?: string;
  start_date?: string;
  end_date?: string;
  page?: number;
  limit?: number;
}

const QuotesPage: React.FC = () => {
  const navigate = useNavigate();
  const [quotes, setQuotes] = useState<QuoteDisplayItem[]>([]);
  const [currentTab, setCurrentTab] = useState<'current' | 'archived'>('current');
  const [searchTerm, setSearchTerm] = useState('');
  const [dateRange, setDateRange] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [totalItems, setTotalItems] = useState(0);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [selectedQuoteId, setSelectedQuoteId] = useState<string | null>(null);
  const [isToggleModalOpen, setIsToggleModalOpen] = useState(false);
  const [isRenewModalOpen, setIsRenewModalOpen] = useState(false);
  const [isNotesModalOpen, setIsNotesModalOpen] = useState(false);
  const [currentNotes, setCurrentNotes] = useState('');
  
  // State for filters
  const [filters, setFilters] = useState<QuoteFilters>({
    is_archived: false,
    search: '',
    start_date: '',
    end_date: ''
  });

  // Add a ref to track API calls in progress
  const apiCallInProgress = useRef<boolean>(false);

  const toast = useToast();

  // Compute total pages based on total items and items per page
  const totalPages = Math.max(1, Math.ceil(totalItems / itemsPerPage));
  
  // Initialize with default date range - last 30 days to today for current tab
  useEffect(() => {
    // Set default date range: last 30 days to today for current tab
    const today = new Date();
    const thirtyDaysAgo = new Date(today);
    thirtyDaysAgo.setDate(today.getDate() - 30);
    
    // Format dates in DD/MM/YYYY format for display
    const formattedStart = format(thirtyDaysAgo, 'dd/MM/yyyy');
    const formattedEnd = format(today, 'dd/MM/yyyy');
    const formattedRange = `${formattedStart} - ${formattedEnd}`;
    
    // Update state
    setDateRange(formattedRange);
    
    // Also update filters with dates in YYYY-MM-DD format for backend
    setFilters(prevFilters => ({
      ...prevFilters,
      start_date: formatDateForBackend(thirtyDaysAgo),
      end_date: formatDateForBackend(today)
    }));
    
    console.log('Set initial date range for current tab:', formattedRange);
  }, []);

  const fetchQuotes = async () => {
    // Skip if an API call is already in progress
    if (apiCallInProgress.current) {
      console.log('API call already in progress, skipping duplicate request');
      return;
    }
    
    apiCallInProgress.current = true;
    setIsLoading(true);
    setError(null);

    try {
      // Determine if we're fetching archived quotes based on current tab
      const fetchArchived = currentTab === 'archived';
      
      // Create filter object for the quoteService
      const filterParams: QuoteFilter = {
        search: filters.search,
        start_date: filters.start_date,
        end_date: filters.end_date,
        page: currentPage,
        limit: itemsPerPage
      };
      
      console.log(`Fetching ${fetchArchived ? 'archived' : 'current'} quotes with params:`, filterParams);
      
      // Use the appropriate quoteService method based on tab
      let response;
      if (fetchArchived) {
        response = await quoteService.getArchivedQuotes(filterParams);
      } else {
        response = await quoteService.getCurrentQuotes(filterParams);
      }
      
      // Process API response
      if (response) {
        console.log('Quote data received:', response);
        
        // Map the response data to our display format with better field handling
        const displayData = Array.isArray(response.items) 
          ? response.items.map((item: any) => {
              // Improve customer name handling
              let customerName = 'Unknown';
              if (item.customer?.name) {
                customerName = item.customer.name;
              } else if (item.customer_name) {
                customerName = item.customer_name;
              } else if (typeof item.customer === 'string') {
                customerName = item.customer;
              }
              
              // Improve store type handling
              let storeType = 'Unknown';
              if (item.store_type?.name) {
                storeType = item.store_type.name;
              } else if (item.store_type_name) {
                // Convert numeric IDs to names if needed
                if (item.store_type_name === '1') storeType = 'Cranbourne';
                else if (item.store_type_name === '2') storeType = 'Sale';
                else storeType = item.store_type_name;
              } else if (typeof item.store_type === 'string') {
                storeType = item.store_type;
              } else if (typeof item.store_type === 'number') {
                if (item.store_type === 1) storeType = 'Cranbourne';
                else if (item.store_type === 2) storeType = 'Sale';
              }
              
              return {
                id: item.id,
                quote_no: item.quote_number?.formatted_number || item.quote_no || 'No Number',
                company_name: customerName,
                customer_id: item.customer?.id || item.customer_id || '',
                store_type: storeType,
                date: item.quote_date || item.date ? formatDate(item.quote_date || item.date) : 'Unknown',
                grand_total_aud: item.grand_total || 0,
                notes: item.notes || '',
                status: item.status || 'draft'
              };
            })
          : [];
        
        console.log(`Mapped ${displayData.length} quotes for display`);
        setQuotes(displayData);
        setTotalItems(response.total || displayData.length);
      } else {
        console.log('No response data received');
        setQuotes([]);
        setTotalItems(0);
      }
    } catch (error) {
      console.error('Error fetching quotes:', error);
      setError('Failed to load quotes. Please try again.');
      setQuotes([]);
      setTotalItems(0);
    } finally {
      setIsLoading(false);
      apiCallInProgress.current = false;
    }
  };

  // Add fetchQuotes as a dependency to useEffect
  useEffect(() => {
    fetchQuotes();
  }, [currentPage, itemsPerPage, filters]); // eslint-disable-line react-hooks/exhaustive-deps

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const searchValue = e.target.value;
    setSearchTerm(searchValue);
    
    // Update filters with search term
    setFilters({
      ...filters,
      search: searchValue || undefined
    });
    
    // Reset to first page
    setCurrentPage(1);
  };

  const handleDateRangeChange = (newDateRange: string) => {
    console.log('User selected date range:', newDateRange);
    setDateRange(newDateRange);
    
    // Parse the date range and update filters
    if (newDateRange) {
      const [startStr, endStr] = newDateRange.split(' - ');
      
      try {
        // Parse from DD/MM/YYYY format
        const [startDay, startMonth, startYear] = startStr.split('/').map(Number);
        const [endDay, endMonth, endYear] = endStr.split('/').map(Number);
        
        const startDate = new Date(startYear, startMonth - 1, startDay);
        const endDate = new Date(endYear, endMonth - 1, endDay);
        
        // Validate date range (30 days)
        const diffTime = Math.abs(endDate.getTime() - startDate.getTime());
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        
        if (diffDays > 30) {
          // If range is more than 30 days, adjust start date to be 30 days before end date
          startDate.setDate(endDate.getDate() - 30);
        }
        
        // Format dates for backend (YYYY-MM-DD)
        const formattedStartDate = formatDateForBackend(startDate);
        const formattedEndDate = formatDateForBackend(endDate);
        
        console.log(`Setting custom date range: ${formattedStartDate} to ${formattedEndDate}`);
        
        // Update filters with new date range
        setFilters(prevFilters => ({
          ...prevFilters,
          start_date: formattedStartDate,
          end_date: formattedEndDate
        }));
        
        // Reset to first page
        setCurrentPage(1);
      } catch (err) {
        console.error("Error parsing date range:", err);
      }
    }
  };

  const handleConversionToggle = (quoteId: string) => {
    // Find the quote to check its status
    const quote = quotes.find(q => q.id === quoteId);
    
    // Only show modal if quote is not already converted
    if (quote && quote.status !== 'converted') {
      setSelectedQuoteId(quoteId);
      setIsToggleModalOpen(true);
    }
  };

  const handleConfirmConversion = async () => {
    if (!selectedQuoteId) return;
    
    setIsLoading(true);
    try {
      // Call API to convert quote to invoice
      await quoteService.convertToInvoice(selectedQuoteId);
      
      // Show success message
      toast.showToast('Quote successfully converted to invoice! Check the Invoices page to view the new invoice.', { type: 'success' });
      
      // Refresh quotes list to show the quote is still there
      fetchQuotes();
      setIsToggleModalOpen(false);
    } catch (error) {
      console.error('Error converting quote to invoice:', error);
      toast.showToast('Failed to convert quote to invoice. Please try again.', { type: 'error' });
    } finally {
      setIsLoading(false);
    }
  };

  const formatDate = (dateStr: string): string => {
    if (!dateStr) return '';
    const parsedDate = parseISO(dateStr);
    return format(parsedDate, 'dd/MM/yyyy');
  };

  const handlePageChange = (pageNumber: number) => {
    setCurrentPage(pageNumber);
  };

  const handleTabChange = (tab: 'current' | 'archived') => {
    if (tab === currentTab) return; // Don't do anything if tab hasn't changed
    
    console.log(`Switching from ${currentTab} tab to ${tab} tab`);
    setCurrentTab(tab);
    setCurrentPage(1); // Reset to first page when changing tabs
    
    // Set date range based on the selected tab
    const today = new Date();
    let startDate = new Date(today);
    let endDate = new Date(today);
    
    if (tab === 'current') {
      // Current tab: Last 30 days to today
      startDate.setDate(today.getDate() - 30);
      endDate = today;
    } else {
      // Archived tab: Very old date to 30 days ago
      startDate = new Date('2020-01-01'); // Very old date to get all old quotes
      endDate.setDate(today.getDate() - 30);
    }
    
    // Format dates for display
    const formattedStartDate = format(startDate, 'dd/MM/yyyy');
    const formattedEndDate = format(endDate, 'dd/MM/yyyy');
    const formattedDateRange = `${formattedStartDate} - ${formattedEndDate}`;
    
    // Update state
    setDateRange(formattedDateRange);
    
    // Update filters for API call
    setFilters({
      ...filters,
      is_archived: tab === 'archived',
      start_date: formatDateForBackend(startDate),
      end_date: formatDateForBackend(endDate)
    });
    
    console.log(`Set date range for ${tab} tab:`, formattedDateRange);
  };

  const handleCreateQuote = () => {
    navigate('/create-quote');
  };

  const handleViewNotes = (quoteId: string) => {
    const quoteWithNotes = quotes.find(q => q.id === quoteId);
    setCurrentNotes(quoteWithNotes?.notes || '');
    setSelectedQuoteId(quoteId);
    setIsNotesModalOpen(true);
  };

  const handleSaveNotes = async () => {
    if (!selectedQuoteId) return;
    
    setIsLoading(true);
    try {
      // Update notes for the quote
      await quoteService.updateQuote(selectedQuoteId, {
        notes: currentNotes
      });
      
      // Update local state
      const updatedQuotes = quotes.map(quote => {
        if (quote.id === selectedQuoteId) {
          return { ...quote, notes: currentNotes };
        }
        return quote;
      });
      
      setQuotes(updatedQuotes);
      setIsNotesModalOpen(false);
      toast.showToast('Notes saved successfully!', { type: 'success' });
    } catch (error) {
      console.error('Error saving notes:', error);
      toast.showToast('Failed to save notes. Please try again later.', { type: 'error' });
    } finally {
      setIsLoading(false);
    }
  };

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const handleRenewQuote = (quoteId: string) => {
    setSelectedQuoteId(quoteId);
    setIsRenewModalOpen(true);
  };

  const handleConfirmRenew = async () => {
    if (!selectedQuoteId) return;
    
    setIsLoading(true);
    try {
      // Call API to renew quote
      await quoteService.renewQuote(selectedQuoteId);
      
      // Show success message
      toast.showToast('Quote successfully renewed and moved to current quotes!', { type: 'success' });
      
      // Switch to current tab to show the renewed quote
      setCurrentTab('current');
      
      // Update date range for current tab
      const today = new Date();
      const thirtyDaysAgo = new Date(today);
      thirtyDaysAgo.setDate(today.getDate() - 30);
      
      const formattedStartDate = format(thirtyDaysAgo, 'dd/MM/yyyy');
      const formattedEndDate = format(today, 'dd/MM/yyyy');
      const formattedDateRange = `${formattedStartDate} - ${formattedEndDate}`;
      
      setDateRange(formattedDateRange);
      setFilters({
        ...filters,
        is_archived: false,
        start_date: formatDateForBackend(thirtyDaysAgo),
        end_date: formatDateForBackend(today)
      });
      
      setIsRenewModalOpen(false);
    } catch (error) {
      console.error('Error renewing quote:', error);
      toast.showToast('Failed to renew quote. Please try again.', { type: 'error' });
    } finally {
      setIsLoading(false);
    }
  };

  const handleBack = () => {
    navigate('/home');
  };

  const handleItemsPerPageChange = (newItemsPerPage: number) => {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1); // Reset to first page when changing items per page
  };
  
  // Helper function to parse date string in DD/MM/YYYY format
  const parseDateFromDisplay = (dateString: string): Date | null => {
    if (!dateString) return null;
    
    try {
      const [day, month, year] = dateString.split('/').map(Number);
      
      // Validate date parts
      if (isNaN(day) || isNaN(month) || isNaN(year) || 
          day < 1 || day > 31 || month < 1 || month > 12 || year < 1000) {
        console.error('Invalid date format:', dateString);
        return null;
      }
      
      const date = new Date(year, month - 1, day);
      
      // Check if date is valid (e.g., not Feb 30)
      if (date.getFullYear() !== year || date.getMonth() !== month - 1 || date.getDate() !== day) {
        console.error('Invalid date values:', dateString);
        return null;
      }
      
      return date;
    } catch (error) {
      console.error('Error parsing date:', error);
      return null;
    }
  };

  // Remove the renderSkeletonRows function and replace with consistent loading
  const renderTableContent = () => {
    if (isLoading) {
      return (
        <LoadingRow>
          <td colSpan={7}>
            <LoadingContainer>
              <LoadingSpinner size="lg" />
              <LoadingText>Loading quotes</LoadingText>
            </LoadingContainer>
          </td>
        </LoadingRow>
      );
    }

    if (quotes.length === 0) {
      return (
        <tr>
          <td colSpan={7} style={{ textAlign: 'center', padding: '20px' }}>
            No {currentTab} quotes found for selected date range.
          </td>
        </tr>
      );
    }

    return quotes.map(quote => (
      <tr key={quote.id}>
        <td>
          {quote.quote_no?.includes('-DEBUG') ? (
            <DebugQuoteNumber>{quote.quote_no}</DebugQuoteNumber>
          ) : (
            quote.quote_no
          )}
        </td>
        <td>{quote.company_name}</td>
        <td>{quote.store_type}</td>
        <td>{quote.date}</td>
        <td>
          {quote.grand_total_aud !== undefined
            ? `$${parseFloat(quote.grand_total_aud.toString()).toFixed(2)}`
            : '$0.00'
          }
        </td>
        <td>
          <NotesIcon hasNotes={!!quote.notes} onClick={() => handleViewNotes(quote.id)}>
            <img src={quotesNotesIcon} alt="Notes" />
          </NotesIcon>
        </td>
        <td>
          {currentTab === 'current' ? (
            <ConvertToggleContainer>
              <ToggleSwitch>
                <ToggleInput 
                  type="checkbox"
                  checked={quote.status === 'converted'}
                  onChange={() => handleConversionToggle(quote.id)}
                  disabled={quote.status === 'converted'}
                />
                <ToggleSlider />
              </ToggleSwitch>
            </ConvertToggleContainer>
          ) : (
            <ConvertContainer>
              <RenewButton onClick={() => handleRenewQuote(quote.id)}>
                Renew Quote
              </RenewButton>
            </ConvertContainer>
          )}
        </td>
      </tr>
    ));
  };

  return (
    <Layout>
      <PageContainer>
        <HeaderContainer>
          <TitleContainer>
            <StyledBackButton onClick={handleBack}>
              <BackIcon src={backButtonIcon} alt="Back" />
            </StyledBackButton>
            <PageTitle>Quotes</PageTitle>
          </TitleContainer>
        </HeaderContainer>
        
        <FiltersContainer>
          <TabContainer>
            <Tab 
              active={currentTab === 'current'} 
              onClick={() => handleTabChange('current')}
            >
              Current
            </Tab>
            <Tab 
              active={currentTab === 'archived'} 
              onClick={() => handleTabChange('archived')}
            >
              Archived
            </Tab>
          </TabContainer>
          
          <SearchContainer>
            <SearchIconWrapper>
              <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <circle cx="11" cy="11" r="8"></circle>
                <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
              </svg>
            </SearchIconWrapper>
            <SearchInput 
              type="text" 
              placeholder="Search" 
              value={searchTerm} 
              onChange={handleSearchChange} 
            />
          </SearchContainer>
          
          <DatePickerWrapper>
            <DateRangePicker 
              value={dateRange} 
              onChange={handleDateRangeChange} 
            />
          </DatePickerWrapper>
          
          <CreateButton onClick={handleCreateQuote}>
            <img src={quotesCreateIcon} alt="Add" />
            Create Quote
          </CreateButton>
        </FiltersContainer>
        
        <TableContainer>
          <Table>
            <TableHeader>
              <tr>
                <th>Quote No.</th>
                <th>Company Name</th>
                <th>Store Type</th>
                <th>Date</th>
                <th>Grand Total (AUD)</th>
                <th>Notes</th>
                <th>{currentTab === 'current' ? 'Converted to Invoice' : 'Renew Quote'}</th>
              </tr>
            </TableHeader>
            <TableBody>
              {renderTableContent()}
            </TableBody>
          </Table>
        </TableContainer>
        
        {!isLoading && totalItems > 0 && (
          <PaginationContainer>
            <Pagination 
              currentPage={currentPage} 
              totalPages={totalPages} 
              onPageChange={handlePageChange} 
              totalItems={totalItems}
              itemsPerPage={itemsPerPage}
              onItemsPerPageChange={handleItemsPerPageChange}
              itemsPerPageOptions={[10, 20, 50]}
              showItemsPerPage={true}
            />
          </PaginationContainer>
        )}
        
        {/* Notes Modal */}
        {isNotesModalOpen && (
          <ModalOverlay>
            <ModalContent>
              <ModalTitle>Quote Notes</ModalTitle>
              <NotesTextarea 
                value={currentNotes} 
                onChange={(e) => setCurrentNotes(e.target.value)} 
                placeholder="Add notes for this quote"
              />
              <ButtonGroup>
                <Button onClick={() => setIsNotesModalOpen(false)}>Cancel</Button>
                <Button primary onClick={handleSaveNotes} disabled={isLoading}>
                  {isLoading ? 'Saving' : 'Save Notes'}
                </Button>
              </ButtonGroup>
            </ModalContent>
          </ModalOverlay>
        )}
        
        {/* Toggle Archive Modal updated to Convert to Invoice Modal */}
        {isToggleModalOpen && (
          <ModalOverlay>
            <ModalContent>
              <ModalTitle>
                Convert To Invoice
              </ModalTitle>
              <p style={{ textAlign: 'center', marginBottom: '20px' }}>
                Are you sure you want to convert this quote to invoice? The quote will remain in current quotes with a converted status and a new invoice will be created.
              </p>
              <ButtonGroup>
                <Button onClick={() => setIsToggleModalOpen(false)}>Cancel</Button>
                <Button primary onClick={handleConfirmConversion} disabled={isLoading}>
                  {isLoading ? 'Processing' : 'Confirm'}
                </Button>
              </ButtonGroup>
            </ModalContent>
          </ModalOverlay>
        )}
        
        {/* Renew Quote Modal */}
        {isRenewModalOpen && (
          <ModalOverlay>
            <ModalContent>
              <ModalTitle>Renew Quote</ModalTitle>
              <p style={{ textAlign: 'center', marginBottom: '20px' }}>
                Date of Issue is renewed to today's date {format(new Date(), 'dd/MM/yyyy')}
              </p>
              <CenteredButtonGroup>
                <Button primary onClick={handleConfirmRenew} disabled={isLoading}>
                  {isLoading ? 'Processing' : 'Okay'}
                </Button>
              </CenteredButtonGroup>
            </ModalContent>
          </ModalOverlay>
        )}
      </PageContainer>
      
      {/* Toast notification */}
      {toast.isVisible && (
        <Toast
          message={toast.message}
          type={toast.type}
          onClose={() => {}}
        />
      )}
    </Layout>
  );
};

export default QuotesPage;