import React, { useState, useEffect, useMemo, useRef } from 'react';
import styled from 'styled-components';
import Layout from '../../layout/Layout';
import { Link, useNavigate } from 'react-router-dom';
import CustomerSelect from '../../common/CustomerSelect';
import quoteService, { QuoteCreate, QuoteItemCreate } from '../../../services/quoteService';
import inventoryService, { Inventory } from '../../../services/inventoryService';
import storeTypeService, { StoreType as StoreTypeModel } from '../../../services/storeTypeService';
import { BackLinkComponent, CustomBackButton } from '../../ui/DesignSystem';
import SkuImage from '../../common/SkuImage';

const PageContainer = styled.div`
  padding: 0 32px 32px 32px;
  min-height: 100vh;
  overflow-y: auto;
`;

// Updated Back Button without text and aligned with breadcrumb
const BackButton = styled.button`
  display: flex;
  align-items: center;
  border: none;
  background: none;
  color: #042B41;
  cursor: pointer;
  padding: 0;
  margin-right: 10px;
  
  svg {
    width: 18px;
    height: 18px;
  }

  &:hover {
    color: #031f30;
  }
`;

const BreadcrumbContainer = styled.div`
  display: flex;
  align-items: center;
  margin-bottom: 24px;
`;

const BreadcrumbNav = styled.div`
  display: flex;
  align-items: center;
  font-size: 24px;
  font-weight: 600;
  color: #042B41;
`;

const BreadcrumbSeparator = styled.span`
  margin: 0 10px;
  color: #6B7280;
`;

const QuotesText = styled(Link)`
  color: #6B7280;
  text-decoration: none;
  font-weight: 600;
  
  &:hover {
    text-decoration: underline;
  }
`;

const CreateQuoteTitle = styled.span`
  font-weight: 700;
  color: #111827;
`;

const FormContainer = styled.form`
  width: 100%;
  border: 1px solid #E5E7EB;
  border-radius: 0 0 4px 4px;
  padding: 1.5rem;
  background-color: white;
  margin-top: 0;
  overflow: visible;
  position: relative;
  margin-bottom: 1rem;
`;

// Updated to make the form layout more consistent
const FormRow = styled.div`
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 1rem;
  margin-bottom: 0.75rem;
  width: 100%;
`;

const FormColumn = styled.div`
  display: flex;
  flex-direction: column;
  width: 100%;
`;

const FormLabel = styled.label`
  font-size: 0.875rem;
  font-weight: 500;
  color: #111827;
  margin-bottom: 0.5rem;
`;

const FormInput = styled.input`
  padding: 0.625rem;
  border: 1px solid #D1D5DB;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  height: 38px;
  width: 100%;
  
  &:focus {
    outline: none;
    border-color: #042B41;
    box-shadow: 0 0 0 2px rgba(4, 43, 65, 0.1);
  }
`;

const FormSelect = styled.select`
  padding: 0.625rem;
  border: 1px solid #D1D5DB;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  background-color: white;
  height: 38px;
  width: 100%;
  appearance: none;
  padding-right: 2rem;
  
  &:focus {
    outline: none;
    border-color: #042B41;
    box-shadow: 0 0 0 2px rgba(4, 43, 65, 0.1);
  }
`;

const FormTextarea = styled.textarea`
  padding: 0.625rem;
  border: 1px solid #D1D5DB;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  min-height: 38px;
  resize: vertical;
  width: 100%;
  background-color: white;
  
  &:focus {
    outline: none;
    border-color: #042B41;
    box-shadow: 0 0 0 2px rgba(4, 43, 65, 0.1);
  }
`;

const StoreType = styled.div`
  padding: 0.625rem;
  border: 1px solid #D1D5DB;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  background-color: #F3F4F6;
  height: 38px;
  width: 100%;
  display: flex;
  align-items: center;
`;

const Table = styled.table`
  width: 100%;
  border-collapse: collapse;
  margin-top: 1.5rem;
  border: 1px solid #E5E7EB;
  background-color: #fff;
  position: relative;
  z-index: 1;
`;

const TableHeader = styled.thead`
  background-color: #042B41;
  color: white;
  
  th {
    padding: 0.75rem;
    text-align: left;
    font-weight: 500;
    font-size: 0.875rem;
    border: 0.5px solid #0A3D5A;
  }
`;

const TableBody = styled.tbody`
  tr {
    border-bottom: 1px solid #E5E7EB;
    
    &:last-child {
      border-bottom: none;
    }
  }
  
  td {
    padding: 0.75rem;
    font-size: 0.875rem;
    border: 1px solid #E5E7EB;
    position: relative;
    overflow: visible;
    vertical-align: top;
  }
`;

const QuantityInput = styled(FormInput)`
  width: 60px;
  text-align: center;
  padding: 0.5rem;
  border: 1px solid #D1D5DB;
  border-radius: 4px;
  height: 36px;
  font-size: 14px;
`;

const AddButton = styled.button`
  background-color: #042B41;
  color: white;
  border: none;
  width: 28px;
  height: 28px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 18px;
  
  &:hover {
    background-color: #031F30;
  }
`;

const RemoveButton = styled.button`
  background-color: white;
  color: #EF4444;
  border: 1px solid #EF4444;
  width: 28px;
  height: 28px;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 18px;
  
  &:hover {
    background-color: #FEF2F2;
  }
`;

const TableContainer = styled.div`
  width: 100%;
  overflow-x: auto;
  overflow-y: visible;
  margin-top: 1.5rem;
  margin-bottom: 1rem;
  position: relative;
  z-index: 1;
  min-height: 200px;
`;

const TotalSection = styled.div`
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  margin-top: 1rem;
`;

const TotalRow = styled.div`
  display: flex;
  justify-content: flex-end;
  align-items: center;
  margin-bottom: 0.75rem;
  
  &:last-child {
    margin-bottom: 0;
    margin-top: 0.5rem;
  }
`;

const TotalLabel = styled.span`
  font-size: 0.875rem;
  font-weight: 500;
  width: 180px;
  text-align: right;
  margin-right: 1rem;
  display: flex;
  justify-content: flex-end;
  align-items: center;
`;

const TotalValue = styled.span`
  font-size: 0.875rem;
  font-weight: 500;
  width: 80px;
  text-align: right;
`;

// Add styled input for total values
const TotalInput = styled.input`
  font-size: 0.875rem;
  font-weight: 500;
  width: 80px;
  text-align: right;
  padding: 4px 8px;
  border: 1px solid #D1D5DB;
  border-radius: 4px;
`;

// Add currency symbol component
const CurrencySymbol = styled.span`
  font-size: 0.875rem;
  font-weight: 500;
  margin-right: 4px;
`;

const Divider = styled.hr`
  width: 280px;
  margin: 0.5rem 0;
  border: none;
  border-top: 1px solid #E5E7EB;
  align-self: flex-end;
`;

const GrandTotalLabel = styled(TotalLabel)`
  font-weight: 600;
`;

const GrandTotalValue = styled(TotalValue)`
  font-weight: 600;
`;

// Non-editable but visually consistent input for grand total
const GrandTotalInput = styled(TotalInput)`
  font-weight: 600;
  background-color: white;
`;

// Add refresh button styles
const RefreshButton = styled.button`
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 2px 4px;
  margin-left: 8px;
  border-radius: 3px;
  font-size: 12px;
  opacity: 0.8;
  
  &:hover {
    opacity: 1;
    background-color: rgba(255, 255, 255, 0.1);
  }
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
`;

const HeaderWithRefresh = styled.div`
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
`;

const ButtonsContainer = styled.div`
  display: flex;
  justify-content: center;
  gap: 1rem;
  margin-top: 2rem;
`;

const CancelButton = styled.button`
  padding: 0.75rem 2rem;
  border: 1px solid #D1D5DB;
  border-radius: 0.375rem;
  background-color: white;
  color: #111827;
  font-weight: 500;
  font-size: 0.875rem;
  cursor: pointer;
  
  &:hover {
    background-color: #F9FAFB;
  }
`;

const SubmitButton = styled.button`
  padding: 0.75rem 2rem;
  border: none;
  border-radius: 0.375rem;
  background-color: #042B41;
  color: white;
  font-weight: 500;
  font-size: 0.875rem;
  cursor: pointer;
  
  &:hover {
    background-color: #031F30;
  }
`;

// Create a special notes textarea component that spans the full height of two rows
const NotesTextarea = styled(FormTextarea)`
  height: 38px;
  min-height: 38px;
  resize: vertical;
`;

// Update the customer select wrapper
const CustomerSelectWrapper = styled.div`
  width: 100%;
  
  input {
    height: 38px !important;
    font-size: 0.875rem;
    padding: 0.625rem;
  }
`;

// Create a SkuSelect component for better UX
const SkuSelect = styled(FormSelect)`
  width: 100%;
  font-size: 0.875rem;
  background-position: right 0.5rem center;
  padding-right: 2rem;
`;

// Create a DateInput component with the right styling
const DateInput = styled(FormInput)`
  width: 100%;
  cursor: pointer;
  position: relative;
  
  &::-webkit-calendar-picker-indicator {
    opacity: 0;
    cursor: pointer;
    position: absolute;
    right: 0;
    top: 0;
    width: 100%;
    height: 100%;
    margin: 0;
    padding: 0;
  }
  
  &::-webkit-inner-spin-button {
    -webkit-appearance: none;
    display: none;
  }
  
  /* Additional styling for better calendar positioning */
  &::-webkit-datetime-edit {
    width: 100%;
    padding: 0;
  }
  
  &::-webkit-datetime-edit-fields-wrapper {
    width: 100%;
  }
`;

// Add mapping from store type ID to name
const STORE_TYPES: Record<number, string> = {
  1: 'Cranbourne',
  2: 'Sale'
};

// Add interface for StoreType
interface StoreType {
  id: number;
  name: string;
  description?: string;
}

interface QuoteFormData {
  storeTypeId: string;
  customerId: string; // Always store as string for compatibility with UI components
  companyId: string;
  companyName: string; // Keep for UI display but not sent to API
  deliverToAddress: string;
  date: string;
  notes: string;
}

interface QuoteItemData {
  id: string;
  sku: string;
  description: string;
  quantity: {
    units: number | null;
    boxes: number | null;
    pieces: number | null;
    m2: number | null;
    total: number;
  };
  unitPrice: number;
  totalPrice: number;
}

// Add tooltip styling
const Tooltip = styled.div`
  position: absolute;
  top: -60px;
  right: 0;
  width: 250px;
  padding: 10px;
  background-color: #333;
  color: white;
  border-radius: 4px;
  font-size: 12px;
  z-index: 100;
  visibility: hidden;
  opacity: 0;
  transition: visibility 0s, opacity 0.3s ease;
  
  &:after {
    content: '';
    position: absolute;
    bottom: -10px;
    right: 10px;
    border-width: 10px 10px 0;
    border-style: solid;
    border-color: #333 transparent transparent;
  }
`;

const InfoIconWrapper = styled.div`
  position: relative;
  display: inline-flex;
  margin-right: 8px;
  
  &:hover ${Tooltip} {
    visibility: visible;
    opacity: 1;
  }
`;

const InfoIcon = styled.span`
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background-color: #3B82F6;
  color: white;
  font-size: 12px;
  font-weight: bold;
  cursor: help;
`;

// Add new styled components for unit price and total price
const UnitPriceInput = styled(FormInput)`
  width: 80px;
  text-align: right;
  padding-right: 8px;
`;

const TotalPriceInput = styled(FormInput)`
  width: 80px;
  text-align: right;
  background-color: #f9fafb;
  padding-right: 8px;
`;

// Add new SearchableSkuDropdown component
const SearchableSkuDropdownContainer = styled.div`
  position: relative;
  width: 100%;
  z-index: 10;
`;

const SearchInput = styled.input`
  padding: 0.625rem;
  border: 1px solid #D1D5DB;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  height: 38px;
  width: 100%;
  background-color: white;
  
  &:focus {
    outline: none;
    border-color: #042B41;
    box-shadow: 0 0 0 2px rgba(4, 43, 65, 0.1);
  }
`;

const DropdownList = styled.div<{ isOpen: boolean }>`
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid #D1D5DB;
  border-top: none;
  border-radius: 0 0 0.375rem 0.375rem;
  max-height: 300px;
  overflow-y: auto;
  z-index: 9999;
  display: ${props => props.isOpen ? 'block' : 'none'};
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
`;

const DropdownItem = styled.div<{ isHighlighted: boolean }>`
  padding: 0.375rem;
  cursor: pointer;
  font-size: 0.875rem;
  background-color: ${props => props.isHighlighted ? '#F3F4F6' : 'white'};
  border-bottom: 1px solid #E5E7EB;
  display: flex;
  align-items: center;
  gap: 8px;
  
  &:hover {
    background-color: #F3F4F6;
  }
  
  &:last-child {
    border-bottom: none;
  }
`;

const DropdownItemContent = styled.div`
  flex: 1;
`;

const SelectedItemContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
`;

const NoResultsItem = styled.div`
  padding: 0.625rem;
  font-size: 0.875rem;
  color: #6B7280;
  text-align: center;
  font-style: italic;
`;

interface SearchableSkuDropdownProps {
  value: string;
  onChange: (value: string) => void;
  inventoryItems: any[];
  disabled?: boolean;
  placeholder?: string;
  lastInventoryRefresh?: number;
}

const SearchableSkuDropdown: React.FC<SearchableSkuDropdownProps> = ({
  value,
  onChange,
  inventoryItems,
  disabled = false,
  placeholder = "Search SKU",
  lastInventoryRefresh
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [highlightedIndex, setHighlightedIndex] = useState(-1);

  const containerRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);

  // Filter inventory items based on search term
  const filteredItems = useMemo(() => {
    if (!searchTerm.trim()) {
      return inventoryItems.slice(0, 100); // Show more initial results with increased dropdown height
    }

    return inventoryItems.filter(item =>
      item.sku_code?.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.description?.toLowerCase().includes(searchTerm.toLowerCase())
    ).slice(0, 100); // Show more filtered results
  }, [inventoryItems, searchTerm]);

  // Handle click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        setHighlightedIndex(-1);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);

  // Update search term when value changes externally
  useEffect(() => {
    if (value && value !== searchTerm) {
      setSearchTerm(value);
    }
  }, [value]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    setSearchTerm(newValue);
    setIsOpen(true);
    setHighlightedIndex(-1);

    // If the input is cleared, also clear the selected value
    if (!newValue.trim()) {
      onChange('');
    }
  };

  const handleInputFocus = () => {
    setIsOpen(true);
  };

  const handleItemSelect = (item: any) => {
    setSearchTerm(item.sku_code);
    onChange(item.sku_code);
    setIsOpen(false);
    setHighlightedIndex(-1);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (!isOpen) {
      if (e.key === 'ArrowDown' || e.key === 'Enter') {
        setIsOpen(true);
        return;
      }
    }

    switch (e.key) {
      case 'ArrowDown':
        e.preventDefault();
        setHighlightedIndex(prev =>
          prev < filteredItems.length - 1 ? prev + 1 : prev
        );
        break;
      case 'ArrowUp':
        e.preventDefault();
        setHighlightedIndex(prev => prev > 0 ? prev - 1 : -1);
        break;
      case 'Enter':
        e.preventDefault();
        if (highlightedIndex >= 0 && filteredItems[highlightedIndex]) {
          handleItemSelect(filteredItems[highlightedIndex]);
        }
        break;
      case 'Escape':
        setIsOpen(false);
        setHighlightedIndex(-1);
        inputRef.current?.blur();
        break;
    }
  };



  // Check if current value has a selected item
  const selectedItem = inventoryItems.find(item => item.sku_code === value);

  return (
    <>
      <SearchableSkuDropdownContainer ref={containerRef}>
        {selectedItem ? (
          <SelectedItemContainer>
            <SkuImage
              imagePath={selectedItem?.image_path}
              alt="SKU"
              width={32}
              height={32}
              enableModal={true}
              key={`${selectedItem?.id}-${lastInventoryRefresh}`}
            />
            <SearchInput
              ref={inputRef}
              type="text"
              value={searchTerm}
              onChange={handleInputChange}
              onFocus={handleInputFocus}
              onKeyDown={handleKeyDown}
              placeholder={disabled ? 'Loading SKUs' : placeholder}
              disabled={disabled}
              style={{ flex: 1 }}
            />
          </SelectedItemContainer>
        ) : (
          <SearchInput
            ref={inputRef}
            type="text"
            value={searchTerm}
            onChange={handleInputChange}
            onFocus={handleInputFocus}
            onKeyDown={handleKeyDown}
            placeholder={disabled ? 'Loading SKUs' : placeholder}
            disabled={disabled}
          />
        )}
        <DropdownList isOpen={isOpen && !disabled}>
          {filteredItems.length > 0 ? (
            filteredItems.map((item, index) => (
              <DropdownItem
                key={item.id}
                isHighlighted={index === highlightedIndex}
                onClick={() => handleItemSelect(item)}
              >
                <SkuImage
                  imagePath={item.image_path}
                  alt="SKU"
                  width={40}
                  height={40}
                  enableModal={true}
                  key={`${item.id}-${lastInventoryRefresh}`}
                />
                <DropdownItemContent>
                  <div style={{ fontWeight: '500' }}>{item.sku_code}</div>
                  {item.description && (
                    <div style={{ fontSize: '0.75rem', color: '#6B7280', marginTop: '2px' }}>
                      {item.description}
                    </div>
                  )}
                </DropdownItemContent>
              </DropdownItem>
            ))
          ) : (
            <NoResultsItem>
              {searchTerm.trim() ? 'No SKUs found' : 'Start typing to search SKUs'}
            </NoResultsItem>
          )}
        </DropdownList>
      </SearchableSkuDropdownContainer>


    </>
  );
};

// Add SelectWrapper for custom dropdown icon like EditInvoiceForm
const SelectWrapper = styled.div`
  position: relative;

  &::after {
    content: '';
    position: absolute;
    top: 50%;
    right: 12px;
    transform: translateY(-50%);
    width: 0;
    height: 0;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-top: 5px solid #333;
    pointer-events: none;
  }
`;

const CreateQuotePage: React.FC = () => {
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const [formData, setFormData] = useState<QuoteFormData>({
    storeTypeId: '', // Default to empty (select a store type)
    customerId: '',  // Start with empty string
    companyId: 'c6c6b678-dc5c-4693-9a6e-d22e1b97dd05', // Default company ID
    companyName: '',
    deliverToAddress: '',
    date: new Date().toISOString().split('T')[0],
    notes: ''
  });

  const [items, setItems] = useState<QuoteItemData[]>([
    {
      id: '1',
      sku: '',
      description: '',
      quantity: {
        units: null,
        boxes: null,
        pieces: null,
        m2: null,
        total: 0
      },
      unitPrice: 0,
      totalPrice: 0
    }
  ]);

  const [totals, setTotals] = useState({
    orderTotal: 0,
    gst: 0,
    shipping: 0,
    grandTotal: 0
  });

  const [inventoryItems, setInventoryItems] = useState<Inventory[]>([]);

  // Add state for store types and loading indicators
  const [storeTypes, setStoreTypes] = useState<StoreTypeModel[]>([]);
  const [isLoadingStoreTypes, setIsLoadingStoreTypes] = useState(false);
  const [isLoadingInventory, setIsLoadingInventory] = useState(false);
  const [lastInventoryRefresh, setLastInventoryRefresh] = useState<number>(Date.now());

  // Create a memoized filtered list of inventory items for better performance
  const filteredInventoryItems = useMemo(() => {
    return inventoryItems.slice(0, 100); // Limit initial list for performance
  }, [inventoryItems]);

  // Create a function to get filtered inventory items for a specific row
  const getFilteredInventoryItems = (rowIndex: number) => {
    // Without the search input, just return a limited list
    return inventoryItems.slice(0, 100); // Limit list for performance
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleCustomerSelect = (customerId: string, customerName: string) => {
    // Store as string in the form data
    setFormData(prev => ({
      ...prev,
      customerId,
      companyName: customerName
    }));
  };

  const handleItemChange = (index: number, field: string, value: any) => {
    const newItems = [...items];

    if (field.startsWith('quantity.')) {
      const quantityField = field.split('.')[1];
      newItems[index].quantity = {
        ...newItems[index].quantity,
        [quantityField]: value === '' ? null : Number(value)
      };

      let total = 0;
      if (newItems[index].quantity.units) total += Number(newItems[index].quantity.units);
      if (newItems[index].quantity.boxes) total += Number(newItems[index].quantity.boxes);
      if (newItems[index].quantity.pieces) total += Number(newItems[index].quantity.pieces);
      if (newItems[index].quantity.m2) total += Number(newItems[index].quantity.m2);

      newItems[index].quantity.total = total;
    } else if (field === 'sku') {
      newItems[index].sku = value;

      // If SKU is entered, try to fetch inventory data
      if (value && value.trim().length > 0) {
        // Find matching inventory item from our existing list first for immediate response
        const matchingItem = inventoryItems.find(item => item.sku_code === value);
        if (matchingItem) {
          // Immediately update description and unit price
          newItems[index].description = matchingItem.description || matchingItem.style_code || '';

          // Only update unit price if it's not set yet
          if (newItems[index].unitPrice === 0) {
            newItems[index].unitPrice = matchingItem.trade_price;
            // Recalculate total price
            newItems[index].totalPrice = newItems[index].quantity.total * matchingItem.trade_price;
          }

          setItems([...newItems]);
          const newTotals = calculateTotals([...newItems], totals.shipping);
          setTotals(newTotals);
        }

        // Also fetch from API to ensure data is accurate and up-to-date
        const fetchInventoryData = async () => {
          try {
            const inventory = await inventoryService.getInventoryBySku(value);
            if (inventory) {
              // Update description and potentially unit price
              newItems[index].description = inventory.description || inventory.style_code || '';

              // Only update unit price if it's not set yet
              if (newItems[index].unitPrice === 0) {
                newItems[index].unitPrice = inventory.trade_price;
                // Recalculate total price
                newItems[index].totalPrice = newItems[index].quantity.total * inventory.trade_price;
              }

              setItems([...newItems]);
              const newTotals = calculateTotals([...newItems], totals.shipping);
              setTotals(newTotals);
            }
          } catch (error) {
            console.error('Error fetching inventory data:', error);
            // Don't show error to user for better UX
          }
        };

        fetchInventoryData();
      }
    } else if (field === 'description') {
      newItems[index].description = value;
    } else if (field === 'unitPrice') {
      newItems[index].unitPrice = Number(value);
    }

    if (field === 'unitPrice' || field.startsWith('quantity.')) {
      newItems[index].totalPrice = newItems[index].quantity.total * newItems[index].unitPrice;
    }

    setItems(newItems);
    const newTotals = calculateTotals(newItems, totals.shipping);
    setTotals(newTotals);
  };

  const calculateTotals = (items: QuoteItemData[], shipping: number = 0, orderTotal?: number, gst?: number) => {
    // Use provided orderTotal if available, otherwise calculate from items
    const calculatedOrderTotal = orderTotal !== undefined ? orderTotal :
      items.reduce((sum, item) => sum + item.totalPrice, 0);

    // Use provided GST if available, otherwise calculate as 10%
    const calculatedGst = gst !== undefined ? gst : calculatedOrderTotal * 0.1;

    // Use provided shipping
    const grandTotal = calculatedOrderTotal + calculatedGst + shipping;

    return {
      orderTotal: calculatedOrderTotal,
      gst: calculatedGst,
      shipping,
      grandTotal
    };
  };

  const handleAddItem = () => {
    const newItems = [...items, {
      id: String(items.length + 1),
      sku: '',
      description: '',
      quantity: {
        units: null,
        boxes: null,
        pieces: null,
        m2: null,
        total: 0
      },
      unitPrice: 0,
      totalPrice: 0
    }];

    setItems(newItems);
  };

  const handleRemoveItem = (index: number) => {
    if (items.length === 1) return;

    const newItems = items.filter((_, i) => i !== index);
    setItems(newItems);
    const newTotals = calculateTotals(newItems, totals.shipping);
    setTotals(newTotals);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsLoading(true);
    setError(null);

    try {
      if (!formData.customerId) {
        throw new Error('Please select a customer');
      }

      if (!formData.storeTypeId) {
        throw new Error('Please select a store type');
      }

      if (items.length === 0 || items.some(item => !item.sku)) {
        throw new Error('Please add at least one item with SKU');
      }

      // For API request, convert to number if it looks like a numeric string
      const customerId = /^\d+$/.test(formData.customerId)
        ? parseInt(formData.customerId, 10)
        : formData.customerId;

      // Convert store type ID to number
      const storeTypeId = parseInt(formData.storeTypeId, 10);

      const requestData: QuoteCreate = {
        company_id: formData.companyId as string,
        store_type_id: storeTypeId,
        customer_id: customerId,  // Could be string or number now
        deliver_to_address: formData.deliverToAddress,
        date: formData.date,
        notes: formData.notes || '',
        items: items.map(item => {
          const quoteItem: QuoteItemCreate = {
            sku_code: item.sku,
            description: item.description || `Item ${item.sku}`, // Provide default description
            quantity_units: item.quantity.units || 0,
            quantity_boxes: item.quantity.boxes || 0,
            quantity_pieces: item.quantity.pieces || 0,
            quantity_m2: item.quantity.m2 || 0,
            unit_price: item.unitPrice
          };
          return quoteItem;
        })
      };

      console.log('Sending quote data to API:', requestData);

      const response = await quoteService.createQuote(requestData);
      console.log('Quote created successfully:', response);

      navigate('/quotes');
    } catch (err) {
      console.error('Error creating quote:', err);
      setError(err instanceof Error ? err.message : 'An error occurred while creating the quote');
    } finally {
      setIsLoading(false);
    }
  };

  // Function to format currency
  const formatCurrency = (amount: number) => {
    return !isNaN(amount) ? `$${amount.toFixed(2)}` : '$0.00';
  };

  // Add effect to fetch store types and inventory items
  useEffect(() => {
    fetchStoreTypes();
    fetchInventoryItems();
  }, []);

  // Auto-refresh inventory data every 30 seconds to pick up newly uploaded images
  useEffect(() => {
    const interval = setInterval(() => {
      // Only auto-refresh if page is visible and not currently loading
      if (!document.hidden && !isLoadingInventory) {
        console.log('Auto-refreshing inventory data...');
        fetchInventoryItems(true);
      }
    }, 30000); // 30 seconds

    return () => clearInterval(interval);
  }, [isLoadingInventory]);

  // Refresh inventory when page becomes visible (user returns from other tabs/windows)
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (!document.hidden && !isLoadingInventory) {
        console.log('Page became visible, refreshing inventory data...');
        fetchInventoryItems(true);
      }
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    window.addEventListener('focus', handleVisibilityChange);

    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
      window.removeEventListener('focus', handleVisibilityChange);
    };
  }, [isLoadingInventory]);

  // Function to fetch store types
  const fetchStoreTypes = async () => {
    setIsLoadingStoreTypes(true);
    try {
      const response = await storeTypeService.getStoreTypes();
      setStoreTypes(response);
      console.log('Successfully loaded store types:', response.length);
    } catch (error) {
      console.error('Error fetching store types:', error);
      // Use default store types if API fails
      setStoreTypes([
        { id: 1, name: 'Cranbourne' },
        { id: 2, name: 'Sale' }
      ]);
    } finally {
      setIsLoadingStoreTypes(false);
    }
  };

  // Function to fetch inventory items
  const fetchInventoryItems = async (force: boolean = false) => {
    setIsLoadingInventory(true);
    try {
      // Clear cache if force refreshing to ensure fresh data
      if (force) {
        inventoryService.clearInventoryCache();
      }

      const response = await inventoryService.getAllInventory();
      // Log the response to debug
      console.log('Fetched inventory items:', response);
      setInventoryItems(response || []);

      if (force) {
        setLastInventoryRefresh(Date.now());
        console.log('Inventory data refreshed with latest images');
      }
    } catch (error) {
      console.error('Error fetching inventory items:', error);
      setInventoryItems([]);
    } finally {
      setIsLoadingInventory(false);
    }
  };

  // Function to refresh inventory data (useful after image uploads)
  const refreshInventoryData = () => {
    console.log('Refreshing inventory data to pick up new images...');
    fetchInventoryItems(true);
  };

  // Add handlers for the editable total fields
  const handleTotalOrderChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseFloat(e.target.value.replace(/[^\d.-]/g, '')) || 0;
    const newTotals = calculateTotals(items, totals.shipping, value);
    setTotals(newTotals);
  };

  const handleGstChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseFloat(e.target.value.replace(/[^\d.-]/g, '')) || 0;
    const newTotals = calculateTotals(items, totals.shipping, totals.orderTotal, value);
    setTotals(newTotals);
  };

  const handleShippingChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = parseFloat(e.target.value.replace(/[^\d.-]/g, '')) || 0;
    const newTotals = calculateTotals(items, value, totals.orderTotal, totals.gst);
    setTotals(newTotals);
  };

  return (
    <Layout>
      <PageContainer>
        <BreadcrumbContainer>
          <BackButton onClick={() => navigate('/quotes')}>
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M19 12H5M12 19l-7-7 7-7" />
            </svg>
          </BackButton>

          <BreadcrumbNav>
            <QuotesText to="/quotes">Quotes</QuotesText>
            <BreadcrumbSeparator>›</BreadcrumbSeparator>
            <CreateQuoteTitle>Create Quote</CreateQuoteTitle>
          </BreadcrumbNav>
        </BreadcrumbContainer>

        {error && (
          <ErrorMessage>
            {error}
          </ErrorMessage>
        )}

        <FormContainer onSubmit={handleSubmit}>
          <FormRow>
            <FormColumn>
              <FormLabel>Store Type</FormLabel>
              <SelectWrapper>
                <FormSelect
                  name="storeTypeId"
                  value={formData.storeTypeId}
                  onChange={handleInputChange}
                  required
                  disabled={isLoadingStoreTypes}
                >
                  <option value="">Select Store Type</option>
                  {storeTypes.map(type => (
                    <option key={type.id} value={type.id}>{type.name}</option>
                  ))}
                </FormSelect>
              </SelectWrapper>
            </FormColumn>

            <FormColumn>
              <FormLabel>Quote To</FormLabel>
              <CustomerSelectWrapper>
                <CustomerSelect
                  name="customerId"
                  value={formData.customerId.toString()}
                  onChange={handleCustomerSelect}
                  required
                  placeholder="Search Customer"
                />
              </CustomerSelectWrapper>
            </FormColumn>

            <FormColumn>
              <FormLabel>Notes</FormLabel>
              <NotesTextarea
                name="notes"
                value={formData.notes}
                onChange={handleInputChange}
              />
            </FormColumn>
          </FormRow>

          <FormRow>
            <FormColumn>
              <FormLabel>Deliver to Address</FormLabel>
              <FormInput
                type="text"
                name="deliverToAddress"
                value={formData.deliverToAddress}
                onChange={handleInputChange}
              />
            </FormColumn>

            <FormColumn>
              <FormLabel>Date</FormLabel>
              <DateInput
                type="date"
                name="date"
                value={formData.date}
                onChange={handleInputChange}
                required
              />
            </FormColumn>

            <FormColumn>
              {/* Empty column for alignment */}
            </FormColumn>
          </FormRow>

          <TableContainer>
            <Table>
              <TableHeader>
                <tr>
                  <th style={{ width: '180px' }} rowSpan={2}>
                    <HeaderWithRefresh>
                      BWA SKU
                      <RefreshButton
                        type="button"
                        onClick={refreshInventoryData}
                        disabled={isLoadingInventory}
                        title="Refresh SKU list to load newly uploaded images"
                      >
                        {isLoadingInventory ? '⟳' : '↻'}
                      </RefreshButton>
                    </HeaderWithRefresh>
                  </th>
                  <th style={{ width: '300px' }} rowSpan={2}>Description</th>
                  <th colSpan={5} style={{ textAlign: 'center' }}>Quantity</th>
                  <th style={{ width: '130px', textAlign: 'center' }} rowSpan={2}>Unit Price<br />excl GST</th>
                  <th style={{ width: '130px', textAlign: 'center' }} rowSpan={2}>Total Price</th>
                  <th style={{ width: '80px' }} rowSpan={2}></th>
                </tr>
                <tr>
                  <th style={{ width: '80px', textAlign: 'center' }}>Units</th>
                  <th style={{ width: '80px', textAlign: 'center' }}>Boxes</th>
                  <th style={{ width: '80px', textAlign: 'center' }}>Pieces</th>
                  <th style={{ width: '80px', textAlign: 'center' }}>M2</th>
                  <th style={{ width: '80px', textAlign: 'center' }}>Total</th>
                </tr>
              </TableHeader>
              <TableBody>
                {items.map((item, index) => (
                  <tr key={item.id}>
                    <td>
                      <SearchableSkuDropdown
                        value={item.sku}
                        onChange={(value) => handleItemChange(index, 'sku', value)}
                        inventoryItems={inventoryItems}
                        disabled={isLoadingInventory}
                        lastInventoryRefresh={lastInventoryRefresh}
                      />
                    </td>
                    <td>
                      <FormInput
                        type="text"
                        value={item.description}
                        onChange={(e) => handleItemChange(index, 'description', e.target.value)}
                        style={{ width: '100%' }}
                        required
                      />
                    </td>
                    <td style={{ textAlign: 'center' }}>
                      <QuantityInput
                        type="text"
                        value={item.quantity.units || ''}
                        onChange={(e) => handleItemChange(index, 'quantity.units', e.target.value)}
                      />
                    </td>
                    <td style={{ textAlign: 'center' }}>
                      <QuantityInput
                        type="text"
                        value={item.quantity.boxes || ''}
                        onChange={(e) => handleItemChange(index, 'quantity.boxes', e.target.value)}
                      />
                    </td>
                    <td style={{ textAlign: 'center' }}>
                      <QuantityInput
                        type="text"
                        value={item.quantity.pieces || ''}
                        onChange={(e) => handleItemChange(index, 'quantity.pieces', e.target.value)}
                      />
                    </td>
                    <td style={{ textAlign: 'center' }}>
                      <QuantityInput
                        type="text"
                        value={item.quantity.m2 || ''}
                        onChange={(e) => handleItemChange(index, 'quantity.m2', e.target.value)}
                      />
                    </td>
                    <td style={{ textAlign: 'center' }}>
                      <QuantityInput
                        type="text"
                        value={item.quantity.total}
                        readOnly
                        style={{ backgroundColor: '#f9fafb' }}
                      />
                    </td>
                    <td style={{ textAlign: 'center' }}>
                      <UnitPriceInput
                        type="number"
                        value={item.unitPrice}
                        onChange={(e) => handleItemChange(index, 'unitPrice', e.target.value)}
                        step="0.01"
                      />
                    </td>
                    <td style={{ textAlign: 'center' }}>
                      <TotalPriceInput
                        type="text"
                        value={formatCurrency(item.totalPrice).replace('$', '')}
                        readOnly
                      />
                    </td>
                    <td style={{ textAlign: 'right' }}>
                      <div style={{ display: 'flex', gap: '4px', justifyContent: 'flex-end' }}>
                        <AddButton type="button" onClick={handleAddItem}>+</AddButton>
                        {items.length > 1 && (
                          <RemoveButton type="button" onClick={() => handleRemoveItem(index)}>×</RemoveButton>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </TableBody>
            </Table>
          </TableContainer>

          <TotalSection>
            <TotalRow>
              <TotalLabel>Total Order:</TotalLabel>
              <CurrencySymbol>$</CurrencySymbol>
              <TotalInput
                type="text"
                value={totals.orderTotal.toFixed(2)}
                onChange={handleTotalOrderChange}
              />
            </TotalRow>
            <TotalRow>
              <TotalLabel>GST:</TotalLabel>
              <CurrencySymbol>$</CurrencySymbol>
              <TotalInput
                type="text"
                value={totals.gst.toFixed(2)}
                onChange={handleGstChange}
              />
            </TotalRow>
            <TotalRow>
              <TotalLabel>
                <InfoIconWrapper>
                  <InfoIcon>i</InfoIcon>
                  <Tooltip>
                    Shipping is calculated based on order weight and distance.
                    For local deliveries: $10 base + $1 per kg.
                    For interstate: $20 base + $2 per kg.
                  </Tooltip>
                </InfoIconWrapper>
                Shipping:
              </TotalLabel>
              <CurrencySymbol>$</CurrencySymbol>
              <TotalInput
                type="text"
                value={totals.shipping.toFixed(2)}
                onChange={handleShippingChange}
              />
            </TotalRow>
            <Divider />
            <TotalRow>
              <GrandTotalLabel>Grand Total:</GrandTotalLabel>
              <CurrencySymbol>$</CurrencySymbol>
              <GrandTotalInput
                type="text"
                value={totals.grandTotal.toFixed(2)}
                readOnly
              />
            </TotalRow>
          </TotalSection>

          <ButtonsContainer>
            <CancelButton type="button" onClick={() => navigate('/quotes')} disabled={isLoading}>
              Cancel
            </CancelButton>
            <SubmitButton type="submit" disabled={isLoading}>
              {isLoading ? 'Submitting' : 'Submit'}
            </SubmitButton>
          </ButtonsContainer>
        </FormContainer>
      </PageContainer>
    </Layout>
  );
};

const ErrorMessage = styled.div`
  background-color: #FEE2E2;
  color: #B91C1C;
  padding: 1rem;
  border-radius: 0.375rem;
  margin-bottom: 1.5rem;
  font-weight: 500;
`;

export default CreateQuotePage; 