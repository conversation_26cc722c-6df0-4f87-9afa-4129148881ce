import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import Table from '../common/Table';
import SearchBar from '../common/SearchBar';
import Button from '../common/Button';

interface Quote {
  id: string;
  quoteNumber: string;
  customer: {
    id: string;
    name: string;
  };
  createdDate: string;
  expiryDate: string;
  status: 'pending' | 'accepted' | 'rejected' | 'expired';
  totalAmount: number;
}

interface CurrentQuotesTableProps {
  quotes: Quote[];
  onViewQuote: (quoteId: string) => void;
  onEditQuote: (quoteId: string) => void;
  onConvertToInvoice: (quoteId: string) => void;
}

const CurrentQuotesTable: React.FC<CurrentQuotesTableProps> = ({
  quotes,
  onViewQuote,
  onEditQuote,
  onConvertToInvoice,
}) => {
  const [searchTerm, setSearchTerm] = useState('');

  const currentQuotes = quotes.filter(
    quote => quote.status === 'pending' || quote.status === 'accepted'
  );

  const filteredQuotes = currentQuotes.filter(
    quote =>
      quote.quoteNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
      quote.customer.name.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getStatusBadgeClass = (status: Quote['status']) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'accepted':
        return 'bg-green-100 text-green-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      case 'expired':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusDisplay = (status: Quote['status']) => {
    return status.charAt(0).toUpperCase() + status.slice(1);
  };

  const columns = [
    {
      header: 'Quote #',
      accessor: 'quoteNumber' as keyof Quote,
      cell: (value: string, row: Quote) => (
        <Link to={`/quotes/${row.id}`} className="text-blue-600 hover:text-blue-900 font-medium">
          {value}
        </Link>
      ),
    },
    {
      header: 'Customer',
      accessor: 'customer' as keyof Quote,
      cell: (value: Quote['customer'], row: Quote) => (
        <Link to={`/customers/${row.customer.id}`} className="text-gray-900 hover:text-gray-600">
          {value.name}
        </Link>
      ),
    },
    { header: 'Created Date', accessor: 'createdDate' as keyof Quote },
    { header: 'Expiry Date', accessor: 'expiryDate' as keyof Quote },
    {
      header: 'Status',
      accessor: 'status' as keyof Quote,
      cell: (value: Quote['status']) => (
        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${getStatusBadgeClass(value)}`}>
          {getStatusDisplay(value)}
        </span>
      ),
    },
    {
      header: 'Amount',
      accessor: 'totalAmount' as keyof Quote,
      cell: (value: number) => <span className="font-medium">${value.toFixed(2)}</span>,
    },
    {
      header: 'Actions',
      accessor: 'id' as keyof Quote,
      cell: (value: string, row: Quote) => (
        <div className="flex space-x-2">
          <Button onClick={() => onViewQuote(value)} variant="secondary" size="sm">
            View
          </Button>
          <Button onClick={() => onEditQuote(value)} variant="primary" size="sm">
            Edit
          </Button>
          {row.status === 'accepted' && (
            <Button onClick={() => onConvertToInvoice(value)} variant="success" size="sm">
              Convert to Invoice
            </Button>
          )}
        </div>
      ),
    },
  ];

  return (
    <div className="flex flex-col">
      <div className="mb-4">
        <SearchBar
          value={searchTerm}
          onChange={setSearchTerm}
          placeholder="Search by quote number or customer name"
        />
      </div>
      <Table
        data={filteredQuotes}
        columns={columns}
        emptyMessage="No current quotes found. Create a new quote to get started."
      />
    </div>
  );
};

export default CurrentQuotesTable; 