{"common": {"actions": {"save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "view": "View", "search": "Search", "filter": "Filter", "clear": "Clear", "add": "Add", "create": "Create", "update": "Update", "submit": "Submit", "back": "Back", "next": "Next", "previous": "Previous", "close": "Close", "confirm": "Confirm", "logout": "Logout"}, "status": {"loading": "Loading...", "error": "Error", "success": "Success", "warning": "Warning", "info": "Info"}, "validation": {"required": "This field is required", "email": "Please enter a valid email address", "password": "Password must be at least 8 characters", "confirmPassword": "Passwords do not match"}}, "navigation": {"dashboard": "Dashboard", "inventory": "Inventory", "customers": "Customers", "suppliers": "Suppliers", "purchaseOrders": "Purchase Orders", "invoices": "Invoices", "quotes": "Quotes", "reports": "Reports", "users": "Users", "settings": "Settings"}, "auth": {"login": {"title": "<PERSON><PERSON>", "email": "Email", "password": "Password", "rememberMe": "Remember me", "forgotPassword": "Forgot password?", "noAccount": "Don't have an account?", "signUp": "Sign up"}, "register": {"title": "Register", "name": "Name", "email": "Email", "password": "Password", "confirmPassword": "Confirm Password", "alreadyHaveAccount": "Already have an account?", "signIn": "Sign in"}}, "inventory": {"title": "Inventory Management", "searchPlaceholder": "Search by name, SKU, or category...", "filters": {"all": "All Items", "lowStock": "Low Stock", "outOfStock": "Out of Stock"}, "emptyState": {"noItems": {"title": "No inventory items found", "description": "Try adjusting your search or filters to find what you're looking for."}, "noLowStock": {"title": "No low stock items", "description": "All your inventory items are well stocked."}, "noOutOfStock": {"title": "No out of stock items", "description": "All your inventory items have stock available."}}, "columns": {"sku": "SKU", "name": "Name", "category": "Category", "quantity": "Quantity", "price": "Price", "supplier": "Supplier", "location": "Location", "status": "Status"}}, "users": {"title": "User Management", "searchPlaceholder": "Search by name or email...", "columns": {"name": "Name", "email": "Email", "role": "Role", "status": "Status", "actions": "Actions"}, "roles": {"admin": "Administrator", "manager": "Manager", "staff": "Staff"}, "status": {"active": "Active", "inactive": "Inactive"}}, "footer": {"about": {"title": "About Us", "company": "Company", "careers": "Careers", "contact": "Contact"}, "support": {"title": "Support", "helpCenter": "Help Center", "documentation": "Documentation", "apiStatus": "API Status"}, "legal": {"title": "Legal", "privacy": "Privacy Policy", "terms": "Terms of Service", "cookies": "<PERSON><PERSON>"}, "copyright": "© {{year}} Builders Warehouse. All rights reserved."}, "errors": {"notFound": "Page not found", "unauthorized": "You are not authorized to access this page", "serverError": "Something went wrong. Please try again later.", "networkError": "Network error. Please check your connection."}}