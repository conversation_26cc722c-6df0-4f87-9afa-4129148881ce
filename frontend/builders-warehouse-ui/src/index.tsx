import React from 'react';
import ReactDOM from 'react-dom/client';
import './index.css';
import './components/styles/responsiveLayout.css';
import App from './App';
import reportWebVitals from './reportWebVitals';
import { Provider } from 'react-redux';
import { store } from './store';
import { setupGlobalInterceptor } from './services/apiInterceptor';
import { checkAuthIssues, setDevelopmentToken } from './utils/authChecker';

// Check for auth issues in development mode
// Uncomment the next line if you need to set a dev token automatically:
// setDevelopmentToken();
checkAuthIssues();

// Wait for DOM to be ready before setting up interceptors
// This ensures all modules are loaded first
document.addEventListener('DOMContentLoaded', () => {
  console.log('Setting up API interceptors...');
  
  // Setup global API interceptor to handle authentication
  // Store restore function for potential cleanup
  const restoreOriginalFetch = setupGlobalInterceptor({
    onAuthError: () => {
      console.warn('Authentication error detected - redirecting to login');
      
      // Only redirect if we're not already on the login page
      if (!window.location.pathname.includes('/login')) {
        window.location.href = '/login';
      }
    }
  });
  
  // Add cleanup on window unload if needed
  window.addEventListener('unload', () => {
    restoreOriginalFetch();
  });
});

const root = ReactDOM.createRoot(
  document.getElementById('root') as HTMLElement
);
root.render(
  <React.StrictMode>
    <Provider store={store}>
      <App />
    </Provider>
  </React.StrictMode>
);

// If you want to start measuring performance in your app, pass a function
// to log results (for example: reportWebVitals(console.log))
// or send to an analytics endpoint. Learn more: https://bit.ly/CRA-vitals
reportWebVitals();
