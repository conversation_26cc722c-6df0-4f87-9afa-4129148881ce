import React from "react";
import {
  BrowserRouter as Router,
  Routes,
  Route,
  Navigate,
} from "react-router-dom";
import { createGlobalStyle } from "styled-components";
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import './components/styles/responsiveLayout.css';
import Login from "./pages/Login";
import ResetPasswordPage from "./components/common/ResetPasswordPage";
import HomePage from "./pages/HomePage";
import ReportsPage from "./pages/ReportsPage";
import CustomersPage from "./components/customers/pages/CustomersPage";
import CustomerHistoryPage from "./components/customers/pages/CustomerHistoryPage";
import OutboundDeliveriesPage from "./components/outboundDelivery/pages/OutboundDeliveriesPage";
import UsersPage from "./components/users/pages/UsersPage";
import UsersDirectPage from "./components/users/pages/UsersDirectPage";
import InboundDeliveryPage from "./components/inboundDelivery/pages/InboundDeliveryPage";
import InboundDeliveryDetailPage from "./components/inboundDelivery/pages/InboundDeliveryDetailPage";
import SuppliersPage from "./components/suppliers/pages/SuppliersPage";
import SupplierDetailPage from "./components/suppliers/pages/SupplierDetailPage";
import SuppliersDashboard from "./components/suppliers/pages/SuppliersDashboard";
import POManagementPage from "./components/poManagement/pages/POManagementPage";
import CreatePOPage from "./components/poManagement/pages/CreatePOPage";
import InvoicesPage from "./components/invoice/pages/InvoicesPage";
import QuotesPage from "./components/quote/pages/QuotesPage";
import CreateQuotePage from "./components/quote/pages/CreateQuotePage";
import InventoryPage from "./components/inventory/pages/InventoryPage";
import { AuthProvider, useAuth } from "./context/AuthContext";
import ProtectedRoute from "./components/layout/ProtectedRoute";
import { ErrorProvider } from "./components/common/ErrorHandler";

const GlobalStyle = createGlobalStyle`
  * {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
    font-family: 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', sans-serif;
  }

  body, html, #root {
    height: 100%;
    background-color: #f5f7fa;
    overflow-x: hidden;
  }
  
  body {
    margin: 0;
    padding: 0;
  }
`;

const App: React.FC = () => {
  return (
    <ErrorProvider>
      <AuthProvider>
        <Router>
          <GlobalStyle />
          <ToastContainer position="top-right" autoClose={3000} hideProgressBar={false} />
          <AppRoutes />
        </Router>
      </AuthProvider>
    </ErrorProvider>
  );
};

const AppRoutes: React.FC = () => {
  // Now we can safely use useAuth because we're inside AuthProvider
  const { isAuthenticated, user } = useAuth();

  return (
    <Routes>
      {/* Public routes */}
      <Route
        path="/"
        element={isAuthenticated ? <Navigate to="/home" replace /> : <Login />}
      />
      <Route
        path="/login"
        element={isAuthenticated ? <Navigate to="/home" replace /> : <Login />}
      />
      <Route
        path="/reset-password"
        element={<ResetPasswordPage />}
      />

      {/* Protected routes - accessible to all authenticated users */}
      <Route
        path="/home"
        element={
          <ProtectedRoute
            isAuthenticated={isAuthenticated}
            userRole={user?.role as string}
            requiredRole="all"
            user={user}
          >
            <HomePage />
          </ProtectedRoute>
        }
      />
      <Route
        path="/customers"
        element={
          <ProtectedRoute
            isAuthenticated={isAuthenticated}
            userRole={user?.role as string}
            requiredRole="all"
            user={user}
          >
            <CustomersPage />
          </ProtectedRoute>
        }
      />
      <Route
        path="/customers/:customerId/history"
        element={
          <ProtectedRoute
            isAuthenticated={isAuthenticated}
            userRole={user?.role as string}
            requiredRole="all"
            user={user}
          >
            <CustomerHistoryPage />
          </ProtectedRoute>
        }
      />
      <Route
        path="/invoices"
        element={
          <ProtectedRoute
            isAuthenticated={isAuthenticated}
            userRole={user?.role as string}
            requiredRole="all"
            user={user}
          >
            <InvoicesPage />
          </ProtectedRoute>
        }
      />
      <Route
        path="/quotes"
        element={
          <ProtectedRoute
            isAuthenticated={isAuthenticated}
            userRole={user?.role as string}
            requiredRole="all"
            user={user}
          >
            <QuotesPage />
          </ProtectedRoute>
        }
      />
      <Route
        path="/create-quote"
        element={
          <ProtectedRoute
            isAuthenticated={isAuthenticated}
            userRole={user?.role as string}
            requiredRole="all"
            user={user}
          >
            <CreateQuotePage />
          </ProtectedRoute>
        }
      />
      <Route
        path="/inventory"
        element={
          <ProtectedRoute
            isAuthenticated={isAuthenticated}
            userRole={user?.role as string}
            requiredRole="all"
            user={user}
          >
            <InventoryPage />
          </ProtectedRoute>
        }
      />
      <Route
        path="/outbound-delivery"
        element={
          <ProtectedRoute
            isAuthenticated={isAuthenticated}
            userRole={user?.role as string}
            requiredRole="all"
            user={user}
          >
            <OutboundDeliveriesPage />
          </ProtectedRoute>
        }
      />
      <Route
        path="/suppliers"
        element={
          <ProtectedRoute
            isAuthenticated={isAuthenticated}
            userRole={user?.role as string}
            requiredRole="all"
            user={user}
          >
            <SuppliersPage />
          </ProtectedRoute>
        }
      />
      <Route
        path="/suppliers/:id"
        element={
          <ProtectedRoute
            isAuthenticated={isAuthenticated}
            userRole={user?.role as string}
            requiredRole="all"
            user={user}
          >
            <SupplierDetailPage />
          </ProtectedRoute>
        }
      />
      <Route
        path="/suppliers-dashboard"
        element={
          <ProtectedRoute
            isAuthenticated={isAuthenticated}
            userRole={user?.role as string}
            requiredRole="all"
            user={user}
          >
            <SuppliersDashboard />
          </ProtectedRoute>
        }
      />
      <Route
        path="/po-management"
        element={
          <ProtectedRoute
            isAuthenticated={isAuthenticated}
            userRole={user?.role as string}
            requiredRole="all"
            user={user}
          >
            <POManagementPage />
          </ProtectedRoute>
        }
      />
      <Route
        path="/po-management/create"
        element={
          <ProtectedRoute
            isAuthenticated={isAuthenticated}
            userRole={user?.role as string}
            requiredRole="all"
            user={user}
          >
            <CreatePOPage />
          </ProtectedRoute>
        }
      />
      <Route
        path="/inbound-delivery"
        element={
          <ProtectedRoute
            isAuthenticated={isAuthenticated}
            userRole={user?.role as string}
            requiredRole="all"
            user={user}
          >
            <InboundDeliveryPage />
          </ProtectedRoute>
        }
      />
      <Route
        path="/inbound-delivery/date/:date_str"
        element={
          <ProtectedRoute
            isAuthenticated={isAuthenticated}
            userRole={user?.role as string}
            requiredRole="all"
            user={user}
          >
            <InboundDeliveryDetailPage />
          </ProtectedRoute>
        }
      />
      <Route
        path="/inbound-delivery/po/:po_number"
        element={
          <ProtectedRoute
            isAuthenticated={isAuthenticated}
            userRole={user?.role as string}
            requiredRole="all"
            user={user}
          >
            <InboundDeliveryDetailPage />
          </ProtectedRoute>
        }
      />
      <Route
        path="/reports"
        element={
          <ProtectedRoute
            isAuthenticated={isAuthenticated}
            userRole={user?.role as string}
            requiredRole="all"
            user={user}
          >
            <ReportsPage />
          </ProtectedRoute>
        }
      />

      {/* Admin-only routes */}
      <Route
        path="/users"
        element={
          <ProtectedRoute
            isAuthenticated={isAuthenticated}
            userRole={user?.role as string}
            requiredRole="admin"
            user={user}
          >
            <UsersPage />
          </ProtectedRoute>
        }
      />

      {/* Direct users page for testing */}
      <Route
        path="/users-direct"
        element={
          <ProtectedRoute
            isAuthenticated={isAuthenticated}
            userRole={user?.role as string}
            requiredRole="admin"
            user={user}
          >
            <UsersDirectPage />
          </ProtectedRoute>
        }
      />

      {/* Fallback route */}
      <Route path="*" element={<Navigate to="/" replace />} />
    </Routes>
  );
};

export default App;
