import React, { useState, useEffect, useCallback, useRef } from 'react';
import styled from 'styled-components';
import { useNavigate, useLocation } from 'react-router-dom';
import Layout from '../components/layout/Layout';
import { useAuth } from '../context/AuthContext';
import { isAdmin } from '../utils/roleUtils';

// Import icons from assets
import customersIcon from '../assets/customersIcon.png';
import newInvoiceIcon from '../assets/newInvoiceIcon.png';
import newQuoteIcon from '../assets/newQuoteIcon.png';
import inventoryIcon from '../assets/InventoryIcon.png';
import outboundDeliveryIcon from '../assets/OutboundDeliveryIcon.png';
import suppliersIcon from '../assets/SuppliersIcon.png';
import poManagementIcon from '../assets/POManagementIcon.png';
import inboundDeliveryIcon from '../assets/InboundDeliveryIcon.png';
import reportsIcon from '../assets/reportsIcon.png';
import usersIcon from '../assets/usersIcon.png';

// Simple fixed-width container approach
const DashboardContainer = styled.div`
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 20px;
  box-sizing: border-box;
  overflow: hidden;
  
  /* Ensure proper spacing on very small screens */
  @media (max-width: 480px) {
    padding: 16px;
  }
  
  @media (max-height: 600px) {
    padding: 12px;
    align-items: flex-start;
    overflow-y: auto;
  }
`;

const DashboardInner = styled.div`
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  
  @media (max-height: 600px) {
    justify-content: flex-start;
  }
`;

const DashboardGrid = styled.div`
  display: grid;
  gap: 20px;
  width: 100%;
  
  /* Default: 5 columns for large screens */
  grid-template-columns: repeat(5, 1fr);
  
  /* 4 columns for medium-large screens */
  @media (max-width: 1200px) {
    grid-template-columns: repeat(4, 1fr);
    gap: 18px;
  }
  
  /* 3 columns for medium screens */
  @media (max-width: 900px) {
    grid-template-columns: repeat(3, 1fr);
    gap: 16px;
  }
  
  /* 2 columns for small screens */
  @media (max-width: 600px) {
    grid-template-columns: repeat(2, 1fr);
    gap: 14px;
  }
  
  /* 1 column for very small screens */
  @media (max-width: 400px) {
    grid-template-columns: 1fr;
    gap: 12px;
  }
  
  /* Adjust for very short screens */
  @media (max-height: 600px) {
    gap: 12px;
  }
  
  @media (max-height: 500px) {
    gap: 10px;
  }
`;

const MenuCard = styled.div<{ $bgColor: string }>`
  background-color: ${props => props.$bgColor};
  border-radius: 12px;
  width: 100%;
  aspect-ratio: 1.1;
  min-height: 140px;
  max-height: 200px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  cursor: pointer;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
  padding: 16px 12px;
  margin: 0;
  position: relative;
  overflow: hidden;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.12);
  }
  
  /* Responsive adjustments */
  @media (max-width: 600px) {
    min-height: 120px;
    max-height: 160px;
    padding: 14px 10px;
    border-radius: 10px;
  }
  
  @media (max-width: 400px) {
    min-height: 100px;
    max-height: 140px;
    padding: 12px 8px;
    border-radius: 8px;
  }
  
  @media (max-height: 600px) {
    min-height: 100px;
    max-height: 130px;
    padding: 12px 10px;
  }
  
  @media (max-height: 500px) {
    min-height: 80px;
    max-height: 110px;
    padding: 10px 8px;
  }
`;

const IconContainer = styled.div`
  margin-bottom: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 56px;
  height: 56px;
  flex-shrink: 0;

  img {
    width: 100%;
    height: 100%;
    object-fit: contain;
    
    &[src*="usersIcon"] {
      width: 110%;
      height: 110%;
    }
  }
  
  /* Responsive icon sizing */
  @media (max-width: 600px) {
    width: 48px;
    height: 48px;
    margin-bottom: 10px;
  }
  
  @media (max-width: 400px) {
    width: 40px;
    height: 40px;
    margin-bottom: 8px;
  }
  
  @media (max-height: 600px) {
    width: 40px;
    height: 40px;
    margin-bottom: 8px;
  }
  
  @media (max-height: 500px) {
    width: 32px;
    height: 32px;
    margin-bottom: 6px;
  }
`;

const CardTitle = styled.h3<{ $textColor: string }>`
  font-size: 1.1rem;
  font-weight: 600;
  color: ${props => props.$textColor};
  margin: 0;
  line-height: 1.3;
  font-family: 'Segoe UI', 'Roboto', sans-serif;
  text-align: center;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  word-break: break-word;
  white-space: normal;
  flex-shrink: 0;
  
  /* Responsive font sizing */
  @media (max-width: 600px) {
    font-size: 1rem;
  }
  
  @media (max-width: 400px) {
    font-size: 0.9rem;
  }
  
  @media (max-height: 600px) {
    font-size: 0.95rem;
  }
  
  @media (max-height: 500px) {
    font-size: 0.85rem;
    -webkit-line-clamp: 1;
  }
`;

const SubTitle = styled.p<{ $textColor: string }>`
  font-size: 0.9rem;
  color: ${props => props.$textColor};
  margin: 4px 0 0 0;
  font-family: 'Segoe UI', 'Roboto', sans-serif;
  text-align: center;
  opacity: 0.85;
  flex-shrink: 0;
  
  /* Responsive font sizing */
  @media (max-width: 600px) {
    font-size: 0.8rem;
  }
  
  @media (max-width: 400px) {
    font-size: 0.75rem;
  }
  
  @media (max-height: 600px) {
    font-size: 0.8rem;
  }
  
  @media (max-height: 500px) {
    font-size: 0.7rem;
    margin: 2px 0 0 0;
  }
`;

interface MenuItemProps {
  title: string;
  subtitle?: string;
  icon: string;
  bgColor: string;
  textColor: string;
  route: string;
  onClick: (route: string) => void;
  className?: string;
}

const MenuItem: React.FC<MenuItemProps> = ({ title, subtitle, icon, bgColor, textColor, route, onClick, className }) => {
  return (
    <MenuCard $bgColor={bgColor} onClick={() => onClick(route)} className={className}>
      <IconContainer>
        <img src={icon} alt={title} />
      </IconContainer>
      <CardTitle $textColor={textColor}>{title}</CardTitle>
      {subtitle && <SubTitle $textColor={textColor}>{subtitle}</SubTitle>}
    </MenuCard>
  );
};

const menuItems = [
  {
    title: "Customers",
    icon: customersIcon,
    bgColor: "#FFEAEE",
    textColor: "#8B1E3F",
    route: "/customers"
  },
  {
    title: "New Invoice",
    icon: newInvoiceIcon,
    bgColor: "#E8F5E9",
    textColor: "#1B5E20",
    route: "/invoices?create=true"
  },
  {
    title: "New Quote",
    icon: newQuoteIcon,
    bgColor: "#E3F2FD",
    textColor: "#0D47A1",
    route: "/create-quote"
  },
  {
    title: "Inventory",
    icon: inventoryIcon,
    bgColor: "#E0F2F1",
    textColor: "#00695C",
    route: "/inventory"
  },
  {
    title: "Outbound Delivery",
    subtitle: "from BWA",
    icon: outboundDeliveryIcon,
    bgColor: "#EDE7F6",
    textColor: "#4527A0",
    route: "/outbound-delivery"
  },
  {
    title: "Suppliers",
    icon: suppliersIcon,
    bgColor: "#F9FBE7",
    textColor: "#827717",
    route: "/suppliers"
  },
  {
    title: "PO Management",
    icon: poManagementIcon,
    bgColor: "#F3E5F5",
    textColor: "#9C27B0",
    route: "/po-management"
  },
  {
    title: "Inbound Delivery",
    subtitle: "to BWA",
    icon: inboundDeliveryIcon,
    bgColor: "#FFF3E0",
    textColor: "#BF360C",
    route: "/inbound-delivery"
  },
  {
    title: "Reports",
    icon: reportsIcon,
    bgColor: "#E1F5FE",
    textColor: "#0277BD",
    route: "/reports"
  }
];

const HomePage: React.FC = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { user } = useAuth();
  const userIsAdmin = isAdmin(user);
  const [isLoading, setIsLoading] = useState(false);
  const [dataFetchInProgress, setDataFetchInProgress] = useState(false);
  const [dataLoaded, setDataLoaded] = useState(false);
  
  // Use a ref to track navigation
  const navigationInProgress = useRef(false);
  
  // Use useCallback to memoize the fetch function
  const fetchHomePageData = useCallback(async () => {
    // Skip if already fetching or data already loaded
    if (dataFetchInProgress || dataLoaded) return;
    
    setDataFetchInProgress(true);
    setIsLoading(true);
    
    try {
      // Fetch data for the dashboard
      console.log('Fetching dashboard data');
      
      // fetch data and update state
      // ...
      
      // Mark data as loaded
      setDataLoaded(true);
    } catch (error) {
      console.error('Error fetching dashboard data:', error);
    } finally {
      setIsLoading(false);
      setDataFetchInProgress(false);
    }
  }, []);
  
  // Use useEffect with an empty dependency array since fetchHomePageData shouldn't change
  useEffect(() => {
    // Only fetch data if not already loaded
    if (!dataLoaded) {
      fetchHomePageData();
    }
  }, [dataLoaded, fetchHomePageData]);
  
  // Reset data loaded flag when component unmounts
  useEffect(() => {
    return () => {
      setDataLoaded(false);
    };
  }, []);

  const handleMenuItemClick = (route: string) => {
    // Prevent duplicate navigation and API calls
    if (navigationInProgress.current) {
      console.log('Navigation already in progress, ignoring click');
      return;
    }
    
    // Set navigation in progress flag
    navigationInProgress.current = true;
    
    // Add a small delay to prevent accidental double clicks
    setTimeout(() => {
      console.log(`Navigating to ${route}`);
      navigate(route);
      
      // Reset the flag after navigation
      setTimeout(() => {
        navigationInProgress.current = false;
      }, 500);
    }, 50);
  };

  // Filter menu items based on user role
  const filteredMenuItems = [
    ...menuItems,
    ...(userIsAdmin ? [{
      title: "Users",
      icon: usersIcon,
      bgColor: "#F8EBF8",
      textColor: "#6A1B9A",
      route: "/users"
    }] : [])
  ];

  return (
    <Layout>
      <DashboardContainer>
        <DashboardInner>
          <DashboardGrid>
            {filteredMenuItems.map(item => (
              <MenuItem
                key={item.route}
                title={item.title}
                subtitle={item.subtitle}
                icon={item.icon}
                bgColor={item.bgColor}
                textColor={item.textColor}
                route={item.route}
                onClick={handleMenuItemClick}
                className="dashboard-tile"
              />
            ))}
          </DashboardGrid>
        </DashboardInner>
      </DashboardContainer>
    </Layout>
  );
};

export default HomePage; 