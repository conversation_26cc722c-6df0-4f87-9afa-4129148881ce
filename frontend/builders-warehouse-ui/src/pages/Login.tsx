import React, { useState, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import styled from 'styled-components';
import logoSvg from '../assets/Builder-Logo.png';
import bgImage from '../assets/signup.jpg';
import { useAuth } from '../context/AuthContext';
import ForgotPasswordModal from '../components/common/ForgotPasswordModal';

const LoginContainer = styled.div`
  display: flex;
  justify-content: flex-start;
  align-items: center;
  min-height: 100vh;
  background: url(${bgImage}) no-repeat center center;
  background-size: cover;
  position: relative;
  padding-left: 50px;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    background-color: rgba(0, 0, 0, 0.5);
  }
`;

const LoginForm = styled.form`
  background: white;
  padding: 2.5rem;
  border-radius: 40px;
  border: none;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.25);
  width: 100%;
  max-width: 500px;
  position: relative;
  z-index: 1;
  margin: 0 auto;
`;

const Logo = styled.div`
  text-align: center;
  margin-bottom: 30px;
  
  img {
    width: 250px;
  }
`;

const Title = styled.h2`
  font-size: 1.75rem;
  color: #333;
  margin-bottom: 30px;
  text-align: center;
  font-weight: 600;
`;

const FormGroup = styled.div`
  margin-bottom: 1.5rem;
  
  label {
    display: block;
    margin-bottom: 0.6rem;
    font-weight: 500;
    color: #333;
  }
  
  input {
    width: 100%;
    padding: 0.85rem;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    font-size: 0.95rem;
    transition: border-color 0.2s;
    
    &:focus {
      outline: none;
      border-color: #042B41;
      box-shadow: 0 0 0 2px rgba(4, 43, 65, 0.1);
    }
  }
`;

const PasswordContainer = styled.div`
  position: relative;
`;

const PasswordToggle = styled.button`
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  background: none;
  border: none;
  cursor: pointer;
  color: #042B41;
  font-size: 18px;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  
  svg {
    width: 20px;
    height: 20px;
  }
`;

const LoginButton = styled.button`
  width: 100%;
  padding: 0.85rem;
  background-color: #042B41;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 0.95rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
  
  &:hover {
    background-color: #031f30;
  }
`;

const ForgotPassword = styled.div`
  text-align: center;
  margin-top: 1.75rem;
  
  button {
    color: #042B41;
    text-decoration: none;
    font-weight: 500;
    cursor: pointer;
    background: none;
    border: none;
    font-size: inherit;
    
    &:hover {
      text-decoration: underline;
    }
  }
`;

const ErrorMessage = styled.div`
  color: #dc3545;
  margin-bottom: 1.5rem;
  padding: 0.75rem 1rem;
  background-color: #fff5f5;
  border: 1px solid #ffccd1;
  border-radius: 6px;
  font-size: 0.9rem;
`;

const Login: React.FC = () => {
  const [username, setUsername] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [formError, setFormError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [showForgotPasswordModal, setShowForgotPasswordModal] = useState(false);
  const navigate = useNavigate();
  const location = useLocation();
  const { login, isAuthenticated } = useAuth();
  const [hasRedirected, setHasRedirected] = useState(false);
  
  // If user is already authenticated, redirect to home page
  useEffect(() => {
    // Only redirect if we haven't redirected before and user is authenticated
    if (isAuthenticated && !hasRedirected) {
      console.log('User already authenticated, redirecting to home');
      setHasRedirected(true);
      // Get the redirect path from location state or default to /home
      const from = location.state?.from?.pathname || '/home';
      // Use setTimeout to prevent concurrent rendering issues
      setTimeout(() => {
      navigate(from, { replace: true });
      }, 0);
    }
  }, [isAuthenticated, navigate, location, hasRedirected]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!username || !password) {
      setFormError('Please enter both username and password.');
      return;
    }
    
    setIsLoading(true);
    setFormError('');
    
    try {
      console.log('Submitting login form');
      await login(username, password);
      
      // Mark as redirected to prevent multiple redirects
      setHasRedirected(true);
      
      // Get the redirect path from location state or default to /home
      const from = location.state?.from?.pathname || '/home';
      console.log('Login successful, redirecting to:', from);
      
      // Use setTimeout to prevent concurrent rendering issues
      setTimeout(() => {
      navigate(from, { replace: true });
      }, 0);
    } catch (error) {
      console.error('Login error:', error);
      setFormError('Invalid username or password. Please try again.');
    } finally {
      setIsLoading(false);
    }
  };

  // If already authenticated, don't render the login form at all
  if (isAuthenticated) {
    return null;
  }

  return (
    <LoginContainer>
      <LoginForm onSubmit={handleSubmit}>
        <Logo>
          <img src={logoSvg} alt="Builders Warehouse Australia" />
        </Logo>
        <Title>Welcome to Builders Warehouse</Title>
        
        {formError && <ErrorMessage>{formError}</ErrorMessage>}
        
        <FormGroup>
          <label htmlFor="username">Username</label>
          <input
            type="text"
            id="username"
            value={username}
            onChange={(e) => setUsername(e.target.value)}
            placeholder="Enter your username"
            disabled={isLoading}
          />
        </FormGroup>
        
        <FormGroup>
          <label htmlFor="password">Password</label>
          <PasswordContainer>
            <input
              type={showPassword ? "text" : "password"}
              id="password"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              placeholder="Enter your password"
              disabled={isLoading}
            />
            <PasswordToggle
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              disabled={isLoading}
            >
              {showPassword ? (
                // Eye with slash (hide password)
                <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z"/>
                  <path d="M2 2l20 20-1.41 1.41L2 4.41 2 2z" stroke="currentColor" strokeWidth="1.5"/>
                </svg>
              ) : (
                // Regular eye (show password)
                <svg xmlns="http://www.w3.org/2000/svg" fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z"/>
                </svg>
              )}
            </PasswordToggle>
          </PasswordContainer>
        </FormGroup>
        
        <LoginButton type="submit" disabled={isLoading}>
          {isLoading ? "Logging in" : "Log In"}
        </LoginButton>
        
        <ForgotPassword>
          <button type="button" onClick={() => {
            setFormError('');
            setShowForgotPasswordModal(true);
          }}>Forgot your password?</button>
        </ForgotPassword>
      </LoginForm>

      <ForgotPasswordModal
        isOpen={showForgotPasswordModal}
        onClose={() => setShowForgotPasswordModal(false)}
      />
    </LoginContainer>
  );
};

export default Login; 