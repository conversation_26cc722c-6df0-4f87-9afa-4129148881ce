import { configureStore } from '@reduxjs/toolkit';
import purchaseOrderReducer from './slices/purchaseOrderSlice';

// Configure the Redux store
export const store = configureStore({
  reducer: {
    purchaseOrders: purchaseOrderReducer,
    // Add other reducers here as needed
  },
  // Add middleware or other store enhancers here
});

// Export types for TypeScript
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch; 