import { createSlice, createAsyncThunk, PayloadAction } from '@reduxjs/toolkit';
import { PurchaseOrder, CreatePurchaseOrderRequest, PurchaseOrderFilters } from '../types';
import apiClient from '../../services/apiClient';
import { ENDPOINTS } from '../../config';

// State type
interface PurchaseOrderState {
  orders: PurchaseOrder[];
  currentOrder: PurchaseOrder | null;
  loading: boolean;
  error: string | null;
  filters: PurchaseOrderFilters;
}

// Initial state
const initialState: PurchaseOrderState = {
  orders: [],
  currentOrder: null,
  loading: false,
  error: null,
  filters: {
    searchQuery: '',
    dateRange: ''
  }
};

// Async thunks
export const fetchPurchaseOrders = createAsyncThunk(
  'purchaseOrders/fetchAll',
  async (_, { rejectWithValue }) => {
    try {
      const response = await apiClient.get<PurchaseOrder[]>(ENDPOINTS.PURCHASE_ORDERS);
      return response;
    } catch (error) {
      if (error instanceof Error) {
        return rejectWithValue(error.message);
      }
      return rejectWithValue('An unknown error occurred');
    }
  }
);

export const fetchPurchaseOrderById = createAsyncThunk(
  'purchaseOrders/fetchById',
  async (id: string | number, { rejectWithValue }) => {
    try {
      const response = await apiClient.get<PurchaseOrder>(`${ENDPOINTS.PURCHASE_ORDERS}/${id}`);
      return response;
    } catch (error) {
      if (error instanceof Error) {
        return rejectWithValue(error.message);
      }
      return rejectWithValue('An unknown error occurred');
    }
  }
);

export const createPurchaseOrder = createAsyncThunk(
  'purchaseOrders/create',
  async (purchaseOrder: CreatePurchaseOrderRequest, { rejectWithValue }) => {
    try {
      const response = await apiClient.post<PurchaseOrder>(ENDPOINTS.PURCHASE_ORDERS, purchaseOrder);
      return response;
    } catch (error) {
      if (error instanceof Error) {
        return rejectWithValue(error.message);
      }
      return rejectWithValue('An unknown error occurred');
    }
  }
);

export const updatePurchaseOrder = createAsyncThunk(
  'purchaseOrders/update',
  async ({ id, data }: { id: string | number, data: Partial<PurchaseOrder> }, { rejectWithValue }) => {
    try {
      const response = await apiClient.put<PurchaseOrder>(`${ENDPOINTS.PURCHASE_ORDERS}/${id}`, data);
      return response;
    } catch (error) {
      if (error instanceof Error) {
        return rejectWithValue(error.message);
      }
      return rejectWithValue('An unknown error occurred');
    }
  }
);

// Slice
const purchaseOrderSlice = createSlice({
  name: 'purchaseOrders',
  initialState,
  reducers: {
    setFilters(state, action: PayloadAction<Partial<PurchaseOrderFilters>>) {
      state.filters = { ...state.filters, ...action.payload };
    },
    toggleOrderExpand(state, action: PayloadAction<string | number>) {
      const order = state.orders.find(o => o.id === action.payload);
      if (order) {
        order.expanded = !order.expanded;
      }
    },
    clearErrors(state) {
      state.error = null;
    },
    setNotes(state, action: PayloadAction<{ orderId: string | number, notes: string }>) {
      const { orderId, notes } = action.payload;
      const order = state.orders.find(o => o.id === orderId);
      if (order) {
        order.notes = notes;
      }
    }
  },
  extraReducers: (builder) => {
    builder
      // Handle fetchPurchaseOrders
      .addCase(fetchPurchaseOrders.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchPurchaseOrders.fulfilled, (state, action) => {
        state.loading = false;
        // Add the expanded property to each order for UI state
        state.orders = action.payload.map(order => ({
          ...order,
          expanded: false
        }));
      })
      .addCase(fetchPurchaseOrders.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      
      // Handle fetchPurchaseOrderById
      .addCase(fetchPurchaseOrderById.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(fetchPurchaseOrderById.fulfilled, (state, action) => {
        state.loading = false;
        state.currentOrder = action.payload;
      })
      .addCase(fetchPurchaseOrderById.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      
      // Handle createPurchaseOrder
      .addCase(createPurchaseOrder.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(createPurchaseOrder.fulfilled, (state, action) => {
        state.loading = false;
        state.orders.push({ ...action.payload, expanded: false });
      })
      .addCase(createPurchaseOrder.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      
      // Handle updatePurchaseOrder
      .addCase(updatePurchaseOrder.pending, (state) => {
        state.loading = true;
        state.error = null;
      })
      .addCase(updatePurchaseOrder.fulfilled, (state, action) => {
        state.loading = false;
        const index = state.orders.findIndex(o => o.id === action.payload.id);
        if (index !== -1) {
          // Preserve the expanded state
          const expanded = state.orders[index].expanded;
          state.orders[index] = { ...action.payload, expanded };
        }
      })
      .addCase(updatePurchaseOrder.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
  },
});

// Export actions and reducer
export const { setFilters, toggleOrderExpand, clearErrors, setNotes } = purchaseOrderSlice.actions;
export default purchaseOrderSlice.reducer; 