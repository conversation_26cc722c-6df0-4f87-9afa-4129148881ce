// Purchase Order Types
export interface PurchaseOrderItem {
  id: string;
  sku: string;
  description: string;
  quantity_ordered: number;
  quantity_received: number;
  expected_delivery_date?: string;
  total: number;
  notes?: string;
}

export interface PurchaseOrder {
  id: string | number;
  supplier_name: string;
  issue_date: string;
  status: "draft" | "sent" | "delivered" | "backorder" | "new";
  email_sent: boolean;
  email_status?: "NOT_SENT" | "SENT" | "ALREADY_SENT";
  isEmailSending?: boolean;
  email_sent_message?: string;
  created_at: string;
  updated_at: string;
  details: PurchaseOrderItem[];
  expanded?: boolean; // UI state, not from API
  notes?: string;
}

// Create PO Request
export interface CreatePurchaseOrderRequest {
  supplier_id: number;
  issue_date: string;
  status: "draft" | "issued" | "received" | "cancelled" | "delivered";
  details: Omit<PurchaseOrderItem, "id">[];
  // Make these fields optional to handle database schema issues
  supplier_name?: string;
  store_type?: string;
  invoice_id?: string;
  payment_terms?: string;
}

// UI State for filters
export interface PurchaseOrderFilters {
  searchQuery: string;
  dateRange: string;
}
