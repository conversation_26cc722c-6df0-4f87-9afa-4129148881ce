import React, { useState, useEffect, useCallback, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import Layout from '../../components/layout/Layout';
import InvoiceView from '../../components/invoice/InvoiceView';
import Pagination from '../../components/common/Pagination';
import backButtonIcon from '../../assets/backButton.png';
import { format, parseISO } from 'date-fns';
import { API_URL, ENDPOINTS, AUTH_TOKEN_KEY } from '../../config';
import PageLoadingSpinner from '../../components/ui/PageLoadingSpinner';

const PageHeader = styled.div`
  padding: 2rem 2rem 3rem;
`;

const PageTitle = styled.h1`
  font-size: 2rem;
  color: #042B41;
  font-weight: 800;
  margin: 0;
`;

// Updated header container to match QuotesPage layout
const HeaderContainer = styled.div`
  display: flex;
  justify-content: flex-start;
  align-items: center;
  margin-bottom: 1.5rem;
`;

// Create TitleContainer similar to QuotesPage
const TitleContainer = styled.div`
  display: flex;
  align-items: center;
  gap: 10px;
`;

// Style for back button without text
const StyledBackButton = styled.button`
  display: flex;
  align-items: center;
  background: none;
  border: none;
  padding: 0;
  cursor: pointer;
  color: #042B41;
`;

const BackIcon = styled.img`
  width: 24px;
  height: 24px;
  margin-right: 8px;
`;

// Update filter container to use flexbox and align items
const FilterContainer = styled.div`
  display: flex;
  gap: 1rem;
  margin-bottom: 1.5rem;
  align-items: center;
  width: 100%;
`;

// Match the TabContainer from QuotesPage
const TabContainer = styled.div`
  display: flex;
  width: 321px;
  height: 44px;
  border-radius: 10px;
  border: 0.5px solid #E5E7EB;
  overflow: hidden;
`;

// Match the Tab from QuotesPage
const TabButton = styled.button<{ active: boolean }>`
  flex: 1;
  padding: 12px;
  background: ${props => props.active ? '#042B41' : 'white'};
  color: ${props => props.active ? 'white' : '#6B7280'};
  cursor: pointer;
  font-size: 14px;
  font-weight: ${props => props.active ? '600' : '500'};
  border: none;
  transition: all 0.2s ease-in-out;
  
  &:first-child {
    border-right: 0.5px solid #E5E7EB;
  }
  
  &:hover {
    background-color: ${props => props.active ? '#042B41' : '#f8f8f8'};
    color: ${props => props.active ? 'white' : '#042B41'};
  }
`;

// Update search container to match quotes page style
const SearchContainer = styled.div`
  position: relative;
  width: 193px;
  height: 42px;
`;

const SearchInput = styled.input`
  width: 100%;
  height: 100%;
  padding: 10px 35px;
  border: 1px solid #E5E7EB;
  border-radius: 10px;
  font-size: 14px;
  font-weight: 500;
  background-color: white;
  
  &:focus {
    outline: none;
    border-color: #042B41;
    box-shadow: 0 0 0 2px rgba(4, 43, 65, 0.1);
  }
  
  &::placeholder {
    color: #9CA3AF;
  }
`;

const SearchIconWrapper = styled.div`
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #9CA3AF;
  display: flex;
  align-items: center;
  justify-content: center;
  pointer-events: none;
`;

// Update filter dropdown to match other elements height
const FilterDropdown = styled.select`
  padding: 0.75rem;
  border: 1px solid #E5E7EB;
  border-radius: 10px;
  font-size: 14px;
  font-weight: 500;
  height: 42px;
  cursor: pointer;
  
  &:focus {
    outline: none;
    border-color: #042B41;
  }
`;

// Modify download button to push it to the right
const DownloadButton = styled.button`
  background-color: #042B41;
  color: white;
  border: none;
  border-radius: 10px;
  padding: 0.75rem 1.5rem;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  display: flex;
  align-items: center;
  height: 42px;
  transition: background-color 0.2s;
  margin-left: auto;
  
  svg {
    margin-right: 0.5rem;
  }
  
  &:hover {
    background-color: #031f30;
  }
`;

const TableContainer = styled.div`
  width: 100%;
  overflow-x: auto;
  margin-bottom: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  border: 1px solid #e0e0e0;
  border-radius: 8px;
`;

const Table = styled.table`
  width: 100%;
  border-collapse: collapse;
  background-color: white;
  table-layout: fixed;
`;

const TableHeader = styled.thead`
  background-color: #042B41;
  color: white;
  
  th {
    padding: 10px 12px;
    text-align: left;
    font-weight: 500;
    font-size: 14px;
    white-space: nowrap;
    vertical-align: middle;
    height: 48px;
  }
`;

const TableBody = styled.tbody`
  tr {
    border-bottom: 1px solid #e0e0e0;
    height: 48px;
    
    &:last-child {
      border-bottom: none;
    }
    
    &:hover {
      background-color: #f9fafb;
    }
  }
  
  td {
    padding: 10px 12px;
    color: #333;
    text-align: left;
    font-size: 14px;
    vertical-align: middle;
    border: 1px solid #e0e0e0;
    height: 48px;
    box-sizing: border-box;
  }
`;

const InvoiceLink = styled.a`
  color: #042B41;
  text-decoration: none;
  font-weight: 500;
  cursor: pointer;
  display: inline-block;
  
  &:hover {
    text-decoration: underline;
  }
`;

const POLink = styled.a`
  color: #042B41;
  text-decoration: none;
  font-weight: 500;
  cursor: pointer;
  display: inline-block;
  
  &:hover {
    text-decoration: underline;
  }
`;

// Loading and error states
const EmptyStateContainer = styled.div`
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 3rem;
  color: #6B7280;
  
  h3 {
    margin-top: 1rem;
    margin-bottom: 0.5rem;
    color: #374151;
  }
  
  p {
    margin-bottom: 0;
    max-width: 300px;
    text-align: center;
  }
`;

const ErrorContainer = styled.div`
  background-color: #FEF2F2;
  border: 1px solid #FCA5A5;
  color: #B91C1C;
  padding: 1rem;
  border-radius: 0.5rem;
  margin-bottom: 1.5rem;
`;

// Add a section title that updates based on active tab
const SectionTitle = styled.h2`
  font-size: 1.5rem;
  color: #042B41;
  font-weight: 600;
  margin: 1.5rem 0;
`;

// Add a TableRowSkeleton component for loading state
const TableRowSkeleton = styled.tr`
  height: 48px;
  
  td {
    position: relative;
    overflow: hidden;
    padding: 10px 12px;
    border: 1px solid #e0e0e0;
    
    &::after {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
      background-size: 200% 100%;
      animation: shimmer 1.5s infinite;
    }
  }
  
  @keyframes shimmer {
    0% {
      background-position: -200% 0;
    }
    100% {
      background-position: 200% 0;
    }
  }
`;

// Add a TableLoadingCell component for skeleton loading
const TableLoadingCell = styled.td`
  height: 48px;
  padding: 10px 12px !important;
  vertical-align: middle;
`;

// Define a type for invoice data
interface SalesReport {
  id: string;
  date: string;
  invoiceNumber: string;
  companyName: string;
  amount: string;
  paymentMode: string;
}

interface POReport {
  id: string | number;
  date: string;
  poNumber: string;
  supplierName: string;
  invoiceNumber: string;
  amount: string;
}

type Report = SalesReport | POReport;

const ReportsPage: React.FC = () => {
  // State management
  const [activeFilter, setActiveFilter] = useState<'sales' | 'po'>('sales');
  const [searchTerm, setSearchTerm] = useState('');
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [totalItems, setTotalItems] = useState(0);
  const [selectedInvoiceId, setSelectedInvoiceId] = useState<string | null>(null);
  const [viewingInvoice, setViewingInvoice] = useState(false);
  const [salesReports, setSalesReports] = useState<SalesReport[]>([]);
  const [poReports, setPOReports] = useState<POReport[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [dataFetchInProgress, setDataFetchInProgress] = useState<{ sales: boolean, po: boolean }>({ sales: false, po: false });

  const navigate = useNavigate();
  const searchTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const apiCallInProgress = useRef<boolean>(false);

  // Calculate pagination values
  const totalPages = Math.ceil(totalItems / itemsPerPage);

  // Implement search term debounce
  useEffect(() => {
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }

    searchTimeoutRef.current = setTimeout(() => {
      setDebouncedSearchTerm(searchTerm);
      setCurrentPage(1); // Reset to first page when search changes
    }, 500); // 500ms debounce delay

    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, [searchTerm]);

  // Fetch sales report data with useCallback to prevent duplicate API calls
  const fetchSalesReports = useCallback(async () => {
    // Return early if already loading or fetch in progress
    if (apiCallInProgress.current || dataFetchInProgress.sales) return;

    setDataFetchInProgress(prev => ({ ...prev, sales: true }));
    setIsLoading(true);
    setError(null);
    apiCallInProgress.current = true;

    try {
      // Get the token for authentication
      const token = localStorage.getItem(AUTH_TOKEN_KEY);

      // Use the reports API endpoint for sales data with proper pagination
      let endpoint = `${API_URL}/api/v1/reports/sales?page=${currentPage}&limit=${itemsPerPage}`;
      if (debouncedSearchTerm) {
        endpoint += `&search=${encodeURIComponent(debouncedSearchTerm)}`;
      }

      console.log('Fetching sales report data from:', endpoint);

      // Make a direct fetch request with proper headers
      const response = await fetch(endpoint, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'Authorization': token ? `Bearer ${token}` : ''
        },
        credentials: 'include',
        mode: 'cors'
      });

      if (!response.ok) {
        throw new Error(`Server responded with status: ${response.status}`);
      }

      const data = await response.json();

      if (!data || !Array.isArray(data.items)) {
        throw new Error('Invalid response format from server');
      }

      // Map the sales data from the reports API
      const formattedSalesData: SalesReport[] = data.items.map((sale: any) => ({
        id: sale.invoice_number || `SALE-${Date.now()}`, // Use invoice_number as ID
        date: formatDate(sale.date),
        invoiceNumber: sale.invoice_number || 'N/A',
        companyName: sale.company_name || 'N/A',
        amount: formatCurrency(sale.total_amount),
        paymentMode: formatPaymentMode(sale.mode_of_payment)
      }));

      setSalesReports(formattedSalesData);
      setTotalItems(data.total || 0);
    } catch (error) {
      console.error('Error fetching sales reports:', error);
      setError('Failed to load sales report data. ' + (error instanceof Error ? error.message : ''));
      setSalesReports([]);
      setTotalItems(0);
    } finally {
      setIsLoading(false);
      setDataFetchInProgress(prev => ({ ...prev, sales: false }));
      apiCallInProgress.current = false;
    }
  }, [currentPage, itemsPerPage, debouncedSearchTerm]);

  // Fetch purchase order report data with useCallback to prevent duplicate API calls
  const fetchPOReports = useCallback(async () => {
    // Return early if already loading or fetch in progress
    if (apiCallInProgress.current || dataFetchInProgress.po) return;

    setDataFetchInProgress(prev => ({ ...prev, po: true }));
    setIsLoading(true);
    setError(null);
    apiCallInProgress.current = true;

    try {
      // Get the token for authentication
      const token = localStorage.getItem(AUTH_TOKEN_KEY);

      // Use the reports API endpoint for purchase orders
      let endpoint = `${API_URL}/api/v1/reports/purchase-orders?page=${currentPage}&limit=${itemsPerPage}`;
      if (debouncedSearchTerm) {
        endpoint += `&search=${encodeURIComponent(debouncedSearchTerm)}`;
      }

      console.log('Fetching PO report data from:', endpoint);

      // Make a direct fetch request with proper headers
      const response = await fetch(endpoint, {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'Content-Type': 'application/json',
          'Authorization': token ? `Bearer ${token}` : ''
        },
        credentials: 'include',
        mode: 'cors'
      });

      if (!response.ok) {
        throw new Error(`Server responded with status: ${response.status}`);
      }

      const data = await response.json();

      if (!data || !Array.isArray(data.items)) {
        throw new Error('Invalid response format from server');
      }

      // Format the PO data from the reports API
      const formattedPOData: POReport[] = data.items.map((po: any) => ({
        id: po.po_number || `PO-${Date.now()}`, // Use po_number as ID, fallback to timestamp
        date: formatDate(po.date),
        poNumber: po.po_number || 'N/A',
        supplierName: po.supplier_name || 'N/A',
        invoiceNumber: po.linked_invoice_number || 'N/A', // Use the linked_invoice_number from API
        amount: formatCurrency(po.total_amount)
      }));

      setPOReports(formattedPOData);
      setTotalItems(data.total || 0);
    } catch (error) {
      console.error('Error fetching PO reports:', error);
      setError('Failed to load purchase order report data. ' + (error instanceof Error ? error.message : ''));
      setPOReports([]);
      setTotalItems(0);
    } finally {
      setIsLoading(false);
      setDataFetchInProgress(prev => ({ ...prev, po: false }));
      apiCallInProgress.current = false;
    }
  }, [currentPage, itemsPerPage, debouncedSearchTerm]);

  // Helper to format dates from API
  const formatDate = (dateStr: string): string => {
    try {
      if (!dateStr) return 'N/A';
      const date = parseISO(dateStr);
      return format(date, 'dd/MM/yyyy');
    } catch (err) {
      console.error('Error formatting date:', err);
      return dateStr || 'N/A';
    }
  };

  // Helper to format currency
  const formatCurrency = (amount: number | string): string => {
    if (amount === undefined || amount === null) return '$0.00';

    const numAmount = typeof amount === 'string' ? parseFloat(amount) : amount;

    return `$${numAmount.toLocaleString('en-AU', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    })}`;
  };

  // Helper to format payment mode
  const formatPaymentMode = (mode?: string): string => {
    if (!mode) return 'N/A';

    // Convert snake_case to Title Case
    return mode
      .split('_')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1).toLowerCase())
      .join(' ');
  };

  // Use useEffect with the memoized fetch functions
  useEffect(() => {
    if (activeFilter === 'sales') {
      fetchSalesReports();
    } else {
      fetchPOReports();
    }
  }, [activeFilter, fetchSalesReports, fetchPOReports]);

  // Navigation functions
  const handleBack = () => {
    navigate('/home');
  };

  const handleFilterChange = (filter: 'sales' | 'po') => {
    if (filter !== activeFilter) {
      setActiveFilter(filter);
      setCurrentPage(1); // Reset to first page
      setSearchTerm(''); // Clear search
      setSelectedInvoiceId(null); // Clear any selected invoice
      setViewingInvoice(false); // Exit invoice viewing mode
      setDataFetchInProgress({ sales: false, po: false }); // Reset data fetched flag
      // Reset data for previous filter
      if (filter === 'sales') {
        setPOReports([]); // Clear PO reports when switching to sales
      } else {
        setSalesReports([]); // Clear sales reports when switching to PO
      }
    }
  };

  // CSV export function
  const handleDownloadCSV = () => {
    const data = activeFilter === 'sales' ? salesReports : poReports;
    if (data.length === 0) return;

    let csvContent = '';

    // Add headers
    if (activeFilter === 'sales') {
      csvContent = 'Date,Invoice Number,Company Name,Total Amount (AUD),Payment Mode\n';

      // Add data rows for sales
      (data as SalesReport[]).forEach(item => {
        csvContent += `${item.date},${item.invoiceNumber},"${item.companyName}",${item.amount},${item.paymentMode}\n`;
      });
    } else {
      csvContent = 'Date,PO Number,Supplier Name,Linked Invoice,Total Amount (AUD)\n';

      // Add data rows for PO
      (data as POReport[]).forEach(item => {
        csvContent += `${item.date},${item.poNumber},"${item.supplierName}",${item.invoiceNumber},${item.amount}\n`;
      });
    }

    // Create and trigger download
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.setAttribute('href', url);
    link.setAttribute('download', `${activeFilter === 'sales' ? 'sales' : 'purchase_orders'}_report.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  const handleViewInvoice = (invoiceId: string) => {
    setSelectedInvoiceId(invoiceId);
    setViewingInvoice(true);
  };

  const handleCloseInvoice = () => {
    setSelectedInvoiceId(null);
    setViewingInvoice(false);
  };

  // Type guards for the Report union type
  const isSalesReport = (report: Report): report is SalesReport => {
    return (report as SalesReport).paymentMode !== undefined;
  };

  const isPOReport = (report: Report): report is POReport => {
    return (report as POReport).poNumber !== undefined;
  };

  // Render skeleton rows for loading state
  const renderSkeletonRows = (count = 5) => {
    return Array(count).fill(0).map((_, index) => (
      <TableRowSkeleton key={`skeleton-${index}`}>
        <TableLoadingCell style={{ width: '15%' }}>
          <div style={{ height: '20px', background: '#f0f0f0', borderRadius: '4px' }}></div>
        </TableLoadingCell>
        <TableLoadingCell style={{ width: '20%' }}>
          <div style={{ height: '20px', background: '#f0f0f0', borderRadius: '4px' }}></div>
        </TableLoadingCell>
        <TableLoadingCell style={{ width: '30%' }}>
          <div style={{ height: '20px', background: '#f0f0f0', borderRadius: '4px' }}></div>
        </TableLoadingCell>
        <TableLoadingCell style={{ width: '15%' }}>
          <div style={{ height: '20px', background: '#f0f0f0', borderRadius: '4px' }}></div>
        </TableLoadingCell>
        <TableLoadingCell style={{ width: '20%' }}>
          <div style={{ height: '20px', background: '#f0f0f0', borderRadius: '4px' }}></div>
        </TableLoadingCell>
      </TableRowSkeleton>
    ));
  };

  // Render the appropriate table based on filter
  const renderTable = () => {
    // Always display the table structure
    return (
      <TableContainer>
        <Table>
          <TableHeader>
            {activeFilter === 'sales' ? (
              <tr>
                <th style={{ width: '15%' }}>Date</th>
                <th style={{ width: '20%' }}>Invoice Number</th>
                <th style={{ width: '30%' }}>Company Name</th>
                <th style={{ width: '15%' }}>Total Amount (AUD)</th>
                <th style={{ width: '20%' }}>Mode of Payment</th>
              </tr>
            ) : (
              <tr>
                <th style={{ width: '15%' }}>Date</th>
                <th style={{ width: '20%' }}>PO Number</th>
                <th style={{ width: '30%' }}>Supplier Name</th>
                <th style={{ width: '15%' }}>Linked to Invoice</th>
                <th style={{ width: '20%' }}>Total Amount (AUD)</th>
              </tr>
            )}
          </TableHeader>
          <TableBody>
            {isLoading ? (
              // Show skeleton loading rows
              renderSkeletonRows()
            ) : error ? (
              // Show error message
              <tr>
                <td colSpan={5} style={{ textAlign: 'center', padding: '2rem' }}>
                  <p style={{ color: '#e53e3e' }}>{error}</p>
                </td>
              </tr>
            ) : activeFilter === 'sales' && salesReports.length > 0 ? (
              // Show sales data
              salesReports.map(report => (
                <tr key={report.id}>
                  <td>{report.date}</td>
                  <td>
                    <InvoiceLink onClick={() => handleViewInvoice(report.id)}>
                      {report.invoiceNumber}
                    </InvoiceLink>
                  </td>
                  <td>{report.companyName}</td>
                  <td>{report.amount}</td>
                  <td>{report.paymentMode}</td>
                </tr>
              ))
            ) : activeFilter === 'po' && poReports.length > 0 ? (
              // Show PO data
              poReports.map(report => (
                <tr key={report.id}>
                  <td>{report.date}</td>
                  <td>
                    <POLink>
                      {report.poNumber}
                    </POLink>
                  </td>
                  <td>{report.supplierName}</td>
                  <td>{report.invoiceNumber}</td>
                  <td>{report.amount}</td>
                </tr>
              ))
            ) : (
              // Display empty state message in the table body
              <tr>
                <td colSpan={5} style={{ textAlign: 'center', padding: '2rem' }}>
                  {activeFilter === 'sales' ? (
                    <>
                      <h3 style={{ margin: '0 0 0.5rem' }}>No Sales Reports</h3>
                      <p style={{ margin: 0 }}>No sales reports found for the selected filters.</p>
                    </>
                  ) : (
                    <>
                      <h3 style={{ margin: '0 0 0.5rem' }}>No Purchase Order Reports</h3>
                      <p style={{ margin: 0 }}>No purchase order reports found for the selected filters.</p>
                    </>
                  )}
                </td>
              </tr>
            )}
          </TableBody>
        </Table>
      </TableContainer>
    );
  };

  // Helper to determine search placeholder
  const getSearchPlaceholder = () => {
    return activeFilter === 'sales'
      ? 'Search invoices'
      : 'Search purchase orders';
  };

  // Pagination handlers
  const handlePageChange = (pageNumber: number) => {
    if (pageNumber !== currentPage) {
      setCurrentPage(pageNumber);
      window.scrollTo(0, 0); // Scroll back to top on page change

      // Data will be re-fetched via the useEffect that depends on currentPage
    }
  };

  const handleItemsPerPageChange = (newItemsPerPage: number) => {
    setItemsPerPage(newItemsPerPage);
    setCurrentPage(1);
  };

  return (
    <Layout>
      <PageHeader>
        <HeaderContainer>
          <TitleContainer>
            <StyledBackButton onClick={handleBack}>
              <BackIcon src={backButtonIcon} alt="Back" />
            </StyledBackButton>
            <PageTitle>Reports</PageTitle>
          </TitleContainer>
        </HeaderContainer>

        <FilterContainer>
          <TabContainer>
            <TabButton
              active={activeFilter === 'sales'}
              onClick={() => handleFilterChange('sales')}
            >
              Sales Reports
            </TabButton>
            <TabButton
              active={activeFilter === 'po'}
              onClick={() => handleFilterChange('po')}
            >
              Purchase Orders
            </TabButton>
          </TabContainer>

          <SearchContainer>
            <SearchIconWrapper>
              <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <circle cx="11" cy="11" r="8"></circle>
                <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
              </svg>
            </SearchIconWrapper>
            <SearchInput
              type="text"
              placeholder={getSearchPlaceholder()}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </SearchContainer>

          <DownloadButton
            onClick={handleDownloadCSV}
            disabled={isLoading || (activeFilter === 'sales' ? salesReports.length === 0 : poReports.length === 0)}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
              <polyline points="7 10 12 15 17 10"></polyline>
              <line x1="12" y1="15" x2="12" y2="3"></line>
            </svg>
            Download as CSV
          </DownloadButton>
        </FilterContainer>

        <SectionTitle>
          {activeFilter === 'sales' ? 'Sales Reports' : 'Purchase Order Reports'}
        </SectionTitle>

        {renderTable()}

        {(activeFilter === 'sales' ? salesReports.length > 0 : poReports.length > 0) && (
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            onPageChange={handlePageChange}
            totalItems={totalItems}
            itemsPerPage={itemsPerPage}
            onItemsPerPageChange={handleItemsPerPageChange}
            itemsPerPageOptions={[10, 20, 30]}
            showItemsPerPage={true}
          />
        )}

        {viewingInvoice && selectedInvoiceId && (
          <InvoiceView
            invoiceId={selectedInvoiceId}
            onClose={handleCloseInvoice}
          />
        )}
      </PageHeader>
    </Layout>
  );
};

export default ReportsPage; 