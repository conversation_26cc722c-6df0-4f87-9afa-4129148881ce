import React from "react";
import { Route, Routes } from "react-router-dom";
import Dashboard from "./components/Dashboard";
import PurchaseOrderList from "./components/PurchaseOrderList";
import PurchaseOrderDetails from "./components/poManagement/PurchaseOrderDetails";
import InboundDeliveryCalendar from "./components/InboundDeliveryCalendar";

/**
 * Application Routes
 *
 * This component defines all the routes for the application.
 * For PurchaseOrderDetails, we use a dynamic route parameter :poNumber
 * to pass the purchase order number to the component.
 */
const AppRoutes = () => {
  return (
    <Routes>
      <Route path="/" element={<Dashboard />} />
      <Route path="/purchase-orders" element={<PurchaseOrderList />} />
      <Route
        path="/purchase-orders/:poNumber"
        element={<PurchaseOrderDetails />}
      />
      <Route
        path="/inbound-delivery/calendar"
        element={<InboundDeliveryCalendar />}
      />
    </Routes>
  );
};

export default AppRoutes;
