import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { getAuthToken } from '../utils/api';
import { ENDPOINTS, API_URL, AUTH_TOKEN_KEY, RETRY_CONFIG } from '../config';
import { ApiError } from '../services/apiClient';
import { authenticatedFetch } from '../services/apiInterceptor';
import { useLocation } from 'react-router-dom';

// Construct the API base URL properly
const API_BASE_URL = `${API_URL}/api/v1`;

// Store original fetch to avoid recursion
const originalFetch = window.fetch;

export interface User {
  id: string; // User ID as UUID string
  user_name: string;
  email: string; // Add email property
  role: string;
  store_type?: {
    id: number;
    name: string;
  } | null;
}

// Define the response type for login
interface LoginResponse {
  access_token: string;
  token_type: string;
  user: {
    id: string;
    user_name: string;
    email: string;
    role: string;
    store_type?: {
      id: number;
      name: string;
    } | null;
  };
}

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  token: string | null;
  login: (username: string, password: string) => Promise<void>;
  logout: () => void;
  isCustomerPath: (path: string) => boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [token, setToken] = useState<string | null>(getAuthToken());
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(!!token);
  const [isLoadingUser, setIsLoadingUser] = useState<boolean>(false);

  // Function to check if a path is customer related
  const isCustomerPath = useCallback((path: string): boolean => {
    return path.includes('/customers');
  }, []);

  // Memoize logout to prevent recreating the function on each render
  const logout = useCallback(() => {
    console.log('Logging out user');
    localStorage.removeItem(AUTH_TOKEN_KEY);
    setToken(null);
    setUser(null);
    setIsAuthenticated(false);
  }, []);

  // Fetch user info with retry mechanism using direct fetch
  const fetchUserInfo = useCallback(async (authToken: string) => {
    if (isLoadingUser || !authToken) return;

    setIsLoadingUser(true);
    let retryCount = 0;
    const MAX_RETRIES = 2;  // Limit to 2 retries to prevent infinite loops

    const attemptFetch = async (): Promise<User> => {
      try {
        console.log(`Fetching user info attempt ${retryCount + 1}/${MAX_RETRIES}`);
        
        // Log the token being used
        console.log('Using auth token:', authToken);
        
        const response = await originalFetch(`${API_URL}${ENDPOINTS.CURRENT_USER}`, {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
            'Authorization': `Bearer ${authToken}`,
            'Content-Type': 'application/json',
            'Origin': window.location.origin,
            'Referer': window.location.href
          },
          credentials: 'omit',
          mode: 'cors'
        });
        
        // Log response details for debugging
        console.log('Response status:', response.status);
        console.log('Response headers:', Object.fromEntries(response.headers));
        
        const responseText = await response.text();
        console.log('Raw response:', responseText);
        
        if (!response.ok) {
          if (response.status === 401) {
            console.error('Authentication failed. Token:', authToken);
            console.error('Response:', responseText);
            throw new Error('Authentication failed');
          }
          throw new Error(`HTTP error ${response.status}: ${responseText}`);
        }
        
        let userData;
        try {
          userData = JSON.parse(responseText);
          console.log('Parsed user data:', userData);
        } catch (e) {
          console.error('Failed to parse user data:', e);
          console.error('Raw response was:', responseText);
          throw new Error('Invalid JSON response');
        }

        // Verify the role in the response
        if (!userData.role) {
          console.error('No role found in user data:', userData);
          throw new Error('Invalid user data: missing role');
        }

        return userData;
      } catch (error) {
        console.error(`Error fetching user info (attempt ${retryCount + 1}):`, error);
        
        // Only retry on network errors or 5xx server errors, not on 401
        const isNetworkError = error instanceof TypeError;
        const is5xxError = error instanceof Error && 
                           error.message.includes('HTTP error 5');
        
        if (retryCount < MAX_RETRIES - 1 && (isNetworkError || is5xxError)) {
          retryCount++;
          console.log(`Retrying in ${RETRY_CONFIG.RETRY_DELAY}ms...`);
          await new Promise(resolve => setTimeout(resolve, RETRY_CONFIG.RETRY_DELAY));
          return attemptFetch();
        }
        
        throw error;
      }
    };

    try {
      const userData = await attemptFetch();
      console.log('Setting user data:', userData);
      setUser(userData);
      setIsAuthenticated(true);
    } catch (error) {
      console.error('Failed to fetch user info:', error);
      // Don't logout for 404 errors, just set a default user
      if (error instanceof Error && error.message.includes('HTTP error 404')) {
        console.log('User endpoint not found, using default guest user');
        setUser({
          id: 'guest',
          user_name: 'Guest User',
          email: '<EMAIL>',
          role: 'guest',
          store_type: null
        });
        setIsAuthenticated(true);
      } else if (error instanceof Error && error.message === 'Authentication failed') {
        logout();
      }
    } finally {
      setIsLoadingUser(false);
    }
  }, [isLoadingUser, logout]);

  // Check if token exists on mount and set authentication state
  useEffect(() => {
    const storedToken = getAuthToken();
    if (storedToken && !user && !isLoadingUser) {
      setToken(storedToken);
      setIsAuthenticated(true);
      fetchUserInfo(storedToken).catch(console.error);
    }
  }, [fetchUserInfo, user, isLoadingUser]);

  const login = useCallback(async (username: string, password: string) => {
    try {
      console.log('Attempting login with username:', username);

      // Create form data
      const formData = new URLSearchParams();
      formData.append('username', username);
      formData.append('password', password);
      formData.append('grant_type', 'password');

      // Make login request
      const response = await originalFetch(`${API_URL}${ENDPOINTS.LOGIN}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Accept': 'application/json',
          'Origin': window.location.origin,
          'Referer': window.location.href
        },
        body: formData.toString(),
        credentials: 'omit',
        mode: 'cors'
      });

      // Get response text first
      const responseText = await response.text();
      console.log('Login response status:', response.status);
      console.log('Login response headers:', Object.fromEntries(response.headers));
      console.log('Raw login response:', responseText);
      
      if (!response.ok) {
        if (response.status === 422) {
          let errorDetail;
          try {
            const errorJson = JSON.parse(responseText);
            errorDetail = errorJson.detail || 'Validation error';
            console.error('Login validation error details:', errorJson);
          } catch (e) {
            errorDetail = responseText;
          }
          throw new Error(`Validation error: ${errorDetail}`);
        }
        
        console.error('Login failed:', responseText);
        throw new Error(`Login failed: ${response.status} ${response.statusText}`);
      }
      
      // Try to parse the response as JSON
      let data: LoginResponse;
      try {
        data = JSON.parse(responseText);
        console.log('Parsed login response:', data);
      } catch (e) {
        console.error('Failed to parse login response as JSON:', e);
        throw new Error('Invalid response format from server');
      }
      
      // Verify the user data in the response
      if (!data.user) {
        console.error('No user data found in login response:', data);
        throw new Error('Invalid login response: missing user data');
      }

      console.log('Login successful, token received. User data:', data.user);

      // Store token in localStorage
      const accessToken = data.access_token;
      localStorage.setItem(AUTH_TOKEN_KEY, accessToken);

      // Set initial user data from login response
      const initialUser: User = {
        id: data.user.id,
        user_name: data.user.user_name || username,
        email: data.user.email || username,
        role: data.user.role,
        store_type: data.user.store_type || null
      };
      console.log('Setting initial user data:', initialUser);

      // Update all state at once to prevent multiple renders
      setToken(accessToken);
      setUser(initialUser);
      setIsAuthenticated(true);

      // Don't fetch user info again since we already have it from login response
      // This prevents the double state update that was causing concurrent rendering issues

      return Promise.resolve();
    } catch (error) {
      console.error('Login error:', error);
      // Provide more specific error messages based on the error type
      if (error instanceof Error) {
        if (error.message.includes('Validation error')) {
          return Promise.reject(new Error(error.message));
        } else if (error.message.includes('Invalid response format')) {
          return Promise.reject(new Error('Server response was not in the expected format'));
        }
      }
      return Promise.reject(error);
    }
  }, []);

  // Memoize the context value to prevent unnecessary re-renders
  const contextValue = React.useMemo(() => ({
    user,
    isAuthenticated,
    token,
    login,
    logout,
    isCustomerPath
  }), [user, isAuthenticated, token, login, logout, isCustomerPath]);

  return (
    <AuthContext.Provider value={contextValue}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  
  const location = useLocation();
  const pathname = location.pathname;

  // Memoize the customer path override to prevent infinite re-renders
  // Use more stable dependencies to prevent unnecessary re-creation
  return React.useMemo(() => {
    // Override isAuthenticated if we're on a customer path
    if (context.isCustomerPath(pathname)) {
      return {
        ...context,
        isAuthenticated: true,
        user: context.user || { 
          id: 'guest', 
          user_name: 'Guest User', 
          email: '<EMAIL>', 
          role: 'guest',
          store_type: null
        }
      };
    }
    
    return context;
  }, [context.user, context.isAuthenticated, context.token, pathname, context.login, context.logout, context.isCustomerPath]);
};