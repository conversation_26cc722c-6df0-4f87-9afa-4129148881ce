{"name": "builders-warehouse", "version": "0.1.0", "private": true, "dependencies": {"@reduxjs/toolkit": "^2.7.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/lodash": "^4.17.17", "@types/node": "^16.18.126", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@types/react-router-dom": "^5.3.3", "@types/styled-components": "^5.1.34", "ag-grid-community": "^33.2.4", "ag-grid-react": "^33.2.4", "axios": "^1.9.0", "chart.js": "^4.4.3", "date-fns": "^4.1.0", "exceljs": "^4.4.0", "html2pdf.js": "^0.10.3", "lodash": "^4.17.21", "node-fetch": "^3.3.2", "react": "^19.1.0", "react-chartjs-2": "^5.2.0", "react-dom": "^19.1.0", "react-icons": "^5.5.0", "react-redux": "^9.2.0", "react-router-dom": "^7.5.1", "react-scripts": "5.0.1", "react-toastify": "^11.0.5", "redux": "^5.0.1", "redux-thunk": "^3.1.0", "styled-components": "^6.1.17", "typescript": "^4.9.5", "web-vitals": "^2.1.4"}, "scripts": {"start": "PORT=4200 react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@types/uuid": "^10.0.0", "http-proxy-middleware": "^3.0.5"}, "proxy": "http://localhost:8000"}