# Forgot Password Modal Feature

## Overview
A new forgot password modal has been added to the login page that matches the exact design and styling of the existing login form.

## Features
- **Consistent Design**: The modal uses the same styling as the login form including:
  - Same background overlay (rgba(0, 0, 0, 0.5))
  - Same white container with 40px border radius
  - Same padding (2.5rem)
  - Same box shadow (0 4px 20px rgba(0, 0, 0, 0.25))
  - Same logo and title styling
  - Same form input styling with focus states
  - Same button styling with hover effects

- **User Experience**:
  - <PERSON><PERSON> opens when clicking "Forgot your password?" on the login page
  - Contains the Builders Warehouse logo
  - Title: "Reset Your Password"
  - Single email input field with placeholder: "Enter Your Registered email id"
  - "Send Reset Link" button with loading state
  - "Back to Login" button to close the modal
  - Form validation for email format
  - Success/error message display
  - Auto-closes after successful submission

## Files Added/Modified

### New Files:
- `src/components/common/ForgotPasswordModal.tsx` - The main modal component

### Modified Files:
- `src/pages/Login.tsx` - Added modal integration and state management

## Component Structure

```typescript
interface ForgotPasswordModalProps {
  isOpen: boolean;
  onClose: () => void;
}
```

## Usage
The modal is automatically integrated into the login page. Users can:
1. Click "Forgot your password?" link on the login form
2. Enter their registered email address
3. Click "Send Reset Link" to submit the request
4. See success/error messages
5. Modal auto-closes on success or can be manually closed

## Styling
The modal maintains complete visual consistency with the login form:
- Same color scheme (#042B41 for primary elements)
- Same typography and spacing
- Same form field styling with focus states
- Same button styling with hover effects
- Responsive design matching the login form

## Future Enhancements
- Integration with actual password reset API endpoint
- Email template for reset links
- Password reset confirmation page
- Rate limiting for reset requests

## Accessibility
- Proper keyboard navigation
- Screen reader compatible
- Focus management
- Semantic HTML structure 