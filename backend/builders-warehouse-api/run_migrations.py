#!/usr/bin/env python3
"""
Run database migrations using Alembic.
This script runs the migrations defined in src/migrations.
"""

import os
import sys
import subprocess
import logging

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def run_migrations():
    """Run the Alembic migrations."""
    try:
        # Ensure we're in the correct directory
        script_dir = os.path.dirname(os.path.abspath(__file__))
        os.chdir(script_dir)
        
        logger.info("Running database migrations...")
        
        # Run the Alembic migrations
        subprocess.check_call(["alembic", "upgrade", "head"])
        
        logger.info("Migrations completed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        logger.error(f"Migration failed with error code {e.returncode}: {e}")
        return False
    except Exception as e:
        logger.error(f"An unexpected error occurred: {e}")
        return False

if __name__ == "__main__":
    success = run_migrations()
    sys.exit(0 if success else 1) 