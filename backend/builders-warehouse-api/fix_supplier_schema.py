#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to add the missing is_active column to the Supplier table
"""
import sys
import os

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from src.database import engine
from sqlalchemy import text, inspect
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def fix_supplier_schema():
    """Add is_active column to Supplier table if it doesn't exist"""
    try:
        # Check if is_active column exists in Supplier table
        inspector = inspect(engine)
        columns = [c['name'] for c in inspector.get_columns('Supplier')]
        
        logger.info(f'Current Supplier table columns: {columns}')
        
        if 'is_active' not in columns:
            logger.info('Adding is_active column to Supplier table...')
            with engine.connect() as conn:
                with conn.begin():
                    conn.execute(text('ALTER TABLE "Supplier" ADD COLUMN is_active BOOLEAN DEFAULT TRUE'))
            logger.info('Successfully added is_active column')
            
            # Update existing records to have is_active = TRUE
            with engine.connect() as conn:
                with conn.begin():
                    result = conn.execute(text('UPDATE "Supplier" SET is_active = TRUE WHERE is_active IS NULL'))
                    logger.info(f'Updated {result.rowcount} existing supplier records')
        else:
            logger.info('is_active column already exists in Supplier table')
            
        # Verify the column was added
        inspector = inspect(engine)
        columns = [c['name'] for c in inspector.get_columns('Supplier')]
        logger.info(f'Final Supplier table columns: {columns}')
        
        return True
        
    except Exception as e:
        logger.error(f'Error fixing supplier schema: {e}')
        return False

if __name__ == "__main__":
    success = fix_supplier_schema()
    if success:
        print("Supplier schema fix completed successfully")
    else:
        print("Supplier schema fix failed")
        sys.exit(1) 