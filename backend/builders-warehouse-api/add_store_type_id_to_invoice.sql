-- Migration to add store_type_id column to Invoice table
-- Run this SQL script in your PostgreSQL database

-- Add the store_type_id column with foreign key constraint
ALTER TABLE "Invoice" 
ADD COLUMN IF NOT EXISTS store_type_id INTEGER REFERENCES "StoreType"(id);

-- Create an index for better performance
CREATE INDEX IF NOT EXISTS ix_invoice_store_type_id ON "Invoice" (store_type_id);

-- Update existing records based on store_type_name if possible
-- First, try to match existing store_type_name values with StoreType.name
UPDATE "Invoice" 
SET store_type_id = (
    SELECT st.id 
    FROM "StoreType" st 
    WHERE LOWER(st.name) = LOWER("Invoice".store_type_name)
    LIMIT 1
)
WHERE store_type_id IS NULL AND store_type_name IS NOT NULL;

-- Update records where store_type_name appears to be an ID (numeric)
UPDATE "Invoice" 
SET store_type_id = CAST(store_type_name AS INTEGER)
WHERE store_type_id IS NULL 
    AND store_type_name IS NOT NULL 
    AND store_type_name ~ '^[0-9]+$'
    AND EXISTS (SELECT 1 FROM "StoreType" WHERE id = CAST("Invoice".store_type_name AS INTEGER));

-- For any remaining records without store_type_id, set to default (first store type)
UPDATE "Invoice" 
SET store_type_id = (
    SELECT id FROM "StoreType" ORDER BY id LIMIT 1
)
WHERE store_type_id IS NULL; 