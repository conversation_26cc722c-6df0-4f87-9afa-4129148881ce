# Builders Warehouse API

A FastAPI backend for the Builders Warehouse application, providing authentication and data storage capabilities.

brew install pango gdk-pixbuf cairo

## Features

- User authentication with JWT tokens
- Email and username-based login options
- Audit logging for tracking database changes (create, update, delete operations)
- PostgreSQL database integration with SQLite fallback
- Clean architecture with separation of concerns
- Swagger/ReDoc API documentation
- Docker support
- Organized log rotation with daily folders and automatic cleanup
- Quote Management System with CRUD operations
- Automated Invoice to Purchase Order integration with configurable email notifications

## Project Structure

The project follows clean architecture principles with a clear separation of concerns:

```
.
├── deployment/              # Deployment configuration files
├── logs/                    # Application logs
│   ├── MM-DD-YYYY/          # Daily log folders
│   ├── app.log              # Symlink to latest app log
│   └── audit.log            # Symlink to latest audit log
├── scripts/                 # Administration and utility scripts
│   ├── create_admin.py      # Create/update admin users
│   └── fetch_audit_logs.py  # Fetch and display audit logs
├── src/
│   ├── api/                 # API endpoint definitions
│   │   └── v1/              # API version 1 routes
│   ├── controllers/         # Route handler logic
│   ├── services/            # Business logic and operations
│   ├── models/              # SQLAlchemy database models
│   ├── schemas/             # Pydantic models for I/O
│   ├── core/                # Core application components
│   │   ├── config/          # Configuration settings
│   │   └── initializer/     # Application initialization
│   ├── db/                  # Database components
│   │   ├── migrations.py    # Database migrations
│   │   ├── session.py       # Database session management
│   │   └── base.py          # Base model definition
│   ├── middleware/          # Custom middleware
│   ├── utils/               # Utility functions
│   └── main.py              # Application entrypoint
├── tests/                   # Unit and integration tests
├── main.py                  # Application entry point
├── env.example              # Example environment variables
├── Dockerfile               # Docker configuration
├── README.md                # Project documentation
└── requirements.txt         # Python dependencies
```

## Database Migrations

The application uses a combination of Alembic-based migrations and direct database migrations for schema evolution. The following migrations are automatically applied during application startup:

### Core Migrations

These migrations are defined in `src/db/migrations.py` and run sequentially:

1. **is_admin column**: Adds the `is_admin` column to the users table
2. **Audit Logs Table**: Creates the `audit_logs` table for tracking changes
3. **Store Types Table**: Creates the `store_types` table with default store types
4. **Customers Module Tables**: Creates the `price_lists`, `customers`, and `invoices` tables
5. **Suppliers Table**: Creates the `suppliers` table with price list support
6. **Roles Table**: Creates the `roles` table and migrates user roles from enum to foreign key references

### Role Migration

A dedicated role migration (`scripts/migrate_roles.py`) was implemented to handle the transition from string-based roles to a proper role table:

1. **Creates Roles Table**: With default roles (admin, manager, staff)
2. **Adds role_id Column**: To the users table as a foreign key
3. **Maps Existing Roles**: Converts existing role strings to role_id references
4. **Adds Foreign Key Constraint**: Between users and roles tables
5. **Makes role_id Required**: By setting the column to non-nullable
6. **Drops Old role Column**: Removes the redundant column after migration

### Running Migrations Manually

You can run migrations manually using:

```bash
# Run all Alembic migrations
python run_migrations.py

# Run the dedicated role migration script
python -m scripts.migrate_roles
```

## Prerequisites

- Python 3.8+
- PostgreSQL (optional, SQLite used as fallback)

## Setup

1. Clone the repository:

   ```
   git clone <repository-url>
   cd builders-warehouse-api
   ```

2. Create a virtual environment and install dependencies:

   ```
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   pip install -r requirements.txt
   ```

3. Create a `.env` file based on `env.example` and update with your configuration:

   ```
   # PostgreSQL connection (recommended for production)
   DATABASE_URL=postgresql://warehouse_user:Password#123@localhost:5432/builders_warehouse

   # SQLite connection (for testing only)
   # DATABASE_URL=sqlite:///./test.db

   # Authentication settings
   SECRET_KEY=09d25e094faa6ca2556c818166b7a9563b93f7099f6f0f4caa6cf63b88e8d3e7
   ALGORITHM=HS256
   ACCESS_TOKEN_EXPIRE_MINUTES=30

   # CORS settings (comma-separated origins)
   CORS_ORIGINS=http://localhost:3000,https://yourdomain.com

   # Logging settings
   LOG_LEVEL=INFO
   LOG_RETENTION_DAYS=30
   LOGS_DIR=logs
   APP_LOG_FILENAME=app.log

   # Audit logging settings
   AUDIT_LOG_PATH=audit.log
   ```

4. Run the application:

   ```
   # Using the uvicorn server directly
   uvicorn src.main:app --reload

   # Or using the main.py wrapper
   python main.py
   ```

5. Access the API documentation at:
   - Swagger UI: http://localhost:8000/docs
   - ReDoc: http://localhost:8000/redoc

## API Endpoints

### Authentication

- `POST /api/v1/auth/login`: OAuth2 login with username to get access token
- `POST /api/v1/auth/api-login`: Login with email to get access token
- `GET /api/v1/auth/audit-logs`: Get audit logs (admin only)

### Search Functionality

The application supports case-insensitive, partial text search across all list endpoints. Each endpoint accepts an optional `search` query parameter that performs text matching across key fields:

- **GET /customers/?search=abc**

  - Searches across: `company_name`, `email`, `phone`
  - Example: `/customers/?search=metro` finds customers with "Metro" in their name, email, or phone

- **GET /invoices/?search=abc**

  - Searches across: `invoice_number`, customer's `company_name`, `store_type`
  - Example: `/invoices/?search=1001` finds invoice #1001 or invoices for customer with 1001 in their name

- **GET /quotes/?search=abc**

  - Searches across: `quote_number`, customer's `company_name`, `store_type`
  - Example: `/quotes/?search=hardware` finds quotes for "Hardware Store"

- **GET /inventory/?search=abc**

  - Searches across: `style_code`, `sku_code`, `description`
  - Example: `/inventory/?search=oak` finds inventory items with "oak" in description or codes

- **GET /suppliers/?search=abc**

  - Searches across: `company_name`, `email`, `phone`
  - Example: `/suppliers/?search=metro` finds suppliers containing "metro"

- **GET /purchase-orders/?search=abc**

  - Searches across: `po_number`, supplier's `company_name`, related `invoice_number`
  - Example: `/purchase-orders/?search=po-123` finds purchase order #PO-123

- **GET /users/?search=abc**
  - Searches across: `user_name`, `email`, `mobile_number`, `role`
  - Example: `/users/?search=admin` finds users with "admin" in their username or role

All search activities are logged for auditing purposes with details about the user, search term, and timestamp.

### Reports

The Reports module provides APIs for viewing, downloading, and managing sales and purchase order reports directly from the database.

#### Reports API

- **GET /api/v1/reports/**

  - Get the last 30 sales (invoices) and last 30 purchase orders
  - No query parameters required
  - Response: JSON with metadata and data:
    ```json
    {
      "total_sales": 150,  // Total count of all sales in the database
      "total_pos": 80,     // Total count of all POs in the database
      "data": {
        "last_30_sales": [...],  // Array of sales data
        "last_30_pos": [...]     // Array of purchase order data
      }
    }
    ```

- **GET /api/v1/reports/sales**

  - Get paginated sales reports (invoices) with optional search
  - Query Parameters:
    - `page`: Page number, starting from 1 (default: 1)
    - `limit`: Number of records per page (default: 30, max: 100)
    - `search`: Optional search term for invoice number or company name
  - Response: JSON with pagination metadata and items array:
    ```json
    {
      "total": 150,       // Total count of sales matching search criteria
      "page": 1,          // Current page number
      "limit": 30,        // Number of records per page
      "items": [...]      // Array of sales data for current page
    }
    ```

- **GET /api/v1/reports/purchase-orders**

  - Get paginated purchase order reports with optional search
  - Query Parameters:
    - `page`: Page number, starting from 1 (default: 1)
    - `limit`: Number of records per page (default: 30, max: 100)
    - `search`: Optional search term for PO number or supplier name
  - Response: JSON with pagination metadata and items array:
    ```json
    {
      "total": 80,       // Total count of POs matching search criteria
      "page": 1,         // Current page number
      "limit": 30,       // Number of records per page
      "items": [...]     // Array of purchase order data for current page
    }
    ```

- **POST /api/v1/reports/download**

  - Download reports as CSV file with pagination support
  - Request Body:
    - `report_type`: Type of report to download (sales or po)
    - `page`: Page number, starting from 1 (default: 1)
    - `limit`: Number of records to include (default: 30, max: 1000)
    - `search`: Optional search term to filter results
  - Response: CSV file download with appropriate headers

### Purchase Orders

The Purchase Order Management module provides functionalities for creating and managing purchase orders with suppliers.

#### Purchase Order API

- **POST /api/v1/purchase-orders/**

  - Create a new purchase order
  - Request body requires:
    - `supplier_name`: Name of the supplier
    - `supplier_id`: Optional ID of the supplier
    - `store_type`: Store type (e.g., "Cranbourne")
    - `invoice_id`: Optional invoice ID (e.g., "INV-668")
    - `issue_date`: Date the PO is issued
    - `status`: Status of the PO (draft, issued, received, delivered, cancelled)
    - `payment_terms`: Optional payment terms (e.g., "Net 30")
    - `details`: List of ordered items with:
      - `sku`: Product SKU
      - `description`: Product description
      - `quantity_ordered`: Number of items ordered
      - `quantity_received`: Number of items received
      - `expected_delivery_date`: Optional date for backorder delivery
      - `total`: Total cost for the line item
      - `notes`: Optional notes for the line item
  - Response: `PurchaseOrderOut`

- **GET /api/v1/purchase-orders/**

  - List all purchase orders with pagination, filtering and sorting
  - Query Parameters:
    - `skip`: Number of records to skip (default: 0)
    - `limit`: Maximum number of records to return (default: 100)
    - `supplier_name`: Filter by supplier name
    - `store_type`: Filter by store type
    - `start_date`: Filter by start date (inclusive)
    - `end_date`: Filter by end date (inclusive)
    - `status`: Filter by status (draft, issued, received, delivered, cancelled)
  - Response: `PurchaseOrderPagination` with total count and items

- **GET /api/v1/purchase-orders/{po_id}**

  - Get a specific purchase order by ID
  - Response: `PurchaseOrderOut`

- **PUT /api/v1/purchase-orders/{po_id}**

  - Update a purchase order
  - Request Body: `PurchaseOrderUpdate`
  - Response: `PurchaseOrderOut`

- **DELETE /api/v1/purchase-orders/{po_id}**

  - Soft-delete a purchase order (sets status to cancelled)
  - Response: 204 No Content

- **PATCH /api/v1/purchase-orders/{po_id}/email-status**

  - Update the email status of a purchase order
  - Request Body: `PurchaseOrderEmailStatusUpdate`
  - Response: `PurchaseOrderOut`

- **PATCH /api/v1/purchase-orders/details/{detail_id}**

  - Update a purchase order detail
  - Request Body: `PurchaseOrderDetailUpdate`
  - Response: `PurchaseOrderDetailOut`

- **PATCH /api/v1/purchase-orders/details/{detail_id}/notes**
  - Update the notes of a purchase order detail
  - Request Body: `PurchaseOrderDetailNotesUpdate`
  - Response: `PurchaseOrderDetailOut`

### Users

The User Management module provides comprehensive user administration with role-based access control, manager assignment, and audit logging.

#### User Management API

- **POST /api/v1/users/**

  - Create a new user (admin only)
  - Request body requires:
    - `user_name`: Unique username
    - `mobile_number`: Valid mobile number (validated format)
    - `email`: Valid email address
    - `password`: Strong password (uppercase, lowercase, number, special char)
    - `role_id`: ID of the user role (from roles endpoint)
    - `store_type_id`: ID of the store type (required for non-admin users)
    - `manager_id`: Optional ID of the user's manager
  - Response: `UserResponse`

- **GET /api/v1/users/**

  - List all users with pagination, search and filtering
  - Query Parameters:
    - `skip`: Number of records to skip (default: 0)
    - `limit`: Maximum number of records to return (default: 100)
    - `search`: Search term for user_name, email, or mobile_number
    - `role`: Filter by role (admin, manager, staff)
    - `store_type_id`: Filter by store type
  - Response: List of `UserResponse`

- **GET /api/v1/users/managers/**

  - Get list of users with manager role
  - Used to populate manager dropdown in the frontend
  - Response: List of `ManagerListItem` (id, user_name)

- **GET /api/v1/users/me**

  - Get current user's information
  - Response: `UserResponse`

- **GET /api/v1/users/{user_id}**

  - Get a specific user by ID
  - Response: `UserResponse`

- **PUT /api/v1/users/{user_id}**

  - Update a user (admin for any user, or self-update)
  - Request Body: `UserUpdate`
  - Response: `UserResponse`

- **DELETE /api/v1/users/{user_id}**
  - Soft-delete a user (admin only)
  - User record is not removed but marked as deleted
  - Response: 204 No Content

#### User Security Features

- **Password Requirements**:

  - Minimum 8 characters
  - At least 1 uppercase letter
  - At least 1 lowercase letter
  - At least 1 number
  - At least 1 special character

- **Authentication Options**:

  - Login by username, email, or mobile number
  - JWT token authentication with role and store information
  - Role-based access control

#### Role-Based User Management Rules

The User Management system enforces specific rules based on the user role:

- **Admin Users**:

  - Admins are store-independent and must not have a store_type assigned
  - If a store_type is provided during creation or update, it will be automatically removed
  - Admins can access functionality across all stores

- **Manager Users**:

  - Managers must be associated with a specific store_type
  - The store_type_id field is required when creating or updating Manager users

- **Staff Users**:
  - Staff must be associated with a specific store_type
  - When created or updated by an Admin, Staff users must have a manager assigned
  - The manager must be a user with the Manager role
- **Admin-Manager Overlap**:
  - If a user has both Admin and Manager roles, they're treated as an Admin for store assignment
  - In this case, store_type_id will be set to null, prioritizing the Admin role's requirements

These rules ensure proper organizational hierarchy and maintain clean data modeling in the system.

### Companies

The Companies module provides access to company-related data and history.

#### Company History API

- **POST /api/v1/companies/history**
  - Retrieve a company's historical invoices and quotes
  - Request body requires:
    - `company_id`: UUID of the company
    - `page`: Page number (default: 1, optional)
    - `limit`: Items per page (default: 10, max: 100, optional)
  - Returns:
    - Two paginated lists: `invoices` and `quotes`
    - Each list item includes:
      - `id`: Invoice/quote identifier
      - `store_type`: Store type name
      - `date`: Date of the invoice/quote
      - `grand_total`: Total amount
      - `notes`: Additional notes (if any)
      - `type`: "invoice" or "quote"
    - Pagination info for both lists:
      - `total`: Total number of records
      - `page`: Current page
      - `limit`: Items per page
  - Used for displaying transaction history per company on the frontend

### Roles Management

The Roles Management module provides role-based access control with a dedicated roles table. This enables dynamic role management and simplifies frontend integration.

#### Roles API

- **GET /api/v1/roles/**

  - Get all available roles
  - Used to populate role dropdowns in the frontend
  - Response Example:
    ```json
    [
      { "id": 1, "name": "admin" },
      { "id": 2, "name": "manager" },
      { "id": 3, "name": "staff" }
    ]
    ```

- **POST /api/v1/roles/**

  - Create a new role (admin only)
  - Request body requires:
    - `name`: Unique role name
  - Response: `RoleResponse`

- **GET /api/v1/roles/{role_id}**

  - Get a specific role by ID (admin only)
  - Response: `RoleResponse`

- **PUT /api/v1/roles/{role_id}**

  - Update a role by ID (admin only)
  - Request body requires:
    - `name`: New role name
  - Response: `RoleResponse`

- **DELETE /api/v1/roles/{role_id}**
  - Delete a role by ID (admin only)
  - Only roles that are not assigned to any users can be deleted
  - Default roles (admin, manager, staff) should not be deleted

#### User Role Integration

In user responses, the role is now returned as a nested object:

```json
{
  "id": 1,
  "user_name": "admin_user",
  "email": "<EMAIL>",
  "mobile_number": "**********",
  "is_active": true,
  "role": {
    "id": 1,
    "name": "admin"
  },
  "created_at": "2023-01-01T00:00:00Z",
  "updated_at": "2023-01-02T00:00:00Z",
  "store_type": { "id": 1, "name": "Cranbourne" },
  "manager": null
}
```

When creating or updating a user, use the `role_id` field to specify the role:

```json
{
  "user_name": "John Doe",
  "email": "<EMAIL>",
  "mobile_number": "**********",
  "password": "StrongPass@123",
  "role_id": 2,
  "store_type_id": 1
}
```

### Global Search

The Global Search API allows users to search across multiple modules in the application with a single query.

#### Global Search API

- **GET /api/v1/search/**

  - Search across multiple modules (users, purchase orders, invoices, suppliers, customers, inventory)
  - Query Parameters:
    - `query`: Search term to query across all modules (required, min 2 characters)
    - `page`: Page number for pagination (default: 1)
    - `limit`: Number of results per module (default: 10, max: 100)
  - Authentication: Requires a valid JWT token
  - Response Example:
    ```json
    {
      "query": "search_term",
      "page": 1,
      "limit": 10,
      "results": [
        {
          "module": "Users",
          "data": [
            {
              "id": 1,
              "user_name": "admin_user",
              "email": "<EMAIL>",
              "mobile_number": "**********",
              "role": "admin",
              "type": "user"
            }
          ]
        },
        {
          "module": "Purchase Orders",
          "data": [
            {
              "id": 123,
              "supplier_name": "ABC Suppliers",
              "issue_date": "2023-10-15T10:30:00",
              "status": "issued",
              "type": "purchase_order"
            }
          ]
        }
      ]
    }
    ```

#### Searchable Fields by Module

- **Users**:

  - `user_name`
  - `email`
  - `mobile_number`
  - `role`

- **Purchase Orders**:

  - `id`
  - `supplier_name`
  - `status`

- **Invoices**:

  - `invoice_number`
  - `customer_name`
  - `total_amount`
  - `status`

- **Suppliers**:

  - `name`
  - `email`
  - `phone`
  - `contact_person`

- **Customers**:

  - `name`
  - `email`
  - `phone`
  - `contact_person`

- **Inventory**:
  - `sku_code`
  - `style_code`
  - `supplier_name`
  - `carton_dimensions`

### Store Type Management

The Store Type Management module allows the system to be organized by different store locations. Users (except admins) must be associated with a specific store type.

#### Store Types API

- `GET /api/v1/store-types/`: Get all available store types
  - Response Format: List of store types with ID and name
  - Example:
    ```json
    [
      { "id": 1, "name": "Cranbourne" },
      { "id": 2, "name": "Perth" }
    ]
    ```

#### User Store Integration

- When creating a user (`POST /api/v1/users/`), the `store_type_id` field is required for non-admin users.
- The system validates that the provided store type exists.
- User login responses include store type information (ID and name).
- JWT tokens contain store type data for authenticated users.
- Admin users have global access across all stores, while Manager and Staff roles are tied to their specific store.

#### Store-Based Authorization

The system implements store-based authorization with these key principles:

1. Admin users can access data from all stores
2. Manager and Staff users can only access data from their assigned store
3. All user actions are logged with their store type for audit purposes
4. Store type information is automatically included in the JWT token during authentication

### Quotes

- `POST /api/v1/quotes/`: Create a new quote with quote items
- `GET /api/v1/quotes/current`: Get current (non-archived) quotes with pagination and filtering
- `GET /api/v1/quotes/archived`: Get archived quotes with pagination and filtering
- `GET /api/v1/quotes/{quote_id}`: Get a quote by ID
- `PUT /api/v1/quotes/{quote_id}`: Update a quote
- `PUT /api/v1/quotes/{quote_id}/convert`: Convert a quote to invoice and archive it
- `PUT /api/v1/quotes/{quote_id}/renew`: Renew an archived quote
- `DELETE /api/v1/quotes/{quote_id}`: Soft delete a quote

### Customers API

The Customers module provides comprehensive management of customer data and their invoice history. The API follows RESTful principles and includes features for searching, filtering, pagination, and bulk operations.

#### Customer Management

- **GET /api/v1/customers/**

  - List all customers with pagination and search
  - Query Parameters:
    - `search`: Search term for company name, contact person, email, or phone
    - `page`: Page number (default: 1)
    - `per_page`: Items per page (default: 10, max: 100)
  - Response: `PaginatedCustomerResponse`

- **POST /api/v1/customers/**

  - Create a new customer
  - Request Body: `CustomerCreate`
  - Response: `CustomerOut`

- **GET /api/v1/customers/{customer_id}**

  - Get customer details by ID
  - Response: `CustomerOut` including price list and invoice summary

- **PUT /api/v1/customers/{customer_id}**

  - Update customer details
  - Request Body: `CustomerUpdate`
  - Response: `CustomerOut`

- **DELETE /api/v1/customers/{customer_id}**
  - Delete a customer and all related invoices
  - Response: 204 No Content

#### Invoice Management

- **GET /api/v1/customers/{customer_id}/history**

  - List customer invoices with filtering and pagination
  - Query Parameters:
    - `start_date`: Filter by date range start (ISO format)
    - `end_date`: Filter by date range end (ISO format)
    - `invoice_type`: Filter by invoice type (e.g., "Retail", "Wholesale")
    - `skip`: Number of records to skip (default: 0)
    - `limit`: Maximum number of records to return (default: 100, max: 500)
  - Response: List of `Invoice` objects

- **POST /api/v1/customers/{customer_id}/history**

  - Create a new invoice for a customer
  - Request Body: `InvoiceCreate`
  - Response: `Invoice`

- **POST /api/v1/customers/{customer_id}/history/bulk_upload**

  - Bulk upload invoices from CSV or XLSX file
  - Request: Multipart form with file upload
  - Response: Upload summary with success/error counts

- **GET /api/v1/customers/{customer_id}/history/{invoice_id}/pdf**
  - Generate and download a PDF invoice

### Inventory API

The Inventory module provides comprehensive management of inventory items with support for CRUD operations and bulk upload from CSV/Excel files. The system tracks detailed product information including dimensions, weights, and packaging details.

#### Inventory Management

- **GET /api/v1/inventory/**

  - List inventory items with pagination and filtering
  - Query Parameters:
    - `skip`: Number of records to skip (default: 0)
    - `limit`: Maximum number of records to return (default: 100, max: 500)
    - `supplier_name`: Filter by supplier name (partial match)
    - `sku_code`: Filter by SKU code (partial match)
    - `style_code`: Filter by style code (partial match)
  - Response: List of `Inventory` objects

- **POST /api/v1/inventory/**

  - Create a new inventory item
  - Request Body: `InventoryCreate`
  - Response: `Inventory`

- **GET /api/v1/inventory/count**

  - Count inventory items with optional filtering
  - Query Parameters:
    - `supplier_name`: Filter by supplier name (partial match)
    - `sku_code`: Filter by SKU code (partial match)
    - `style_code`: Filter by style code (partial match)
  - Response: `{"total": number}`

- **GET /api/v1/inventory/{inventory_id}**

  - Get inventory item details by ID
  - Response: `Inventory`

- **GET /api/v1/inventory/sku/{sku_code}**

  - Get inventory item by SKU code
  - Response: `Inventory`

- **PUT /api/v1/inventory/{inventory_id}**

  - Update inventory item details
  - Request Body: `InventoryUpdate`
  - Response: `Inventory`

- **DELETE /api/v1/inventory/{inventory_id}**
  - Delete an inventory item
  - Response: 204 No Content

#### Bulk Operations

- **POST /api/v1/inventory/upload-file**
  - Bulk upload inventory items from CSV or Excel file
  - Request: Multipart form with file upload
  - Response: `CSVImportResult` with success count and error details

#### Inventory Data Model

The inventory system tracks the following data for each item:

| Field             | Description                          | Type    |
| ----------------- | ------------------------------------ | ------- |
| sku_code          | Unique SKU Code                      | String  |
| style_code        | Style Code                           | String  |
| supplier_name     | Supplier Name                        | String  |
| carton            | Carton number                        | Integer |
| units_per_carton  | Units per carton                     | Integer |
| carton_dimensions | Carton Dimensions (e.g. 40x30x25 cm) | String  |
| weight_per_unit   | Weight per unit in kg                | Float   |
| weight_per_carton | Weight per carton in kg              | Float   |
| units_per_pallet  | Number of units per pallet           | Integer |
| pallet_weight     | Weight of pallet in kg               | Float   |

The system also maintains audit fields to track creation and modification details.

### Data Models

#### Customer

```json
{
  "id": "uuid",
  "company_name": "string",
  "contact_person": "string",
  "email": "string",
  "phone": "string",
  "is_account": true,
  "billing_address": "string",
  "billing_suburb": "string",
  "billing_postcode": "string",
  "price_list_id": "uuid",
  "price_list": {
    "id": "uuid",
    "name": "string",
    "currency": "string"
  },
  "invoice_summary": {
    "total_invoices": 0,
    "total_value": "0.00"
  }
}
```

#### Invoice

```json
{
  "id": "uuid",
  "invoice_no": "string",
  "store_type": "string",
  "date": "2023-05-15",
  "total_gst": "10.00",
  "grand_total": "110.00",
  "notes": "string",
  "customer_id": "uuid"
}
```

### Bulk Upload Format

CSV or XLSX files for bulk invoice upload must include these columns:

- `invoice_no`: Unique invoice identifier
- `store_type`: Type of store/invoice
- `date`: Invoice date (YYYY-MM-DD)
- `total_gst`: GST amount
- `grand_total`: Total amount including GST
- `notes`: Optional notes/description

### Error Responses

- `404 Not Found`: Resource not found
- `409 Conflict`: Unique constraint violation (e.g., duplicate email)
- `422 Unprocessable Entity`: Validation error
- `500 Internal Server Error`: Server-side error

## Logging System

The application uses a structured logging system:

- Daily log folders in `MM-DD-YYYY` format
- Automatic log rotation based on the `LOG_RETENTION_DAYS` setting
- Symlinks to the latest log files for easy access
- Separate application and audit logs
- Log level configurable via the `LOG_LEVEL` environment variable

## Utility Scripts

### Create Admin User

```
# Using default admin credentials
python -m scripts.create_admin

# With custom credentials
python -m scripts.create_admin <EMAIL> admin_user Password123
```

### Fetch Audit Logs

```
# Show all logs in table format
python -m scripts.fetch_audit_logs

# Filter by table name
python -m scripts.fetch_audit_logs --table users

# Filter by operation
python -m scripts.fetch_audit_logs --operation CREATE

# Change output format
python -m scripts.fetch_audit_logs --format json
python -m scripts.fetch_audit_logs --format csv
```

## Quote Management System

The Quote Management System provides functionality for managing quotes with the following features:

- Create quotes with multiple SKU line items
- Auto-generate sequential quote numbers (e.g., QUO-001)
- Auto-calculate grand total based on line items
- Search quotes by company name or quote number
- Filter quotes by date range
- Paginate results
- Convert quotes to invoices
- Archive and renew quotes
- Soft delete quotes for audit purposes

### Quote Structure

Each quote contains:

- Basic information (company name, store type, date, etc.)
- Delivery address
- Customer reference
- Multiple SKU line items with quantities and prices
- Total price calculation

### Store Types

The system supports two store types:

- Cranbourne
- Other Branches

### Quote Lifecycle

Quotes follow this lifecycle:

1. Created as a current quote
2. Can be updated with new notes or details
3. Can be converted to an invoice (automatically archived)
4. Archived quotes can be renewed for reuse
5. Soft deletion for audit trail

## Audit Logging

The system logs all database changes (create, update, delete) with the following information:

- Timestamp (ISO format)
- Table name
- Operation type (CREATE, UPDATE, DELETE)
- Entity ID
- User ID (who performed the operation)
- Changes made (before/after values for updates)
- IP address

Audit logs are stored in the database for security monitoring and analysis. Database storage allows for efficient querying, filtering, and retention of audit records.

## Testing

The project includes unit and integration tests:

```
# Run all tests
pytest

# Run specific test file
pytest tests/test_api.py

# Run with coverage report
pytest --cov=src tests/
```

## Docker Deployment

You can deploy the application using Docker:

```
# Build the Docker image
docker build -t builders-warehouse-api .

# Run the container
docker run -p 8000:8000 -d --name builders-warehouse-api builders-warehouse-api
```

## Development Guidelines

1. Follow clean architecture principles:

   - Routes handle API requests and responses
   - Controllers coordinate service calls
   - Services implement business logic
   - Models define database structure
   - Schemas handle input/output validation

2. Maintain separation of concerns:

   - Don't import from higher layers in lower layers
   - Keep database operations in service layer
   - Keep API-specific logic in controllers
   - Use dependency injection for decoupling

3. Use async/await for all I/O operations

4. Apply formatting and linting:

   ```
   # Format code with black
   black src/

   # Sort imports with isort
   isort src/

   # Check code style with flake8
   flake8 src/
   ```

### Purchase Orders

The Purchase Order module allows managing orders to suppliers, tracking ordered items, monitoring delivery status, and managing email communications.

#### Purchase Order Endpoints

- `GET /api/v1/purchase-orders/`

  - Get all purchase orders with filtering and pagination
  - Query Parameters:
    - `supplier_name`: Filter by supplier name (partial match)
    - `start_date`: Filter by start date (inclusive)
    - `end_date`: Filter by end date (inclusive)
    - `status`: Filter by status (draft, issued, received, cancelled)
    - `skip`: Number of records to skip
    - `limit`: Maximum number of records to return
  - Response: `PurchaseOrderPagination`

- `POST /api/v1/purchase-orders/`

  - Create a new purchase order with details
  - Request Body: `PurchaseOrderCreate`
  - Response: `PurchaseOrderOut`

- `GET /api/v1/purchase-orders/{po_id}`

  - Get a purchase order by ID with its details
  - Response: `PurchaseOrderOut`

- `PUT /api/v1/purchase-orders/{po_id}`

  - Update a purchase order's details
  - Request Body: `PurchaseOrderUpdate`
  - Response: `PurchaseOrderOut`

- `DELETE /api/v1/purchase-orders/{po_id}`

  - Soft delete a purchase order
  - Response: 204 No Content

- `PATCH /api/v1/purchase-orders/{po_id}/email-status`

  - Update the email status of a purchase order
  - Request Body: `PurchaseOrderEmailStatusUpdate`
  - Response: `PurchaseOrderOut`

- `PATCH /api/v1/purchase-orders/details/{detail_id}`

  - Update a purchase order detail
  - Request Body: `PurchaseOrderDetailUpdate`
  - Response: `PurchaseOrderDetail`

- `PATCH /api/v1/purchase-orders/details/{detail_id}/notes`
  - Update the notes of a purchase order detail
  - Request Body: `PurchaseOrderDetailNotesUpdate`
  - Response: `PurchaseOrderDetail`

#### Data Models

##### Purchase Order

```json
{
  "id": 1,
  "supplier_name": "ABC Supplies",
  "issue_date": "2023-05-15T10:00:00",
  "status": "issued",
  "email_sent": true,
  "email_sent_message": "Email sent on 2023-05-15",
  "created_at": "2023-05-15T10:00:00",
  "updated_at": "2023-05-15T10:30:00",
  "details": [
    {
      "id": 1,
      "purchase_order_id": 1,
      "sku": "ABC-123",
      "description": "Paint Brushes",
      "quantity_ordered": 50,
      "quantity_received": 25,
      "expected_delivery_date": "2023-05-20T00:00:00",
      "total": 250.0,
      "notes": "Bulk order",
      "created_at": "2023-05-15T10:00:00",
      "updated_at": "2023-05-15T10:00:00"
    }
  ]
}
```

### Invoice Management

The Invoice Management module provides comprehensive functionality for creating and managing customer invoices with automatic calculations for taxes, surcharges, and totals.

#### Invoice Endpoints

- `POST /api/v1/invoices/`

  - Create a new invoice with line items
  - Request Body: `InvoiceCreate`
  - Auto-fills `store_type` based on the logged-in user
  - Auto-generates unique `invoice_number` in format INV-YYYYMMDD-XXX
  - Auto-calculates financial fields:
    - `total_order`: Sum of all line item totals
    - `credit_card_surcharge`: 2.5% of total if payment mode is credit card
    - `total_gst`: 10% GST on (total_order + credit_card_surcharge)
    - `grand_total`: Sum of all the above plus shipping
  - Response: `InvoiceOut`
  - Audit logging is performed automatically

- `GET /api/v1/invoices/`

  - List all invoices with filtering and pagination
  - Query Parameters:
    - `customer_id`: Filter by customer
    - `store_type`: Filter by store type
    - `start_date`: Filter by date range start (inclusive)
    - `end_date`: Filter by date range end (inclusive)
    - `search`: Search by invoice number or customer name
    - `skip`: Number of records to skip
    - `limit`: Maximum number of records to return
  - Response: `InvoicePagination`

- `GET /api/v1/invoices/{invoice_id}`

  - Get a single invoice with all details
  - Response: `InvoiceOut`

- `PUT /api/v1/invoices/{invoice_id}`
  - Update an invoice and its line items
  - Request Body: `InvoiceUpdate`
  - Recalculates all financial totals if relevant fields change
  - Response: `InvoiceOut`
  - Audit logging is performed automatically

#### Data Models

##### Invoice

```json
{
  "id": "uuid",
  "invoice_no": "INV-20230515-001",
  "store_type": "Cranbourne",
  "sale_type": "trade",
  "mode_of_payment": "credit_card",
  "purchase_order_number": "PO12345",
  "po_id": 123,
  "customer_id": "uuid",
  "deliver_to_address": "123 Main St, Cranbourne VIC 3977",
  "date": "2023-05-15",
  "dont_send_po": false,
  "linked_quote_id": "uuid",
  "notes": "Customer requested urgent delivery",
  "total_order": 1000.0,
  "credit_card_surcharge": 25.0,
  "total_gst": 102.5,
  "shipping": 15.0,
  "grand_total": 1142.5,
  "created_at": "2023-05-15T10:00:00",
  "updated_at": "2023-05-15T10:30:00",
  "items": [
    {
      "id": "uuid",
      "invoice_id": "uuid",
      "sku": "TILE-123",
      "description": "White Ceramic Tiles",
      "units": 0,
      "boxes": 10,
      "pieces": 0,
      "m2": 20.5,
      "unit_price": 45.0,
      "total_price": 450.0,
      "created_at": "2023-05-15T10:00:00",
      "updated_at": "2023-05-15T10:00:00"
    }
  ]
}
```

#### Features

- Support for different payment modes (cash, bank transfer, credit card)
- Automatic 2.5% surcharge for credit card payments
- Sale type differentiation (trade vs retail)
- Link to original quote if applicable
- Configurable shipping costs
- Comprehensive audit logging
- No delete functionality by design (invoices can only be created and updated)

# Recent Updates

## Supplier API Update

The Supplier POST API has been enhanced to use a standardized format:

- Supplier information is now wrapped in a `total` object
- `supplier_name` is now treated as a Supplier ID (integer)
- `price_list` accepts an array of Price List IDs
- Validation ensures all referenced IDs exist in their respective database tables

Example request payload:

```json
{
  "total": {
    "supplier_name": 7,
    "phone_no": "**********",
    "email": "<EMAIL>",
    "address": "123 Main St",
    "price_list": [101, 102, 103]
  }
}
```

## Invoice-Purchase Order Integration

The system now features automatic integration between Invoices and Purchase Orders with the following capabilities:

### Automatic PO Creation

- When a new invoice is created, the system automatically creates a linked Purchase Order
- If an existing PO ID is provided during invoice creation, the system will link to that PO
- The system extracts relevant data from the invoice to populate the PO:
  - `store_type`: Copied from invoice's store type
  - `supplier_id`: Used from invoice or defaults to a system supplier
  - `date_issued`: Matches the invoice date
  - `invoice_id`: References the newly created invoice
  - `total_amount`: Matches the invoice total amount
  - `gst`: Transfers the GST value
  - `total_payable`: Copies the grand total

### "Send PO" Configuration

The system supports configurable behavior for purchase order communication:

#### When `dont_send_po = true`:

- The Purchase Order is still created and linked to the invoice
- A draft email entry is created in the `draft_mails` table with:
  - `invoice_id`: Reference to the invoice
  - `po_id`: Reference to the purchase order
  - `subject`: Set as "Draft PO for Invoice #{invoice_number}"
  - `body`: Placeholder text with invoice details
  - `ready_to_send`: Set to `false`
  - `created_by`: The current user's ID
  - `created_at`: Automatically stamped with current time

#### When `dont_send_po = false`:

- The Purchase Order is created and linked to the invoice
- A draft email entry is created with `ready_to_send = true`
- The system logs that the PO is ready for sending
- The email will be processed by the email sending service (when implemented)

### Draft Mails System

The system introduces a `draft_mails` table that stores email drafts with the following fields:

- `id`: Unique identifier
- `invoice_id`: Reference to the invoice
- `po_id`: Reference to the purchase order
- `subject`: Email subject line
- `body`: Email content
- `ready_to_send`: Flag indicating if the email is ready to be sent
- `sent`: Flag indicating if the email has been sent
- `sent_at`: Timestamp when the email was sent
- `created_by`: User who created the draft
- `created_at`: Creation timestamp
- `updated_at`: Last update timestamp

### Audit Logging

The system logs all activities related to invoice and PO creation/updates, including:

- Invoice creation with automatic PO generation
- Changes to `dont_send_po` flag
- Draft email status changes
- PO linkage changes

These logs can be accessed through the standard audit log endpoints.

## Repository Pattern

The codebase now follows a proper repository pattern with the `crud` folder renamed to `repository`. This change aligns with modern architectural approaches and clarifies the role of these modules as data access layers for interacting with database models.

## Database Migrations

New migrations have been added:

- Store Types table: Used for categorizing users by store/location
- Enhanced Supplier table: Updated to support detailed supplier price lists as JSON

## Model Refactoring: Invoices and Quotes

### Overview of Changes

The Invoice and Quote models have been refactored to improve data normalization by replacing string-based fields with proper ID-based foreign key references.

### Key Changes

#### New Models Created:

1. **Company**: Stores company information previously embedded in Customer and referenced directly in Invoice/Quote
2. **InvoiceNumber**: Manages unique formatted invoice numbers with metadata
3. **QuoteNumber**: Manages unique formatted quote numbers with metadata

#### Invoice Model Changes:

- `invoice_no` (string) → `invoice_number_id` (UUID) referencing the `InvoiceNumber` table
- `store_type` (string) → `store_type_id` (integer) referencing the `StoreType` table
- Added `company_id` (UUID) referencing the `Company` table

#### Quote Model Changes:

- `quote_no` (string) → `quote_number_id` (UUID) referencing the `QuoteNumber` table
- `company_name` (string) → `company_id` (UUID) referencing the `Company` table
- `store_type` (string) → `store_type_id` (integer) referencing the `StoreType` table

### Benefits

- Improved relational integrity through foreign key constraints
- Better normalization to prevent data duplication
- Simplified queries for filtering and joining related data
- Clearer data model with explicit relationships

### API Impact

- Request payloads now require IDs rather than string values for related entities
- Response payloads include nested objects with both IDs and display values
- Existing endpoints maintain backward compatibility during transition

### Migration Path

A migration script has been created that:

1. Creates the new tables
2. Populates them with data from existing records
3. Adds and populates the new foreign key columns
4. Temporarily maintains the old string columns for backward compatibility

The string columns will be removed in a future release after all services have been updated to use the new ID-based fields.

## Prisma ORM Integration

The application now supports Prisma ORM alongside SQLAlchemy. This hybrid approach allows for using the best features of both ORMs.

### Requirements

- Node.js (v14 or higher)
- npm (comes with Node.js)
- PostgreSQL database

### Setup

1. Install Node.js dependencies:

   ```bash
   npm install
   ```

2. Set up your database connection in the `.env` file:

   ```
   DATABASE_URL="postgresql://username:password@localhost:5432/mydatabase"
   ```

3. Generate the Prisma client:

   ```bash
   npx prisma generate
   ```

4. Push the database schema to your database:
   ```bash
   npx prisma db push
   ```

### Usage

The Prisma ORM is available through the `PrismaClient` class in `src/db/prisma_client.py`. This class provides a Python interface to the Prisma client, allowing you to perform CRUD operations on your models.

Example:

```python
from src.db.prisma_client import PrismaClient

# Find all companies
companies = await PrismaClient.find_many(model="company")

# Find a company by ID
company = await PrismaClient.find_unique(model="company", where={"id": 1})

# Create a new company
new_company = await PrismaClient.create(model="company", data={"name": "New Company"})

# Update a company
updated_company = await PrismaClient.update(
    model="company",
    where={"id": 1},
    data={"name": "Updated Company"}
)

# Delete a company
deleted_company = await PrismaClient.delete(model="company", where={"id": 1})
```

### API Example

A sample API implementation using Prisma ORM is available at `/prisma/companies`. This endpoint demonstrates how to use the Prisma client with FastAPI.
