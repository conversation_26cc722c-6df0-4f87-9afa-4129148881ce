import psycopg2
import os

try:
    # Try direct connection with the credentials from env.example
    conn = psycopg2.connect(
        host='localhost',
        database='builders_warehouse',
        user='warehouse_user',
        password='Password#123',
        port=5432
    )
    cur = conn.cursor()
    
    # Check if image_path column exists
    cur.execute("SELECT column_name FROM information_schema.columns WHERE table_name = 'Inventory' AND column_name = 'image_path'")
    result = cur.fetchone()
    
    if result:
        print('✓ image_path column already exists in Inventory table')
    else:
        print('Adding image_path column to Inventory table...')
        cur.execute('ALTER TABLE "Inventory" ADD COLUMN image_path VARCHAR')
        conn.commit()
        print('✓ image_path column added successfully')
    
    cur.close()
    conn.close()
    
except Exception as e:
    print(f'Error: {e}') 