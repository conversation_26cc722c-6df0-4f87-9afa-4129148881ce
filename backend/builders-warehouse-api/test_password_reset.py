#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to test password reset confirmation directly
"""
import sys
import os
import asyncio

# Add the src directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.database import SessionLocal
# Import directly to avoid circular imports
from src.services.password_reset_service import PasswordResetService
from src.schemas.password_reset import PasswordResetConfirm

async def test_password_reset():
    """Test password reset confirmation directly"""
    db = SessionLocal()
    try:
        # Test data
        confirm_data = PasswordResetConfirm(
            token="5_874ufjxwEOF-7nzY1csgK92CJbbVPqv9uH_eVPEL4",
            new_password="Password123!"
        )
        
        print("Testing password reset confirmation...")
        print(f"Token: {confirm_data.token}")
        print(f"New password: {confirm_data.new_password}")
        
        # Call the service directly
        result = await PasswordResetService.confirm_password_reset(
            db=db,
            confirm_data=confirm_data
        )
        
        print(f"✅ Success: {result.message}")
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()

if __name__ == "__main__":
    asyncio.run(test_password_reset()) 