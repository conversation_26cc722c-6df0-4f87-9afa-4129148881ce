-- Migration to add missing fields to Invoice table
-- Run this SQL script in your PostgreSQL database

ALTER TABLE "Invoice" 
ADD COLUMN IF NOT EXISTS store_type_name VARCHAR,
ADD COLUMN IF NOT EXISTS mode_of_payment VARCHAR DEFAULT 'cash',
ADD COLUMN IF NOT EXISTS po_number VARCHAR,
ADD COLUMN IF NOT EXISTS dont_send_po BOOLEAN DEFAULT FALSE,
ADD COLUMN IF NOT EXISTS total_order FLOAT DEFAULT 0.0,
ADD COLUMN IF NOT EXISTS credit_card_surcharge FLOAT DEFAULT 0.0,
ADD COLUMN IF NOT EXISTS shipping FLOAT DEFAULT 0.0;

-- Update existing records to have default values
UPDATE "Invoice" 
SET 
    store_type_name = COALESCE(store_type_name, ''),
    mode_of_payment = COALESCE(mode_of_payment, 'cash'),
    po_number = COALESCE(po_number, ''),
    dont_send_po = COALESCE(dont_send_po, FALSE),
    total_order = COALESCE(total_order, 0.0),
    credit_card_surcharge = COALESCE(credit_card_surcharge, 0.0),
    shipping = COALESCE(shipping, 0.0)
WHERE 
    store_type_name IS NULL 
    OR mode_of_payment IS NULL 
    OR po_number IS NULL 
    OR dont_send_po IS NULL 
    OR total_order IS NULL 
    OR credit_card_surcharge IS NULL 
    OR shipping IS NULL; 