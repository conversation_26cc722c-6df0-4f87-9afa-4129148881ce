import os
import logging
from logging.handlers import RotatingFileHandler
import sys
from datetime import datetime

from src.config import settings

def setup_logging():
    """
    Set up logging configuration for the application
    - Creates daily log folders in MM-DD-YYYY format
    - Stores app.log and error.log in each daily folder
    """
    # Get today's date for the folder name in MM-DD-YYYY format
    today = datetime.now().strftime("%m-%d-%Y")
    
    # Create base logs directory if it doesn't exist
    os.makedirs(settings.LOGS_DIR, exist_ok=True)
    
    # Create today's log directory
    daily_log_dir = os.path.join(settings.LOGS_DIR, today)
    os.makedirs(daily_log_dir, exist_ok=True)
    
    # Configure log file paths
    app_log_file = os.path.join(daily_log_dir, "app.log")
    error_log_file = os.path.join(daily_log_dir, "error.log")
    
    # Configure logging level
    log_level = getattr(logging, settings.LOG_LEVEL.upper(), logging.INFO)
    
    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(log_level)
    
    # Clear existing handlers
    if root_logger.handlers:
        root_logger.handlers.clear()
    
    # Console handler for all logs
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(log_level)
    console_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    console_handler.setFormatter(console_formatter)
    root_logger.addHandler(console_handler)
    
    # File handler for app.log with rotation - all logs at configured level
    max_size_mb = 10
    backup_count = 5
    app_file_handler = RotatingFileHandler(
        app_log_file,
        maxBytes=max_size_mb * 1024 * 1024,  # Convert MB to bytes
        backupCount=backup_count
    )
    app_file_handler.setLevel(log_level)
    app_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    app_file_handler.setFormatter(app_formatter)
    root_logger.addHandler(app_file_handler)
    
    # File handler for error.log with rotation - only ERROR and above
    error_file_handler = RotatingFileHandler(
        error_log_file,
        maxBytes=max_size_mb * 1024 * 1024,
        backupCount=backup_count
    )
    error_file_handler.setLevel(logging.ERROR)
    error_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(pathname)s:%(lineno)d - %(message)s'
    )
    error_file_handler.setFormatter(error_formatter)
    root_logger.addHandler(error_file_handler)
    
    # Set SQLAlchemy logging level to WARNING to reduce noise
    logging.getLogger('sqlalchemy').setLevel(logging.WARNING)
    
    # Set uvicorn access logs to WARNING to reduce noise
    logging.getLogger("uvicorn.access").setLevel(logging.WARNING)
    
    # Log that logging has been set up
    logging.info(f"Logging setup complete. Log level: {settings.LOG_LEVEL}")
    logging.info(f"App logs: {app_log_file}")
    logging.info(f"Error logs: {error_log_file}") 