"""add outbound deliveries tables

Revision ID: add_outbound_deliveries
Revises: add_inbound_deliveries
Create Date: 2024-07-14 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from datetime import datetime

# revision identifiers, used by Alembic.
revision = 'add_outbound_deliveries'
down_revision = None
branch_labels = None
depends_on = None

def upgrade():
    # Create outbound_deliveries table
    op.create_table(
        'outbound_deliveries',
        sa.Column('invoice_no', sa.String(50), nullable=False),
        sa.Column('invoice_date', sa.Date(), nullable=False),
        sa.Column('linked_po', sa.String(50), nullable=False),
        sa.Column('sku', sa.String(100), nullable=False),
        sa.Column('description', sa.String(255), nullable=False),
        sa.Column('status', sa.String(100), nullable=False, default='Transfer Stock to Sale by Internal driver'),
        sa.PrimaryKeyConstraint('invoice_no')
    )
    
    # Create indexes for outbound_deliveries
    op.create_index('ix_outbound_deliveries_invoice_no', 'outbound_deliveries', ['invoice_no'])
    op.create_index('ix_outbound_deliveries_linked_po', 'outbound_deliveries', ['linked_po'])
    
    # Create delivered_orders table
    op.create_table(
        'delivered_orders',
        sa.Column('invoice_no', sa.String(50), nullable=False),
        sa.Column('invoice_date', sa.Date(), nullable=False),
        sa.Column('linked_po', sa.String(50), nullable=False),
        sa.Column('sku', sa.String(100), nullable=False),
        sa.Column('description', sa.String(255), nullable=False),
        sa.Column('delivered_date', sa.DateTime(), nullable=False, default=datetime.utcnow),
        sa.PrimaryKeyConstraint('invoice_no')
    )
    
    # Create indexes for delivered_orders
    op.create_index('ix_delivered_orders_invoice_no', 'delivered_orders', ['invoice_no'])
    op.create_index('ix_delivered_orders_linked_po', 'delivered_orders', ['linked_po'])

def downgrade():
    # Drop indexes for delivered_orders
    op.drop_index('ix_delivered_orders_linked_po', table_name='delivered_orders')
    op.drop_index('ix_delivered_orders_invoice_no', table_name='delivered_orders')
    
    # Drop delivered_orders table
    op.drop_table('delivered_orders')
    
    # Drop indexes for outbound_deliveries
    op.drop_index('ix_outbound_deliveries_linked_po', table_name='outbound_deliveries')
    op.drop_index('ix_outbound_deliveries_invoice_no', table_name='outbound_deliveries')
    
    # Drop outbound_deliveries table
    op.drop_table('outbound_deliveries') 