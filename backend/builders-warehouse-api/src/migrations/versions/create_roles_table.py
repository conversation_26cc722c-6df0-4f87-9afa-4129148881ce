"""create_roles_table

Revision ID: 001
Revises: 
Create Date: 2023-06-15

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = '001'
down_revision = None
branch_labels = None
depends_on = None


def upgrade():
    # Create roles table
    op.create_table(
        'roles',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('name', sa.String(), nullable=False),
        sa.Column('created_at', sa.DateTime(timezone=True), server_default=sa.text('now()'), nullable=True),
        sa.Column('updated_at', sa.DateTime(timezone=True), nullable=True),
        sa.PrimaryKeyConstraint('id')
    )
    op.create_index(op.f('ix_roles_id'), 'roles', ['id'], unique=False)
    op.create_index(op.f('ix_roles_name'), 'roles', ['name'], unique=True)
    
    # Insert default roles
    op.execute("INSERT INTO roles (name) VALUES ('admin')")
    op.execute("INSERT INTO roles (name) VALUES ('manager')")
    op.execute("INSERT INTO roles (name) VALUES ('staff')")
    
    # Add role_id column to users table
    op.add_column('users', sa.Column('role_id', sa.Integer(), nullable=True))
    
    # Create foreign key reference
    op.create_foreign_key('fk_users_role_id_roles', 'users', 'roles', ['role_id'], ['id'])
    
    # Migrate existing role strings to role_id references
    op.execute("UPDATE users SET role_id = (SELECT id FROM roles WHERE name = users.role) WHERE role IS NOT NULL")
    
    # Make role_id not nullable
    op.alter_column('users', 'role_id', nullable=False)
    
    # Drop the old role column
    op.drop_column('users', 'role')


def downgrade():
    # Add back the role column
    op.add_column('users', sa.Column('role', sa.String(), nullable=True))
    
    # Migrate role_id references back to role strings
    op.execute("UPDATE users SET role = (SELECT name FROM roles WHERE id = users.role_id) WHERE role_id IS NOT NULL")
    
    # Drop the role_id column
    op.drop_constraint('fk_users_role_id_roles', 'users', type_='foreignkey')
    op.drop_column('users', 'role_id')
    
    # Drop the roles table
    op.drop_index(op.f('ix_roles_name'), table_name='roles')
    op.drop_index(op.f('ix_roles_id'), table_name='roles')
    op.drop_table('roles') 