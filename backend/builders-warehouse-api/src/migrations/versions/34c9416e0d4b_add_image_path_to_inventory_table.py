"""add_image_path_to_inventory_table

Revision ID: 34c9416e0d4b
Revises: 00938643067d
Create Date: 2025-05-27 14:28:45.670990

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = '34c9416e0d4b'
down_revision = '00938643067d'
branch_labels = None
depends_on = None


def upgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.alter_column('AuditLog', 'event_type',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=True)
    op.alter_column('AuditLog', 'email',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=True)
    op.alter_column('AuditLog', 'status',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=True)
    op.alter_column('AuditLog', 'ip_address',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=True)
    op.alter_column('AuditLog', 'table_name',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=True)
    op.alter_column('AuditLog', 'operation',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=True)
    op.create_index(op.f('ix_AuditLog_email'), 'AuditLog', ['email'], unique=False)
    op.create_index(op.f('ix_AuditLog_event_type'), 'AuditLog', ['event_type'], unique=False)
    op.create_index(op.f('ix_AuditLog_id'), 'AuditLog', ['id'], unique=False)
    op.create_index(op.f('ix_AuditLog_operation'), 'AuditLog', ['operation'], unique=False)
    op.create_index(op.f('ix_AuditLog_status'), 'AuditLog', ['status'], unique=False)
    op.create_index(op.f('ix_AuditLog_table_name'), 'AuditLog', ['table_name'], unique=False)
    op.alter_column('Company', 'name',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=False)
    op.alter_column('Company', 'phone',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=True)
    op.alter_column('Company', 'email',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=True)
    op.create_index(op.f('ix_Company_name'), 'Company', ['name'], unique=True)
    op.alter_column('Customer', 'company_name',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=False)
    op.alter_column('Customer', 'contact_person',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=True)
    op.alter_column('Customer', 'email',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=True)
    op.alter_column('Customer', 'phone',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=True)
    op.alter_column('Customer', 'billing_suburb',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=True)
    op.alter_column('Customer', 'billing_postcode',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=True)
    op.create_unique_constraint(None, 'Customer', ['email'])
    op.create_unique_constraint(None, 'Customer', ['company_name'])
    op.drop_constraint('Customer_price_list_id_fkey', 'Customer', type_='foreignkey')
    op.create_foreign_key(None, 'Customer', 'PriceList', ['price_list_id'], ['id'])
    op.alter_column('DraftMail', 'ready_to_send',
               existing_type=sa.BOOLEAN(),
               nullable=True,
               existing_server_default=sa.text('false'))
    op.alter_column('DraftMail', 'sent',
               existing_type=sa.BOOLEAN(),
               nullable=True,
               existing_server_default=sa.text('false'))
    op.alter_column('DraftMail', 'sent_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(),
               existing_nullable=True)
    op.alter_column('DraftMail', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(),
               existing_nullable=False,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.alter_column('DraftMail', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               type_=sa.DateTime(),
               existing_nullable=False,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.create_index(op.f('ix_DraftMail_id'), 'DraftMail', ['id'], unique=False)
    op.add_column('InboundDelivery', sa.Column('ship_to_customer', sa.Boolean(), nullable=False))
    op.alter_column('InboundDelivery', 'delivery_number',
               existing_type=sa.TEXT(),
               type_=sa.String(length=50),
               nullable=True)
    op.alter_column('InboundDelivery', 'supplier_name',
               existing_type=sa.TEXT(),
               type_=sa.String(length=255),
               existing_nullable=False)
    op.alter_column('InboundDelivery', 'items',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               type_=sa.JSON(),
               nullable=True)
    op.alter_column('InboundDelivery', 'status',
               existing_type=sa.TEXT(),
               type_=sa.String(length=50),
               existing_nullable=False)
    op.create_index(op.f('ix_InboundDelivery_id'), 'InboundDelivery', ['id'], unique=False)
    op.create_index(op.f('ix_InboundDelivery_supplier_name'), 'InboundDelivery', ['supplier_name'], unique=False)
    op.add_column('Inventory', sa.Column('image_path', sa.String(), nullable=True))
    op.alter_column('Inventory', 'created_at',
               existing_type=postgresql.TIMESTAMP(precision=3),
               type_=sa.DateTime(timezone=True),
               nullable=True,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.alter_column('Inventory', 'updated_at',
               existing_type=postgresql.TIMESTAMP(precision=3),
               type_=sa.DateTime(timezone=True),
               nullable=True)
    op.alter_column('Inventory', 'carton',
               existing_type=sa.INTEGER(),
               nullable=True,
               existing_server_default=sa.text('1'))
    op.alter_column('Inventory', 'carton_dimensions',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=True)
    op.alter_column('Inventory', 'sku_code',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=False)
    op.alter_column('Inventory', 'style_code',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=False)
    op.create_index(op.f('ix_Inventory_id'), 'Inventory', ['id'], unique=False)
    op.create_index(op.f('ix_Inventory_sku_code'), 'Inventory', ['sku_code'], unique=True)
    op.create_index(op.f('ix_Inventory_style_code'), 'Inventory', ['style_code'], unique=False)
    op.create_index(op.f('ix_Inventory_supplier_id'), 'Inventory', ['supplier_id'], unique=False)
    op.create_foreign_key(None, 'Inventory', 'Supplier', ['supplier_id'], ['id'], ondelete='SET NULL')
    op.create_foreign_key(None, 'Inventory', 'User', ['updated_by'], ['id'], ondelete='SET NULL')
    op.create_foreign_key(None, 'Inventory', 'User', ['created_by'], ['id'], ondelete='SET NULL')
    op.alter_column('Invoice', 'invoice_number',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=False)
    op.alter_column('Invoice', 'status',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=False,
               existing_server_default=sa.text("'draft'::text"))
    op.alter_column('Invoice', 'invoice_type',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=True)
    op.alter_column('Invoice', 'mode_of_payment',
               existing_type=sa.VARCHAR(),
               type_=sa.Enum('CASH', 'BANK_TRANSFER', 'CREDIT_CARD', 'CHEQUE', name='paymentmode'),
               existing_nullable=True,
               existing_server_default=sa.text("'cash'::character varying"))
    op.alter_column('Invoice', 'dont_send_po',
               existing_type=sa.BOOLEAN(),
               nullable=False,
               existing_server_default=sa.text('false'))
    op.alter_column('Invoice', 'total_order',
               existing_type=sa.DOUBLE_PRECISION(precision=53),
               nullable=False,
               existing_server_default=sa.text('0.0'))
    op.alter_column('Invoice', 'credit_card_surcharge',
               existing_type=sa.DOUBLE_PRECISION(precision=53),
               nullable=False,
               existing_server_default=sa.text('0.0'))
    op.alter_column('Invoice', 'shipping',
               existing_type=sa.DOUBLE_PRECISION(precision=53),
               nullable=False,
               existing_server_default=sa.text('0.0'))
    op.create_index(op.f('ix_Invoice_id'), 'Invoice', ['id'], unique=False)
    op.create_index(op.f('ix_Invoice_invoice_number'), 'Invoice', ['invoice_number'], unique=True)
    op.create_index(op.f('ix_Invoice_linked_quote_id'), 'Invoice', ['linked_quote_id'], unique=False)
    op.drop_constraint('Invoice_customer_id_fkey', 'Invoice', type_='foreignkey')
    op.drop_constraint('Invoice_company_id_fkey', 'Invoice', type_='foreignkey')
    op.create_foreign_key(None, 'Invoice', 'PurchaseOrder', ['po_id'], ['id'])
    op.create_foreign_key(None, 'Invoice', 'Customer', ['customer_id'], ['id'])
    op.create_foreign_key(None, 'Invoice', 'Company', ['company_id'], ['id'])
    op.alter_column('PriceList', 'name',
               existing_type=sa.TEXT(),
               type_=sa.String(length=255),
               existing_nullable=False)
    op.alter_column('PriceList', 'currency',
               existing_type=sa.TEXT(),
               type_=sa.String(length=3),
               nullable=True,
               existing_server_default=sa.text("'AUD'::text"))
    op.alter_column('PriceList', 'created_at',
               existing_type=postgresql.TIMESTAMP(precision=3),
               nullable=True,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.alter_column('PriceList', 'updated_at',
               existing_type=postgresql.TIMESTAMP(precision=3),
               nullable=True)
    op.create_unique_constraint(None, 'PriceList', ['name'])
    op.alter_column('PurchaseOrder', 'po_number',
               existing_type=sa.TEXT(),
               type_=sa.String(length=50),
               existing_nullable=False)
    op.alter_column('PurchaseOrder', 'items',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               nullable=True)
    op.alter_column('PurchaseOrder', 'total_amount',
               existing_type=sa.DOUBLE_PRECISION(precision=53),
               type_=sa.Numeric(precision=10, scale=2),
               existing_nullable=False)
    op.alter_column('PurchaseOrder', 'status',
               existing_type=sa.TEXT(),
               type_=sa.String(length=50),
               existing_nullable=False)
    op.create_index(op.f('ix_PurchaseOrder_id'), 'PurchaseOrder', ['id'], unique=False)
    op.create_index(op.f('ix_PurchaseOrder_po_number'), 'PurchaseOrder', ['po_number'], unique=True)
    op.drop_constraint('PurchaseOrder_company_id_fkey', 'PurchaseOrder', type_='foreignkey')
    op.drop_constraint('PurchaseOrder_supplier_id_fkey', 'PurchaseOrder', type_='foreignkey')
    op.create_foreign_key(None, 'PurchaseOrder', 'Company', ['company_id'], ['id'])
    op.create_foreign_key(None, 'PurchaseOrder', 'Supplier', ['supplier_id'], ['id'])
    op.alter_column('Quote', 'quote_number',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=False)
    op.alter_column('Quote', 'status',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=False,
               existing_server_default=sa.text("'draft'::text"))
    op.alter_column('Quote', 'store_type',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=True)
    op.alter_column('Quote', 'grand_total',
               existing_type=sa.DOUBLE_PRECISION(precision=53),
               type_=sa.Numeric(precision=10, scale=2),
               existing_nullable=False)
    op.alter_column('Quote', 'total_gst',
               existing_type=sa.DOUBLE_PRECISION(precision=53),
               type_=sa.Numeric(precision=10, scale=2),
               existing_nullable=False,
               existing_server_default=sa.text('0'))
    op.create_index(op.f('ix_Quote_id'), 'Quote', ['id'], unique=False)
    op.create_index(op.f('ix_Quote_quote_number'), 'Quote', ['quote_number'], unique=True)
    op.create_index('ix_quotes_date', 'Quote', ['quote_date'], unique=False)
    op.drop_constraint('Quote_customer_id_fkey', 'Quote', type_='foreignkey')
    op.drop_constraint('Quote_company_id_fkey', 'Quote', type_='foreignkey')
    op.create_foreign_key(None, 'Quote', 'Customer', ['customer_id'], ['id'])
    op.create_foreign_key(None, 'Quote', 'Company', ['company_id'], ['id'])
    op.alter_column('Role', 'name',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=False)
    op.alter_column('Role', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               nullable=True,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.alter_column('Role', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               nullable=True,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.create_index(op.f('ix_Role_id'), 'Role', ['id'], unique=False)
    op.create_index(op.f('ix_Role_name'), 'Role', ['name'], unique=True)
    op.alter_column('StoreType', 'name',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=False)
    op.alter_column('StoreType', 'description',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=True)
    op.alter_column('StoreType', 'is_active',
               existing_type=sa.BOOLEAN(),
               nullable=True,
               existing_server_default=sa.text('true'))
    op.alter_column('StoreType', 'created_at',
               existing_type=postgresql.TIMESTAMP(precision=3),
               nullable=True,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.alter_column('StoreType', 'updated_at',
               existing_type=postgresql.TIMESTAMP(precision=3),
               nullable=True,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.create_index(op.f('ix_StoreType_id'), 'StoreType', ['id'], unique=False)
    op.create_index(op.f('ix_StoreType_name'), 'StoreType', ['name'], unique=True)
    op.alter_column('Supplier', 'price_list',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               type_=sa.JSON(),
               existing_nullable=True,
               existing_server_default=sa.text("'[]'::jsonb"))
    op.alter_column('Supplier', 'is_active',
               existing_type=sa.BOOLEAN(),
               nullable=False,
               existing_server_default=sa.text('true'))
    op.create_index(op.f('ix_Supplier_id'), 'Supplier', ['id'], unique=False)
    op.alter_column('User', 'email',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               nullable=True)
    op.alter_column('User', 'user_name',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=True)
    op.alter_column('User', 'hashed_password',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=True)
    op.alter_column('User', 'mobile_number',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=True)
    op.alter_column('User', 'is_active',
               existing_type=sa.BOOLEAN(),
               nullable=True,
               existing_server_default=sa.text('true'))
    op.alter_column('User', 'is_deleted',
               existing_type=sa.BOOLEAN(),
               nullable=True,
               existing_server_default=sa.text('false'))
    op.alter_column('User', 'role',
               existing_type=sa.TEXT(),
               type_=sa.String(),
               existing_nullable=True)
    op.alter_column('User', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               nullable=True,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.create_index(op.f('ix_User_email'), 'User', ['email'], unique=True)
    op.create_index(op.f('ix_User_id'), 'User', ['id'], unique=False)
    op.create_index(op.f('ix_User_mobile_number'), 'User', ['mobile_number'], unique=True)
    op.create_index(op.f('ix_User_user_name'), 'User', ['user_name'], unique=True)
    op.drop_constraint('User_modified_by_id_fkey', 'User', type_='foreignkey')
    op.drop_constraint('User_store_type_id_fkey', 'User', type_='foreignkey')
    op.drop_constraint('User_created_by_id_fkey', 'User', type_='foreignkey')
    op.drop_constraint('User_manager_id_fkey', 'User', type_='foreignkey')
    op.drop_constraint('User_role_id_fkey', 'User', type_='foreignkey')
    op.create_foreign_key(None, 'User', 'User', ['created_by_id'], ['id'])
    op.create_foreign_key(None, 'User', 'Role', ['role_id'], ['id'])
    op.create_foreign_key(None, 'User', 'User', ['manager_id'], ['id'])
    op.create_foreign_key(None, 'User', 'StoreType', ['store_type_id'], ['id'])
    op.create_foreign_key(None, 'User', 'User', ['modified_by_id'], ['id'])
    # ### end Alembic commands ###


def downgrade():
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_constraint(None, 'User', type_='foreignkey')
    op.drop_constraint(None, 'User', type_='foreignkey')
    op.drop_constraint(None, 'User', type_='foreignkey')
    op.drop_constraint(None, 'User', type_='foreignkey')
    op.drop_constraint(None, 'User', type_='foreignkey')
    op.create_foreign_key('User_role_id_fkey', 'User', 'Role', ['role_id'], ['id'], onupdate='CASCADE', ondelete='SET NULL')
    op.create_foreign_key('User_manager_id_fkey', 'User', 'User', ['manager_id'], ['id'], onupdate='CASCADE', ondelete='SET NULL')
    op.create_foreign_key('User_created_by_id_fkey', 'User', 'User', ['created_by_id'], ['id'], onupdate='CASCADE', ondelete='SET NULL')
    op.create_foreign_key('User_store_type_id_fkey', 'User', 'StoreType', ['store_type_id'], ['id'], onupdate='CASCADE', ondelete='SET NULL')
    op.create_foreign_key('User_modified_by_id_fkey', 'User', 'User', ['modified_by_id'], ['id'], onupdate='CASCADE', ondelete='SET NULL')
    op.drop_index(op.f('ix_User_user_name'), table_name='User')
    op.drop_index(op.f('ix_User_mobile_number'), table_name='User')
    op.drop_index(op.f('ix_User_id'), table_name='User')
    op.drop_index(op.f('ix_User_email'), table_name='User')
    op.alter_column('User', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               nullable=False,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.alter_column('User', 'role',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=True)
    op.alter_column('User', 'is_deleted',
               existing_type=sa.BOOLEAN(),
               nullable=False,
               existing_server_default=sa.text('false'))
    op.alter_column('User', 'is_active',
               existing_type=sa.BOOLEAN(),
               nullable=False,
               existing_server_default=sa.text('true'))
    op.alter_column('User', 'mobile_number',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=True)
    op.alter_column('User', 'hashed_password',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=True)
    op.alter_column('User', 'user_name',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=True)
    op.alter_column('User', 'email',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               nullable=False)
    op.drop_index(op.f('ix_Supplier_id'), table_name='Supplier')
    op.alter_column('Supplier', 'is_active',
               existing_type=sa.BOOLEAN(),
               nullable=True,
               existing_server_default=sa.text('true'))
    op.alter_column('Supplier', 'price_list',
               existing_type=sa.JSON(),
               type_=postgresql.JSONB(astext_type=sa.Text()),
               existing_nullable=True,
               existing_server_default=sa.text("'[]'::jsonb"))
    op.drop_index(op.f('ix_StoreType_name'), table_name='StoreType')
    op.drop_index(op.f('ix_StoreType_id'), table_name='StoreType')
    op.alter_column('StoreType', 'updated_at',
               existing_type=postgresql.TIMESTAMP(precision=3),
               nullable=False,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.alter_column('StoreType', 'created_at',
               existing_type=postgresql.TIMESTAMP(precision=3),
               nullable=False,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.alter_column('StoreType', 'is_active',
               existing_type=sa.BOOLEAN(),
               nullable=False,
               existing_server_default=sa.text('true'))
    op.alter_column('StoreType', 'description',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=True)
    op.alter_column('StoreType', 'name',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=False)
    op.drop_index(op.f('ix_Role_name'), table_name='Role')
    op.drop_index(op.f('ix_Role_id'), table_name='Role')
    op.alter_column('Role', 'updated_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               nullable=False,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.alter_column('Role', 'created_at',
               existing_type=postgresql.TIMESTAMP(timezone=True),
               nullable=False,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.alter_column('Role', 'name',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=False)
    op.drop_constraint(None, 'Quote', type_='foreignkey')
    op.drop_constraint(None, 'Quote', type_='foreignkey')
    op.create_foreign_key('Quote_company_id_fkey', 'Quote', 'Company', ['company_id'], ['id'], onupdate='CASCADE', ondelete='RESTRICT')
    op.create_foreign_key('Quote_customer_id_fkey', 'Quote', 'Customer', ['customer_id'], ['id'], onupdate='CASCADE', ondelete='RESTRICT')
    op.drop_index('ix_quotes_date', table_name='Quote')
    op.drop_index(op.f('ix_Quote_quote_number'), table_name='Quote')
    op.drop_index(op.f('ix_Quote_id'), table_name='Quote')
    op.alter_column('Quote', 'total_gst',
               existing_type=sa.Numeric(precision=10, scale=2),
               type_=sa.DOUBLE_PRECISION(precision=53),
               existing_nullable=False,
               existing_server_default=sa.text('0'))
    op.alter_column('Quote', 'grand_total',
               existing_type=sa.Numeric(precision=10, scale=2),
               type_=sa.DOUBLE_PRECISION(precision=53),
               existing_nullable=False)
    op.alter_column('Quote', 'store_type',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=True)
    op.alter_column('Quote', 'status',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=False,
               existing_server_default=sa.text("'draft'::text"))
    op.alter_column('Quote', 'quote_number',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=False)
    op.drop_constraint(None, 'PurchaseOrder', type_='foreignkey')
    op.drop_constraint(None, 'PurchaseOrder', type_='foreignkey')
    op.create_foreign_key('PurchaseOrder_supplier_id_fkey', 'PurchaseOrder', 'Supplier', ['supplier_id'], ['id'], onupdate='CASCADE', ondelete='RESTRICT')
    op.create_foreign_key('PurchaseOrder_company_id_fkey', 'PurchaseOrder', 'Company', ['company_id'], ['id'], onupdate='CASCADE', ondelete='RESTRICT')
    op.drop_index(op.f('ix_PurchaseOrder_po_number'), table_name='PurchaseOrder')
    op.drop_index(op.f('ix_PurchaseOrder_id'), table_name='PurchaseOrder')
    op.alter_column('PurchaseOrder', 'status',
               existing_type=sa.String(length=50),
               type_=sa.TEXT(),
               existing_nullable=False)
    op.alter_column('PurchaseOrder', 'total_amount',
               existing_type=sa.Numeric(precision=10, scale=2),
               type_=sa.DOUBLE_PRECISION(precision=53),
               existing_nullable=False)
    op.alter_column('PurchaseOrder', 'items',
               existing_type=postgresql.JSONB(astext_type=sa.Text()),
               nullable=False)
    op.alter_column('PurchaseOrder', 'po_number',
               existing_type=sa.String(length=50),
               type_=sa.TEXT(),
               existing_nullable=False)
    op.drop_constraint(None, 'PriceList', type_='unique')
    op.alter_column('PriceList', 'updated_at',
               existing_type=postgresql.TIMESTAMP(precision=3),
               nullable=False)
    op.alter_column('PriceList', 'created_at',
               existing_type=postgresql.TIMESTAMP(precision=3),
               nullable=False,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.alter_column('PriceList', 'currency',
               existing_type=sa.String(length=3),
               type_=sa.TEXT(),
               nullable=False,
               existing_server_default=sa.text("'AUD'::text"))
    op.alter_column('PriceList', 'name',
               existing_type=sa.String(length=255),
               type_=sa.TEXT(),
               existing_nullable=False)
    op.drop_constraint(None, 'Invoice', type_='foreignkey')
    op.drop_constraint(None, 'Invoice', type_='foreignkey')
    op.drop_constraint(None, 'Invoice', type_='foreignkey')
    op.create_foreign_key('Invoice_company_id_fkey', 'Invoice', 'Company', ['company_id'], ['id'], onupdate='CASCADE', ondelete='RESTRICT')
    op.create_foreign_key('Invoice_customer_id_fkey', 'Invoice', 'Customer', ['customer_id'], ['id'], onupdate='CASCADE', ondelete='RESTRICT')
    op.drop_index(op.f('ix_Invoice_linked_quote_id'), table_name='Invoice')
    op.drop_index(op.f('ix_Invoice_invoice_number'), table_name='Invoice')
    op.drop_index(op.f('ix_Invoice_id'), table_name='Invoice')
    op.alter_column('Invoice', 'shipping',
               existing_type=sa.DOUBLE_PRECISION(precision=53),
               nullable=True,
               existing_server_default=sa.text('0.0'))
    op.alter_column('Invoice', 'credit_card_surcharge',
               existing_type=sa.DOUBLE_PRECISION(precision=53),
               nullable=True,
               existing_server_default=sa.text('0.0'))
    op.alter_column('Invoice', 'total_order',
               existing_type=sa.DOUBLE_PRECISION(precision=53),
               nullable=True,
               existing_server_default=sa.text('0.0'))
    op.alter_column('Invoice', 'dont_send_po',
               existing_type=sa.BOOLEAN(),
               nullable=True,
               existing_server_default=sa.text('false'))
    op.alter_column('Invoice', 'mode_of_payment',
               existing_type=sa.Enum('CASH', 'BANK_TRANSFER', 'CREDIT_CARD', 'CHEQUE', name='paymentmode'),
               type_=sa.VARCHAR(),
               existing_nullable=True,
               existing_server_default=sa.text("'cash'::character varying"))
    op.alter_column('Invoice', 'invoice_type',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=True)
    op.alter_column('Invoice', 'status',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=False,
               existing_server_default=sa.text("'draft'::text"))
    op.alter_column('Invoice', 'invoice_number',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=False)
    op.drop_constraint(None, 'Inventory', type_='foreignkey')
    op.drop_constraint(None, 'Inventory', type_='foreignkey')
    op.drop_constraint(None, 'Inventory', type_='foreignkey')
    op.drop_index(op.f('ix_Inventory_supplier_id'), table_name='Inventory')
    op.drop_index(op.f('ix_Inventory_style_code'), table_name='Inventory')
    op.drop_index(op.f('ix_Inventory_sku_code'), table_name='Inventory')
    op.drop_index(op.f('ix_Inventory_id'), table_name='Inventory')
    op.alter_column('Inventory', 'style_code',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=False)
    op.alter_column('Inventory', 'sku_code',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=False)
    op.alter_column('Inventory', 'carton_dimensions',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=True)
    op.alter_column('Inventory', 'carton',
               existing_type=sa.INTEGER(),
               nullable=False,
               existing_server_default=sa.text('1'))
    op.alter_column('Inventory', 'updated_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(precision=3),
               nullable=False)
    op.alter_column('Inventory', 'created_at',
               existing_type=sa.DateTime(timezone=True),
               type_=postgresql.TIMESTAMP(precision=3),
               nullable=False,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.drop_column('Inventory', 'image_path')
    op.drop_index(op.f('ix_InboundDelivery_supplier_name'), table_name='InboundDelivery')
    op.drop_index(op.f('ix_InboundDelivery_id'), table_name='InboundDelivery')
    op.alter_column('InboundDelivery', 'status',
               existing_type=sa.String(length=50),
               type_=sa.TEXT(),
               existing_nullable=False)
    op.alter_column('InboundDelivery', 'items',
               existing_type=sa.JSON(),
               type_=postgresql.JSONB(astext_type=sa.Text()),
               nullable=False)
    op.alter_column('InboundDelivery', 'supplier_name',
               existing_type=sa.String(length=255),
               type_=sa.TEXT(),
               existing_nullable=False)
    op.alter_column('InboundDelivery', 'delivery_number',
               existing_type=sa.String(length=50),
               type_=sa.TEXT(),
               nullable=False)
    op.drop_column('InboundDelivery', 'ship_to_customer')
    op.drop_index(op.f('ix_DraftMail_id'), table_name='DraftMail')
    op.alter_column('DraftMail', 'updated_at',
               existing_type=sa.DateTime(),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=False,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.alter_column('DraftMail', 'created_at',
               existing_type=sa.DateTime(),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=False,
               existing_server_default=sa.text('CURRENT_TIMESTAMP'))
    op.alter_column('DraftMail', 'sent_at',
               existing_type=sa.DateTime(),
               type_=postgresql.TIMESTAMP(timezone=True),
               existing_nullable=True)
    op.alter_column('DraftMail', 'sent',
               existing_type=sa.BOOLEAN(),
               nullable=False,
               existing_server_default=sa.text('false'))
    op.alter_column('DraftMail', 'ready_to_send',
               existing_type=sa.BOOLEAN(),
               nullable=False,
               existing_server_default=sa.text('false'))
    op.drop_constraint(None, 'Customer', type_='foreignkey')
    op.create_foreign_key('Customer_price_list_id_fkey', 'Customer', 'PriceList', ['price_list_id'], ['id'], onupdate='CASCADE', ondelete='SET NULL')
    op.drop_constraint(None, 'Customer', type_='unique')
    op.drop_constraint(None, 'Customer', type_='unique')
    op.alter_column('Customer', 'billing_postcode',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=True)
    op.alter_column('Customer', 'billing_suburb',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=True)
    op.alter_column('Customer', 'phone',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=True)
    op.alter_column('Customer', 'email',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=True)
    op.alter_column('Customer', 'contact_person',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=True)
    op.alter_column('Customer', 'company_name',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=False)
    op.drop_index(op.f('ix_Company_name'), table_name='Company')
    op.alter_column('Company', 'email',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=True)
    op.alter_column('Company', 'phone',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=True)
    op.alter_column('Company', 'name',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=False)
    op.drop_index(op.f('ix_AuditLog_table_name'), table_name='AuditLog')
    op.drop_index(op.f('ix_AuditLog_status'), table_name='AuditLog')
    op.drop_index(op.f('ix_AuditLog_operation'), table_name='AuditLog')
    op.drop_index(op.f('ix_AuditLog_id'), table_name='AuditLog')
    op.drop_index(op.f('ix_AuditLog_event_type'), table_name='AuditLog')
    op.drop_index(op.f('ix_AuditLog_email'), table_name='AuditLog')
    op.alter_column('AuditLog', 'operation',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=True)
    op.alter_column('AuditLog', 'table_name',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=True)
    op.alter_column('AuditLog', 'ip_address',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=True)
    op.alter_column('AuditLog', 'status',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=True)
    op.alter_column('AuditLog', 'email',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=True)
    op.alter_column('AuditLog', 'event_type',
               existing_type=sa.String(),
               type_=sa.TEXT(),
               existing_nullable=True)
    # ### end Alembic commands ### 