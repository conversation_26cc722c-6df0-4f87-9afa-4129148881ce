"""Refactor Invoices and Quotes to use ID-based references

Revision ID: 5a97af5e4d28
Revises: latest  # Replace with actual previous revision ID
Create Date: 2025-05-09 16:15:00

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql
import uuid

# revision identifiers, used by Alembic.
revision = '5a97af5e4d28'
down_revision = None  # Replace with actual previous revision ID
branch_labels = None
depends_on = None


def upgrade():
    # Create new tables first
    
    # Create companies table
    op.create_table(
        'companies',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, default=uuid.uuid4),
        sa.Column('name', sa.String(255), nullable=False, index=True, unique=True),
        sa.Column('contact_person', sa.String(255), nullable=True),
        sa.Column('email', sa.String(255), nullable=True),
        sa.Column('phone', sa.String(20), nullable=True),
        sa.Column('address', sa.Text, nullable=True),
        sa.Column('suburb', sa.String(255), nullable=True),
        sa.Column('postcode', sa.String(20), nullable=True),
        sa.Column('is_active', sa.Boolean, nullable=False, default=True),
        sa.Column('created_at', sa.DateTime, nullable=False, server_default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime, nullable=False, server_default=sa.func.now(), onupdate=sa.func.now())
    )
    
    # Create invoice_numbers table
    op.create_table(
        'invoice_numbers',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, default=uuid.uuid4),
        sa.Column('formatted_number', sa.String(50), nullable=False, index=True, unique=True),
        sa.Column('sequence', sa.Integer, nullable=False),
        sa.Column('year', sa.Integer, nullable=False),
        sa.Column('month', sa.Integer, nullable=False),
        sa.Column('day', sa.Integer, nullable=False),
        sa.Column('prefix', sa.String(10), nullable=False, server_default='INV'),
        sa.Column('created_at', sa.DateTime, nullable=False, server_default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime, nullable=False, server_default=sa.func.now(), onupdate=sa.func.now())
    )
    
    # Create quote_numbers table
    op.create_table(
        'quote_numbers',
        sa.Column('id', postgresql.UUID(as_uuid=True), primary_key=True, default=uuid.uuid4),
        sa.Column('formatted_number', sa.String(50), nullable=False, index=True, unique=True),
        sa.Column('sequence', sa.Integer, nullable=False),
        sa.Column('year', sa.Integer, nullable=False),
        sa.Column('month', sa.Integer, nullable=False),
        sa.Column('day', sa.Integer, nullable=False),
        sa.Column('prefix', sa.String(10), nullable=False, server_default='QUO'),
        sa.Column('created_at', sa.DateTime, nullable=False, server_default=sa.func.now()),
        sa.Column('updated_at', sa.DateTime, nullable=False, server_default=sa.func.now(), onupdate=sa.func.now())
    )
    
    # Data Migration: 
    # 1. Populate companies table from existing customer data
    op.execute("""
        INSERT INTO companies (id, name, contact_person, email, phone, address, suburb, postcode, is_active, created_at, updated_at)
        SELECT 
            id, 
            company_name, 
            contact_person, 
            email, 
            phone, 
            billing_address, 
            billing_suburb, 
            billing_postcode, 
            true, 
            created_at, 
            updated_at
        FROM customers
    """)
    
    # 2. Populate invoice_numbers from invoices table
    op.execute("""
        INSERT INTO invoice_numbers (id, formatted_number, sequence, year, month, day, prefix, created_at, updated_at)
        SELECT 
            uuid_generate_v4(), 
            invoice_no, 
            CAST(SUBSTRING(invoice_no FROM POSITION('-' IN invoice_no) + 9) AS INTEGER), 
            CAST(SUBSTRING(invoice_no FROM POSITION('-' IN invoice_no) + 1 FOR 4) AS INTEGER),
            CAST(SUBSTRING(invoice_no FROM POSITION('-' IN invoice_no) + 5 FOR 2) AS INTEGER),
            CAST(SUBSTRING(invoice_no FROM POSITION('-' IN invoice_no) + 7 FOR 2) AS INTEGER),
            SUBSTRING(invoice_no FROM 1 FOR POSITION('-' IN invoice_no) - 1),
            created_at, 
            updated_at
        FROM invoices
    """)
    
    # 3. Populate quote_numbers from quotes table
    op.execute("""
        INSERT INTO quote_numbers (id, formatted_number, sequence, year, month, day, prefix, created_at, updated_at)
        SELECT 
            uuid_generate_v4(), 
            quote_no, 
            CAST(SUBSTRING(quote_no FROM POSITION('-' IN quote_no) + 9) AS INTEGER), 
            CAST(SUBSTRING(quote_no FROM POSITION('-' IN quote_no) + 1 FOR 4) AS INTEGER),
            CAST(SUBSTRING(quote_no FROM POSITION('-' IN quote_no) + 5 FOR 2) AS INTEGER),
            CAST(SUBSTRING(quote_no FROM POSITION('-' IN quote_no) + 7 FOR 2) AS INTEGER),
            SUBSTRING(quote_no FROM 1 FOR POSITION('-' IN quote_no) - 1),
            created_at, 
            updated_at
        FROM quotes
    """)
    
    # Add new columns to invoices table
    op.add_column('invoices', sa.Column('invoice_number_id', postgresql.UUID(as_uuid=True), nullable=True))
    op.add_column('invoices', sa.Column('store_type_id', sa.Integer, nullable=True))
    op.add_column('invoices', sa.Column('company_id', postgresql.UUID(as_uuid=True), nullable=True))
    
    # Update invoices with references
    op.execute("""
        UPDATE invoices i
        SET 
            invoice_number_id = (SELECT id FROM invoice_numbers WHERE formatted_number = i.invoice_no LIMIT 1),
            store_type_id = (SELECT id FROM store_types WHERE name = i.store_type LIMIT 1),
            company_id = i.customer_id
    """)
    
    # Make the new columns non-nullable
    op.alter_column('invoices', 'invoice_number_id', nullable=False)
    op.alter_column('invoices', 'store_type_id', nullable=False)
    op.alter_column('invoices', 'company_id', nullable=False)
    
    # Add foreign key constraints
    op.create_foreign_key('fk_invoice_invoice_number', 'invoices', 'invoice_numbers', ['invoice_number_id'], ['id'])
    op.create_foreign_key('fk_invoice_store_type', 'invoices', 'store_types', ['store_type_id'], ['id'])
    op.create_foreign_key('fk_invoice_company', 'invoices', 'companies', ['company_id'], ['id'])
    
    # Add new columns to quotes table
    op.add_column('quotes', sa.Column('quote_number_id', postgresql.UUID(as_uuid=True), nullable=True))
    op.add_column('quotes', sa.Column('store_type_id', sa.Integer, nullable=True))
    op.add_column('quotes', sa.Column('company_id', postgresql.UUID(as_uuid=True), nullable=True))
    
    # Update quotes with references
    op.execute("""
        UPDATE quotes q
        SET 
            quote_number_id = (SELECT id FROM quote_numbers WHERE formatted_number = q.quote_no LIMIT 1),
            store_type_id = (SELECT id FROM store_types WHERE name = q.store_type LIMIT 1),
            company_id = (SELECT id FROM companies WHERE name = q.company_name LIMIT 1)
    """)
    
    # Make the new columns non-nullable
    op.alter_column('quotes', 'quote_number_id', nullable=False)
    op.alter_column('quotes', 'store_type_id', nullable=False)
    op.alter_column('quotes', 'company_id', nullable=False)
    
    # Add foreign key constraints
    op.create_foreign_key('fk_quote_quote_number', 'quotes', 'quote_numbers', ['quote_number_id'], ['id'])
    op.create_foreign_key('fk_quote_store_type', 'quotes', 'store_types', ['store_type_id'], ['id'])
    op.create_foreign_key('fk_quote_company', 'quotes', 'companies', ['company_id'], ['id'])
    
    # Create unique constraints on the new ID fields
    op.create_unique_constraint('uq_invoice_invoice_number_id', 'invoices', ['invoice_number_id'])
    op.create_unique_constraint('uq_quote_quote_number_id', 'quotes', ['quote_number_id'])
    
    # Create indexes
    op.create_index('ix_invoices_company_id', 'invoices', ['company_id'])
    op.create_index('ix_quotes_company_id', 'quotes', ['company_id'])
    
    # Drop old columns (commented out for safety - uncomment after confirming data migration success)
    # op.drop_column('invoices', 'invoice_no')
    # op.drop_column('invoices', 'store_type')
    # op.drop_column('quotes', 'quote_no')
    # op.drop_column('quotes', 'store_type')
    # op.drop_column('quotes', 'company_name')


def downgrade():
    # Restore old columns if they were dropped
    # op.add_column('invoices', sa.Column('invoice_no', sa.String(50), nullable=True))
    # op.add_column('invoices', sa.Column('store_type', sa.String(50), nullable=True))
    # op.add_column('quotes', sa.Column('quote_no', sa.String(20), nullable=True))
    # op.add_column('quotes', sa.Column('store_type', sa.String(50), nullable=True))
    # op.add_column('quotes', sa.Column('company_name', sa.String(255), nullable=True))
    
    # Restore data from the reference tables
    # op.execute("""
    #     UPDATE invoices i
    #     SET 
    #         invoice_no = (SELECT formatted_number FROM invoice_numbers WHERE id = i.invoice_number_id),
    #         store_type = (SELECT name FROM store_types WHERE id = i.store_type_id)
    # """)
    
    # op.execute("""
    #     UPDATE quotes q
    #     SET 
    #         quote_no = (SELECT formatted_number FROM quote_numbers WHERE id = q.quote_number_id),
    #         store_type = (SELECT name FROM store_types WHERE id = q.store_type_id),
    #         company_name = (SELECT name FROM companies WHERE id = q.company_id)
    # """)
    
    # Drop foreign key constraints
    op.drop_constraint('fk_invoice_invoice_number', 'invoices', type_='foreignkey')
    op.drop_constraint('fk_invoice_store_type', 'invoices', type_='foreignkey')
    op.drop_constraint('fk_invoice_company', 'invoices', type_='foreignkey')
    op.drop_constraint('fk_quote_quote_number', 'quotes', type_='foreignkey')
    op.drop_constraint('fk_quote_store_type', 'quotes', type_='foreignkey')
    op.drop_constraint('fk_quote_company', 'quotes', type_='foreignkey')
    
    # Drop unique constraints
    op.drop_constraint('uq_invoice_invoice_number_id', 'invoices', type_='unique')
    op.drop_constraint('uq_quote_quote_number_id', 'quotes', type_='unique')
    
    # Drop indexes
    op.drop_index('ix_invoices_company_id', table_name='invoices')
    op.drop_index('ix_quotes_company_id', table_name='quotes')
    
    # Drop new columns
    op.drop_column('invoices', 'invoice_number_id')
    op.drop_column('invoices', 'store_type_id')
    op.drop_column('invoices', 'company_id')
    op.drop_column('quotes', 'quote_number_id')
    op.drop_column('quotes', 'store_type_id')
    op.drop_column('quotes', 'company_id')
    
    # Drop new tables
    op.drop_table('invoice_numbers')
    op.drop_table('quote_numbers')
    op.drop_table('companies') 