"""add_notes_to_outbound_deliveries

Revision ID: add_notes_to_outbound_deliveries
Revises: add_outbound_deliveries_tables
Create Date: 2023-11-15 12:00:00

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'add_notes_to_outbound_deliveries'
down_revision = 'add_outbound_deliveries_tables'
branch_labels = None
depends_on = None


def upgrade():
    # Add notes column to outbound_deliveries table
    op.add_column('outbound_deliveries', sa.Column('notes', sa.Text(), nullable=True))
    
    # Add notes column to delivered_orders table
    op.add_column('delivered_orders', sa.Column('notes', sa.Text(), nullable=True))


def downgrade():
    # Drop notes column from outbound_deliveries table
    op.drop_column('outbound_deliveries', 'notes')
    
    # Drop notes column from delivered_orders table
    op.drop_column('delivered_orders', 'notes') 