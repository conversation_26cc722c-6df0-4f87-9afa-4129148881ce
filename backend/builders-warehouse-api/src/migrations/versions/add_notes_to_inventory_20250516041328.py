"""Add notes column to Inventory table

Revision ID: add_notes_to_inventory
Revises: 
Create Date: 2025-05-16 04:13:28

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = 'add_notes_to_inventory'
down_revision = None  # Set this to the ID of the previous migration if known
branch_labels = None
depends_on = None

def upgrade():
    # Add notes column to Inventory table
    op.add_column('Inventory', sa.Column('notes', sa.Text(), nullable=True))

def downgrade():
    # Drop notes column from Inventory table
    op.drop_column('Inventory', 'notes') 