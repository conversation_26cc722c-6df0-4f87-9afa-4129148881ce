"""add inbound deliveries table

Revision ID: add_inbound_deliveries
Revises: 
Create Date: 2024-03-19 10:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from datetime import datetime

# revision identifiers, used by Alembic.
revision = 'add_inbound_deliveries'
down_revision = None
branch_labels = None
depends_on = None

def upgrade():
    op.create_table(
        'inbound_deliveries',
        sa.Column('id', sa.Integer(), nullable=False),
        sa.Column('po_number', sa.String(50), nullable=False),
        sa.Column('supplier_name', sa.String(255), nullable=False),
        sa.Column('sku', sa.String(50), nullable=False),
        sa.Column('quantity_ordered', sa.Integer(), nullable=False, default=0),
        sa.Column('quantity_received', sa.Integer(), nullable=False, default=0),
        sa.Column('linked_invoice', sa.String(50), nullable=True),
        sa.Column('ordered_by', sa.String(100), nullable=False),
        sa.Column('balance_order', sa.Integer(), nullable=False, default=0),
        sa.Column('ship_to_customer', sa.Boolean(), nullable=False, default=False),
        sa.Column('po_date', sa.DateTime(), nullable=False),
        sa.Column('new_expected_date', sa.DateTime(), nullable=True),
        sa.Column('status', sa.String(50), nullable=False, default='pending'),
        sa.Column('created_at', sa.DateTime(), nullable=False, default=datetime.utcnow),
        sa.Column('updated_at', sa.DateTime(), nullable=False, default=datetime.utcnow, onupdate=datetime.utcnow),
        sa.PrimaryKeyConstraint('id')
    )
    
    # Create indexes
    op.create_index('ix_inbound_deliveries_po_number', 'inbound_deliveries', ['po_number'])
    op.create_index('ix_inbound_deliveries_supplier_name', 'inbound_deliveries', ['supplier_name'])

def downgrade():
    op.drop_index('ix_inbound_deliveries_supplier_name', table_name='inbound_deliveries')
    op.drop_index('ix_inbound_deliveries_po_number', table_name='inbound_deliveries')
    op.drop_table('inbound_deliveries') 