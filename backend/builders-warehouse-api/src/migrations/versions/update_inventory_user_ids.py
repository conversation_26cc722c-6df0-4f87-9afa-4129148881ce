"""update_inventory_user_ids

Revision ID: update_inventory_user_ids
Revises: create_audit_logs
Create Date: 2025-05-09

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects.postgresql import UUID


# revision identifiers, used by Alembic.
revision = 'update_inventory_user_ids'
down_revision = 'create_audit_logs'  # Adjust this as needed
branch_labels = None
depends_on = None


def upgrade():
    # Modify columns in inventory table for created_by and updated_by to use UUID instead of Integer
    op.alter_column('inventory', 'created_by', 
                    existing_type=sa.INTEGER(), 
                    type_=UUID(as_uuid=True),
                    nullable=True)
    op.alter_column('inventory', 'updated_by', 
                    existing_type=sa.INTEGER(), 
                    type_=UUID(as_uuid=True),
                    nullable=True)
    
    # Update foreign key constraints
    op.drop_constraint('inventory_created_by_fkey', 'inventory', type_='foreignkey')
    op.drop_constraint('inventory_updated_by_fkey', 'inventory', type_='foreignkey')
    
    op.create_foreign_key('inventory_created_by_fkey', 'inventory', 'users', 
                         ['created_by'], ['id'], ondelete='SET NULL')
    op.create_foreign_key('inventory_updated_by_fkey', 'inventory', 'users',
                         ['updated_by'], ['id'], ondelete='SET NULL')


def downgrade():
    # Revert the changes
    op.drop_constraint('inventory_created_by_fkey', 'inventory', type_='foreignkey')
    op.drop_constraint('inventory_updated_by_fkey', 'inventory', type_='foreignkey')
    
    op.alter_column('inventory', 'created_by', 
                    existing_type=UUID(as_uuid=True), 
                    type_=sa.INTEGER(),
                    nullable=True)
    op.alter_column('inventory', 'updated_by', 
                    existing_type=UUID(as_uuid=True), 
                    type_=sa.INTEGER(),
                    nullable=True)
    
    op.create_foreign_key('inventory_created_by_fkey', 'inventory', 'users', 
                         ['created_by'], ['id'], ondelete='SET NULL')
    op.create_foreign_key('inventory_updated_by_fkey', 'inventory', 'users',
                         ['updated_by'], ['id'], ondelete='SET NULL') 