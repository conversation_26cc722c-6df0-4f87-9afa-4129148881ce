"""add ship_to_customer column to InboundDelivery

Revision ID: add_ship_to_customer
Revises: add_inbound_deliveries
Create Date: 2025-05-27 13:15:00.000000

"""
from alembic import op
import sqlalchemy as sa

# revision identifiers, used by Alembic.
revision = 'add_ship_to_customer'
down_revision = 'add_inbound_deliveries'
branch_labels = None
depends_on = None

def upgrade():
    # Add ship_to_customer column to InboundDelivery table
    op.add_column('InboundDelivery', sa.Column('ship_to_customer', sa.<PERSON><PERSON>an(), nullable=False, default=False))

def downgrade():
    # Remove ship_to_customer column from InboundDelivery table
    op.drop_column('InboundDelivery', 'ship_to_customer') 