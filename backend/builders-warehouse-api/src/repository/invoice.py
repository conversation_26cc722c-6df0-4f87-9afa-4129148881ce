"""
Invoice repository module
This module provides functions to interact with invoices in the database
"""
import logging
import uuid
import json
from typing import List, Optional, Dict, Any, Tuple, Union
from datetime import datetime, date, timedelta
from decimal import Decimal
from fastapi import HTTPException, status
from sqlalchemy import or_, func, desc, and_, between
from sqlalchemy.orm import Session, joinedload
from sqlalchemy.sql.expression import func as sqlfunc

from src.models.invoice import Invoice, InvoiceItem
from src.models.customer import Customer
from src.models.purchase_order import PurchaseOrder
from src.models.draft_mail import DraftMail
from src.models.company import Company
from src.schemas.invoice import InvoiceCreate, InvoiceUpdate, InvoiceItemCreate
from src.repository.purchase_order import create_purchase_order
from src.repository.draft_mail import create_draft_mail
from src.schemas.purchase_order import PurchaseOrderCreate, PurchaseOrderDetailCreate

# Import models/enums if they exist in your project
try:
    from src.models.enums import PaymentMode, SaleType
except ImportError:
    # If they don't exist as separate enums, use the ones from invoice
    from src.models.invoice import PaymentMode, SaleType

logger = logging.getLogger(__name__)

def generate_invoice_number(db: Session) -> str:
    """Generate a unique invoice number with format INV-YYYYMMDD-XXX"""
    today = datetime.now()
    date_part = today.strftime("%Y%m%d")
    
    # Find the latest invoice number for today
    latest_invoice = db.query(Invoice).filter(
        Invoice.invoice_number.like(f"INV-{date_part}-%")
    ).order_by(desc(Invoice.invoice_number)).first()
    
    if latest_invoice:
        # Extract sequence number and increment
        try:
            seq_str = latest_invoice.invoice_number.split('-')[-1]
            seq_num = int(seq_str) + 1
        except (ValueError, IndexError):
            seq_num = 1
    else:
        seq_num = 1
    
    # Format with leading zeros for the sequence
    return f"INV-{date_part}-{seq_num:03d}"

def create_invoice(
    db: Session, 
    invoice_data: InvoiceCreate, 
    user_id: int,
    user_store_type: str = None
) -> Invoice:
    """Create a new invoice with items and link or create a purchase order"""
    try:
        # Generate invoice number
        invoice_no = generate_invoice_number(db)
        logger.info(f"Generated invoice number: {invoice_no}")
        
        # Process items for JSON serialization
        serialized_items = []
        for item in invoice_data.items:
            item_dict = item.model_dump()
            # Convert Decimal to float for JSON serialization
            for k, v in item_dict.items():
                if isinstance(v, Decimal):
                    item_dict[k] = float(v)
            serialized_items.append(item_dict)
        
        # Convert UUID strings to integers for the database
        company_id = 1  # Default company ID
        customer_id = 1  # Default customer ID
        
        # Use default IDs to simplify
        logger.info(f"Using company_id={company_id}, customer_id={customer_id}")
        
        # Create invoice with appropriate field mappings
        db_invoice = Invoice(
            invoice_number=invoice_no,  # Use invoice_number instead of invoice_no
            company_id=company_id,  # Use integer ID instead of UUID
            customer_id=customer_id,  # Use integer ID instead of UUID
            invoice_date=invoice_data.date,  # Map date to invoice_date
            due_date=invoice_data.date,  # Set due date same as invoice date for now
            status="draft",
            invoice_type=invoice_data.sale_type.value if invoice_data.sale_type else "trade",
            notes=invoice_data.notes,
            items={"items": serialized_items}
        )
        
        # Add to session and flush to get the ID, but don't commit yet
        db.add(db_invoice)
        db.flush()
        
        # Check if the invoice has an ID
        if not hasattr(db_invoice, 'id') or db_invoice.id is None:
            logger.error("Error: Invoice ID is None after flush")
            raise ValueError("Invoice ID is None after flush")
        
        logger.info(f"Created invoice with ID: {db_invoice.id}")
        
        # Calculate totals
        db_invoice.calculate_totals()
        db.flush()
        
        # Process PO creation in a simplified way to avoid complexity
        po_id = None
        try:
            # Skip PO creation entirely - simplify to fix the issue
            logger.info("Skipping PO creation to simplify")
            
            # Commit the transaction
            db.commit()
            logger.info(f"Committed invoice {db_invoice.id} successfully")
            
            # Return the invoice directly
            return db_invoice
            
        except Exception as inner_error:
            logger.error(f"Error in PO creation process: {str(inner_error)}")
            db.rollback()
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to create invoice: {str(inner_error)}"
            )
    
    except Exception as e:
        logger.error(f"Error creating invoice: {str(e)}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create invoice: {str(e)}"
        )

def get_invoices(
    db: Session, 
    skip: int = 0, 
    limit: int = 100,
    customer_id: Optional[uuid.UUID] = None,
    store_type: Optional[str] = None,
    start_date: Optional[date] = None,
    end_date: Optional[date] = None,
    search: Optional[str] = None,
) -> Tuple[List[Dict[str, Any]], int]:
    """Get all invoices with optional filtering"""
    print(f"Repository: get_invoices called with dates: {start_date} to {end_date}")
    
    # Import StoreType here to avoid circular imports
    from src.models.store_type import StoreType
    
    # Select only the specific columns we need rather than the whole model
    # to avoid lazy loading issues, and include store type name
    query = db.query(
        Invoice.id,
        Invoice.invoice_number,
        Invoice.company_id,
        Invoice.customer_id,
        Invoice.invoice_date,
        Invoice.status,  # Add status field
        Invoice.grand_total,
        Invoice.notes,
        Invoice.invoice_type,
        Invoice.store_type_id,
        Invoice.created_at,
        Invoice.updated_at,
        Invoice.linked_quote_id,  # Add linked_quote_id to the query
        StoreType.name.label("store_type_name"),
        Customer.company_name.label("customer_name")
    ).outerjoin(
        StoreType, Invoice.store_type_id == StoreType.id
    ).join(
        Customer, Invoice.customer_id == Customer.id
    )
    
    # Apply filters
    if customer_id:
        query = query.filter(Invoice.customer_id == customer_id)
    
    if store_type:
        # Use invoice_type instead of store_type
        query = query.filter(Invoice.invoice_type == store_type)
    
    # Search by invoice number or customer name
    if search:
        search_term = f"%{search}%"
        query = query.filter(
            or_(
                Invoice.invoice_number.ilike(search_term),
                Customer.company_name.ilike(search_term)
            )
        )
    
    # Debug the original query
    print(f"Base query: {str(query)}")
    
    # Handle date filters - use invoice_date instead of date
    if start_date and end_date:
        # Use BETWEEN for date range (inclusive of both start and end)
        print(f"Applying date range filter: {start_date} to {end_date}")
        query = query.filter(Invoice.invoice_date.between(start_date, end_date))
    elif start_date:
        print(f"Applying start date filter: >= {start_date}")
        query = query.filter(Invoice.invoice_date >= start_date)
    elif end_date:
        print(f"Applying end date filter: <= {end_date}")
        query = query.filter(Invoice.invoice_date <= end_date)
    
    # Get total count for pagination
    count_query = db.query(func.count()).select_from(
        query.with_entities(Invoice.id).subquery()
    )
    total = count_query.scalar() or 0
    print(f"Total count before pagination: {total}")
    
    # Get results with pagination
    results = query.order_by(desc(Invoice.invoice_date), desc(Invoice.created_at)).offset(skip).limit(limit).all()
    print(f"Number of results after pagination: {len(results)}")
    
    # Format results
    invoices = []
    for row in results:
        # Format as string UUID for proper serialization
        invoice_id_str = str(row.id)
        
        # Create a proper invoice_number object
        invoice_number_obj = {
            "id": invoice_id_str,
            "formatted_number": row.invoice_number
        }
        
        # Debug: check if linked_quote_id exists
        if hasattr(row, 'linked_quote_id'):
            print(f"Invoice {row.invoice_number} has linked_quote_id: {row.linked_quote_id}")
        else:
            print(f"Invoice {row.invoice_number} does NOT have linked_quote_id attribute")
        
        # Ensure we're matching the expected schema
        invoice_dict = {
            "id": invoice_id_str,
            "invoice_number": invoice_number_obj,
            "invoice_no": row.invoice_number,  # Keep for backward compatibility
            "customer_id": str(row.customer_id),
            "customer_name": row.customer_name,
            "company_id": str(row.company_id),
            "company_name": "Builders Warehouse",  # Default company name
            "store_type_id": row.store_type_id,
            "store_type_name": row.store_type_name or "Unknown",  # Use actual store type name
            "store_type": {
                "id": row.store_type_id or 1,
                "name": row.store_type_name or "Unknown"  # Use store type name, not invoice type
            },
            "status": row.status or "saved",  # Ensure status is properly set
            "po_id": None,  # Add for consistency
            "date": row.invoice_date.date() if row.invoice_date else None,  # Convert datetime to date
            "grand_total": row.grand_total,
            "notes": row.notes
        }
        
        # Explicitly add linked_quote_id if it exists
        if hasattr(row, 'linked_quote_id') and row.linked_quote_id:
            invoice_dict["linked_quote_id"] = row.linked_quote_id
            print(f"Added linked_quote_id {row.linked_quote_id} to invoice {row.invoice_number}")
        
        invoices.append(invoice_dict)
        print(f"Found invoice: {row.invoice_number}, date: {row.invoice_date}")
    
    print(f"Found {len(invoices)} invoices with date range: {start_date} to {end_date}")
    return invoices, total

def get_invoice(db: Session, invoice_id: Union[uuid.UUID, int, str]) -> Optional[Dict[str, Any]]:
    """Get an invoice by ID with details"""
    try:
        # Try to determine the correct type of ID and query accordingly
        if isinstance(invoice_id, str):
            # Check if it looks like a formatted invoice number (starts with INV-)
            if invoice_id.startswith("INV-"):
                print(f"Looking up invoice by formatted number: {invoice_id}")
                id_filter = Invoice.invoice_number == invoice_id
            else:
                try:
                    # Try to convert to UUID first
                    uuid_id = uuid.UUID(invoice_id)
                    print(f"Converted string to UUID: {uuid_id}")
                    id_filter = Invoice.id == uuid_id
                except ValueError:
                    # If not a valid UUID, try as an integer
                    try:
                        int_id = int(invoice_id)
                        print(f"Converted string to int: {int_id}")
                        id_filter = Invoice.id == int_id
                    except ValueError:
                        # Not a valid integer either
                        print(f"Invalid invoice_id format: {invoice_id}")
                        return None
        elif isinstance(invoice_id, uuid.UUID):
            print(f"Using UUID directly: {invoice_id}")
            id_filter = Invoice.id == invoice_id
        elif isinstance(invoice_id, int):
            print(f"Using integer ID directly: {invoice_id}")
            id_filter = Invoice.id == invoice_id
        else:
            print(f"Unsupported invoice_id type: {type(invoice_id)}")
            return None
            
        # Select specific columns directly rather than using joinedload
        # to avoid greenlet spawn errors
        query = db.query(
            Invoice.id,
            Invoice.invoice_number,
            Invoice.company_id,
            Invoice.customer_id,
            Invoice.invoice_date,
            Invoice.due_date,
            Invoice.status,
            Invoice.invoice_type,
            Invoice.grand_total,
            Invoice.total_gst,
            Invoice.notes,
            Invoice.items,
            Invoice.created_at,
            Invoice.updated_at,
            Customer.company_name,
            Customer.email.label("customer_email")
        ).join(
            Customer, Invoice.customer_id == Customer.id
        ).filter(
            id_filter
        )
        
        # Execute the query
        row = query.first()
        
        if row:
            print(f"Found invoice {row.id}: {row.invoice_number}")
            
            # Convert to dictionary
            invoice_dict = {
                "id": row.id,
                "invoice_number": row.invoice_number,
                "customer_id": row.customer_id,
                "company_id": row.company_id,
                "invoice_date": row.invoice_date,
                "due_date": row.due_date,
                "status": row.status,
                "invoice_type": row.invoice_type,
                "grand_total": row.grand_total,
                "total_gst": row.total_gst,
                "notes": row.notes,
                "items": row.items,
                "created_at": row.created_at,
                "updated_at": row.updated_at,
                "customer": {
                    "name": row.company_name,
                    "email": row.customer_email
                }
            }
            
            return invoice_dict
        else:
            print(f"No invoice found for ID: {invoice_id}")
            # Debug: List all existing invoice IDs to help troubleshoot
            existing_ids = debug_list_invoice_ids(db)
            logger.warning(f"Available invoice IDs: {existing_ids}")
            return None
            
    except Exception as e:
        print(f"Error retrieving invoice: {str(e)}")
        return None

def debug_list_invoice_ids(db: Session) -> List[int]:
    """Debug function to list all invoice IDs that exist in the database"""
    try:
        result = db.query(Invoice.id).all()
        ids = [row[0] for row in result]
        logger.info(f"Found {len(ids)} invoices in database with IDs: {ids}")
        return ids
    except Exception as e:
        logger.error(f"Error listing invoice IDs: {str(e)}")
        return []

def update_invoice(
    db: Session, 
    invoice_id: Union[uuid.UUID, int, str], 
    invoice_update: InvoiceUpdate,
    user_id: int
) -> Optional[Dict[str, Any]]:
    """Update an invoice and handle purchase order linking"""
    logger.info(f"Attempting to update invoice with ID: {invoice_id} (type: {type(invoice_id)})")
    
    # First, query for the invoice directly without using get_invoice
    # to get the actual model object we'll update
    try:
        # Construct the query based on invoice_id type
        if isinstance(invoice_id, str):
            # Check if it looks like a formatted invoice number (starts with INV-)
            if invoice_id.startswith("INV-"):
                logger.info(f"Looking up invoice by formatted number: {invoice_id}")
                db_invoice = db.query(Invoice).filter(Invoice.invoice_number == invoice_id).first()
            else:
                try:
                    uuid_id = uuid.UUID(invoice_id)
                    logger.info(f"Converted string to UUID: {uuid_id}")
                    db_invoice = db.query(Invoice).filter(Invoice.id == uuid_id).first()
                except ValueError:
                    try:
                        int_id = int(invoice_id)
                        logger.info(f"Converted string to int: {int_id}")
                        db_invoice = db.query(Invoice).filter(Invoice.id == int_id).first()
                    except ValueError:
                        logger.error(f"Invalid invoice_id format: {invoice_id}")
                        return None
        elif isinstance(invoice_id, uuid.UUID):
            logger.info(f"Using UUID directly: {invoice_id}")
            db_invoice = db.query(Invoice).filter(Invoice.id == invoice_id).first()
        elif isinstance(invoice_id, int):
            logger.info(f"Using integer ID directly: {invoice_id}")
            db_invoice = db.query(Invoice).filter(Invoice.id == invoice_id).first()
        else:
            logger.error(f"Unsupported invoice_id type: {type(invoice_id)}")
            return None
        
        if not db_invoice:
            logger.warning(f"No invoice found for ID: {invoice_id}")
            return None
            
        logger.info(f"Found invoice {db_invoice.id}: {db_invoice.invoice_number}")
        
        # Update invoice fields
        update_data = invoice_update.model_dump(exclude_unset=True)
        logger.info(f"Update data: {update_data}")
        
        # Check if po_id is being updated
        new_po_id = update_data.get('po_id')
        old_po_id = getattr(db_invoice, 'po_id', None)
        po_id_changed = new_po_id is not None and new_po_id != old_po_id
        
        # If po_id is provided in the update, ensure it exists
        if po_id_changed:
            po_exists = db.query(PurchaseOrder).filter(PurchaseOrder.id == new_po_id).first()
            if not po_exists:
                logger.error(f"Purchase order with ID {new_po_id} not found")
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"Purchase order with ID {new_po_id} not found"
                )
        
        # Update each attribute from the update data
        for key, value in update_data.items():
            if hasattr(db_invoice, key):
                logger.debug(f"Updating {key}: {getattr(db_invoice, key)} -> {value}")
                
                # Handle enum conversions for specific fields
                if key == 'mode_of_payment' and value is not None:
                    # Convert string to PaymentMode enum
                    if isinstance(value, str):
                        try:
                            from src.models.invoice import PaymentMode
                            value = PaymentMode(value)
                            logger.debug(f"Converted mode_of_payment string '{update_data[key]}' to enum: {value}")
                        except ValueError as e:
                            logger.error(f"Invalid payment mode value: {update_data[key]}")
                            raise HTTPException(
                                status_code=status.HTTP_400_BAD_REQUEST,
                                detail=f"Invalid payment mode: {update_data[key]}"
                            )
                    elif hasattr(value, 'value'):
                        # It's already an enum object
                        value = PaymentMode(value.value)
                
                elif key == 'sale_type' and value is not None:
                    # Convert string to SaleType enum
                    if isinstance(value, str):
                        try:
                            from src.models.invoice import SaleType
                            value = SaleType(value)
                            logger.debug(f"Converted sale_type string '{update_data[key]}' to enum: {value}")
                        except ValueError as e:
                            logger.error(f"Invalid sale type value: {update_data[key]}")
                            raise HTTPException(
                                status_code=status.HTTP_400_BAD_REQUEST,
                                detail=f"Invalid sale type: {update_data[key]}"
                            )
                    elif hasattr(value, 'value'):
                        # It's already an enum object
                        value = SaleType(value.value)
                
                setattr(db_invoice, key, value)
            else:
                logger.warning(f"Invoice model does not have attribute: {key}")
                
        # Commit the changes
        db.commit()
        db.refresh(db_invoice)
        logger.info(f"Successfully updated invoice {invoice_id}")
        
        # Now use get_invoice to return the updated invoice in dictionary format
        result = get_invoice(db, invoice_id)
        if result:
            logger.info(f"Successfully retrieved updated invoice data")
        else:
            logger.error(f"Failed to retrieve updated invoice data")
        return result
        
    except Exception as e:
        logger.error(f"Error updating invoice {invoice_id}: {str(e)}")
        db.rollback()
        return None 