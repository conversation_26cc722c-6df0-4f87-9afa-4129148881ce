"""
Repository Factory Module
This module provides factory methods for selecting the appropriate repository implementation.
"""
import logging
from typing import Any, Dict, List, Optional, Tuple, Type, Union, cast

from src.config import settings

logger = logging.getLogger(__name__)

class RepositoryFactory:
    """
    Factory for creating repositories
    """
    
    @staticmethod
    def get_customer_repository():
        """
        Get the customer repository implementation
        
        Returns:
            Customer repository implementation
        """
        # Use SQLAlchemy repository
        from src.repository.customer import SQLAlchemyCustomerRepository
        logger.info("Using SQLAlchemy customer repository")
        return SQLAlchemyCustomerRepository 