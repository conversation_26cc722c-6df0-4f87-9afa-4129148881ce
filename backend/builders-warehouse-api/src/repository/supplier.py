from typing import List, Optional, Dict, Any, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import or_, desc, inspect, func
import json
import logging
import uuid

from src.models.supplier import Supplier
from src.schemas.supplier import SupplierCreate, SupplierUpdate, PriceItem, SKU
from src.models.audit_log import AuditLog
from src.models.customer import PriceList

logger = logging.getLogger(__name__)

# Custom JSON encoder to handle UUID serialization
class CustomJSONEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, uuid.UUID):
            return str(obj)
        return super().default(obj)

def create_supplier(db: Session, supplier_data: SupplierCreate, user_id: Optional[int] = None) -> Supplier:
    """
    Create a new supplier
    """
    try:
        # Process price list data if provided
        price_list_json = None
        if hasattr(supplier_data, 'price_list') and supplier_data.price_list:
            # Convert price list items to dictionary format and apply calculations
            price_list_json = []
            sku_id_counter = 1  # Auto-increment ID for SKU objects
            
            for item in supplier_data.price_list:
                # Convert to dict and trigger calculations via model_post_init
                item_dict = item.model_dump()
                
                # Generate SKU object if item has code field (for backward compatibility)
                if 'sku' not in item_dict and 'code' in item_dict:
                    item_dict['sku'] = {'id': sku_id_counter, 'code': item_dict.pop('code')}
                    sku_id_counter += 1
                elif 'sku' in item_dict and isinstance(item_dict['sku'], str):
                    # Handle case where sku is still a string (backward compatibility)
                    item_dict['sku'] = {'id': sku_id_counter, 'code': item_dict['sku']}
                    sku_id_counter += 1
                
                # Create PriceItem instance to trigger calculations
                calculated_item = PriceItem(**item_dict)
                
                # Store the calculated values
                price_list_json.append(calculated_item.model_dump())
        
        # Create supplier object with all fields
        supplier_dict = {
            'name': supplier_data.supplier_name,
            'phone': supplier_data.phone_no,
            'email': supplier_data.email,
            'address': supplier_data.address,
            'price_list': price_list_json
        }
        
        # Create the supplier with the dictionary
        db_supplier = Supplier(**supplier_dict)
        
        db.add(db_supplier)
        db.flush()
        
        # Audit logging
        entity_data = {
            key: getattr(db_supplier, key) 
            for key in db_supplier.__table__.columns.keys()
            if key not in ['created_at', 'updated_at', 'price_list']  # Exclude price_list from audit log
        }
        
        log_entry = AuditLog(
            event_type="CREATE",
            status="success",
            email=None,
            additional_info=json.dumps({
                "entity_type": "suppliers",
                "entity_id": str(db_supplier.id),
                "user_id": str(user_id) if user_id else None,
                "entity_data": entity_data
            }, cls=CustomJSONEncoder)  # Use custom encoder
        )
        
        db.add(log_entry)
        db.commit()
        db.refresh(db_supplier)
        
        # Set virtual properties for frontend compatibility
        db_supplier.supplier_name = db_supplier.name
        db_supplier.phone_no = db_supplier.phone
        
        # Transform the price_list items to ensure they have the fields expected by PriceItem schema
        if db_supplier.price_list:
            # The price_list should already contain all calculated fields from PriceItem
            # But we need to transform old format to new SKU object format
            if isinstance(db_supplier.price_list, list):
                transformed_price_list = []
                for i, item in enumerate(db_supplier.price_list):
                    if isinstance(item, dict):
                        # Create a copy of the item
                        transformed_item = item.copy()
                        
                        # Transform old code format to new sku object format
                        if 'sku' not in transformed_item and 'code' in transformed_item:
                            transformed_item['sku'] = {
                                'id': i + 1,
                                'code': transformed_item.pop('code')
                            }
                        elif 'sku' in transformed_item and isinstance(transformed_item['sku'], str):
                            # Handle case where sku is still a string
                            transformed_item['sku'] = {
                                'id': i + 1,
                                'code': transformed_item['sku']
                            }
                        elif 'sku' not in transformed_item:
                            # If no sku and no code, create empty sku
                            transformed_item['sku'] = {
                                'id': i + 1,
                                'code': ''
                            }
                        
                        transformed_price_list.append(transformed_item)
                
                db_supplier.price_list = transformed_price_list
            else:
                db_supplier.price_list = []
        else:
            db_supplier.price_list = []
        
        logger.info(f"Created supplier: {db_supplier.name}")
        return db_supplier
    except Exception as e:
        logger.error(f"Error creating supplier: {str(e)}")
        db.rollback()
        raise

def get_supplier(db: Session, supplier_id: int) -> Optional[Supplier]:
    """
    Get a supplier by ID
    """
    try:
        logger.info(f"Getting supplier with ID: {supplier_id}")
        
        # First check if the supplier exists
        supplier_exists = db.query(Supplier.id).filter(Supplier.id == supplier_id).first()
        if not supplier_exists:
            logger.warning(f"Supplier with ID {supplier_id} not found")
            return None
            
        # Query the Supplier model directly
        supplier = db.query(Supplier).filter(Supplier.id == supplier_id).first()
        
        # If we got a supplier, make sure the virtual properties are set
        if supplier:
            # Set virtual properties explicitly for frontend compatibility
            supplier.supplier_name = supplier.name
            supplier.phone_no = supplier.phone
            
            # Ensure price_list is initialized
            if supplier.price_list is None:
                supplier.price_list = []
            else:
                # Transform the price_list items to ensure they have the fields expected by PriceItem schema
                # But we need to transform old format to new SKU object format
                if isinstance(supplier.price_list, list):
                    transformed_price_list = []
                    for i, item in enumerate(supplier.price_list):
                        if isinstance(item, dict):
                            # Create a copy of the item
                            transformed_item = item.copy()
                            
                            # Transform old code format to new sku object format
                            if 'sku' not in transformed_item and 'code' in transformed_item:
                                transformed_item['sku'] = {
                                    'id': i + 1,
                                    'code': transformed_item.pop('code')
                                }
                            elif 'sku' in transformed_item and isinstance(transformed_item['sku'], str):
                                # Handle case where sku is still a string
                                transformed_item['sku'] = {
                                    'id': i + 1,
                                    'code': transformed_item['sku']
                                }
                            elif 'sku' not in transformed_item:
                                # If no sku and no code, create empty sku
                                transformed_item['sku'] = {
                                    'id': i + 1,
                                    'code': ''
                                }
                            
                            transformed_price_list.append(transformed_item)
                    
                    supplier.price_list = transformed_price_list
                else:
                    supplier.price_list = []
                
            logger.info(f"Successfully retrieved supplier: {supplier.name}")
        else:
            logger.warning(f"Could not retrieve supplier with ID {supplier_id}")
            
        return supplier
    except Exception as e:
        logger.error(f"Error getting supplier with ID {supplier_id}: {str(e)}")
        # Return None instead of raising the exception to handle the error gracefully
        return None

def get_supplier_by_name(db: Session, supplier_name: str) -> Optional[Supplier]:
    """
    Get a supplier by name
    """
    try:
        # Query only the columns that we know exist in the database
        query = db.query(
            Supplier.id,
            Supplier.name,
            Supplier.phone,
            Supplier.email,
            Supplier.address,
            Supplier.created_at,
            Supplier.updated_at
        ).filter(Supplier.name == supplier_name)
        
        # Execute the query
        row = query.first()
        if not row:
            return None
            
        # Create a Supplier instance
        supplier = Supplier()
        supplier.id = row.id
        supplier.name = row.name
        supplier.phone = row.phone
        supplier.email = row.email
        supplier.address = row.address
        supplier.created_at = row.created_at
        supplier.updated_at = row.updated_at
        
        # Set price_list to None by default
        try:
            supplier.price_list = None
        except:
            pass
            
        return supplier
    except Exception as e:
        logger.error(f"Error getting supplier by name: {str(e)}")
        raise

def get_suppliers(
    db: Session, 
    skip: int = 0, 
    limit: int = 100,
    search: Optional[str] = None,
    is_active: Optional[bool] = None
) -> Tuple[List[Supplier], int]:
    """
    Get all suppliers with optional filtering and pagination
    
    Returns:
        Tuple of (suppliers list, total count)
    """
    try:
        # Check if price_list column exists
        inspector = inspect(db.get_bind())
        columns = [c['name'] for c in inspector.get_columns('Supplier')]
        price_list_exists = 'price_list' in columns
        
        # Log the database inspection
        logger.info(f"Supplier table columns: {columns}")
        logger.info(f"price_list column exists: {price_list_exists}")
        
        if price_list_exists:
            # If price_list column exists, query all columns
            query = db.query(Supplier)
        else:
            # Query only the columns that we know exist in the database
            query = db.query(
                Supplier.id,
                Supplier.name,
                Supplier.phone,
                Supplier.email,
                Supplier.address,
                Supplier.created_at,
                Supplier.updated_at
            )
        
        # Apply search filter
        if search:
            search_term = f"%{search}%"
            query = query.filter(
                or_(
                    Supplier.name.ilike(search_term),
                    Supplier.email.ilike(search_term),
                    Supplier.phone.ilike(search_term)
                )
            )
        
        # Apply active filter
        if is_active is not None:
            query = query.filter(Supplier.is_active == is_active)
        
        # Get the total count for pagination
        try:
            count_query = db.query(func.count()).select_from(query.subquery())
            total_count = count_query.scalar()
        except Exception as e:
            logger.error(f"Error getting total count: {str(e)}")
            # Fall back to len of results if count query fails
            total_count = 0
        
        # Order by name case-insensitive and apply pagination
        # Use func.lower to ensure proper alphabetical ordering regardless of case
        query = query.order_by(func.lower(Supplier.name)).offset(skip).limit(limit)
        
        try:
            # If price_list exists, just return the query results
            if price_list_exists:
                suppliers = query.all()
                
                # Add supplier_name and phone_no fields for frontend compatibility
                for supplier in suppliers:
                    supplier.supplier_name = supplier.name
                    supplier.phone_no = supplier.phone
                    
                    # Ensure price_list items have required fields
                    if hasattr(supplier, 'price_list') and supplier.price_list is not None:
                        if isinstance(supplier.price_list, list):
                            # Transform old format to new SKU object format
                            transformed_price_list = []
                            for i, item in enumerate(supplier.price_list):
                                if isinstance(item, dict):
                                    # Create a copy of the item
                                    transformed_item = item.copy()
                                    
                                    # Transform old code format to new sku object format
                                    if 'sku' not in transformed_item and 'code' in transformed_item:
                                        transformed_item['sku'] = {
                                            'id': i + 1,
                                            'code': transformed_item.pop('code')
                                        }
                                    elif 'sku' in transformed_item and isinstance(transformed_item['sku'], str):
                                        # Handle case where sku is still a string
                                        transformed_item['sku'] = {
                                            'id': i + 1,
                                            'code': transformed_item['sku']
                                        }
                                    elif 'sku' not in transformed_item:
                                        # If no sku and no code, create empty sku
                                        transformed_item['sku'] = {
                                            'id': i + 1,
                                            'code': ''
                                        }
                                    
                                    transformed_price_list.append(transformed_item)
                            
                            supplier.price_list = transformed_price_list
                        else:
                            supplier.price_list = []
                
            else:
                # Execute the query and get results as dictionaries
                result_proxy = query.all()
                
                # Convert result to Supplier objects
                suppliers = []
                for row in result_proxy:
                    # Create a dictionary from the row
                    supplier_dict = {
                        'id': row.id,
                        'name': row.name,
                        'supplier_name': row.name,  # Add supplier_name field
                        'phone': row.phone,
                        'phone_no': row.phone,      # Add phone_no field
                        'email': row.email,
                        'address': row.address,
                        'created_at': row.created_at,
                        'updated_at': row.updated_at,
                        'price_list': []  # Default empty list for price_list
                    }
                    
                    # Create a Supplier instance
                    supplier = Supplier()
                    for key, value in supplier_dict.items():
                        setattr(supplier, key, value)
                    
                    suppliers.append(supplier)
                
            # If we got here without getting total_count, use len of results
            if total_count == 0 and suppliers:
                total_count = len(suppliers)
                
            return suppliers, total_count
            
        except Exception as e:
            logger.error(f"Error processing query results: {str(e)}")
            import traceback
            logger.error(traceback.format_exc())
            # Return empty list and count based on total_count (which might be 0)
            return [], total_count
            
    except Exception as e:
        logger.error(f"Error in get_suppliers: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        # Return empty list and zero count on error
        return [], 0

def update_supplier(
    db: Session, 
    supplier_id: int, 
    supplier_data: SupplierUpdate, 
    user_id: Optional[int] = None
) -> Optional[Supplier]:
    """
    Update an existing supplier
    """
    try:
        db_supplier = get_supplier(db, supplier_id)
        if not db_supplier:
            return None

        # Keep track of old values for audit logging
        old_values = {
            key: getattr(db_supplier, key) 
            for key in db_supplier.__table__.columns.keys()
            if key not in ['created_at', 'updated_at', 'price_list']
        }
        
        # Get update data excluding None values
        update_data = supplier_data.model_dump(exclude_unset=True)
        
        # Process price_list separately if provided
        price_list_json = None
        if 'price_list' in update_data and update_data['price_list'] is not None:
            # Convert to JSON serializable format without triggering validation
            # This prevents conflicts with existing calculated values
            price_list_json = []
            for item in update_data['price_list']:
                if hasattr(item, 'model_dump'):
                    # It's a Pydantic model - use as-is to preserve calculated values
                    item_dict = item.model_dump()
                elif isinstance(item, dict):
                    # It's already a dictionary - use as-is
                    item_dict = item.copy()
                else:
                    # Convert to dict if it's some other type
                    item_dict = dict(item)
                
                # Ensure SKU object format for consistency
                if 'sku' not in item_dict and 'code' in item_dict:
                    item_dict['sku'] = {
                        'id': len(price_list_json) + 1,
                        'code': item_dict.pop('code')
                    }
                elif 'sku' in item_dict and isinstance(item_dict['sku'], str):
                    # Handle case where sku is still a string
                    item_dict['sku'] = {
                        'id': len(price_list_json) + 1,
                        'code': item_dict['sku']
                    }
                elif 'sku' not in item_dict:
                    # If no sku and no code, create empty sku
                    item_dict['sku'] = {
                        'id': len(price_list_json) + 1,
                        'code': ''
                    }
                
                price_list_json.append(item_dict)
            
            update_data.pop('price_list')  # Remove from update_data to handle separately
        
        # Map field names from DTO to DB schema
        field_mapping = {
            'supplier_name': 'name',
            'phone_no': 'phone',
        }
        
        # Apply mapping and remove fields that don't exist in the DB
        mapped_data = {}
        for key, value in update_data.items():
            if key in field_mapping:
                mapped_data[field_mapping[key]] = value
            elif key in ['email', 'address', 'is_active']:
                mapped_data[key] = value
        
        # Track which fields were actually changed
        changed_fields = {}
        
        # Update regular fields
        for key, value in mapped_data.items():
            if hasattr(db_supplier, key) and getattr(db_supplier, key) != value:
                changed_fields[key] = value
                setattr(db_supplier, key, value)
        
        # Update price_list if provided and column exists
        if price_list_json is not None:
            try:
                db_supplier.price_list = price_list_json
                changed_fields['price_list'] = "Updated price list"
            except Exception as e:
                logger.warning(f"Could not update price_list. Error: {str(e)}")
        
        # Only create audit log if something actually changed
        if changed_fields:
            new_values = {k: changed_fields[k] for k in changed_fields.keys() if k != 'price_list'}
            
            log_entry = AuditLog(
                event_type="UPDATE",
                status="success",
                email=None,
                additional_info=json.dumps({
                    "entity_type": "suppliers",
                    "entity_id": str(db_supplier.id),
                    "user_id": str(user_id) if user_id else None,
                    "before": {k: old_values.get(k, None) for k in new_values.keys()},
                    "after": new_values,
                    "price_list_updated": 'price_list' in changed_fields
                }, cls=CustomJSONEncoder)  # Use custom encoder
            )
            
            db.add(log_entry)
            logger.info(f"Updated supplier: {db_supplier.name}")
        
        db.commit()
        db.refresh(db_supplier)
        
        # Apply the same field transformation as in get_supplier to ensure response compatibility
        # Set virtual properties for frontend compatibility
        db_supplier.supplier_name = db_supplier.name
        db_supplier.phone_no = db_supplier.phone
        
        # Transform price_list items to ensure they have the fields expected by PriceItem schema
        if db_supplier.price_list is None:
            db_supplier.price_list = []
        else:
            if isinstance(db_supplier.price_list, list):
                # Transform old format to new SKU object format
                transformed_price_list = []
                for i, item in enumerate(db_supplier.price_list):
                    if isinstance(item, dict):
                        # Create a copy of the item
                        transformed_item = item.copy()
                        
                        # Transform old code format to new sku object format
                        if 'sku' not in transformed_item and 'code' in transformed_item:
                            transformed_item['sku'] = {
                                'id': i + 1,
                                'code': transformed_item.pop('code')
                            }
                        elif 'sku' in transformed_item and isinstance(transformed_item['sku'], str):
                            # Handle case where sku is still a string
                            transformed_item['sku'] = {
                                'id': i + 1,
                                'code': transformed_item['sku']
                            }
                        elif 'sku' not in transformed_item:
                            # If no sku and no code, create empty sku
                            transformed_item['sku'] = {
                                'id': i + 1,
                                'code': ''
                            }
                        
                        transformed_price_list.append(transformed_item)
                
                db_supplier.price_list = transformed_price_list
            else:
                db_supplier.price_list = []
        
        return db_supplier
    except Exception as e:
        db.rollback()
        logger.error(f"Error updating supplier: {str(e)}")
        raise

def delete_supplier(db: Session, supplier_id: int, user_id: Optional[int] = None) -> bool:
    """
    Delete a supplier directly (hard delete since we don't have is_active field)
    """
    db_supplier = get_supplier(db, supplier_id)
    if not db_supplier:
        return False
    
    supplier_name = db_supplier.name  # Store for logging
    
    # Create audit log entry before deleting
    log_entry = AuditLog(
        event_type="DELETE",
        status="success",
        email=None,
        additional_info=json.dumps({
            "entity_type": "suppliers",
            "entity_id": str(supplier_id),
            "user_id": user_id,
            "deleted_supplier": {
                "id": db_supplier.id,
                "name": db_supplier.name,
                "email": db_supplier.email
            }
        }, cls=CustomJSONEncoder)  # Use custom encoder
    )
    
    db.add(log_entry)
    
    # Hard delete the supplier
    db.delete(db_supplier)
    db.commit()
    
    logger.info(f"Deleted supplier: {supplier_name} (ID: {supplier_id})")
    return True 