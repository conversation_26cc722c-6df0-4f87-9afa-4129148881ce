from sqlalchemy.orm import Session, joinedload
from sqlalchemy import desc, asc, and_, or_, func, between, inspect
from datetime import datetime, date, timedelta
from typing import List, Optional, Dict, Any, Tuple, Union
from fastapi import HTTPException, status
import uuid
import logging
import json
import time

from src.models.purchase_order import PurchaseOrder, PurchaseOrderDetailItem
from src.models.supplier import Supplier
from src.models.invoice import Invoice
from src.schemas.purchase_order import PurchaseOrderCreate, PurchaseOrderUpdate, PurchaseOrderDetailUpdate, PurchaseOrderEmailStatusUpdate, PurchaseOrderDetailNotesUpdate

# Custom JSON encoder to handle date objects
class DateTimeEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, (date, datetime)):
            return obj.isoformat()
        return super().default(obj)

# Purchase Order CRUD operations
def create_purchase_order(db: Session, po: PurchaseOrderCreate) -> PurchaseOrder:
    """Create a new purchase order with details"""
    try:
        # Convert item details to JSON format for storage
        items_data = []
        if po.details:
            for i, item in enumerate(po.details):
                # Convert the model to a dict
                item_dict = item.model_dump()
                
                # Add a unique ID for each item
                item_dict['id'] = str(i + 1)  # Simple string ID, 1-based
                
                # Convert date objects to strings
                if item_dict.get('expected_delivery_date') and isinstance(item_dict['expected_delivery_date'], date):
                    item_dict['expected_delivery_date'] = item_dict['expected_delivery_date'].isoformat()
                
                items_data.append(item_dict)
        
        # Create a simple PO number using timestamp to avoid complex queries
        timestamp = int(time.time())
        po_number = f"PO-{timestamp % 100000}"  # Use last 5 digits of timestamp
        
        # Calculate expected delivery date from the item with the latest date
        expected_date = None
        if po.details:
            expected_dates = [item.expected_delivery_date for item in po.details if item.expected_delivery_date]
            if expected_dates:
                expected_date = max(expected_dates)
        
        # Create metadata for fields that don't exist in the database schema
        # Convert all date objects to ISO format strings
        metadata = {
            'supplier_name': po.supplier_name,
            'store_type': po.store_type,
            'invoice_id': po.invoice_id,
            'payment_terms': po.payment_terms,
            'issue_date': po.issue_date.isoformat() if po.issue_date else None
        }
        
        # Create the purchase order with only fields that actually exist in the database
        # Do NOT include ship_to_customer as it doesn't exist in the database schema
        db_po = PurchaseOrder(
            po_number=po_number,
            company_id=1,  # Default company ID
            supplier_id=po.supplier_id,
            order_date=datetime.combine(po.issue_date, datetime.min.time()) if po.issue_date else datetime.utcnow(),
            status=po.status.value if hasattr(po.status, 'value') else po.status,
            total_amount=sum(float(item.total) for item in po.details) if po.details else 0.0
        )
        
        # Set expected_date if available
        if expected_date:
            db_po.expected_date = datetime.combine(expected_date, datetime.min.time())
        
        # Store items and metadata in the JSON field
        # Use our custom encoder to ensure all dates are serialized properly
        db_po.items = json.loads(json.dumps({
            "items": items_data,
            "metadata": metadata
        }, cls=DateTimeEncoder))
        
        # Make sure to NOT set ship_to_customer attribute
        db.add(db_po)
        db.commit()  # Use commit to save the transaction
        db.refresh(db_po)  # Refresh to get updated fields
        return db_po
    except Exception as e:
        logging.error(f"Error creating purchase order: {str(e)}")
        db.rollback()
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error creating purchase order: {str(e)}"
        )

def get_purchase_orders(
    db: Session, 
    skip: int = 0, 
    limit: int = 100,
    supplier_name: Optional[str] = None,
    start_date: Optional[date] = None,
    end_date: Optional[date] = None,
    status: Optional[str] = None,
    include_deleted: bool = False,
    store_type: Optional[str] = None,  # Added store_type filter
    search: Optional[str] = None  # Added search parameter
) -> Tuple[List[PurchaseOrder], int]:
    """Get all purchase orders with optional filtering"""
    try:
        # Query the PurchaseOrder model directly since we've aligned it with the database
        query = db.query(PurchaseOrder)
        
        # Apply filters
        if not include_deleted:
            query = query.filter(PurchaseOrder.status != "cancelled")
        
        if supplier_name:
            # Need to join with Supplier to filter by name
            query = query.join(PurchaseOrder.supplier).filter(
                Supplier.name.ilike(f"%{supplier_name}%")
            )
        
        if start_date and end_date:
            # Add time component to make it inclusive of the full day
            start_datetime = datetime.combine(start_date, datetime.min.time())
            end_datetime = datetime.combine(end_date, datetime.max.time())
            query = query.filter(between(PurchaseOrder.order_date, start_datetime, end_datetime))
        elif start_date:
            start_datetime = datetime.combine(start_date, datetime.min.time())
            query = query.filter(PurchaseOrder.order_date >= start_datetime)
        elif end_date:
            end_datetime = datetime.combine(end_date, datetime.max.time())
            query = query.filter(PurchaseOrder.order_date <= end_datetime)
        
        if status:
            query = query.filter(PurchaseOrder.status == status)
        
        # Add store_type filter
        if store_type:
            query = query.filter(PurchaseOrder.store_type == store_type)
            
        # Add search filter if provided - match against po_number or supplier name
        if search:
            query = query.outerjoin(PurchaseOrder.supplier).filter(
                or_(
                    PurchaseOrder.po_number.ilike(f"%{search}%"),
                    Supplier.name.ilike(f"%{search}%")
                )
            )
            
        # Get total count for pagination
        total = query.count()
        
        # Apply pagination and sorting (newest first)
        results = query.order_by(desc(PurchaseOrder.order_date)).offset(skip).limit(limit).all()
        
        return results, total
    except Exception as e:
        import logging
        logging.error(f"Error fetching purchase orders: {str(e)}")
        return [], 0

def get_purchase_order(db: Session, po_id: int, include_deleted: bool = False) -> Optional[PurchaseOrder]:
    """Get a purchase order by ID with details"""
    query = db.query(PurchaseOrder).filter(PurchaseOrder.id == po_id)
    
    if not include_deleted:
        query = query.filter(PurchaseOrder.status != "cancelled")
    
    return query.first()

def update_purchase_order(db: Session, po_id: int, po_update: PurchaseOrderUpdate) -> Optional[PurchaseOrder]:
    """Update a purchase order"""
    db_po = get_purchase_order(db, po_id)
    if not db_po:
        return None
    
    # Update only the provided fields
    update_data = po_update.model_dump(exclude_unset=True)
    
    # Handle any fields that need special processing
    if 'details' in update_data:
        # Convert details to JSON format
        details_data = []
        for item in update_data['details']:
            if isinstance(item, dict):
                item_dict = item
            else:
                item_dict = item.model_dump()
                
            # Convert date objects to strings
            if item_dict.get('expected_delivery_date') and isinstance(item_dict['expected_delivery_date'], date):
                item_dict['expected_delivery_date'] = item_dict['expected_delivery_date'].isoformat()
                
            details_data.append(item_dict)
        
        # Get existing metadata if any
        metadata = {}
        if db_po.items and isinstance(db_po.items, dict) and 'metadata' in db_po.items:
            metadata = db_po.items['metadata']
        
        # Update the items field with the new details
        db_po.items = json.loads(json.dumps({
            "items": details_data,
            "metadata": metadata
        }, cls=DateTimeEncoder))
        
        # Remove details from the update data as we've processed it
        del update_data['details']
    
    # Special handling for issue_date (convert date to datetime)
    if 'issue_date' in update_data and update_data['issue_date']:
        db_po.order_date = datetime.combine(update_data['issue_date'], datetime.min.time())
        
        # Also update in metadata
        if db_po.items and isinstance(db_po.items, dict):
            if 'metadata' not in db_po.items:
                db_po.items['metadata'] = {}
            db_po.items['metadata']['issue_date'] = update_data['issue_date'].isoformat()
            
            # Ensure proper JSON serialization
            db_po.items = json.loads(json.dumps(db_po.items, cls=DateTimeEncoder))
        
        del update_data['issue_date']
    
    # Handle JSON fields that should be stored in metadata
    for field in ['supplier_name', 'store_type', 'invoice_id', 'payment_terms']:
        if field in update_data:
            if db_po.items and isinstance(db_po.items, dict):
                if 'metadata' not in db_po.items:
                    db_po.items['metadata'] = {}
                db_po.items['metadata'][field] = update_data[field]
                
                # Ensure proper JSON serialization
                db_po.items = json.loads(json.dumps(db_po.items, cls=DateTimeEncoder))
            
            del update_data[field]
    
    # Update status if provided
    if 'status' in update_data:
        status_value = update_data['status']
        if hasattr(status_value, 'value'):
            db_po.status = status_value.value
        else:
            db_po.status = status_value
        del update_data['status']
    
    # Update remaining fields
    for key, value in update_data.items():
        if hasattr(db_po, key):
            setattr(db_po, key, value)
    
    # Recalculate total if needed
    if 'details' in update_data:
        db_po.update_total()
    
    db.commit()
    db.refresh(db_po)
    return db_po

def delete_purchase_order(db: Session, po_id: int) -> bool:
    """Soft delete a purchase order"""
    db_po = get_purchase_order(db, po_id)
    if not db_po:
        return False
    
    db_po.is_deleted = True  # This will set status to "cancelled" via property
    db.commit()
    return True

def update_purchase_order_email_status(db: Session, po_id: int, email_status: PurchaseOrderEmailStatusUpdate) -> Optional[PurchaseOrder]:
    """Update the email status of a purchase order"""
    db_po = get_purchase_order(db, po_id)
    if not db_po:
        return None
    
    # Ensure items is a dict
    if db_po.items is None or not isinstance(db_po.items, dict):
        db_po.items = {}
    
    # Get current items structure
    current_items = db_po.items.get('items', [])
    current_metadata = db_po.items.get('metadata', {})
    
    # Update with email data
    db_po.items = json.loads(json.dumps({
        'items': current_items,
        'metadata': current_metadata,
        'email_sent': email_status.email_sent,
        'email_sent_message': email_status.email_sent_message
    }, cls=DateTimeEncoder))
    
    db.commit()
    db.refresh(db_po)
    return db_po

# Purchase Order Detail operations
def get_purchase_order_detail(db: Session, detail_id: int, po_id: int) -> Optional[Dict[str, Any]]:
    """Get a purchase order detail by ID"""
    # Get the purchase order
    po = get_purchase_order(db, po_id)
    if not po or not po.items or 'items' not in po.items:
        return None
    
    # Find the detail with the matching ID
    for detail in po.items['items']:
        if detail.get('id') == detail_id:
            return detail
    
    return None

def update_purchase_order_detail(db: Session, detail_id: int, detail_update: PurchaseOrderDetailUpdate) -> Optional[Dict[str, Any]]:
    """Update a purchase order detail"""
    # First, get the purchase order containing the detail
    # We need the po_id which should be part of the detail_update
    po_id = detail_update.purchase_order_id
    
    po = get_purchase_order(db, po_id)
    if not po or not po.items or 'items' not in po.items:
        return None
    
    # Find and update the detail
    updated_detail = None
    items_data = po.items['items']
    
    # Convert detail_id to string for comparison with string IDs
    str_detail_id = str(detail_id)
    
    for i, detail in enumerate(items_data):
        # Check both integer and string representations of ID
        if detail.get('id') == detail_id or detail.get('id') == str_detail_id:
            # Update fields
            update_data = detail_update.model_dump(exclude_unset=True)
            
            # Convert date objects to strings
            if 'expected_delivery_date' in update_data and isinstance(update_data['expected_delivery_date'], date):
                update_data['expected_delivery_date'] = update_data['expected_delivery_date'].isoformat()
            
            # Ensure ID is preserved
            update_data['id'] = detail.get('id', str_detail_id)
                
            items_data[i].update(update_data)
            updated_detail = items_data[i]
            break
    
    if updated_detail:
        # Update the items with serialized JSON
        metadata = po.items.get('metadata', {})
        email_sent = po.items.get('email_sent', False)
        email_sent_message = po.items.get('email_sent_message', None)
        
        po.items = json.loads(json.dumps({
            'items': items_data,
            'metadata': metadata,
            'email_sent': email_sent,
            'email_sent_message': email_sent_message
        }, cls=DateTimeEncoder))
        
        # Recalculate totals
        po.update_total()
        db.commit()
        db.refresh(po)
        
        # Return the updated detail
        for detail in po.items['items']:
            if detail.get('id') == str_detail_id:
                return detail
    
    return None

def update_purchase_order_detail_notes(db: Session, detail_id: int, notes_update: PurchaseOrderDetailNotesUpdate) -> Optional[Dict[str, Any]]:
    """Update the notes of a purchase order detail"""
    # We need the po_id to locate the purchase order
    # For simplicity, we can assume it's part of the notes_update or get it from the detail
    po_id = notes_update.purchase_order_id if hasattr(notes_update, 'purchase_order_id') else None
    
    if not po_id:
        return None
    
    po = get_purchase_order(db, po_id)
    if not po or not po.items or 'items' not in po.items:
        return None
    
    # Convert detail_id to string for comparison with string IDs
    str_detail_id = str(detail_id)
    
    # Find and update the detail notes
    updated_detail = None
    items_data = po.items['items']
    for i, detail in enumerate(items_data):
        # Check both integer and string representations of ID
        if detail.get('id') == detail_id or detail.get('id') == str_detail_id:
            items_data[i]['notes'] = notes_update.notes
            # Ensure ID is preserved
            items_data[i]['id'] = detail.get('id', str_detail_id)
            updated_detail = items_data[i]
            break
    
    if updated_detail:
        # Update the items with serialized JSON
        metadata = po.items.get('metadata', {})
        email_sent = po.items.get('email_sent', False)
        email_sent_message = po.items.get('email_sent_message', None)
        
        po.items = json.loads(json.dumps({
            'items': items_data,
            'metadata': metadata,
            'email_sent': email_sent,
            'email_sent_message': email_sent_message
        }, cls=DateTimeEncoder))
        
        db.commit()
        db.refresh(po)
        
        # Return the updated detail
        for detail in po.items['items']:
            if detail.get('id') == str_detail_id:
                return detail
    
    return None 