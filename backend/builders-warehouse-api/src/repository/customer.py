"""
SQLAlchemy repository for Customer model
This module provides methods to interact with the Customer table using SQLAlchemy ORM
"""
from typing import List, Optional, Dict, Any, Tuple
from sqlalchemy import select, func, and_, or_, String
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload
import logging

from src.models.customer import Customer, PriceList
from src.models.invoice import Invoice
from src.models.quote import Quote
from src.models.store_type import StoreType

logger = logging.getLogger(__name__)


class SQLAlchemyCustomerRepository:
    """
    Repository for handling Customer operations using SQLAlchemy ORM
    """
    
    @classmethod
    async def create_customer(cls, db: AsyncSession, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Create a new customer
        
        Args:
            db: Database session
            data: Customer data including company_name, email, etc.
            
        Returns:
            The created customer as a dictionary
        """
        try:
            # Create the customer object
            db_obj = Customer(
                company_name=data.get("company_name"),
                contact_person=data.get("contact_person"),
                email=data.get("email"),
                phone=data.get("phone"),
                is_account=data.get("is_account", False),
                billing_address=data.get("billing_address"),
                billing_suburb=data.get("billing_suburb"),
                billing_postcode=data.get("billing_postcode"),
                price_list_id=data.get("price_list_id")
            )
            
            # Add to session and commit
            db.add(db_obj)
            await db.commit()
            await db.refresh(db_obj)
            
            # Convert to dictionary
            return cls._model_to_dict(db_obj)
        except Exception as e:
            logger.error(f"Error creating customer: {e}")
            await db.rollback()
            raise
    
    @classmethod
    async def get_customer_by_id(cls, db: AsyncSession, customer_id: int) -> Optional[Dict[str, Any]]:
        """
        Get a customer by ID
        
        Args:
            db: Database session
            customer_id: The customer ID
            
        Returns:
            The customer as a dictionary or None if not found
        """
        try:
            # Only load the price_list and not the invoices to avoid missing column errors
            result = await db.execute(
                select(Customer)
                .options(selectinload(Customer.price_list))
                .where(Customer.id == customer_id)
            )
            customer = result.scalars().first()
            
            if not customer:
                return None
                
            return cls._model_to_dict(customer)
        except Exception as e:
            logger.error(f"Error getting customer by ID: {e}")
            raise
    
    @classmethod
    async def get_customer_by_email(cls, db: AsyncSession, email: str) -> Optional[Dict[str, Any]]:
        """
        Get a customer by email
        
        Args:
            db: Database session
            email: The customer email
            
        Returns:
            The customer as a dictionary or None if not found
        """
        try:
            result = await db.execute(
                select(Customer)
                .options(selectinload(Customer.price_list))
                .where(Customer.email == email)
            )
            customer = result.scalars().first()
            
            if not customer:
                return None
                
            return cls._model_to_dict(customer)
        except Exception as e:
            logger.error(f"Error getting customer by email: {e}")
            raise
    
    @classmethod
    async def list_customers(
        cls,
        db: AsyncSession, 
        skip: int = 0, 
        limit: int = 100, 
        search: Optional[str] = None
    ) -> Tuple[List[Dict[str, Any]], int]:
        """
        List customers with optional search and pagination
        
        Args:
            db: Database session
            skip: Number of records to skip
            limit: Maximum number of records to return
            search: Optional search term for company_name, email, etc.
            
        Returns:
            Tuple of (customers list as dictionaries, total count)
        """
        try:
            # Build the base query with price_list loading only
            # Avoid loading invoices to prevent issues with missing columns
            query = select(Customer).options(
                selectinload(Customer.price_list)
                # Removed selectinload(Customer.invoices) to avoid errors with missing po_id column
            )
            
            # Apply search filter if provided
            if search:
                search_filter = or_(
                    Customer.company_name.ilike(f"%{search}%"),
                    Customer.email.ilike(f"%{search}%"),
                    Customer.contact_person.ilike(f"%{search}%"),
                    Customer.phone.ilike(f"%{search}%")
                )
                query = query.where(search_filter)
            
            # Get total count
            count_query = select(func.count()).select_from(query.subquery())
            result = await db.execute(count_query)
            total_count = result.scalar_one()
            
            # Apply pagination and ordering by company_name alphabetically (case-insensitive)
            query = query.order_by(func.lower(Customer.company_name).asc()).offset(skip).limit(limit)
            
            # Execute query
            result = await db.execute(query)
            customers = result.scalars().all()
            
            # Convert to dictionaries
            customer_dicts = [cls._model_to_dict(customer) for customer in customers]
            
            return customer_dicts, total_count
        except Exception as e:
            logger.error(f"Error listing customers: {e}")
            raise
    
    @classmethod
    async def update_customer(cls, db: AsyncSession, customer_id: int, data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Update a customer
        
        Args:
            db: Database session
            customer_id: The customer ID
            data: The data to update
            
        Returns:
            The updated customer as a dictionary
        """
        try:
            # Get the customer
            result = await db.execute(
                select(Customer)
                .options(selectinload(Customer.price_list))
                .where(Customer.id == customer_id)
            )
            customer = result.scalars().first()
            
            if not customer:
                raise ValueError(f"Customer with ID {customer_id} not found")
            
            # Update attributes
            for key, value in data.items():
                if hasattr(customer, key):
                    setattr(customer, key, value)
            
            # Commit changes
            await db.commit()
            await db.refresh(customer)
            
            return cls._model_to_dict(customer)
        except Exception as e:
            logger.error(f"Error updating customer: {e}")
            await db.rollback()
            raise
    
    @classmethod
    async def delete_customer(cls, db: AsyncSession, customer_id: int) -> Dict[str, Any]:
        """
        Delete a customer
        
        Args:
            db: Database session
            customer_id: The customer ID
            
        Returns:
            The deleted customer as a dictionary
        """
        try:
            # Get the customer
            result = await db.execute(
                select(Customer)
                .options(selectinload(Customer.price_list))
                .where(Customer.id == customer_id)
            )
            customer = result.scalars().first()
            
            if not customer:
                raise ValueError(f"Customer with ID {customer_id} not found")
            
            # Store customer data before deletion
            customer_dict = cls._model_to_dict(customer)
            
            # Delete the customer
            await db.delete(customer)
            await db.commit()
            
            return customer_dict
        except Exception as e:
            logger.error(f"Error deleting customer: {e}")
            await db.rollback()
            raise
    
    @classmethod
    async def get_customer_invoice_summary(cls, db: AsyncSession, customer_id: int) -> Dict[str, Any]:
        """
        Get a summary of invoices for a customer
        
        Args:
            db: Database session
            customer_id: The customer ID
            
        Returns:
            Summary data including count, total amount, etc.
        """
        try:
            # Log the customer ID being queried
            logger.info(f"Getting invoice summary for customer ID: {customer_id}")
            
            # Count total invoices using a direct query
            count_query = select(func.count()).select_from(Invoice).where(Invoice.customer_id == customer_id)
            result = await db.execute(count_query)
            invoice_count = result.scalar_one()
            
            logger.info(f"Found {invoice_count} invoices for customer {customer_id}")
            
            # Get the latest 5 invoices using a direct query with specific columns
            # to avoid triggering lazy loading of relationships
            invoice_query = select(
                Invoice.id,
                Invoice.invoice_number,
                Invoice.grand_total,
                Invoice.invoice_date,
                Invoice.status,
                Invoice.created_at
            ).where(
                Invoice.customer_id == customer_id
            ).order_by(
                Invoice.created_at.desc()
            ).limit(5)
            
            result = await db.execute(invoice_query)
            invoice_rows = result.all()
            
            # Create dicts manually from the query results
            recent_invoices = []
            total_amount = 0
            
            for row in invoice_rows:
                invoice_dict = {
                    "id": row.id,
                    "invoice_number": row.invoice_number,
                    "grand_total": row.grand_total,
                    "date": row.invoice_date,
                    "status": row.status,
                    "created_at": row.created_at
                }
                recent_invoices.append(invoice_dict)
                
                # Sum up the total amount
                if row.grand_total:
                    total_amount += float(row.grand_total)
            
            return {
                "invoice_count": invoice_count,
                "total_amount": total_amount,
                "recent_invoices": recent_invoices
            }
        except Exception as e:
            logger.error(f"Error getting customer invoice summary for ID {customer_id}: {str(e)}")
            # Return empty data instead of raising an exception
            return {
                "invoice_count": 0,
                "total_amount": 0,
                "recent_invoices": []
            }
    
    @classmethod
    async def list_customer_invoices(
        cls,
        db: AsyncSession,
        customer_id: int,
        params: Dict[str, Any]
    ) -> Tuple[List[Dict[str, Any]], int]:
        """
        List invoices and quotes for a customer
        
        Args:
            db: Database session
            customer_id: The customer ID
            params: Filtering and pagination parameters
            
        Returns:
            Tuple of (list of invoices and quotes, total count)
        """
        try:
            # Extract parameters
            skip = params.skip if hasattr(params, 'skip') else 0
            limit = params.limit if hasattr(params, 'limit') else 10
            
            # Query invoices
            invoice_query = select(
                Invoice.id,
                Invoice.invoice_number,
                Invoice.customer_id,
                Invoice.company_id,
                Invoice.invoice_date,
                Invoice.due_date,
                Invoice.status,
                Invoice.invoice_type,
                Invoice.grand_total,
                Invoice.total_gst,
                Invoice.notes,
                Invoice.created_at,
                Invoice.updated_at,
                Invoice.store_type_name
            ).where(Invoice.customer_id == customer_id)
            
            # Apply date filters for invoices
            if hasattr(params, 'start_date') and params.start_date:
                invoice_query = invoice_query.where(Invoice.invoice_date >= params.start_date)
            if hasattr(params, 'end_date') and params.end_date:
                invoice_query = invoice_query.where(Invoice.invoice_date <= params.end_date)
            if hasattr(params, 'invoice_type') and params.invoice_type:
                invoice_query = invoice_query.where(Invoice.store_type_name == params.invoice_type)
            
            # Query quotes
            quote_query = select(
                Quote.id,
                Quote.quote_number,
                Quote.customer_id,
                Quote.company_id,
                Quote.quote_date,
                Quote.valid_until,
                Quote.status,
                Quote.grand_total,
                Quote.total_gst,
                Quote.notes,
                Quote.created_at,
                Quote.updated_at,
                Quote.store_type
            ).where(Quote.customer_id == customer_id)
            
            # Apply date filters for quotes
            if hasattr(params, 'start_date') and params.start_date:
                quote_query = quote_query.where(Quote.quote_date >= params.start_date)
            if hasattr(params, 'end_date') and params.end_date:
                quote_query = quote_query.where(Quote.quote_date <= params.end_date)
            if hasattr(params, 'invoice_type') and params.invoice_type:
                quote_query = quote_query.where(Quote.store_type == params.invoice_type)
            
            # Get total counts
            invoice_count_query = select(func.count()).select_from(invoice_query.subquery())
            quote_count_query = select(func.count()).select_from(quote_query.subquery())
            
            invoice_count_result = await db.execute(invoice_count_query)
            quote_count_result = await db.execute(quote_count_query)
            
            invoice_count = invoice_count_result.scalar_one()
            quote_count = quote_count_result.scalar_one()
            total_count = invoice_count + quote_count
            
            # Execute queries to get actual data
            invoice_result = await db.execute(invoice_query.order_by(Invoice.created_at.desc()))
            quote_result = await db.execute(quote_query.order_by(Quote.created_at.desc()))
            
            invoice_rows = invoice_result.all()
            quote_rows = quote_result.all()
            
            # Combine and transform results
            combined_results = []
            
            # Helper function to get store type name by ID
            async def get_store_type_name(store_type_value):
                if not store_type_value:
                    return ''
                
                # If it's already a name (string that's not a number), return it
                if isinstance(store_type_value, str) and not store_type_value.isdigit():
                    return store_type_value
                
                # If it's a number or numeric string, look up the name
                try:
                    store_type_id = int(store_type_value)
                    store_type_query = select(StoreType.name).where(StoreType.id == store_type_id)
                    store_type_result = await db.execute(store_type_query)
                    store_type_name = store_type_result.scalar_one_or_none()
                    return store_type_name or store_type_value
                except (ValueError, TypeError):
                    return store_type_value
            
            # Add invoices
            for row in invoice_rows:
                store_type_name = await get_store_type_name(row.store_type_name)
                combined_results.append({
                    "id": row.id,
                    "invoice_number": row.invoice_number,
                    "customer_id": row.customer_id,
                    "company_id": row.company_id,
                    "invoice_date": row.invoice_date,
                    "due_date": row.due_date,
                    "status": row.status,
                    "invoice_type": row.invoice_type,
                    "grand_total": row.grand_total,
                    "total_gst": row.total_gst,
                    "notes": row.notes,
                    "created_at": row.created_at,
                    "updated_at": row.updated_at,
                    "store_type_name": store_type_name,
                    "type": "invoice"
                })
            
            # Add quotes
            for row in quote_rows:
                store_type_name = await get_store_type_name(row.store_type)
                combined_results.append({
                    "id": row.id,
                    "invoice_number": row.quote_number,  # Use quote_number as invoice_number for consistency
                    "customer_id": row.customer_id,
                    "company_id": row.company_id,
                    "invoice_date": row.quote_date,  # Use quote_date as invoice_date for consistency
                    "due_date": row.valid_until,
                    "status": row.status,
                    "invoice_type": "quote",
                    "grand_total": row.grand_total,
                    "total_gst": row.total_gst,
                    "notes": row.notes,
                    "created_at": row.created_at,
                    "updated_at": row.updated_at,
                    "store_type_name": store_type_name,
                    "type": "quote"
                })
            
            # Sort combined results by created_at date (most recent first)
            combined_results.sort(key=lambda x: x["created_at"], reverse=True)
            
            # Apply pagination to combined results
            start_index = skip
            end_index = start_index + limit
            paginated_results = combined_results[start_index:end_index]
            
            return paginated_results, total_count
        except Exception as e:
            logger.error(f"Error listing customer invoices and quotes for customer ID {customer_id}: {str(e)}")
            return [], 0
    
    @staticmethod
    def _model_to_dict(model: Any) -> Dict[str, Any]:
        """
        Convert a SQLAlchemy model instance to a dictionary
        
        Args:
            model: SQLAlchemy model instance
            
        Returns:
            Dictionary representation of the model
        """
        if not model:
            return {}
            
        result = {}
        for column in model.__table__.columns:
            result[column.name] = getattr(model, column.name)
            
        # Handle relationships - only include price_list but not invoices
        if hasattr(model, "price_list") and model.price_list:
            result["price_list"] = SQLAlchemyCustomerRepository._model_to_dict(model.price_list)
            
        # We don't include invoices at all to avoid greenlet_spawn errors in async context
        # by avoiding triggering lazy loading
            
        return result 