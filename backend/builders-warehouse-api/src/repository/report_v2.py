from sqlalchemy import desc, func, or_
from sqlalchemy.orm import Session, joinedload
from typing import List, Tuple, Optional, Dict, Any
import csv
import io
from datetime import datetime

from src.models.invoice import Invoice
from src.models.purchase_order import PurchaseOrder
from src.models.customer import Customer
from src.models.supplier import Supplier

def get_last_30_sales(db: Session) -> Tuple[List[Dict[str, Any]], int]:
    """
    Get the last 30 sales (invoices) ordered by date descending.
    Returns the sales data and total count.
    """
    # Get total count of all invoices
    total_count = db.query(func.count(Invoice.id)).scalar()
    
    # Get the last 30 invoices
    invoices = (
        db.query(Invoice)
        .options(joinedload(Invoice.customer))
        .order_by(desc(Invoice.invoice_date))
        .limit(30)
        .all()
    )
    
    # Transform to output format
    result = []
    for invoice in invoices:
        customer_name = None
        if invoice.customer:
            customer_name = invoice.customer.company_name
        elif hasattr(invoice, 'company_name'):
            customer_name = invoice.company_name
            
        # Determine the payment mode
        payment_mode = "unknown"
        if hasattr(invoice, 'mode_of_payment'):
            payment_mode = invoice.mode_of_payment
            
        result.append({
            "date": invoice.invoice_date.date() if invoice.invoice_date else None,
            "invoice_number": invoice.invoice_number,
            "company_name": customer_name or "Unknown Customer",
            "total_amount": float(invoice.grand_total) if invoice.grand_total else 0.0,
            "mode_of_payment": payment_mode
        })
    
    return result, total_count

def get_last_30_pos(db: Session) -> Tuple[List[Dict[str, Any]], int]:
    """
    Get the last 30 purchase orders ordered by date descending.
    Returns the PO data and total count.
    """
    # Get total count of all POs
    total_count = db.query(func.count(PurchaseOrder.id)).scalar()
    
    # Get the last 30 POs with supplier and invoice relationships
    purchase_orders = (
        db.query(PurchaseOrder)
        .options(joinedload(PurchaseOrder.supplier))
        .order_by(desc(PurchaseOrder.order_date))
        .limit(30)
        .all()
    )
    
    # Transform to output format
    result = []
    for po in purchase_orders:
        supplier_name = None
        if po.supplier:
            supplier_name = po.supplier.name
        elif hasattr(po, 'supplier_name') and po.supplier_name:
            supplier_name = po.supplier_name
        
        # Find linked invoice by looking for invoices that reference this PO
        linked_invoice_number = None
        linked_invoice = db.query(Invoice).filter(Invoice.po_id == po.id).first()
        if linked_invoice:
            linked_invoice_number = linked_invoice.invoice_number
            
        result.append({
            "date": po.order_date.date() if po.order_date else None,
            "po_number": po.po_number or f"PO-{po.id}",
            "supplier_name": supplier_name or "Unknown Supplier",
            "linked_invoice_number": linked_invoice_number,
            "total_amount": float(po.total_amount) if po.total_amount else 0.0
        })
    
    return result, total_count

def get_paginated_sales(
    db: Session, 
    page: int = 1, 
    limit: int = 30,
    search: Optional[str] = None
) -> Tuple[List[Dict[str, Any]], int]:
    """
    Get paginated sales (invoices) ordered by date descending.
    Returns the sales data and total count.
    """
    # Start with a base query
    query = db.query(Invoice).options(joinedload(Invoice.customer))
    
    # Apply search filter if provided
    if search:
        search_term = f"%{search}%"
        query = query.join(Invoice.customer).filter(
            or_(
                Invoice.invoice_number.ilike(search_term),
                Customer.company_name.ilike(search_term)
            )
        )
    
    # Get total count based on current filters
    total_count = query.count()
    
    # Calculate offset
    offset = (page - 1) * limit
    
    # Apply pagination and ordering
    invoices = (
        query
        .order_by(desc(Invoice.invoice_date))
        .offset(offset)
        .limit(limit)
        .all()
    )
    
    # Transform to output format
    result = []
    for invoice in invoices:
        customer_name = None
        if invoice.customer:
            customer_name = invoice.customer.company_name
        elif hasattr(invoice, 'company_name'):
            customer_name = invoice.company_name
            
        # Determine the payment mode
        payment_mode = "unknown"
        if hasattr(invoice, 'mode_of_payment'):
            payment_mode = invoice.mode_of_payment
            
        result.append({
            "date": invoice.invoice_date.date() if invoice.invoice_date else None,
            "invoice_number": invoice.invoice_number,
            "company_name": customer_name or "Unknown Customer",
            "total_amount": float(invoice.grand_total) if invoice.grand_total else 0.0,
            "mode_of_payment": payment_mode
        })
    
    return result, total_count

def get_paginated_pos(
    db: Session, 
    page: int = 1, 
    limit: int = 30,
    search: Optional[str] = None
) -> Tuple[List[Dict[str, Any]], int]:
    """
    Get paginated purchase orders ordered by date descending.
    Returns the PO data and total count.
    """
    # Start with a base query
    query = db.query(PurchaseOrder).options(joinedload(PurchaseOrder.supplier))
    
    # Apply search filter if provided
    if search:
        search_term = f"%{search}%"
        query = query.join(PurchaseOrder.supplier).filter(
            or_(
                PurchaseOrder.po_number.ilike(search_term),
                Supplier.name.ilike(search_term)
            )
        )
    
    # Get total count based on current filters
    total_count = query.count()
    
    # Calculate offset
    offset = (page - 1) * limit
    
    # Apply pagination and ordering
    purchase_orders = (
        query
        .order_by(desc(PurchaseOrder.order_date))
        .offset(offset)
        .limit(limit)
        .all()
    )
    
    # Transform to output format
    result = []
    for po in purchase_orders:
        supplier_name = None
        if po.supplier:
            supplier_name = po.supplier.name
        elif hasattr(po, 'supplier_name') and po.supplier_name:
            supplier_name = po.supplier_name
        
        # Find linked invoice by looking for invoices that reference this PO
        linked_invoice_number = None
        linked_invoice = db.query(Invoice).filter(Invoice.po_id == po.id).first()
        if linked_invoice:
            linked_invoice_number = linked_invoice.invoice_number
            
        result.append({
            "date": po.order_date.date() if po.order_date else None,
            "po_number": po.po_number or f"PO-{po.id}",
            "supplier_name": supplier_name or "Unknown Supplier",
            "linked_invoice_number": linked_invoice_number,
            "total_amount": float(po.total_amount) if po.total_amount else 0.0
        })
    
    return result, total_count

def generate_sales_csv(
    db: Session, 
    page: int = 1,
    limit: int = 30,
    search: Optional[str] = None
) -> str:
    """Generate CSV data for sales reports with pagination"""
    sales, _ = get_paginated_sales(db, page, limit, search)
    
    # Create StringIO to hold CSV data
    output = io.StringIO()
    writer = csv.writer(output)
    
    # Write header
    writer.writerow(['Date', 'Invoice Number', 'Customer', 'Total Amount (AUD)', 'Payment Method'])
    
    # Write data rows
    for sale in sales:
        writer.writerow([
            sale['date'].strftime('%d/%m/%Y') if sale['date'] else '',
            sale['invoice_number'],
            sale['company_name'],
            f"{sale['total_amount']:.2f}",
            sale['mode_of_payment']
        ])
    
    return output.getvalue()

def generate_pos_csv(
    db: Session, 
    page: int = 1,
    limit: int = 30,
    search: Optional[str] = None
) -> str:
    """Generate CSV data for purchase order reports with pagination"""
    pos, _ = get_paginated_pos(db, page, limit, search)
    
    # Create StringIO to hold CSV data
    output = io.StringIO()
    writer = csv.writer(output)
    
    # Write header
    writer.writerow(['Date', 'PO Number', 'Supplier', 'Linked Invoice Number', 'Total Amount (AUD)'])
    
    # Write data rows
    for po in pos:
        writer.writerow([
            po['date'].strftime('%d/%m/%Y') if po['date'] else '',
            po['po_number'],
            po['supplier_name'],
            po['linked_invoice_number'] or '',
            f"{po['total_amount']:.2f}"
        ])
    
    return output.getvalue() 