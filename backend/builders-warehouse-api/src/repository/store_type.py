from sqlalchemy.orm import Session
from sqlalchemy import text
from typing import Optional, List, Dict, Any
import os
import psycopg2
from psycopg2.extras import RealDictCursor
import logging

from src.models.store_type import StoreType
from src.schemas.store_type import StoreTypeCreate, StoreTypeResponse

# Set up logging
logger = logging.getLogger(__name__)

def get_store_types(db: Session, skip: int = 0, limit: int = 100) -> List[StoreType]:
    """Get all store types"""
    return db.query(StoreType).offset(skip).limit(limit).all()

def get_store_type(db: Session, store_type_id: int) -> Optional[StoreType]:
    """Get a store type by ID"""
    return db.query(StoreType).filter(StoreType.id == store_type_id).first()

def get_store_type_by_name(db: Session, name: str) -> Optional[StoreType]:
    """
    Get a store type by name (case insensitive)
    
    Due to issues with SQLAlchemy and missing columns, this uses direct SQL
    to retrieve the store type and creates a StoreType-like object
    """
    try:
        # Use raw SQL query instead of SQLAlchemy
        query = text("""
            SELECT id, name, description, is_active, created_at, updated_at
            FROM "StoreType"
            WHERE name = :name
            LIMIT 1
        """)
        
        # Execute the query
        result = db.execute(query, {"name": name})
        row = result.fetchone()
        
        if row:
            # Create a StoreType object with the fetched data
            store_type = StoreType(
                id=row[0],
                name=row[1],
                description=row[2],
                is_active=row[3],
                created_at=row[4],
                updated_at=row[5]
            )
            return store_type
            
        # Try with case-insensitive search
        query = text("""
            SELECT id, name, description, is_active, created_at, updated_at
            FROM "StoreType"
            WHERE LOWER(name) = LOWER(:name)
            LIMIT 1
        """)
        
        result = db.execute(query, {"name": name})
        row = result.fetchone()
        
        if row:
            # Create a StoreType object with the fetched data
            store_type = StoreType(
                id=row[0],
                name=row[1],
                description=row[2],
                is_active=row[3],
                created_at=row[4],
                updated_at=row[5]
            )
            return store_type
            
        return None
    except Exception as e:
        logger.error(f"Error in get_store_type_by_name: {e}")
        # If SQLAlchemy fails, fall back to direct database connection
        return get_store_type_by_name_direct(db, name)

def get_store_type_by_name_direct(db: Session, name: str) -> Optional[Any]:
    """Fallback method using direct psycopg2 connection"""
    # Get connection parameters from environment or use defaults
    DB_USER = os.environ.get("DB_USER", "warehouse_user")
    DB_PASSWORD = os.environ.get("DB_PASSWORD", "Password#123")
    DB_HOST = os.environ.get("DB_HOST", "localhost")
    DB_PORT = os.environ.get("DB_PORT", "5432")
    DB_NAME = os.environ.get("DB_NAME", "builders_warehouse")
    
    conn = None
    try:
        # Connect to database
        conn = psycopg2.connect(
            user=DB_USER,
            password=DB_PASSWORD,
            host=DB_HOST,
            port=DB_PORT,
            database=DB_NAME,
            cursor_factory=RealDictCursor
        )
        
        # Create a cursor
        cursor = conn.cursor()
        
        # Execute query to find store type by name
        sql = """
        SELECT 
            id, name, description, is_active, created_at, updated_at
        FROM 
            "StoreType"
        WHERE 
            name = %s
        LIMIT 1
        """
        cursor.execute(sql, (name,))
        
        # Fetch result
        result = cursor.fetchone()
        
        if result:
            # Create a simple object that mimics a SQLAlchemy model
            class StoreTypeProxy:
                def __init__(self, **kwargs):
                    for key, value in kwargs.items():
                        setattr(self, key, value)
            
            # Return a StoreType object with the fetched data
            return StoreTypeProxy(**result)
        
        # Try with case-insensitive match
        sql = """
        SELECT 
            id, name, description, is_active, created_at, updated_at
        FROM 
            "StoreType"
        WHERE 
            LOWER(name) = LOWER(%s)
        LIMIT 1
        """
        cursor.execute(sql, (name,))
        result = cursor.fetchone()
        
        if result:
            # Create a simple object that mimics a SQLAlchemy model
            class StoreTypeProxy:
                def __init__(self, **kwargs):
                    for key, value in kwargs.items():
                        setattr(self, key, value)
            
            # Return a StoreType object with the fetched data
            return StoreTypeProxy(**result)
            
        return None
        
    except Exception as e:
        logger.error(f"Error in get_store_type_by_name_direct: {e}")
        return None
    finally:
        if conn:
            conn.close()

def create_store_type(db: Session, store_type: StoreTypeCreate) -> StoreType:
    """Create a new store type"""
    db_store_type = StoreType(
        name=store_type.name,
        description=store_type.description
    )
    db.add(db_store_type)
    db.commit()
    db.refresh(db_store_type)
    return db_store_type

def update_store_type(db: Session, store_type_id: int, store_type_data: StoreTypeCreate) -> Optional[StoreType]:
    """Update a store type"""
    db_store_type = get_store_type(db, store_type_id)
    if db_store_type:
        db_store_type.name = store_type_data.name
        db_store_type.description = store_type_data.description
        db.commit()
        db.refresh(db_store_type)
    return db_store_type

def delete_store_type(db: Session, store_type_id: int) -> bool:
    """Delete a store type"""
    db_store_type = get_store_type(db, store_type_id)
    if db_store_type:
        db.delete(db_store_type)
        db.commit()
        return True
    return False 