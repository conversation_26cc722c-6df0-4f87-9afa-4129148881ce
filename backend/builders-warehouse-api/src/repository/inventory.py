from typing import List, Optional, Dict, Any, Tuple
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import desc, or_, and_, join, func
import json
import logging
import uuid

from src.models.inventory import Inventory
from src.models.supplier import Supplier
from src.schemas.inventory import InventoryCreate, InventoryUpdate
from src.models.audit_log import AuditLog

logger = logging.getLogger(__name__)

def create_inventory(db: Session, inventory: InventoryCreate, user_id: Optional[uuid.UUID] = None) -> Inventory:
    """
    Create a new inventory item
    """
    # Verify supplier exists
    if inventory.supplier_id:
        supplier = db.query(Supplier).filter(Supplier.id == inventory.supplier_id).first()
        if not supplier:
            raise ValueError(f"Supplier with ID {inventory.supplier_id} not found")

    db_inventory = Inventory(
        sku_code=inventory.sku_code,
        style_code=inventory.style_code,
        supplier_id=inventory.supplier_id,
        carton=inventory.carton,
        units_per_carton=inventory.units_per_carton,
        carton_dimensions=inventory.carton_dimensions,
        weight_per_unit=inventory.weight_per_unit,
        weight_per_carton=inventory.weight_per_carton,
        units_per_pallet=inventory.units_per_pallet,
        pallet_weight=inventory.pallet_weight,
        created_by=user_id,
        updated_by=user_id
    )
    
    db.add(db_inventory)
    db.flush()
    
    # Generate a deterministic UUID from inventory ID for audit log
    inventory_uuid = uuid.uuid5(uuid.NAMESPACE_OID, f"inventory:{db_inventory.id}")
    
    # Audit logging
    log_entry = AuditLog(
        table_name="inventory",
        operation="CREATE",
        entity_id=inventory_uuid,
        user_id=user_id,
        event_type="CREATE",
        status="success",
        additional_info=json.dumps({"new": {
            key: getattr(db_inventory, key) for key in db_inventory.__table__.columns.keys()
            if key not in ['created_at', 'updated_at', 'created_by', 'updated_by']
        }})
    )
    
    db.add(log_entry)
    db.commit()
    db.refresh(db_inventory)
    
    logger.info(f"Created inventory: {db_inventory.sku_code}")
    return db_inventory

def get_inventory(db: Session, inventory_id: int) -> Optional[Inventory]:
    """
    Get an inventory item by ID
    """
    return db.query(Inventory).filter(Inventory.id == inventory_id).first()

def get_inventory_by_sku(db: Session, sku_code: str) -> Optional[Inventory]:
    """
    Get an inventory item by SKU code
    """
    return db.query(Inventory).filter(Inventory.sku_code == sku_code).first()

def get_inventories(
    db: Session, 
    skip: int = 0, 
    limit: int = 100,
    supplier_id: Optional[int] = None,
    sku_code: Optional[str] = None,
    style_code: Optional[str] = None,
    search: Optional[str] = None
) -> List[Inventory]:
    """
    Get multiple inventory items with optional filtering
    
    Note: Due to UUID vs Integer type conflict, we're disabling relationship loading
    for creator and updater users to avoid SQL errors.
    """
    # Import inside function to avoid circular imports
    from sqlalchemy.orm import noload
    
    # Use columns from inventory table and join with supplier for searching
    query = db.query(Inventory)
    
    # Explicitly disable relationship loading to avoid UUID vs Integer type mismatch
    query = query.options(
        noload(Inventory.creator),
        noload(Inventory.updater)
    )
    
    # Filter by supplier_id if provided
    if supplier_id:
        query = query.filter(Inventory.supplier_id == supplier_id)
    
    if sku_code:
        query = query.filter(Inventory.sku_code.ilike(f"%{sku_code}%"))
    
    if style_code:
        query = query.filter(Inventory.style_code.ilike(f"%{style_code}%"))
    
    # Add search functionality across relevant fields
    if search:
        # Join with Supplier table for searching by supplier name
        query = query.join(Supplier, Inventory.supplier_id == Supplier.id, isouter=True)
        
        search_filter = or_(
            Inventory.style_code.ilike(f"%{search}%"),
            Inventory.sku_code.ilike(f"%{search}%"),
            Supplier.name.ilike(f"%{search}%"),
            Inventory.description.ilike(f"%{search}%") if hasattr(Inventory, 'description') else False
        )
        query = query.filter(search_filter)
    
    # Get results with alphabetical ordering by sku_code
    # Using a case-insensitive sort to ensure proper alphabetical ordering
    result = query.order_by(func.lower(Inventory.sku_code)).offset(skip).limit(limit).all()
    return result

def count_inventories(
    db: Session,
    supplier_id: Optional[int] = None,
    sku_code: Optional[str] = None,
    style_code: Optional[str] = None,
    search: Optional[str] = None
) -> int:
    """
    Count inventory items with optional filtering
    """
    from sqlalchemy import or_
    
    query = db.query(Inventory)
    
    # Filter by supplier_id if provided
    if supplier_id:
        query = query.filter(Inventory.supplier_id == supplier_id)
    
    if sku_code:
        query = query.filter(Inventory.sku_code.ilike(f"%{sku_code}%"))
    
    if style_code:
        query = query.filter(Inventory.style_code.ilike(f"%{style_code}%"))
    
    # Add search functionality across relevant fields
    if search:
        # Join with Supplier table for searching by supplier name
        query = query.join(Supplier, Inventory.supplier_id == Supplier.id, isouter=True)
        
        search_filter = or_(
            Inventory.style_code.ilike(f"%{search}%"),
            Inventory.sku_code.ilike(f"%{search}%"),
            Supplier.name.ilike(f"%{search}%"),
            Inventory.description.ilike(f"%{search}%") if hasattr(Inventory, 'description') else False
        )
        query = query.filter(search_filter)
    
    return query.count()

def update_inventory(
    db: Session, 
    inventory_id: int, 
    inventory_update: InventoryUpdate, 
    user_id: Optional[uuid.UUID] = None
) -> Optional[Inventory]:
    """
    Update an existing inventory item
    """
    db_inventory = get_inventory(db, inventory_id)
    if not db_inventory:
        return None

    # Keep track of old values for audit logging
    old_values = {key: getattr(db_inventory, key) for key in db_inventory.__table__.columns.keys()
                 if key not in ['created_at', 'updated_at', 'created_by', 'updated_by']}
    
    # Get update data excluding None values
    update_data = inventory_update.dict(exclude_unset=True)
    
    # Track which fields were actually changed
    changed_fields = {}
    
    # Verify supplier exists if changing supplier_id
    if 'supplier_id' in update_data and update_data['supplier_id'] != getattr(db_inventory, 'supplier_id'):
        supplier = db.query(Supplier).filter(Supplier.id == update_data['supplier_id']).first()
        if not supplier:
            raise ValueError(f"Supplier with ID {update_data['supplier_id']} not found")
    
    for key, value in update_data.items():
        if hasattr(db_inventory, key) and getattr(db_inventory, key) != value:
            changed_fields[key] = value
            setattr(db_inventory, key, value)
    
    # Set updated_by
    if user_id:
        db_inventory.updated_by = user_id
    
    # Only create audit log if something actually changed
    if changed_fields:
        # Generate a deterministic UUID from inventory ID for audit log
        inventory_uuid = uuid.uuid5(uuid.NAMESPACE_OID, f"inventory:{db_inventory.id}")
        
        log_entry = AuditLog(
            table_name="inventory",
            operation="UPDATE",
            entity_id=inventory_uuid,
            user_id=user_id,
            event_type="UPDATE",
            status="success",
            additional_info=json.dumps({
                "old": {k: old_values[k] for k in changed_fields.keys()},
                "new": changed_fields
            })
        )
        
        db.add(log_entry)
    
    db.commit()
    db.refresh(db_inventory)
    return db_inventory

def delete_inventory(db: Session, inventory_id: int, user_id: Optional[uuid.UUID] = None) -> bool:
    """
    Delete an inventory item
    """
    db_inventory = get_inventory(db, inventory_id)
    if not db_inventory:
        return False
    
    # Capture inventory data for audit log
    inventory_data = {key: getattr(db_inventory, key) for key in db_inventory.__table__.columns.keys()
                     if key not in ['created_at', 'updated_at', 'created_by', 'updated_by']}
    
    db.delete(db_inventory)
    
    # Generate a deterministic UUID from inventory ID for audit log
    inventory_uuid = uuid.uuid5(uuid.NAMESPACE_OID, f"inventory:{inventory_id}")
    
    # Audit logging
    log_entry = AuditLog(
        table_name="inventory",
        operation="DELETE",
        entity_id=inventory_uuid,
        user_id=user_id,
        event_type="DELETE",
        status="success",
        additional_info=json.dumps({"deleted": inventory_data})
    )
    
    db.add(log_entry)
    db.commit()
    
    logger.info(f"Deleted inventory: {inventory_id}")
    return True

def bulk_create_inventories(
    db: Session, 
    inventories: List[InventoryCreate], 
    user_id: Optional[uuid.UUID] = None
) -> Tuple[int, List[str]]:
    """
    Bulk create multiple inventory items
    Returns tuple of (success_count, error_messages)
    """
    success_count = 0
    error_messages = []
    
    for inventory in inventories:
        try:
            # Check if SKU already exists
            existing = get_inventory_by_sku(db, inventory.sku_code)
            if existing:
                error_messages.append(f"SKU {inventory.sku_code} already exists")
                continue
                
            create_inventory(db, inventory, user_id)
            success_count += 1
        except Exception as e:
            error_message = f"Error creating inventory {inventory.sku_code}: {str(e)}"
            error_messages.append(error_message)
            logger.error(error_message)
            db.rollback()
    
    return success_count, error_messages 