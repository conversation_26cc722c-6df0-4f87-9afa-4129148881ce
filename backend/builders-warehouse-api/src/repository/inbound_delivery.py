from sqlalchemy.orm import Session
from sqlalchemy import func, and_, extract, String
from datetime import datetime, date
from typing import List, Optional, Dict, Any
from fastapi import HTTPException

from src.models.inbound_delivery import InboundDelivery
from src.schemas.inbound_delivery import InboundDeliveryCreate, InboundDeliveryUpdate
from src.models.outbound_delivery import OutboundDelivery
from src.models.purchase_order import PurchaseOrder
from src.models.supplier import Supplier  # Import here to avoid circular imports
from src.models.invoice import Invoice  # Import for invoice details

def create_inbound_delivery(db: Session, delivery: InboundDeliveryCreate) -> InboundDelivery:
    """Create a new inbound delivery record"""
    db_delivery = InboundDelivery(**delivery.model_dump())
    db.add(db_delivery)
    db.commit()
    db.refresh(db_delivery)
    return db_delivery

def get_inbound_delivery(db: Session, delivery_id: int) -> Optional[InboundDelivery]:
    """Get an inbound delivery by ID"""
    return db.query(InboundDelivery).filter(InboundDelivery.id == delivery_id).first()

def get_inbound_deliveries_by_po(
    db: Session,
    po_number: Optional[str] = None,
    supplier: Optional[str] = None,
    delivery_date: Optional[date] = None,
    skip: int = 0,
    limit: int = 10
) -> Dict[str, Any]:
    """Get purchase orders for a specific PO or date with pagination"""
    deliveries = []
    total = 0
    
    # If we're searching by PO number, just query purchase orders
    if po_number:
        # Join with Supplier to get supplier_name
        query = db.query(PurchaseOrder, Supplier).outerjoin(
            Supplier, PurchaseOrder.supplier_id == Supplier.id
        )
        query = query.filter(PurchaseOrder.po_number == po_number)
        
        if supplier:
            query = query.filter(Supplier.name.ilike(f"%{supplier}%"))
        
        total = query.count()
        results = query.offset(skip).limit(limit).all()
        
        # Convert PurchaseOrder objects to the delivery format
        for po, supplier_obj in results:
            # Extract item details from the items JSON
            items_data = {}
            expected_date = None
            
            # Extract data from the PO details (first item)
            if po.details and len(po.details) > 0:
                first_item = po.details[0]
                items_data["sku"] = first_item.get("sku")
                items_data["quantity_ordered"] = first_item.get("quantity_ordered", 0)
                items_data["quantity_received"] = first_item.get("quantity_received", 0)
                expected_date = first_item.get("expected_delivery_date")
            else:
                # Fallback to the items JSON if no details
                if po.items and isinstance(po.items, dict):
                    if "items" in po.items and isinstance(po.items["items"], list) and len(po.items["items"]) > 0:
                        first_item = po.items["items"][0]
                        items_data["sku"] = first_item.get("sku")
                        items_data["quantity_ordered"] = first_item.get("quantity", 0)
                        items_data["quantity_received"] = first_item.get("quantity_received", 0)
                    else:
                        items_data["sku"] = po.items.get("sku")
                        items_data["quantity_ordered"] = po.items.get("quantity", 0)
                        items_data["quantity_received"] = po.items.get("quantity_received", 0)
            
            # Get the linked invoice information
            invoice_info = None
            if po.invoice_id:
                try:
                    # Try to fetch the invoice by ID
                    invoice = db.query(Invoice).filter(Invoice.id == po.invoice_id).first()
                    if invoice:
                        invoice_info = invoice.invoice_number
                    else:
                        # If invoice not found by ID, see if invoice_id might be the invoice number
                        invoice = db.query(Invoice).filter(Invoice.invoice_number == po.invoice_id).first()
                        if invoice:
                            invoice_info = invoice.invoice_number
                        else:
                            # If still not found, format as INV-PO-{po_number}-{invoice_id}
                            invoice_info = f"INV-PO-{po.po_number}-{po.invoice_id}"
                except Exception as e:
                    import logging
                    logger = logging.getLogger(__name__)
                    logger.warning(f"Error fetching invoice {po.invoice_id}: {str(e)}")
                    # Fallback to a formatted invoice number if the lookup fails
                    invoice_info = f"INV-PO-{po.po_number}-{po.invoice_id}" if po.invoice_id else None
            
            # Format delivery dict
            delivery_dict = {
                "id": po.id,
                "po_number": po.po_number,
                "supplier_name": supplier_obj.name if supplier_obj else "Unknown Supplier",
                "delivery_date": po.order_date,
                "status": po.status,
                "created_at": po.created_at,
                "updated_at": po.updated_at,
                # Extract item details
                "sku": items_data.get("sku"),
                "quantity_ordered": items_data.get("quantity_ordered"),
                "quantity_received": items_data.get("quantity_received", 0),
                "ordered_by": po.items.get("ordered_by") if po.items and isinstance(po.items, dict) else None,
                "balance_order": (
                    items_data.get("quantity_ordered", 0) - items_data.get("quantity_received", 0)
                ),
                "linked_invoice": invoice_info,  # Use the full invoice number or formatted fallback
                "ship_to_customer": po.ship_to_customer,
                "po_date": po.order_date.isoformat() if po.order_date else None,
                "new_expected_date": expected_date or (po.expected_date.isoformat() if po.expected_date else None),
            }
            deliveries.append(delivery_dict)
    
    # If we're searching by date, query purchase orders for that date
    elif delivery_date:
        # Join with Supplier to get supplier_name
        query = db.query(PurchaseOrder, Supplier).outerjoin(
            Supplier, PurchaseOrder.supplier_id == Supplier.id
        )
        
        if supplier:
            query = query.filter(Supplier.name.ilike(f"%{supplier}%"))
        
        query = query.filter(func.date(PurchaseOrder.order_date) == delivery_date)
        results = query.all()
        
        # Convert PurchaseOrder objects to the delivery format
        for po, supplier_obj in results:
            # Extract item details from the items JSON
            items_data = {}
            expected_date = None
            
            # Extract data from the PO details (first item)
            if po.details and len(po.details) > 0:
                first_item = po.details[0]
                items_data["sku"] = first_item.get("sku")
                items_data["quantity_ordered"] = first_item.get("quantity_ordered", 0)
                items_data["quantity_received"] = first_item.get("quantity_received", 0)
                expected_date = first_item.get("expected_delivery_date")
            else:
                # Fallback to the items JSON if no details
                if po.items and isinstance(po.items, dict):
                    if "items" in po.items and isinstance(po.items["items"], list) and len(po.items["items"]) > 0:
                        first_item = po.items["items"][0]
                        items_data["sku"] = first_item.get("sku")
                        items_data["quantity_ordered"] = first_item.get("quantity", 0)
                        items_data["quantity_received"] = first_item.get("quantity_received", 0)
                    else:
                        items_data["sku"] = po.items.get("sku")
                        items_data["quantity_ordered"] = po.items.get("quantity", 0)
                        items_data["quantity_received"] = po.items.get("quantity_received", 0)
            
            # Get the linked invoice information
            invoice_info = None
            if po.invoice_id:
                try:
                    # Try to fetch the invoice by ID
                    invoice = db.query(Invoice).filter(Invoice.id == po.invoice_id).first()
                    if invoice:
                        invoice_info = invoice.invoice_number
                    else:
                        # If invoice not found by ID, see if invoice_id might be the invoice number
                        invoice = db.query(Invoice).filter(Invoice.invoice_number == po.invoice_id).first()
                        if invoice:
                            invoice_info = invoice.invoice_number
                        else:
                            # If still not found, format as INV-PO-{po_number}-{invoice_id}
                            invoice_info = f"INV-PO-{po.po_number}-{po.invoice_id}"
                except Exception as e:
                    import logging
                    logger = logging.getLogger(__name__)
                    logger.warning(f"Error fetching invoice {po.invoice_id}: {str(e)}")
                    # Fallback to a formatted invoice number if the lookup fails
                    invoice_info = f"INV-PO-{po.po_number}-{po.invoice_id}" if po.invoice_id else None
            
            delivery_dict = {
                "id": po.id,
                "po_number": po.po_number,
                "supplier_name": supplier_obj.name if supplier_obj else "Unknown Supplier",
                "delivery_date": po.order_date,
                "status": po.status,
                "created_at": po.created_at,
                "updated_at": po.updated_at,
                # Extract item details
                "sku": items_data.get("sku"),
                "quantity_ordered": items_data.get("quantity_ordered"),
                "quantity_received": items_data.get("quantity_received", 0),
                "ordered_by": po.items.get("ordered_by") if po.items and isinstance(po.items, dict) else None,
                "balance_order": (
                    items_data.get("quantity_ordered", 0) - items_data.get("quantity_received", 0)
                ),
                "linked_invoice": invoice_info,  # Use the full invoice number or formatted fallback
                "ship_to_customer": po.ship_to_customer,
                "po_date": po.order_date.isoformat() if po.order_date else None,
                "new_expected_date": expected_date or (po.expected_date.isoformat() if po.expected_date else None),
            }
            deliveries.append(delivery_dict)
        
        # Apply skip/limit pagination manually 
        total = len(deliveries)
        deliveries = deliveries[skip:skip+limit]
    
    # If neither po_number nor delivery_date is provided, just query all purchase orders
    else:
        # Join with Supplier to get supplier_name
        query = db.query(PurchaseOrder, Supplier).outerjoin(
            Supplier, PurchaseOrder.supplier_id == Supplier.id
        )
        
        if supplier:
            query = query.filter(Supplier.name.ilike(f"%{supplier}%"))
        
        total = query.count()
        results = query.offset(skip).limit(limit).all()
        
        # Convert PurchaseOrder objects to the delivery format
        for po, supplier_obj in results:
            # Extract item details from the items JSON
            items_data = {}
            expected_date = None
            
            # Extract data from the PO details (first item)
            if po.details and len(po.details) > 0:
                first_item = po.details[0]
                items_data["sku"] = first_item.get("sku")
                items_data["quantity_ordered"] = first_item.get("quantity_ordered", 0)
                items_data["quantity_received"] = first_item.get("quantity_received", 0)
                expected_date = first_item.get("expected_delivery_date")
            else:
                # Fallback to the items JSON if no details
                if po.items and isinstance(po.items, dict):
                    if "items" in po.items and isinstance(po.items["items"], list) and len(po.items["items"]) > 0:
                        first_item = po.items["items"][0]
                        items_data["sku"] = first_item.get("sku")
                        items_data["quantity_ordered"] = first_item.get("quantity", 0)
                        items_data["quantity_received"] = first_item.get("quantity_received", 0)
                    else:
                        items_data["sku"] = po.items.get("sku")
                        items_data["quantity_ordered"] = po.items.get("quantity", 0)
                        items_data["quantity_received"] = po.items.get("quantity_received", 0)
            
            # Get the linked invoice information
            invoice_info = None
            if po.invoice_id:
                try:
                    # Try to fetch the invoice by ID
                    invoice = db.query(Invoice).filter(Invoice.id == po.invoice_id).first()
                    if invoice:
                        invoice_info = invoice.invoice_number
                    else:
                        # If invoice not found by ID, see if invoice_id might be the invoice number
                        invoice = db.query(Invoice).filter(Invoice.invoice_number == po.invoice_id).first()
                        if invoice:
                            invoice_info = invoice.invoice_number
                        else:
                            # If still not found, format as INV-PO-{po_number}-{invoice_id}
                            invoice_info = f"INV-PO-{po.po_number}-{po.invoice_id}"
                except Exception as e:
                    import logging
                    logger = logging.getLogger(__name__)
                    logger.warning(f"Error fetching invoice {po.invoice_id}: {str(e)}")
                    # Fallback to a formatted invoice number if the lookup fails
                    invoice_info = f"INV-PO-{po.po_number}-{po.invoice_id}" if po.invoice_id else None
            
            delivery_dict = {
                "id": po.id,
                "po_number": po.po_number,
                "supplier_name": supplier_obj.name if supplier_obj else "Unknown Supplier",
                "delivery_date": po.order_date,
                "status": po.status,
                "created_at": po.created_at,
                "updated_at": po.updated_at,
                # Extract item details
                "sku": items_data.get("sku"),
                "quantity_ordered": items_data.get("quantity_ordered"),
                "quantity_received": items_data.get("quantity_received", 0),
                "ordered_by": po.items.get("ordered_by") if po.items and isinstance(po.items, dict) else None,
                "balance_order": (
                    items_data.get("quantity_ordered", 0) - items_data.get("quantity_received", 0)
                ),
                "linked_invoice": invoice_info,  # Use the full invoice number or formatted fallback
                "ship_to_customer": po.ship_to_customer,
                "po_date": po.order_date.isoformat() if po.order_date else None,
                "new_expected_date": expected_date or (po.expected_date.isoformat() if po.expected_date else None),
            }
            deliveries.append(delivery_dict)
    
    return {
        "po_number": po_number if po_number else "All",
        "delivery_date": delivery_date.isoformat() if delivery_date else None,
        "data": deliveries,
        "pagination": {
            "total": total,
            "page": (skip // limit) + 1 if limit > 0 else 1,
            "limit": limit
        }
    }

def get_calendar_highlights(db: Session, month: int, year: int) -> List[Dict[str, Any]]:
    """
    Get calendar highlights for a specific month/year using both InboundDelivery
    and PurchaseOrder data to ensure complete calendar data
    """
    try:
        import logging
        logging.info(f"Getting calendar highlights for {month}/{year}")
        calendar_days = {}
        
        # First, get all relevant purchase orders
        purchase_orders = db.query(PurchaseOrder).filter(
            and_(
                func.date_part('month', PurchaseOrder.order_date) == month,
                func.date_part('year', PurchaseOrder.order_date) == year,
                PurchaseOrder.status != 'cancelled'
            )
        ).all()
        
        logging.info(f"Found {len(purchase_orders)} purchase orders")
        
        # Add purchase orders to calendar_days
        for po in purchase_orders:
            # Skip if order_date is not available
            if not po.order_date:
                continue
                
            # Convert to date object if it's a datetime
            delivery_date = po.order_date.date() if hasattr(po.order_date, 'date') else po.order_date
            date_key = delivery_date.isoformat() if hasattr(delivery_date, 'isoformat') else str(delivery_date)
            
            logging.info(f"Processing PO {po.id} for date {date_key}")
            
            if date_key not in calendar_days:
                calendar_days[date_key] = {
                    "date": delivery_date,
                    "po_numbers": [],
                    "status": "pending"
                }
            
            # Add PO reference
            po_reference = po.po_number if po.po_number else f"PO-{po.id}"
            if po_reference not in calendar_days[date_key]["po_numbers"]:
                calendar_days[date_key]["po_numbers"].append(po_reference)
            
            # Update status based on PO status
            if po.status == "delivered":
                calendar_days[date_key]["status"] = "Completed Delivery"
            elif po.status == "ordered" and calendar_days[date_key]["status"] != "Completed Delivery":
                calendar_days[date_key]["status"] = "Partial Delivery"
        
        # Then, get all inbound deliveries for the specified month/year
        # Avoid including columns that might not exist in the actual database
        inbound_deliveries = db.query(
            InboundDelivery.id,
            InboundDelivery.delivery_number,
            InboundDelivery.supplier_name,
            InboundDelivery.delivery_date,
            InboundDelivery.items,
            InboundDelivery.status
        ).filter(
            and_(
                func.date_part('month', InboundDelivery.delivery_date) == month,
                func.date_part('year', InboundDelivery.delivery_date) == year,
                InboundDelivery.status != 'cancelled'
            )
        ).all()
        
        logging.info(f"Found {len(inbound_deliveries)} inbound deliveries")
        
        # Add inbound deliveries to calendar_days
        for delivery_row in inbound_deliveries:
            # For query that returns tuples of column values
            if isinstance(delivery_row, tuple):
                delivery_id, delivery_number, supplier_name, delivery_date, items, status = delivery_row
            else:
                # For ORM objects
                delivery_id = delivery_row.id
                delivery_number = delivery_row.delivery_number
                delivery_date = delivery_row.delivery_date
                status = delivery_row.status
            
            # Convert to date object if it's a datetime
            delivery_date = delivery_date.date() if hasattr(delivery_date, 'date') else delivery_date
            date_key = delivery_date.isoformat() if hasattr(delivery_date, 'isoformat') else str(delivery_date)
            
            logging.info(f"Processing delivery {delivery_id} for date {date_key}")
            
            if date_key not in calendar_days:
                calendar_days[date_key] = {
                    "date": delivery_date,
                    "po_numbers": [],
                    "status": "pending"
                }
            
            # Add delivery reference
            delivery_reference = delivery_number if delivery_number else f"DEL-{delivery_id}"
            if delivery_reference not in calendar_days[date_key]["po_numbers"]:
                calendar_days[date_key]["po_numbers"].append(delivery_reference)
            
            # Update status based on delivery status
            if status == "completed":
                calendar_days[date_key]["status"] = "Completed Delivery"
            elif status == "partial" and calendar_days[date_key]["status"] != "Completed Delivery":
                calendar_days[date_key]["status"] = "Partial Delivery"
            elif status == "pending" and calendar_days[date_key]["status"] not in ["Completed Delivery", "Partial Delivery"]:
                calendar_days[date_key]["status"] = "Pending"
        
        return list(calendar_days.values())
    except Exception as e:
        # Log the error and return an empty list instead of letting exception propagate
        import logging
        logging.error(f"Error in get_calendar_highlights: {str(e)}")
        return []

def update_inbound_delivery(
    db: Session,
    delivery_id: int,
    delivery_update: InboundDeliveryUpdate
) -> Optional[InboundDelivery]:
    """Update an inbound delivery"""
    db_delivery = get_inbound_delivery(db, delivery_id)
    if not db_delivery:
        return None
    
    update_data = delivery_update.model_dump(exclude_unset=True)
    for key, value in update_data.items():
        setattr(db_delivery, key, value)
    
    # Update status based on quantities
    db_delivery.status = db_delivery.calculate_status()
    
    db.commit()
    db.refresh(db_delivery)
    return db_delivery

def update_ship_to_customer(
    db: Session,
    po_number: str,
    ship_to_customer: bool
) -> List[PurchaseOrder]:
    """
    Update ship_to_customer flag for a PurchaseOrder.
    Once ship_to_customer is set to True, it cannot be changed back to False.
    When set to True, automatically creates an outbound delivery record.
    """
    import logging
    logger = logging.getLogger(__name__)
    
    # Find the purchase order
    po = db.query(PurchaseOrder).filter(
        PurchaseOrder.po_number == po_number
    ).first()
    
    if not po:
        logger.warning(f"No PurchaseOrder found with PO number {po_number}")
        return []
    
    logger.info(f"Found PO {po_number} with current ship_to_customer={po.ship_to_customer}, attempting to set to {ship_to_customer}")
    
    # If trying to set ship_to_customer to False and it's already set to True,
    # raise an exception to prevent the change
    if not ship_to_customer and po.ship_to_customer:
        logger.warning(f"Attempted to set ship_to_customer from True to False for PO {po_number}, which is not allowed")
        raise HTTPException(
            status_code=422,
            detail="Ship to customer cannot be disabled once enabled. This delivery has already been marked for shipping to customer."
        )
    
    # If value is not changing, just return the PO
    if po.ship_to_customer == ship_to_customer:
        logger.info(f"PO {po_number} ship_to_customer already set to {ship_to_customer}, no change needed")
        return [po]
    
    # Update the ship_to_customer value (now a property that updates items JSON)
    po.ship_to_customer = ship_to_customer
    
    # Ensure the JSON update is saved to the database
    # Force SQLAlchemy to detect the change by reassigning the items field
    if not po.items:
        po.items = {}
    if 'metadata' not in po.items:
        po.items['metadata'] = {}
    po.items['metadata']['ship_to_customer'] = ship_to_customer
    
    # Force SQLAlchemy to detect the change by reassigning the entire items field
    items_copy = dict(po.items)
    po.items = None
    db.flush()
    po.items = items_copy
    
    # If toggled ON, create an outbound delivery record automatically
    if ship_to_customer:
        logger.info(f"Ship to customer set to True for PO {po_number}, creating outbound delivery")
        
        try:
            # Extract item details from the PO
            sku = None
            description = None
            
            # Try to get SKU and description from PO items JSON
            if po.items and isinstance(po.items, dict):
                # Check if there are items in the items array
                if "items" in po.items and isinstance(po.items["items"], list) and len(po.items["items"]) > 0:
                    first_item = po.items["items"][0]
                    sku = first_item.get("sku")
                    description = first_item.get("description")
                else:
                    # Fallback to direct items properties
                    sku = po.items.get("sku")
                    description = po.items.get("description")
            
            # Get supplier name for description fallback
            supplier = db.query(Supplier).filter(Supplier.id == po.supplier_id).first()
            supplier_name = supplier.name if supplier else "Unknown Supplier"
            
            # Use supplier name as description if no description found
            if not description:
                description = supplier_name
            
            # Generate invoice number
            today = datetime.now()
            invoice_no = f"INV-{po_number}-{today.strftime('%H%M%S')}"
            
            # Check if outbound delivery already exists for this PO
            existing_outbound = db.query(OutboundDelivery.invoice_no).filter(
                OutboundDelivery.linked_po == po_number
            ).first()
            
            if existing_outbound:
                logger.info(f"Outbound delivery already exists for PO {po_number}: {existing_outbound[0] if existing_outbound else 'Unknown'}")
            else:
                # Create new outbound delivery
                outbound_delivery = OutboundDelivery(
                    invoice_no=invoice_no,
                    invoice_date=today.date(),
                    linked_po=po_number,
                    sku=sku or "N/A",
                    description=description or supplier_name,
                    status="New"
                )
                
                db.add(outbound_delivery)
                logger.info(f"Created outbound delivery {invoice_no} for PO {po_number}")
                
        except Exception as e:
            logger.error(f"Error creating outbound delivery for PO {po_number}: {str(e)}")
            # Don't fail the ship_to_customer update if outbound delivery creation fails
            # Just log the error and continue
    
    # Commit changes
    db.commit()
    db.refresh(po)
    
    return [po] 