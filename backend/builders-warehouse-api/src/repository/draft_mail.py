from sqlalchemy.orm import Session
from sqlalchemy import desc
from typing import List, Optional, Dict, Any, Union
from fastapi import HTTPException, status
import logging
import uuid

from src.models.draft_mail import DraftMail
from src.models.invoice import Invoice
from src.models.purchase_order import PurchaseOrder

logger = logging.getLogger(__name__)

def create_draft_mail(
    db: Session,
    invoice_id: int,
    po_id: int,
    subject: str,
    body: str,
    created_by: Union[int, str, uuid.UUID],
    ready_to_send: bool = False
) -> DraftMail:
    """
    Create a new draft mail record
    
    Args:
        db: Database session
        invoice_id: ID of the associated invoice
        po_id: ID of the associated purchase order
        subject: Email subject
        body: Email body
        created_by: ID of the user creating the draft (UUID or string that can be converted to UUID)
        ready_to_send: Whether the draft is ready to be sent
        
    Returns:
        The created DraftMail object
    """
    try:
        # Validate that invoice exists
        invoice = db.query(Invoice).filter(Invoice.id == invoice_id).first()
        if not invoice:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Invoice with ID {invoice_id} not found"
            )
            
        # Validate that purchase order exists
        po = db.query(PurchaseOrder).filter(PurchaseOrder.id == po_id).first()
        if not po:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Purchase Order with ID {po_id} not found"
            )
        
        # Convert created_by to UUID if it's not already
        if not isinstance(created_by, uuid.UUID):
            try:
                created_by = uuid.UUID(str(created_by))
            except ValueError:
                # If conversion fails, use a default UUID
                logger.warning(f"Invalid created_by value: {created_by}. Using default UUID.")
                created_by = uuid.uuid4()
        
        # Create the draft mail
        draft_mail = DraftMail(
            invoice_id=invoice_id,
            po_id=po_id,
            subject=subject,
            body=body,
            ready_to_send=ready_to_send,
            created_by=created_by
        )
        
        db.add(draft_mail)
        db.commit()
        db.refresh(draft_mail)
        
        return draft_mail
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        db.rollback()
        logger.error(f"Error creating draft mail: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create draft mail: {str(e)}"
        )

def get_draft_mails_by_invoice(db: Session, invoice_id: int) -> List[DraftMail]:
    """Get all draft mails for a specific invoice"""
    return db.query(DraftMail).filter(DraftMail.invoice_id == invoice_id).all()

def get_draft_mails_by_po(db: Session, po_id: int) -> List[DraftMail]:
    """Get all draft mails for a specific purchase order"""
    return db.query(DraftMail).filter(DraftMail.po_id == po_id).all()

def mark_draft_as_ready(db: Session, draft_id: int) -> Optional[DraftMail]:
    """Mark a draft mail as ready to send"""
    draft = db.query(DraftMail).filter(DraftMail.id == draft_id).first()
    if not draft:
        return None
        
    draft.ready_to_send = True
    db.commit()
    db.refresh(draft)
    return draft

def mark_draft_as_sent(db: Session, draft_id: int) -> Optional[DraftMail]:
    """Mark a draft mail as sent"""
    from datetime import datetime
    
    draft = db.query(DraftMail).filter(DraftMail.id == draft_id).first()
    if not draft:
        return None
        
    draft.sent = True
    draft.sent_at = datetime.utcnow()
    db.commit()
    db.refresh(draft)
    return draft 