from sqlalchemy.orm import Session
from sqlalchemy import desc, asc, and_, or_, func, between
from datetime import datetime, date, timedelta
from typing import List, Optional, Dict, Any, Tuple, Union
from fastapi import HTTPException, status
import csv
import io
import uuid

from src.models.report import SalesReport, POReport
from src.schemas.report import SalesReportCreate, POReportCreate, ReportType
from src.models.purchase_order import PurchaseOrder
from src.models.invoice import Invoice

# Report CRUD operations
def get_last_n_sales(db: Session, n: int = 30) -> List[SalesReport]:
    """Get the last n sales reports"""
    return db.query(SalesReport).order_by(desc(SalesReport.date)).limit(n).all()

def get_last_n_pos(db: Session, n: int = 30) -> List[POReport]:
    """Get the last n purchase order reports"""
    return db.query(POReport).order_by(desc(POReport.date)).limit(n).all()

def create_sales_report(db: Session, sales: SalesReportCreate) -> SalesReport:
    """Create a new sales report entry"""
    db_sales = SalesReport(
        date=sales.date,
        invoice_number=sales.invoice_number,
        company_name=sales.company_name,
        total_amount=sales.total_amount,
        mode_of_payment=sales.mode_of_payment
    )
    db.add(db_sales)
    db.commit()
    db.refresh(db_sales)
    return db_sales

def create_po_report(db: Session, po: POReportCreate) -> POReport:
    """Create a new purchase order report entry"""
    db_po = POReport(
        date=po.date,
        po_number=po.po_number,
        supplier_name=po.supplier_name,
        linked_invoice_number=po.linked_invoice_number,
        total_amount=po.total_amount
    )
    db.add(db_po)
    db.commit()
    db.refresh(db_po)
    return db_po

def delete_sales_reports(db: Session, ids: List[int]) -> int:
    """Delete sales reports by IDs. Returns number of reports deleted."""
    count = db.query(SalesReport).filter(SalesReport.id.in_(ids)).delete(synchronize_session=False)
    db.commit()
    return count

def delete_po_reports(db: Session, ids: List[int]) -> int:
    """Delete purchase order reports by IDs. Returns number of reports deleted."""
    count = db.query(POReport).filter(POReport.id.in_(ids)).delete(synchronize_session=False)
    db.commit()
    return count

def generate_sales_csv(db: Session, n: int = 30) -> str:
    """Generate CSV data for sales reports"""
    sales = get_last_n_sales(db, n)
    
    # Create StringIO to hold CSV data
    output = io.StringIO()
    writer = csv.writer(output)
    
    # Write header
    writer.writerow(['Date', 'Invoice Number', 'Company Name', 'Total Amount (AUD)', 'Mode of Payment'])
    
    # Write data rows
    for sale in sales:
        writer.writerow([
            sale.date.strftime('%d/%m/%Y'),
            sale.invoice_number,
            sale.company_name,
            float(sale.total_amount),
            sale.mode_of_payment
        ])
    
    return output.getvalue()

def generate_po_csv(db: Session, n: int = 30) -> str:
    """Generate CSV data for purchase order reports"""
    pos = get_last_n_pos(db, n)
    
    # Create StringIO to hold CSV data
    output = io.StringIO()
    writer = csv.writer(output)
    
    # Write header
    writer.writerow(['Date', 'PO Number', 'Supplier Name', 'Linked to Invoice Number', 'Total Amount (AUD)'])
    
    # Write data rows
    for po in pos:
        writer.writerow([
            po.date.strftime('%d/%m/%Y'),
            po.po_number,
            po.supplier_name,
            po.linked_invoice_number or '',
            float(po.total_amount)
        ])
    
    return output.getvalue()

def process_sales_csv(db: Session, csv_data: str) -> Tuple[int, List[Dict[str, Any]]]:
    """Process CSV data for sales reports. Returns number of successful imports and list of failed rows."""
    reader = csv.DictReader(io.StringIO(csv_data))
    success_count = 0
    failed_rows = []
    
    for row in reader:
        try:
            # Validate and transform CSV data
            date_str = row.get('Date', '').strip()
            try:
                # Try different date formats
                try:
                    date_obj = datetime.strptime(date_str, '%d/%m/%Y').date()
                except ValueError:
                    date_obj = datetime.strptime(date_str, '%Y-%m-%d').date()
            except ValueError:
                raise ValueError(f"Invalid date format: {date_str}")
            
            invoice_number = row.get('Invoice Number', '').strip()
            if not invoice_number:
                raise ValueError("Invoice Number is required")
            
            company_name = row.get('Company Name', '').strip()
            if not company_name:
                raise ValueError("Company Name is required")
            
            total_amount_str = row.get('Total Amount (AUD)', '').strip().replace(',', '')
            if not total_amount_str:
                raise ValueError("Total Amount is required")
            
            try:
                total_amount = float(total_amount_str)
            except ValueError:
                raise ValueError(f"Invalid Total Amount: {total_amount_str}")
            
            mode_of_payment = row.get('Mode of Payment', '').strip()
            if not mode_of_payment:
                raise ValueError("Mode of Payment is required")
            
            # Create SalesReport entry
            sales_data = SalesReportCreate(
                date=date_obj,
                invoice_number=invoice_number,
                company_name=company_name,
                total_amount=total_amount,
                mode_of_payment=mode_of_payment
            )
            create_sales_report(db, sales_data)
            success_count += 1
            
        except Exception as e:
            failed_rows.append({
                'row': dict(row),
                'error': str(e)
            })
    
    return success_count, failed_rows

def process_po_csv(db: Session, csv_data: str) -> Tuple[int, List[Dict[str, Any]]]:
    """Process CSV data for purchase order reports. Returns number of successful imports and list of failed rows."""
    reader = csv.DictReader(io.StringIO(csv_data))
    success_count = 0
    failed_rows = []
    
    for row in reader:
        try:
            # Validate and transform CSV data
            date_str = row.get('Date', '').strip()
            try:
                # Try different date formats
                try:
                    date_obj = datetime.strptime(date_str, '%d/%m/%Y').date()
                except ValueError:
                    date_obj = datetime.strptime(date_str, '%Y-%m-%d').date()
            except ValueError:
                raise ValueError(f"Invalid date format: {date_str}")
            
            po_number = row.get('PO Number', '').strip()
            if not po_number:
                raise ValueError("PO Number is required")
            
            supplier_name = row.get('Supplier Name', '').strip()
            if not supplier_name:
                raise ValueError("Supplier Name is required")
            
            linked_invoice_number = row.get('Linked to Invoice Number', '').strip()
            # This can be empty
            
            total_amount_str = row.get('Total Amount (AUD)', '').strip().replace(',', '')
            if not total_amount_str:
                raise ValueError("Total Amount is required")
            
            try:
                total_amount = float(total_amount_str)
            except ValueError:
                raise ValueError(f"Invalid Total Amount: {total_amount_str}")
            
            # Create POReport entry
            po_data = POReportCreate(
                date=date_obj,
                po_number=po_number,
                supplier_name=supplier_name,
                linked_invoice_number=linked_invoice_number if linked_invoice_number else None,
                total_amount=total_amount
            )
            create_po_report(db, po_data)
            success_count += 1
            
        except Exception as e:
            failed_rows.append({
                'row': dict(row),
                'error': str(e)
            })
    
    return success_count, failed_rows

def sync_reports_from_db(db: Session) -> Tuple[int, int]:
    """
    Sync reports from actual invoice and purchase order data in the database.
    Returns counts of synced sales and POs.
    """
    # Get recent invoices and create sales reports
    invoices = db.query(Invoice).order_by(desc(Invoice.created_at)).limit(30).all()
    sales_count = 0
    
    for invoice in invoices:
        # Check if this invoice already has a report
        existing = db.query(SalesReport).filter(SalesReport.invoice_number == invoice.invoice_number).first()
        if not existing:
            # Create a new sales report
            db_sales = SalesReport(
                date=invoice.invoice_date.date() if hasattr(invoice, 'invoice_date') and invoice.invoice_date else datetime.utcnow().date(),
                invoice_number=invoice.invoice_number,
                company_name=invoice.company_name if hasattr(invoice, 'company_name') and invoice.company_name else "Unknown",
                total_amount=invoice.total_amount,
                mode_of_payment=invoice.payment_method if hasattr(invoice, 'payment_method') and invoice.payment_method else "Unknown"
            )
            db.add(db_sales)
            sales_count += 1
    
    # Get recent purchase orders and create PO reports
    pos = db.query(PurchaseOrder).order_by(desc(PurchaseOrder.created_at)).limit(30).all()
    po_count = 0
    
    for po in pos:
        # Check if this PO already has a report
        existing = db.query(POReport).filter(POReport.po_number == po.po_number).first()
        if not existing:
            # Create a new PO report
            db_po = POReport(
                date=po.order_date.date() if hasattr(po, 'order_date') and po.order_date else datetime.utcnow().date(),
                po_number=po.po_number,
                supplier_name=po.supplier_name if hasattr(po, 'supplier_name') and po.supplier_name else "Unknown",
                linked_invoice_number=po.invoice_id if hasattr(po, 'invoice_id') and po.invoice_id else None,
                total_amount=po.total_amount if hasattr(po, 'total_amount') else 0.0
            )
            db.add(db_po)
            po_count += 1
    
    db.commit()
    return sales_count, po_count 