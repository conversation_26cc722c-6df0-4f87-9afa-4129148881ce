from sqlalchemy.orm import Session
from sqlalchemy import and_, or_
from datetime import datetime
from typing import List, Optional, Dict, Any
from fastapi import HTTPException, status

from src.models.outbound_delivery import OutboundDelivery, DeliveredOrder
from src.schemas.outbound_delivery import OutboundDeliveryCreate

# Create a new outbound delivery
def create_outbound_delivery(
    db: Session,
    delivery_data: OutboundDeliveryCreate
) -> OutboundDelivery:
    """Create a new outbound delivery record"""
    try:
        # Check if invoice number already exists
        existing = db.query(OutboundDelivery).filter(
            OutboundDelivery.invoice_no == delivery_data.invoice_no
        ).first()
        
        if existing:
            raise HTTPException(
                status_code=400,
                detail=f"Invoice number {delivery_data.invoice_no} already exists"
            )
            
        # Create new outbound delivery
        new_delivery = OutboundDelivery(
            invoice_no=delivery_data.invoice_no,
            invoice_date=delivery_data.invoice_date,
            linked_po=delivery_data.linked_po,
            sku=delivery_data.sku,
            description=delivery_data.description,
            status=delivery_data.status
        )
        
        db.add(new_delivery)
        db.commit()
        db.refresh(new_delivery)
        
        return new_delivery
    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(
            status_code=500,
            detail=f"Failed to create outbound delivery: {str(e)}"
        )

# Fetch outbound deliveries in process
def get_outbound_deliveries(
    db: Session,
    status: Optional[str] = None,
    sku: Optional[str] = None,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    page: int = 1,
    limit: int = 10
) -> Dict[str, Any]:
    query = db.query(OutboundDelivery)
    if status:
        query = query.filter(OutboundDelivery.status == status)
    if sku:
        query = query.filter(OutboundDelivery.sku.ilike(f"%{sku}%"))
    if start_date:
        query = query.filter(OutboundDelivery.invoice_date >= start_date)
    if end_date:
        query = query.filter(OutboundDelivery.invoice_date <= end_date)
    total = query.count()
    deliveries = query.order_by(OutboundDelivery.invoice_date.desc()).offset((page-1)*limit).limit(limit).all()
    return {
        "data": deliveries,
        "pagination": {
            "total": total,
            "page": page,
            "limit": limit
        }
    }

# Update outbound delivery status
def update_outbound_delivery_status(
    db: Session,
    invoice_no: str,
    new_status: str
) -> Dict[str, Any]:
    delivery = db.query(OutboundDelivery).filter_by(invoice_no=invoice_no).first()
    if not delivery:
        raise HTTPException(status_code=404, detail="Invoice not found")
    allowed_statuses = [
        "New",
        "Transfer Stock to Sale by Internal driver",
        "Pickup by Customer",
        "Transfer Stock to Sale by Courier",
        "Delivery by Internal Driver",
        "Delivery by External Courier",
        "Delivered"
    ]
    if new_status not in allowed_statuses:
        raise HTTPException(status_code=400, detail="Invalid status value")
    delivery.status = new_status
    if new_status == "Delivered":
        # Move to DeliveredOrder
        delivered = DeliveredOrder(
            invoice_no=delivery.invoice_no,
            invoice_date=delivery.invoice_date,
            linked_po=delivery.linked_po,
            sku=delivery.sku,
            description=delivery.description,
            delivered_date=datetime.utcnow()
        )
        db.add(delivered)
        db.delete(delivery)
    db.commit()
    return {"invoice_no": invoice_no, "new_status": new_status}

# Fetch delivered orders
def get_delivered_orders(
    db: Session,
    sku: Optional[str] = None,
    invoice_date: Optional[str] = None,
    page: int = 1,
    limit: int = 10
) -> Dict[str, Any]:
    query = db.query(DeliveredOrder)
    if sku:
        query = query.filter(DeliveredOrder.sku.ilike(f"%{sku}%"))
    if invoice_date:
        query = query.filter(DeliveredOrder.invoice_date == invoice_date)
    total = query.count()
    orders = query.order_by(DeliveredOrder.invoice_date.desc()).offset((page-1)*limit).limit(limit).all()
    return {
        "data": orders,
        "pagination": {
            "total": total,
            "page": page,
            "limit": limit
        }
    } 