from fastapi import APIRouter, Depends, HTTPException, status, Query, Body, Response
from fastapi.responses import StreamingResponse
from sqlalchemy.orm import Session
from typing import Dict, Any, Optional
import logging
from datetime import date

from src.database import get_db
from src.schemas.report_v2 import (
    ReportResponse, ReportType, 
    PaginatedSalesResponse, PaginatedPurchaseOrdersResponse,
    CSVDownloadRequest
)
from src.repository.report_v2 import (
    get_last_30_sales, get_last_30_pos,
    get_paginated_sales, get_paginated_pos,
    generate_sales_csv, generate_pos_csv
)
from src.utils.audit_log import create_audit_log

# Setup logger
logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/reports",
    tags=["Reports"],
    responses={404: {"description": "Not found"}},
)

@router.get("", response_model=ReportResponse)
def get_reports(db: Session = Depends(get_db)):
    """
    Get the last 30 sales (invoices) and last 30 purchase orders.
    
    Returns:
    - total_sales: Total count of all sales
    - total_pos: Total count of all purchase orders
    - data: Object containing last_30_sales and last_30_pos arrays
    """
    try:
        # Get the last 30 sales and total count
        sales, total_sales = get_last_30_sales(db)
        
        # Get the last 30 purchase orders and total count
        pos, total_pos = get_last_30_pos(db)
        
        # Return the results
        return {
            "total_sales": total_sales,
            "total_pos": total_pos,
            "data": {
                "last_30_sales": sales,
                "last_30_pos": pos
            }
        }
    except Exception as e:
        logger.error(f"Error getting reports: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve reports: {str(e)}"
        )

@router.get("/sales", response_model=PaginatedSalesResponse)
def get_paginated_sales_reports(
    page: int = Query(1, ge=1, description="Page number, starting from 1"),
    limit: int = Query(30, ge=1, le=100, description="Number of records per page"),
    search: Optional[str] = Query(None, description="Search term for invoice number or company name"),
    db: Session = Depends(get_db)
):
    """
    Get paginated sales reports (invoices) with optional search.
    
    Parameters:
    - page: Page number (starts from 1)
    - limit: Number of records per page (default: 30)
    - search: Optional search term for invoice number or company name
    
    Returns:
    - total: Total count of sales matching the search criteria
    - page: Current page number
    - limit: Number of records per page
    - items: Array of sales data for the current page
    """
    try:
        # Get paginated sales
        sales, total = get_paginated_sales(db, page, limit, search)
        
        return {
            "total": total,
            "page": page,
            "limit": limit,
            "items": sales
        }
    except Exception as e:
        logger.error(f"Error getting paginated sales: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve sales reports: {str(e)}"
        )

@router.get("/purchase-orders", response_model=PaginatedPurchaseOrdersResponse)
def get_paginated_purchase_order_reports(
    page: int = Query(1, ge=1, description="Page number, starting from 1"),
    limit: int = Query(30, ge=1, le=100, description="Number of records per page"),
    search: Optional[str] = Query(None, description="Search term for PO number or supplier name"),
    db: Session = Depends(get_db)
):
    """
    Get paginated purchase order reports with optional search.
    
    Parameters:
    - page: Page number (starts from 1)
    - limit: Number of records per page (default: 30)
    - search: Optional search term for PO number or supplier name
    
    Returns:
    - total: Total count of POs matching the search criteria
    - page: Current page number
    - limit: Number of records per page
    - items: Array of purchase order data for the current page
    """
    try:
        # Get paginated purchase orders
        pos, total = get_paginated_pos(db, page, limit, search)
        
        return {
            "total": total,
            "page": page,
            "limit": limit,
            "items": pos
        }
    except Exception as e:
        logger.error(f"Error getting paginated purchase orders: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve purchase order reports: {str(e)}"
        )

@router.post("/download", response_class=StreamingResponse)
def download_reports_csv(
    request: CSVDownloadRequest = Body(...),
    db: Session = Depends(get_db)
):
    """
    Download reports as CSV file with pagination support.
    
    Request body:
    - report_type: Type of report to download (sales or po)
    - page: Page number (starts from 1)
    - limit: Number of records to include (default: 30)
    - search: Optional search term
    
    Response: CSV file download
    """
    try:
        if request.report_type == ReportType.SALES:
            csv_data = generate_sales_csv(db, request.page, request.limit, request.search)
            filename = f"sales_report_{date.today().strftime('%Y%m%d')}.csv"
            description = "Sales Report"
        else:  # report_type == ReportType.PO
            csv_data = generate_pos_csv(db, request.page, request.limit, request.search)
            filename = f"po_report_{date.today().strftime('%Y%m%d')}.csv"
            description = "Purchase Order Report"
        
        # Log the download operation to audit log
        create_audit_log(
            db, 
            user_id=None,  # This would typically be the authenticated user's ID
            action="DOWNLOAD",
            resource_type="REPORTS",
            resource_id=None,
            details={
                "report_type": request.report_type,
                "page": request.page,
                "limit": request.limit,
                "search": request.search,
                "filename": filename
            }
        )
        
        # Create a StreamingResponse with the CSV data
        response = StreamingResponse(
            iter([csv_data]), 
            media_type="text/csv"
        )
        response.headers["Content-Disposition"] = f"attachment; filename={filename}"
        response.headers["X-Report-Description"] = description
        
        return response
    except Exception as e:
        logger.error(f"Error generating CSV for download: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate CSV for download: {str(e)}"
        ) 