from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import date
import logging

from src.database import get_db
from src.models.purchase_order import PurchaseOrder
from src.schemas.purchase_order import (
    PurchaseOrderCreate, PurchaseOrderUpdate, PurchaseOrderOut, 
    PurchaseOrderEmailStatusUpdate, PurchaseOrderDetailNotesUpdate,
    PurchaseOrderDetailUpdate, PurchaseOrderPagination, PurchaseOrderStatus,
    PurchaseOrderDetailOut
)
from src.repository.purchase_order import (
    create_purchase_order, get_purchase_orders, get_purchase_order,
    update_purchase_order, delete_purchase_order, update_purchase_order_email_status,
    update_purchase_order_detail_notes, update_purchase_order_detail,
    get_purchase_order_detail
)

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/test",
    tags=["Test"],
    responses={404: {"description": "Not found"}},
)

# Test endpoint without authentication
@router.get("/purchase-orders", response_model=PurchaseOrderPagination)
def read_purchase_orders_test(
    db: Session = Depends(get_db),
    skip: int = Query(0, ge=0, description="Number of items to skip"),
    limit: int = Query(10, ge=1, le=100, description="Number of items to return"),
    supplier_name: Optional[str] = Query(None, description="Filter by supplier name (partial match)"),
    start_date: Optional[date] = Query(None, description="Filter by start date (inclusive)"),
    end_date: Optional[date] = Query(None, description="Filter by end date (inclusive)"),
    status: Optional[PurchaseOrderStatus] = Query(None, description="Filter by status")
):
    """Test endpoint to get all purchase orders with filtering and pagination without authentication"""
    purchase_orders, total = get_purchase_orders(
        db, skip=skip, limit=limit, 
        supplier_name=supplier_name,
        start_date=start_date,
        end_date=end_date,
        status=status if status is None else status.value
    )
    
    # Ensure limit is not zero to avoid division by zero
    page_limit = max(1, limit)
    
    return {
        "items": purchase_orders,
        "total": total,
        "page": skip // page_limit + 1,
        "size": limit
    } 