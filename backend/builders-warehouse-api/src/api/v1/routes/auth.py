from fastapi import <PERSON><PERSON><PERSON><PERSON>, Depends, Request, Query
from fastapi.security import OAuth2PasswordRequestForm
from sqlalchemy.orm import Session
from typing import List, Optional
import logging

from src.db.session import get_db
from src.controllers.auth_controller import AuthController
from src.models.user import User
from src.schemas.user import Token, UserLogin
from src.schemas.audit_log import AuditLogResponse, AuditLogFilter
from src.utils.auth import get_current_active_admin

logger = logging.getLogger(__name__)

# Create router with prefix and tags
router = APIRouter(
    prefix="/auth",
    tags=["Authentication"]
)

@router.post("/login", response_model=Token)
async def login_for_access_token(
    form_data: OAuth2PasswordRequestForm = Depends(), 
    db: Session = Depends(get_db), 
    request: Request = None
):
    """
    Standard OAuth2 login endpoint using email/password
    """
    login_data = UserLogin(email=form_data.username, password=form_data.password)
    return await AuthController.email_login(
        db=db,
        login_data=login_data,
        request=request
    )

@router.post("/api-login", response_model=Token)
async def email_login(
    login_data: UserLogin, 
    db: Session = Depends(get_db), 
    request: Request = None
):
    """
    Login endpoint using email/password
    """
    return await AuthController.email_login(
        db=db,
        login_data=login_data,
        request=request
    )

@router.get("/audit-logs", response_model=List[AuditLogResponse])
async def get_audit_logs(
    table_name: Optional[str] = Query(None, description="Filter by table name"),
    operation: Optional[str] = Query(None, description="Filter by operation type (CREATE, UPDATE, DELETE)"),
    entity_id: Optional[str] = Query(None, description="Filter by entity ID"),
    user_id: Optional[int] = Query(None, description="Filter by user ID"),
    from_date: Optional[str] = Query(None, description="Filter by from date (ISO format)"),
    to_date: Optional[str] = Query(None, description="Filter by to date (ISO format)"),
    skip: int = Query(0, description="Number of records to skip"),
    limit: int = Query(100, description="Maximum number of records to return"),
    db: Session = Depends(get_db),
    current_admin: User = Depends(get_current_active_admin)
):
    """
    Get audit logs with filtering options (admin only)
    """
    filter_params = AuditLogFilter(
        table_name=table_name,
        operation=operation,
        entity_id=entity_id,
        user_id=user_id,
        from_date=from_date,
        to_date=to_date
    )
    
    return await AuthController.get_audit_logs(
        db=db,
        filter_params=filter_params,
        skip=skip,
        limit=limit
    ) 