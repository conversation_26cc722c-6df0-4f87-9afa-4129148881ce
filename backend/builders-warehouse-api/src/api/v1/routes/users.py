from fastapi import APIRouter, Depends, HTTPException, status, Request, Query
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
import math

# ======================================================================
# TEMPORARY FIX: UUID handling in routes now accepts string type for user_id
# Instead of integers, all IDs are now accepted as strings to properly
# handle UUID values from the client.
#
# Changes:
# 1. Changed user_id parameters from int to str in all routes
# 2. Added conversion of current_user.id to string for proper comparison
# ======================================================================

from src.db.session import get_db
from src.models.user import User, UserRole
from src.schemas.user import UserCreate, UserResponse, UserUpdate, ManagerListItem
from src.utils.auth import get_current_user, get_current_active_admin
from src.controllers.user_controller import UserController
from src.utils.audit_log import create_audit_log
import logging

logger = logging.getLogger(__name__)

# Create router with prefix and tags
router = APIRouter(
    prefix="/users",
    tags=["Users"]
)

@router.post("/", response_model=UserResponse, status_code=status.HTTP_201_CREATED)
async def create_user(
    user: UserCreate, 
    request: Request,
    current_admin: User = Depends(get_current_active_admin),
    db: Session = Depends(get_db)
):
    """
    Create a new user (admin only)
    
    Requires:
    - user_name: Username for the new user
    - mobile_number: Valid mobile number (10-15 digits with optional + prefix)
    - email: Valid email address
    - password: Strong password (min 8 chars, with uppercase, lowercase, number, special char)
    - role: User role (admin, manager, staff)
    
    Role-specific requirements:
    - Admin users: 
        - store_type_id must not be provided (will be ignored if sent)
    - Manager users:
        - store_type_id is required
    - Staff users: 
        - store_type_id is required
        - manager_id is required when created by an Admin (must reference a valid manager)
    
    Password strength rules:
    - At least 8 characters
    - At least one uppercase letter
    - At least one lowercase letter
    - At least one number
    - At least one special character
    """
    return await UserController.create_user(db=db, user_data=user, request=request, created_by_id=current_admin.id)

@router.get("/", response_model=Dict[str, Any])
async def get_users(
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(100, ge=1, le=500, description="Maximum number of records to return"),
    search: Optional[str] = Query(None, description="Search by user_name, email, mobile_number, or role"),
    role: Optional[UserRole] = None,
    store_type_id: Optional[int] = None,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get all users with optional filtering
    
    Parameters:
    - skip: Number of records to skip (for pagination)
    - limit: Maximum number of records to return
    - search: Search term for user_name, email, or mobile_number
    - role: Filter by user role
    - store_type_id: Filter by store type ID
    """
    # If search parameter is provided, log the search action
    if search:
        create_audit_log(
            db=db,
            user_id=str(current_user.id),
            action="search",
            resource_type="users",
            details={"search_term": search}
        )
    
    # Get the count first
    total_count = await UserController.get_users_count(
        db=db,
        search=search,
        role=role,
        store_type_id=store_type_id
    )
    
    # Get the users with pagination
    users = await UserController.get_users(
        db=db,
        skip=skip,
        limit=limit,
        search=search,
        role=role,
        store_type_id=store_type_id
    )
    
    # Convert SQLAlchemy User objects to dictionaries for serialization
    user_dicts = []
    for user in users:
        user_dict = {
            "id": str(user.id),
            "user_name": user.user_name,
            "email": user.email,
            "mobile_number": user.mobile_number or "",
            "is_active": user.is_active,
            "role": user.role,
            "created_at": user.created_at,
            "updated_at": user.updated_at,
            "store_type": None,
            "manager": None
        }
        
        # Include store_type if available
        if hasattr(user, 'store_type') and user.store_type:
            user_dict["store_type"] = {
                "id": user.store_type.id,
                "name": user.store_type.name
            }
        
        # Include manager if available
        if hasattr(user, 'manager') and user.manager:
            user_dict["manager"] = {
                "id": str(user.manager.id),
                "user_name": user.manager.user_name
            }
        
        user_dicts.append(user_dict)
    
    # Calculate pagination values
    page = (skip // limit) + 1 if limit > 0 else 1
    pages = math.ceil(total_count / limit) if limit > 0 else 1
    
    # Return in standard pagination format
    return {
        "items": user_dicts,
        "total": total_count,
        "page": page,
        "pages": pages
    }

@router.get("/managers/", response_model=List[ManagerListItem])
async def get_managers(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get list of users with manager role
    
    Returns a list of users with role = Manager, containing only id and user_name
    Used to populate manager dropdown in the frontend
    """
    return await UserController.get_managers(db=db)

@router.get("/count", response_model=int)
async def get_users_count(
    search: Optional[str] = Query(None, description="Search by user_name, email, mobile_number, or role"),
    role: Optional[UserRole] = None,
    store_type_id: Optional[int] = None,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get total count of users matching filter criteria
    
    Parameters:
    - search: Search term for user_name, email, or mobile_number
    - role: Filter by user role
    - store_type_id: Filter by store type ID
    
    Returns the total count as an integer
    """
    return await UserController.get_users_count(
        db=db,
        search=search,
        role=role,
        store_type_id=store_type_id
    )

@router.get("/me", response_model=UserResponse)
async def read_current_user(current_user: User = Depends(get_current_user)):
    """
    Get the current authenticated user's information
    """
    # Format user data properly for response
    user_data = {
        "id": str(current_user.id),
        "user_name": current_user.user_name,
        "email": current_user.email,
        "mobile_number": current_user.mobile_number or "",  # Convert None to empty string
        "is_active": current_user.is_active,
        "role": current_user.role,
        "created_at": current_user.created_at,
        "updated_at": current_user.updated_at,
        "store_type": None,
        "manager": None
    }
    
    # Include store_type if available
    if hasattr(current_user, 'store_type') and current_user.store_type:
        user_data["store_type"] = {
            "id": current_user.store_type.id,
            "name": current_user.store_type.name
        }
    
    # Include manager if available
    if hasattr(current_user, 'manager') and current_user.manager:
        user_data["manager"] = {
            "id": str(current_user.manager.id),
            "user_name": current_user.manager.user_name
        }
    
    return user_data

@router.get("/{user_id}", response_model=UserResponse)
async def get_user(
    user_id: str, 
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get a specific user by ID
    """
    return await UserController.get_user(db=db, user_id=user_id)

@router.put("/{user_id}", response_model=UserResponse)
async def update_user(
    user_id: str, 
    user_update: UserUpdate, 
    request: Request,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Update a user by ID (admin only except for self-update)
    
    Updates can include:
    - user_name: Username
    - mobile_number: Valid mobile number
    - email: Valid email address
    - password: Strong password
    - is_active: Active status
    - role: User role
    
    Role-specific requirements:
    - Admin users: 
        - store_type_id must not be provided (will be ignored if sent)
    - Manager users:
        - store_type_id is required if setting/changing role
    - Staff users: 
        - store_type_id is required if setting/changing role
        - manager_id is required when updated by an Admin (must reference a valid manager)
    """
    # Allow users to update their own profile or admins to update any profile
    # Convert both IDs to strings for comparison
    current_user_id_str = str(current_user.id)
    if current_user.role != UserRole.ADMIN and current_user_id_str != user_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to update this user"
        )
        
    return await UserController.update_user(
        db=db,
        user_id=user_id,
        user_update=user_update,
        modified_by_id=current_user.id,
        request=request
    )

@router.delete("/{user_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_user(
    user_id: str, 
    request: Request,
    current_admin: User = Depends(get_current_active_admin),
    db: Session = Depends(get_db)
):
    """
    Soft-delete a user by ID (admin only)
    
    User record is not removed from the database, but is_deleted is set to True
    and the user can no longer log in.
    """
    return await UserController.delete_user(
        db=db,
        user_id=user_id,
        deleted_by_id=current_admin.id,
        request=request
    ) 