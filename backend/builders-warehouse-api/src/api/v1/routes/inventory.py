from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form, Query, Path as FastAPIPath, status, Header
from sqlalchemy.orm import Session
import logging
from sqlalchemy import or_, and_
import math
import os
import shutil
from pathlib import Path

from src.database import get_db
from src.models.user import User
from src.models.inventory import Inventory as InventoryModel  # Renamed to avoid conflict
from src.models.supplier import Supplier
from src.repository import inventory as inventory_crud
from src.schemas.inventory import Inventory, InventoryCreate, InventoryUpdate, CSVImportResult
from src.utils.file_importers import process_inventory_file
from src.api.v1.dependencies import get_current_active_user, get_current_user
from src.utils.audit_log import create_audit_log

router = APIRouter(prefix="/inventory", tags=["inventory"])
logger = logging.getLogger(__name__)

# Ensure the SKU images directory exists
SKU_IMAGES_DIR = Path("public/images/SKU")
SKU_IMAGES_DIR.mkdir(parents=True, exist_ok=True)

# POST routes
@router.post("/", response_model=Dict[str, Any], status_code=status.HTTP_201_CREATED)
async def create_inventory(
    inventory: InventoryCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Create a new inventory item
    """
    # Check if SKU already exists
    db_inventory = inventory_crud.get_inventory_by_sku(db, inventory.sku_code)
    if db_inventory:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Inventory with SKU {inventory.sku_code} already exists"
        )
    
    # Verify supplier exists if supplier_id is provided
    supplier_name = None
    if inventory.supplier_id:
        supplier = db.query(Supplier).filter(Supplier.id == inventory.supplier_id).first()
        if not supplier:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Supplier with ID {inventory.supplier_id} not found"
            )
        supplier_name = supplier.name
        
    # Create the inventory item
    created_inventory = inventory_crud.create_inventory(db, inventory, current_user.id)
    
    # Create response dictionary with only the needed fields
    response_dict = {
        "id": created_inventory.id,
        "sku_code": created_inventory.sku_code,
        "style_code": created_inventory.style_code,
        "supplier_id": created_inventory.supplier_id,
        "supplier_name": supplier_name,
        "carton": created_inventory.carton,
        "units_per_carton": created_inventory.units_per_carton,
        "carton_dimensions": created_inventory.carton_dimensions,
        "weight_per_unit": created_inventory.weight_per_unit,
        "weight_per_carton": created_inventory.weight_per_carton,
        "units_per_pallet": created_inventory.units_per_pallet,
        "pallet_weight": created_inventory.pallet_weight,
        "notes": created_inventory.notes,
        "created_at": created_inventory.created_at,
        "updated_at": created_inventory.updated_at
    }
    
    # Convert UUID objects to strings where needed
    if hasattr(created_inventory, "created_by") and created_inventory.created_by:
        response_dict["created_by"] = str(created_inventory.created_by)
    else:
        response_dict["created_by"] = None
        
    if hasattr(created_inventory, "updated_by") and created_inventory.updated_by:
        response_dict["updated_by"] = str(created_inventory.updated_by)
    else:
        response_dict["updated_by"] = None
    
    # Add optional fields if they exist
    if hasattr(created_inventory, "description"):
        response_dict["description"] = created_inventory.description
    
    if hasattr(created_inventory, "category"):
        response_dict["category"] = created_inventory.category
    
    if hasattr(created_inventory, "stock_on_hand"):
        response_dict["stock_on_hand"] = created_inventory.stock_on_hand
    
    if hasattr(created_inventory, "retail_price"):
        response_dict["retail_price"] = created_inventory.retail_price
    
    if hasattr(created_inventory, "cost_price"):
        response_dict["cost_price"] = created_inventory.cost_price
    
    if hasattr(created_inventory, "color"):
        response_dict["color"] = created_inventory.color
    
    if hasattr(created_inventory, "size"):
        response_dict["size"] = created_inventory.size
    
    return response_dict

@router.post("/upload-file", response_model=CSVImportResult)
async def upload_inventory_file(
    file: UploadFile = File(...),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Upload inventory items from a CSV or Excel file
    """
    # Process the file with db session for supplier lookup
    inventory_items, errors = await process_inventory_file(file, db)
    
    if not inventory_items and errors:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail={"message": "Error processing file", "errors": errors}
        )
    
    # Bulk create inventories
    success_count, bulk_errors = inventory_crud.bulk_create_inventories(
        db, inventory_items, current_user.id
    )
    
    # Combine errors from file processing and database operations
    all_errors = errors + bulk_errors
    
    return CSVImportResult(
        success=success_count,
        errors=len(all_errors),
        error_details=all_errors
    )

@router.post("/upload-images-bulk", status_code=status.HTTP_200_OK)
async def upload_sku_images(
    files: List[UploadFile] = File(...),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Upload up to 10 SKU images. Image filenames should match SKU codes (e.g., BB69033.png maps to SKU BB69033).
    """
    logger.info(f"Received {len(files) if files else 0} files for upload")
    
    if not files:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="No files were uploaded"
        )
    
    if len(files) > 10:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Maximum 10 files can be uploaded at once"
        )
    
    results = {
        "success": [],
        "errors": []
    }
    
    for file in files:
        # Extract the SKU code from the filename (remove extension)
        filename = file.filename
        if not filename:
            results["errors"].append({"file": "unknown", "error": "Missing filename"})
            continue
            
        sku_code = os.path.splitext(filename)[0]
        
        # Check if the SKU exists in inventory
        inventory_item = inventory_crud.get_inventory_by_sku(db, sku_code)
        if not inventory_item:
            results["errors"].append({"file": filename, "error": f"SKU {sku_code} not found in inventory"})
            continue
        
        try:
            # Save the file to the SKU images directory
            file_path = os.path.join(SKU_IMAGES_DIR, filename)
            
            # Read the file content
            content = await file.read()
            
            # Write the content to the destination file
            with open(file_path, "wb") as f:
                f.write(content)
                
            # Update the inventory item with the image path
            relative_image_path = f"images/SKU/{filename}"
            inventory_item.image_path = relative_image_path
            db.commit()
            db.refresh(inventory_item)
                
            results["success"].append({"file": filename, "sku": sku_code, "image_path": relative_image_path})
            
            # Log the upload
            create_audit_log(
                db,
                user_id=str(current_user.id),
                action="upload_sku_image",
                resource_type="inventory",
                resource_id=str(inventory_item.id),
                details={"message": f"Uploaded image for SKU {sku_code}", "filename": filename, "image_path": relative_image_path}
            )
            
        except Exception as e:
            logger.error(f"Error saving image {filename}: {str(e)}")
            results["errors"].append({"file": filename, "error": str(e)})
    
    return results

# GET routes - static paths first
@router.get("/items", response_model=Dict[str, Any])
async def list_inventory(
    skip: int = 0,
    limit: int = 100,
    search: Optional[str] = None,
    supplier_id: Optional[int] = None,
    sku_code: Optional[str] = None,
    style_code: Optional[str] = None,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user),
    authorization: Optional[str] = Header(None)
):
    """
    Get a list of inventory items with pagination and filtering
    """
    # Build the query with filters
    query = db.query(InventoryModel)
    
    # Apply filters
    filters = []
    
    if search:
        # Join with Supplier table for searching by supplier name
        query = query.join(Supplier, InventoryModel.supplier_id == Supplier.id, isouter=True)
        
        search_filter = or_(
            InventoryModel.sku_code.ilike(f"%{search}%"),
            InventoryModel.style_code.ilike(f"%{search}%"),
            InventoryModel.description.ilike(f"%{search}%") if hasattr(InventoryModel, "description") else False,
            Supplier.name.ilike(f"%{search}%")
        )
        filters.append(search_filter)
    
    if supplier_id:
        filters.append(InventoryModel.supplier_id == supplier_id)
    
    if sku_code:
        filters.append(InventoryModel.sku_code.ilike(f"%{sku_code}%"))
    
    if style_code:
        filters.append(InventoryModel.style_code.ilike(f"%{style_code}%"))
    
    # Apply all filters
    if filters:
        query = query.filter(and_(*filters))
    
    # Get count before pagination
    total_count = query.count()
    
    # Apply pagination and ordering
    inventory_items = query.order_by(InventoryModel.sku_code.asc()).offset(skip).limit(limit).all()
    
    # Format results
    results = []
    for item in inventory_items:
        # Get supplier information if supplier_id is set
        supplier_name = None
        if item.supplier_id:
            supplier = db.query(Supplier).filter(Supplier.id == item.supplier_id).first()
            if supplier:
                supplier_name = supplier.name
            
        result_item = {
            "id": item.id,
            "sku_code": item.sku_code,
            "style_code": item.style_code,
            "supplier_id": item.supplier_id,
            "supplier_name": supplier_name,
            "description": item.description if hasattr(item, "description") else None,
            "category": item.category if hasattr(item, "category") else None,
            "stock_on_hand": item.stock_on_hand if hasattr(item, "stock_on_hand") else None,
            "retail_price": item.retail_price if hasattr(item, "retail_price") else None,
            "cost_price": item.cost_price if hasattr(item, "cost_price") else None,
            "carton": item.carton,
            "units_per_carton": item.units_per_carton,
            "carton_dimensions": item.carton_dimensions,
            "weight_per_unit": item.weight_per_unit,
            "weight_per_carton": item.weight_per_carton,
            "units_per_pallet": item.units_per_pallet,
            "pallet_weight": item.pallet_weight,
            "color": item.color if hasattr(item, "color") else None,
            "size": item.size if hasattr(item, "size") else None,
            "notes": item.notes,
            "image_path": item.image_path if hasattr(item, "image_path") else None,
            "created_at": item.created_at,
            "updated_at": item.updated_at
        }
        results.append(result_item)
    
    return {
        "items": results,
        "total": total_count,
        "page": skip // limit + 1 if limit > 0 else 1,
        "pages": (total_count + limit - 1) // limit if limit > 0 else 1
    }

@router.get("/count")
async def count_inventories(
    supplier_id: Optional[int] = Query(None, description="Filter by supplier ID"),
    sku_code: Optional[str] = None,
    style_code: Optional[str] = None,
    search: Optional[str] = Query(None, description="Search by style_code, sku_code, or description"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Count inventory items with optional filtering
    
    Parameters:
    - supplier_id: Filter by supplier ID
    - sku_code: Filter by SKU code
    - style_code: Filter by style code
    - search: Search across style_code, sku_code, and description fields
    """
    total = inventory_crud.count_inventories(
        db, 
        supplier_id=supplier_id,
        sku_code=sku_code,
        style_code=style_code,
        search=search
    )
    return {"total": total}

@router.get("/", response_model=Dict[str, Any])
async def read_inventories(
    skip: Optional[int] = Query(0, ge=0),
    limit: Optional[int] = Query(100, ge=1, le=500),
    supplier_id: Optional[int] = Query(None, description="Filter by supplier ID"),
    sku_code: Optional[str] = None,
    style_code: Optional[str] = None,
    search: Optional[str] = Query(None, description="Search by style_code, sku_code, or description"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Get a list of inventory items with optional filtering
    
    Parameters:
    - skip: Number of records to skip
    - limit: Maximum number of records to return
    - supplier_id: Filter by supplier ID
    - sku_code: Filter by SKU code
    - style_code: Filter by style code
    - search: Search across style_code, sku_code, and description fields
    """
    if search:
        # Log the search action
        create_audit_log(
            db=db,
            user_id=str(current_user.id),
            action="search",
            resource_type="inventory",
            details={"search_term": search}
        )
    
    # Get total count for pagination
    total_count = inventory_crud.count_inventories(
        db,
        supplier_id=supplier_id,
        sku_code=sku_code,
        style_code=style_code,
        search=search
    )
    
    # Get the inventory items with pagination
    inventories = inventory_crud.get_inventories(
        db, 
        skip=skip, 
        limit=limit,
        supplier_id=supplier_id,
        sku_code=sku_code,
        style_code=style_code,
        search=search
    )
    
    # Format results to include supplier_name
    formatted_inventories = []
    for item in inventories:
        # Get supplier information if supplier_id is set
        supplier_name = None
        if item.supplier_id:
            supplier = db.query(Supplier).filter(Supplier.id == item.supplier_id).first()
            if supplier:
                supplier_name = supplier.name
        
        formatted_item = {
            "id": item.id,
            "sku_code": item.sku_code,
            "style_code": item.style_code,
            "supplier_id": item.supplier_id,
            "supplier_name": supplier_name,
            "carton": item.carton,
            "units_per_carton": item.units_per_carton,
            "carton_dimensions": item.carton_dimensions,
            "weight_per_unit": item.weight_per_unit,
            "weight_per_carton": item.weight_per_carton,
            "units_per_pallet": item.units_per_pallet,
            "pallet_weight": item.pallet_weight,
            "notes": item.notes,
            "created_at": item.created_at,
            "updated_at": item.updated_at
        }
        
        # Add optional fields if they exist
        if hasattr(item, "description"):
            formatted_item["description"] = item.description
        if hasattr(item, "category"):
            formatted_item["category"] = item.category
        if hasattr(item, "stock_on_hand"):
            formatted_item["stock_on_hand"] = item.stock_on_hand
        if hasattr(item, "retail_price"):
            formatted_item["retail_price"] = item.retail_price
        if hasattr(item, "cost_price"):
            formatted_item["cost_price"] = item.cost_price
        if hasattr(item, "color"):
            formatted_item["color"] = item.color
        if hasattr(item, "size"):
            formatted_item["size"] = item.size
        if hasattr(item, "image_path"):
            formatted_item["image_path"] = item.image_path
            
        formatted_inventories.append(formatted_item)
    
    # Calculate pagination values
    page = (skip // limit) + 1 if limit > 0 else 1
    pages = math.ceil(total_count / limit) if limit > 0 else 1
    
    # Return in standard pagination format
    return {
        "items": formatted_inventories,
        "total": total_count,
        "page": page,
        "pages": pages
    }

@router.get("/sku/{sku_code}", response_model=Dict[str, Any])
async def read_inventory_by_sku(
    sku_code: str,
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user),
    authorization: Optional[str] = Header(None)
):
    """
    Get inventory details by SKU code (enhanced version of the existing endpoint)
    """
    # Check if the SKU exists
    inventory_item = db.query(InventoryModel).filter(InventoryModel.sku_code == sku_code).first()
    
    if not inventory_item:
        raise HTTPException(status_code=404, detail=f"Inventory item with SKU {sku_code} not found")
    
    # Get supplier information if supplier_id is set
    supplier_name = None
    if inventory_item.supplier_id:
        supplier = db.query(Supplier).filter(Supplier.id == inventory_item.supplier_id).first()
        if supplier:
            supplier_name = supplier.name
    
    # Format response with additional fields
    result = {
        "id": inventory_item.id,
        "sku_code": inventory_item.sku_code,
        "style_code": inventory_item.style_code,
        "supplier_id": inventory_item.supplier_id,
        "supplier_name": supplier_name,
        "description": inventory_item.description if hasattr(inventory_item, "description") else None,
        "category": inventory_item.category if hasattr(inventory_item, "category") else None,
        "stock_on_hand": inventory_item.stock_on_hand if hasattr(inventory_item, "stock_on_hand") else None,
        "retail_price": inventory_item.retail_price if hasattr(inventory_item, "retail_price") else None,
        "cost_price": inventory_item.cost_price if hasattr(inventory_item, "cost_price") else None,
        "carton": inventory_item.carton,
        "units_per_carton": inventory_item.units_per_carton,
        "carton_dimensions": inventory_item.carton_dimensions,
        "weight_per_unit": inventory_item.weight_per_unit,
        "weight_per_carton": inventory_item.weight_per_carton,
        "units_per_pallet": inventory_item.units_per_pallet,
        "pallet_weight": inventory_item.pallet_weight,
        "color": inventory_item.color if hasattr(inventory_item, "color") else None,
        "size": inventory_item.size if hasattr(inventory_item, "size") else None,
        "notes": inventory_item.notes,
        "created_at": inventory_item.created_at,
        "updated_at": inventory_item.updated_at
    }
    
    return result

@router.get("/sku-image/{sku_code}", status_code=status.HTTP_200_OK)
async def get_sku_image_info(
    sku_code: str,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Check if an image exists for the given SKU code
    """
    # First check if the SKU exists in inventory
    inventory_item = inventory_crud.get_inventory_by_sku(db, sku_code)
    if not inventory_item:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"SKU {sku_code} not found in inventory"
        )
    
    # Check if image_path is stored in database
    if inventory_item.image_path:
        # Verify the file still exists on disk
        full_path = os.path.join("public", inventory_item.image_path)
        if os.path.exists(full_path):
            filename = os.path.basename(inventory_item.image_path)
            return {
                "sku": sku_code,
                "has_image": True,
                "image_path": f"/{inventory_item.image_path}",
                "filename": filename
            }
        else:
            # File missing from disk, clear the database path
            inventory_item.image_path = None
            db.commit()
    
    # Fallback: Check common image extensions in file system
    for ext in ['.jpg', '.jpeg', '.png', '.gif']:
        image_path = os.path.join(SKU_IMAGES_DIR, f"{sku_code}{ext}")
        if os.path.exists(image_path):
            # Update database with found image
            relative_path = f"images/SKU/{sku_code}{ext}"
            inventory_item.image_path = relative_path
            db.commit()
            
            return {
                "sku": sku_code,
                "has_image": True,
                "image_path": f"/{relative_path}",
                "filename": f"{sku_code}{ext}"
            }
    
    return {
        "sku": sku_code,
        "has_image": False
    }

# Parameterized routes last
@router.get("/{inventory_id}", response_model=Dict[str, Any])
async def read_inventory(
    inventory_id: int = FastAPIPath(..., ge=1),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Get an inventory item by ID
    """
    db_inventory = inventory_crud.get_inventory(db, inventory_id)
    if not db_inventory:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Inventory with ID {inventory_id} not found"
        )
    
    # Get supplier information
    supplier_name = None
    if db_inventory.supplier_id:
        supplier = db.query(Supplier).filter(Supplier.id == db_inventory.supplier_id).first()
        if supplier:
            supplier_name = supplier.name
    
    # Create response dictionary with only the needed fields
    response_dict = {
        "id": db_inventory.id,
        "sku_code": db_inventory.sku_code,
        "style_code": db_inventory.style_code,
        "supplier_id": db_inventory.supplier_id,
        "supplier_name": supplier_name,
        "carton": db_inventory.carton,
        "units_per_carton": db_inventory.units_per_carton,
        "carton_dimensions": db_inventory.carton_dimensions,
        "weight_per_unit": db_inventory.weight_per_unit,
        "weight_per_carton": db_inventory.weight_per_carton,
        "units_per_pallet": db_inventory.units_per_pallet,
        "pallet_weight": db_inventory.pallet_weight,
        "notes": db_inventory.notes,
        "created_at": db_inventory.created_at,
        "updated_at": db_inventory.updated_at
    }
    
    # Convert UUID objects to strings where needed
    if hasattr(db_inventory, "created_by") and db_inventory.created_by:
        response_dict["created_by"] = str(db_inventory.created_by)
    else:
        response_dict["created_by"] = None
        
    if hasattr(db_inventory, "updated_by") and db_inventory.updated_by:
        response_dict["updated_by"] = str(db_inventory.updated_by)
    else:
        response_dict["updated_by"] = None
    
    # Add optional fields if they exist
    if hasattr(db_inventory, "description"):
        response_dict["description"] = db_inventory.description
    
    if hasattr(db_inventory, "category"):
        response_dict["category"] = db_inventory.category
    
    if hasattr(db_inventory, "stock_on_hand"):
        response_dict["stock_on_hand"] = db_inventory.stock_on_hand
    
    if hasattr(db_inventory, "retail_price"):
        response_dict["retail_price"] = db_inventory.retail_price
    
    if hasattr(db_inventory, "cost_price"):
        response_dict["cost_price"] = db_inventory.cost_price
    
    if hasattr(db_inventory, "color"):
        response_dict["color"] = db_inventory.color
    
    if hasattr(db_inventory, "size"):
        response_dict["size"] = db_inventory.size
    
    if hasattr(db_inventory, "image_path"):
        response_dict["image_path"] = db_inventory.image_path
    
    return response_dict

@router.put("/{inventory_id}", response_model=Dict[str, Any])
async def update_inventory(
    inventory_id: int = FastAPIPath(..., ge=1),
    inventory: InventoryUpdate = None,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Update an existing inventory item
    """
    # Check if inventory exists
    db_inventory = inventory_crud.get_inventory(db, inventory_id)
    if not db_inventory:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Inventory with ID {inventory_id} not found"
        )
    
    # Verify supplier exists if supplier_id is provided
    if inventory.supplier_id:
        supplier = db.query(Supplier).filter(Supplier.id == inventory.supplier_id).first()
        if not supplier:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Supplier with ID {inventory.supplier_id} not found"
            )
    
    # Check if updating to an existing SKU
    if inventory.sku_code and inventory.sku_code != db_inventory.sku_code:
        existing = inventory_crud.get_inventory_by_sku(db, inventory.sku_code)
        if existing:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Inventory with SKU {inventory.sku_code} already exists"
            )
    
    # Update the inventory item
    updated_inventory = inventory_crud.update_inventory(db, inventory_id, inventory, current_user.id)
    
    # Get supplier information
    supplier_name = None
    if updated_inventory.supplier_id:
        supplier = db.query(Supplier).filter(Supplier.id == updated_inventory.supplier_id).first()
        if supplier:
            supplier_name = supplier.name
    
    # Create response dictionary with only the needed fields
    response_dict = {
        "id": updated_inventory.id,
        "sku_code": updated_inventory.sku_code,
        "style_code": updated_inventory.style_code,
        "supplier_id": updated_inventory.supplier_id,
        "supplier_name": supplier_name,
        "carton": updated_inventory.carton,
        "units_per_carton": updated_inventory.units_per_carton,
        "carton_dimensions": updated_inventory.carton_dimensions,
        "weight_per_unit": updated_inventory.weight_per_unit,
        "weight_per_carton": updated_inventory.weight_per_carton,
        "units_per_pallet": updated_inventory.units_per_pallet,
        "pallet_weight": updated_inventory.pallet_weight,
        "notes": updated_inventory.notes,
        "created_at": updated_inventory.created_at,
        "updated_at": updated_inventory.updated_at
    }
    
    # Convert UUID objects to strings where needed
    if hasattr(updated_inventory, "created_by") and updated_inventory.created_by:
        response_dict["created_by"] = str(updated_inventory.created_by)
    else:
        response_dict["created_by"] = None
        
    if hasattr(updated_inventory, "updated_by") and updated_inventory.updated_by:
        response_dict["updated_by"] = str(updated_inventory.updated_by)
    else:
        response_dict["updated_by"] = None
    
    # Add optional fields if they exist
    if hasattr(updated_inventory, "description"):
        response_dict["description"] = updated_inventory.description
    
    if hasattr(updated_inventory, "category"):
        response_dict["category"] = updated_inventory.category
    
    if hasattr(updated_inventory, "stock_on_hand"):
        response_dict["stock_on_hand"] = updated_inventory.stock_on_hand
    
    if hasattr(updated_inventory, "retail_price"):
        response_dict["retail_price"] = updated_inventory.retail_price
    
    if hasattr(updated_inventory, "cost_price"):
        response_dict["cost_price"] = updated_inventory.cost_price
    
    if hasattr(updated_inventory, "color"):
        response_dict["color"] = updated_inventory.color
    
    if hasattr(updated_inventory, "size"):
        response_dict["size"] = updated_inventory.size
    
    if hasattr(updated_inventory, "image_path"):
        response_dict["image_path"] = updated_inventory.image_path
    
    return response_dict

@router.delete("/{inventory_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_inventory(
    inventory_id: int = FastAPIPath(..., ge=1),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Delete an inventory item
    """
    success = inventory_crud.delete_inventory(db, inventory_id, current_user.id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Inventory with ID {inventory_id} not found"
        )
    return None 