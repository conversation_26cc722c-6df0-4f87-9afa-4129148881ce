from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List
import logging

from src.db.session import get_db
from src.models.user import User
from src.schemas.role import RoleResponse, RoleCreate, RoleUpdate, RoleListResponse
from src.utils.auth import get_current_active_admin
from src.controllers.role_controller import RoleController

logger = logging.getLogger(__name__)

# Create router with prefix and tags
router = APIRouter(
    prefix="/roles",
    tags=["Roles"]
)

@router.get("/", response_model=List[RoleResponse])
async def get_roles(
    db: Session = Depends(get_db)
):
    """
    Get all available roles
    
    Returns a list of all roles available in the system.
    This endpoint is used to populate role dropdowns in the frontend.
    """
    return RoleController.get_roles(db=db)

@router.post("/", response_model=RoleResponse, status_code=status.HTTP_201_CREATED)
async def create_role(
    role: RoleCreate,
    current_admin: User = Depends(get_current_active_admin),
    db: Session = Depends(get_db)
):
    """
    Create a new role (admin only)
    
    Requires:
    - name: Name of the role
    """
    return RoleController.create_role(db=db, role_data=role)

@router.get("/{role_id}", response_model=RoleResponse)
async def get_role(
    role_id: int,
    current_admin: User = Depends(get_current_active_admin),
    db: Session = Depends(get_db)
):
    """
    Get a specific role by ID (admin only)
    """
    return RoleController.get_role(db=db, role_id=role_id)

@router.put("/{role_id}", response_model=RoleResponse)
async def update_role(
    role_id: int,
    role_update: RoleUpdate,
    current_admin: User = Depends(get_current_active_admin),
    db: Session = Depends(get_db)
):
    """
    Update a role by ID (admin only)
    
    Updates can include:
    - name: Name of the role
    """
    return RoleController.update_role(db=db, role_id=role_id, role_update=role_update)

@router.delete("/{role_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_role(
    role_id: int,
    current_admin: User = Depends(get_current_active_admin),
    db: Session = Depends(get_db)
):
    """
    Delete a role by ID (admin only)
    
    Only roles that are not assigned to any users can be deleted.
    Default roles (admin, manager, staff) should not be deleted.
    """
    return RoleController.delete_role(db=db, role_id=role_id) 