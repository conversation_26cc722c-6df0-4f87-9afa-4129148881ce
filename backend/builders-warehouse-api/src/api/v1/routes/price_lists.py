from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, Query, status
from sqlalchemy.ext.asyncio import AsyncSession
from uuid import UUID
import logging

from src.database import get_async_db
from src.schemas.customer import PriceList, PriceListCreate
from src.services.price_list_service import PriceListService

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/price-lists", tags=["price_lists"])
price_list_service = PriceListService()

@router.get("/", response_model=List[PriceList])
async def list_price_lists(
    db: AsyncSession = Depends(get_async_db)
):
    """
    List all price lists.
    """
    try:
        price_lists = await price_list_service.list_price_lists(db)
        return price_lists
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))

@router.post("/", response_model=PriceList)
async def create_price_list(
    price_list: PriceListCreate,
    db: AsyncSession = Depends(get_async_db)
):
    """
    Create a new price list.
    """
    try:
        created_price_list = await price_list_service.create_price_list(db, price_list)
        return created_price_list
    except Exception as e:
        if "unique constraint" in str(e).lower():
            raise HTTPException(status_code=status.HTTP_409_CONFLICT, detail="Price list with this name already exists")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e))

@router.get("/{price_list_id}", response_model=PriceList)
async def get_price_list(
    price_list_id: UUID,
    db: AsyncSession = Depends(get_async_db)
):
    """
    Get a specific price list by ID.
    """
    try:
        price_list = await price_list_service.get_price_list_by_id(db, price_list_id)
        if not price_list:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Price list not found")
        return price_list
    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(e)) 