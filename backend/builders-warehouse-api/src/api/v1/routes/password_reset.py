from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
import logging

from src.database import get_db
from src.services.password_reset_service import PasswordResetService
from src.schemas.password_reset import (
    PasswordResetRequest,
    PasswordResetResponse,
    PasswordResetConfirm,
    PasswordResetConfirmResponse
)

logger = logging.getLogger(__name__)

# Create router with prefix and tags
router = APIRouter(
    prefix="/password-reset",
    tags=["Password Reset"]
)

@router.post("/request", response_model=PasswordResetResponse)
async def request_password_reset(
    request_data: PasswordResetRequest,
    db: Session = Depends(get_db)
):
    """
    Request password reset for a user by email
    
    This endpoint:
    1. Validates if the email exists in the database
    2. Generates a secure reset token
    3. Sends a password reset email with the token
    4. Returns a generic success message for security
    """
    return await PasswordResetService.request_password_reset(
        db=db,
        request_data=request_data,
        frontend_url="http://localhost:4200"  # Updated to match frontend port
    )

@router.post("/confirm", response_model=PasswordResetConfirmResponse)
async def confirm_password_reset(
    confirm_data: PasswordResetConfirm,
    db: Session = Depends(get_db)
):
    """
    Confirm password reset and update user password
    
    This endpoint:
    1. Validates the reset token
    2. Checks if the token is not expired and not used
    3. Updates the user's password
    4. Marks the token as used
    5. Invalidates all other tokens for the user
    """
    return await PasswordResetService.confirm_password_reset(
        db=db,
        confirm_data=confirm_data
    ) 