from fastapi import APIRouter, Depends, HTTPException, status
from typing import Any
import logging

from src.api.v1.dependencies import get_current_user
from src.controllers.company_controller import CompanyController
from src.schemas.company import CompanyHistoryRequest, CompanyHistoryResponse

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/companies",
    tags=["Companies"],
    dependencies=[Depends(get_current_user)]
)


@router.post("/history", response_model=CompanyHistoryResponse)
def get_company_history(request: CompanyHistoryRequest) -> Any:
    """
    Get history of invoices and quotes for a specific company.
    
    This endpoint returns a paginated list of invoices and quotes associated with a company.
    Each list item includes essential information like store type, date, grand total, notes, and type.
    
    - **company_id**: Required - UUID of the company to retrieve history for
    - **page**: Optional - Page number (default: 1)
    - **limit**: Optional - Number of items per page (default: 10, max: 100)
    
    Returns separate pagination data for invoices and quotes.
    """
    result = CompanyController.get_company_history(
        company_id=str(request.company_id),
        page=request.page,
        limit=request.limit
    )
    
    return result 