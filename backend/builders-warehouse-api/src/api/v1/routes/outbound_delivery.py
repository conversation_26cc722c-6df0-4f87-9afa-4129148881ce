from fastapi import APIRouter, Depends, HTTPException, Query, Path, status, Header
from sqlalchemy.orm import Session
from typing import Optional, Dict, Any, List
from datetime import datetime
import logging

from src.database import get_db
from src.models.outbound_delivery import OutboundDelivery, DeliveredOrder
from src.schemas.outbound_delivery import (
    OutboundDeliveryOut, DeliveredOrderOut, PagedOutboundDeliveryResponse, 
    PagedDeliveredOrderResponse, OutboundDeliveryCreate, OutboundDeliveryUpdate
)
from src.repository.outbound_delivery import (
    get_outbound_deliveries, update_outbound_delivery_status, get_delivered_orders,
    create_outbound_delivery
)
from src.api.v1.dependencies import get_current_user

logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/outbound",
    tags=["Outbound Delivery"],
    responses={404: {"description": "Not found"}},
)

@router.post("/", response_model=Dict[str, Any])
async def create_outbound_delivery(
    delivery: OutboundDeliveryCreate,
    authorization: Optional[str] = Header(None),
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Create a new outbound delivery record
    """
    try:
        # Check if the outbound delivery already exists
        existing_delivery = db.query(OutboundDelivery).filter(OutboundDelivery.invoice_no == delivery.invoice_no).first()
        if existing_delivery:
            raise HTTPException(status_code=400, detail=f"Outbound delivery with invoice number {delivery.invoice_no} already exists")
        
        # Create a new outbound delivery record using the schema
        new_delivery = OutboundDelivery(
            invoice_no=delivery.invoice_no,
            invoice_date=delivery.invoice_date,
            linked_po=delivery.linked_po,
            sku=delivery.sku,
            description=delivery.description,
            status=delivery.status,
            notes=delivery.notes
        )
        
        db.add(new_delivery)
        db.commit()
        db.refresh(new_delivery)
        
        return {
            "invoice_no": new_delivery.invoice_no,
            "invoice_date": new_delivery.invoice_date.isoformat(),
            "linked_po": new_delivery.linked_po,
            "sku": new_delivery.sku,
            "description": new_delivery.description,
            "status": new_delivery.status,
            "notes": new_delivery.notes
        }
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Error creating outbound delivery: {str(e)}")

@router.get("/", response_model=Dict[str, Any])
async def get_outbound_deliveries(
    status: Optional[str] = None,
    sku: Optional[str] = None,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    page: int = 1,
    limit: int = 10,
    authorization: Optional[str] = Header(None),
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get a list of outbound deliveries with pagination and filtering
    """
    try:
        # Build the query with filters
        query = db.query(OutboundDelivery)
        
        # Apply filters
        if status:
            query = query.filter(OutboundDelivery.status == status)
            
        if sku:
            query = query.filter(OutboundDelivery.sku.ilike(f"%{sku}%"))
            
        # Date range filters
        if start_date:
            try:
                start_date_obj = datetime.strptime(start_date, "%Y-%m-%d")
                query = query.filter(OutboundDelivery.invoice_date >= start_date_obj)
            except ValueError:
                raise HTTPException(status_code=400, detail="Invalid start_date format. Use YYYY-MM-DD")
                
        if end_date:
            try:
                end_date_obj = datetime.strptime(end_date, "%Y-%m-%d")
                query = query.filter(OutboundDelivery.invoice_date <= end_date_obj)
            except ValueError:
                raise HTTPException(status_code=400, detail="Invalid end_date format. Use YYYY-MM-DD")
        
        # Get total count before pagination
        total_count = query.count()
        
        # Apply pagination and ordering
        skip = (page - 1) * limit
        query = query.order_by(OutboundDelivery.invoice_date.desc())
        deliveries = query.offset(skip).limit(limit).all()
        
        # Format results to match the frontend expectations
        results = []
        for delivery in deliveries:
            results.append({
                "invoice_no": delivery.invoice_no,
                "invoice_date": delivery.invoice_date.strftime("%Y-%m-%d") if delivery.invoice_date else None,
                "linked_po": delivery.linked_po,
                "sku": delivery.sku,
                "description": delivery.description,
                "status": delivery.status,
                "notes": delivery.notes,
                "hasNotes": bool(delivery.notes)
            })
        
        return {
            "data": results,
            "pagination": {
                "total": total_count,
                "page": page,
                "limit": limit
            }
        }
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving outbound deliveries: {str(e)}")

@router.get("/delivered", response_model=Dict[str, Any])
async def get_delivered_orders(
    sku: Optional[str] = None,
    invoice_date: Optional[str] = None,
    page: int = 1,
    limit: int = 10,
    authorization: Optional[str] = Header(None),
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get a list of delivered orders with pagination and filtering
    """
    try:
        # Build the query with filters
        query = db.query(DeliveredOrder)
        
        # Apply filters
        if sku:
            query = query.filter(DeliveredOrder.sku.ilike(f"%{sku}%"))
            
        # Exact date filter
        if invoice_date:
            try:
                invoice_date_obj = datetime.strptime(invoice_date, "%Y-%m-%d")
                query = query.filter(DeliveredOrder.invoice_date == invoice_date_obj)
            except ValueError:
                raise HTTPException(status_code=400, detail="Invalid invoice_date format. Use YYYY-MM-DD")
        
        # Get total count before pagination
        total_count = query.count()
        
        # Apply pagination and ordering
        skip = (page - 1) * limit
        query = query.order_by(DeliveredOrder.delivered_date.desc())
        orders = query.offset(skip).limit(limit).all()
        
        # Format results to match the frontend expectations
        results = []
        for order in orders:
            results.append({
                "invoice_no": order.invoice_no,
                "invoice_date": order.invoice_date.strftime("%Y-%m-%d") if order.invoice_date else None,
                "linked_po": order.linked_po,
                "sku": order.sku,
                "description": order.description,
                "delivered_date": order.delivered_date.strftime("%Y-%m-%d") if order.delivered_date else None,
                "notes": order.notes,
                "hasNotes": bool(order.notes)
            })
        
        return {
            "data": results,
            "pagination": {
                "total": total_count,
                "page": page,
                "limit": limit
            }
        }
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving delivered orders: {str(e)}")

@router.patch("/{invoice_no}/status/", response_model=Dict[str, Any])
async def update_delivery_status(
    invoice_no: str,
    status_update: dict,
    authorization: Optional[str] = Header(None),
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Update the status of an outbound delivery
    """
    try:
        # Extract the new status from the request body
        new_status = status_update.get("status", None)
        
        if not new_status:
            raise HTTPException(status_code=400, detail="Status is required")
            
        # Find the outbound delivery
        delivery = db.query(OutboundDelivery).filter(OutboundDelivery.invoice_no == invoice_no).first()
        
        if not delivery:
            raise HTTPException(status_code=404, detail=f"Outbound delivery with invoice number {invoice_no} not found")
        
        # Update the status
        delivery.status = new_status
        
        # If status is "Delivered", move to delivered orders table
        if new_status == "Delivered":
            # Create a new delivered order record
            delivered_order = DeliveredOrder(
                invoice_no=delivery.invoice_no,
                invoice_date=delivery.invoice_date,
                linked_po=delivery.linked_po,
                sku=delivery.sku,
                description=delivery.description,
                delivered_date=datetime.now(),
                notes=delivery.notes
            )
            
            db.add(delivered_order)
            
            # Delete the outbound delivery record
            db.delete(delivery)
            
        db.commit()
        
        return {"message": f"Status updated to {new_status}"}
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Error updating delivery status: {str(e)}")

@router.patch("/{invoice_no}/notes/", response_model=Dict[str, Any])
async def update_delivery_notes(
    invoice_no: str,
    notes_update: dict,
    authorization: Optional[str] = Header(None),
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Update the notes of an outbound delivery
    """
    try:
        # Extract the notes from the request body
        notes = notes_update.get("notes", None)
        
        if notes is None:
            raise HTTPException(status_code=400, detail="Notes field is required")
            
        # Try to find in outbound deliveries first
        delivery = db.query(OutboundDelivery).filter(OutboundDelivery.invoice_no == invoice_no).first()
        
        if delivery:
            # Update the notes
            delivery.notes = notes
            db.commit()
            return {
                "invoice_no": delivery.invoice_no,
                "notes": delivery.notes,
                "hasNotes": bool(delivery.notes)
            }
        
        # If not found in outbound deliveries, check delivered orders
        delivered_order = db.query(DeliveredOrder).filter(DeliveredOrder.invoice_no == invoice_no).first()
        
        if delivered_order:
            # Update the notes
            delivered_order.notes = notes
            db.commit()
            return {
                "invoice_no": delivered_order.invoice_no,
                "notes": delivered_order.notes,
                "hasNotes": bool(delivered_order.notes)
            }
        
        # If not found in either table
        raise HTTPException(status_code=404, detail=f"Delivery with invoice number {invoice_no} not found")
        
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Error updating delivery notes: {str(e)}")

@router.get("/{invoice_no}/notes/", response_model=Dict[str, Any])
async def get_delivery_notes(
    invoice_no: str,
    authorization: Optional[str] = Header(None),
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Get the notes of an outbound delivery
    """
    try:
        # Try to find in outbound deliveries first
        delivery = db.query(OutboundDelivery).filter(OutboundDelivery.invoice_no == invoice_no).first()
        
        if delivery:
            return {
                "invoice_no": delivery.invoice_no,
                "notes": delivery.notes,
                "hasNotes": bool(delivery.notes)
            }
        
        # If not found in outbound deliveries, check delivered orders
        delivered_order = db.query(DeliveredOrder).filter(DeliveredOrder.invoice_no == invoice_no).first()
        
        if delivered_order:
            return {
                "invoice_no": delivered_order.invoice_no,
                "notes": delivered_order.notes,
                "hasNotes": bool(delivered_order.notes)
            }
        
        # If not found in either table
        raise HTTPException(status_code=404, detail=f"Delivery with invoice number {invoice_no} not found")
        
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error retrieving delivery notes: {str(e)}") 