from fastapi import APIRouter, Depends, HTTPException, Query, status, Path, Request, Body, Header
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any, Union
from datetime import date, datetime
import uuid
from pydantic import ValidationError
import logging

from src.database import get_db
from src.schemas.invoice import (
    InvoiceCreate, InvoiceUpdate, InvoiceOut, 
    InvoiceResponse, InvoicePagination
)
from src.repository.invoice import (
    create_invoice, get_invoices, get_invoice, update_invoice
)
from src.services.auditlog import log_action
from src.utils.auth import get_current_user
from src.schemas.user import UserResponse
from src.repository.store_type import get_store_type_by_name
from src.models.purchase_order import PurchaseOrder
from src.utils.audit_log import create_audit_log

router = APIRouter(
    prefix="/invoices",
    tags=["Invoices"],
    responses={404: {"description": "Not found"}},
)

logger = logging.getLogger(__name__)

# Create a new invoice with items
@router.post("", response_model=InvoiceOut, status_code=status.HTTP_201_CREATED)
def create_new_invoice(
    request_data: Dict[str, Any] = Body(...),
    db: Session = Depends(get_db),
    current_user: UserResponse = Depends(get_current_user),
    request: Request = None
):
    """
    Create a new invoice with items and optionally link to a purchase order
    
    The invoice can include:
    - Basic invoice information (customer, dates, notes)
    - An optional po_id field to link to an existing purchase order
    - A list of invoice line items
    - A dont_send_po flag to control PO email behavior
    
    When dont_send_po is true:
    - A draft email entry is created but not marked as ready to send
    
    When dont_send_po is false or not provided:
    - A draft email entry is created and marked as ready to send
    
    The response includes the created invoice with all calculated fields
    and the linked purchase order ID.
    """
    try:
        # Import StoreType here to avoid circular imports
        from src.models.store_type import StoreType
        
        # Process incoming data and transform it to match the schema
        processed_data = {}
        
        # Copy fields from request_data to processed_data
        for field in request_data:
            if field not in ['items', 'store_type']:
                processed_data[field] = request_data[field]
        
        # Handle store_type transformation
        if 'store_type' in request_data:
            store_type_data = request_data['store_type']
            if isinstance(store_type_data, dict) and 'name' in store_type_data:
                # Look up store type ID by name
                store_type = db.query(StoreType).filter(StoreType.name == store_type_data['name']).first()
                if store_type:
                    processed_data['store_type_id'] = store_type.id
                else:
                    # Default to first store type if not found
                    default_store = db.query(StoreType).first()
                    processed_data['store_type_id'] = default_store.id if default_store else 1
            elif isinstance(store_type_data, (str, int)):
                # If it's already an ID or string, try to convert
                processed_data['store_type_id'] = int(store_type_data)
        else:
            # Default store type
            processed_data['store_type_id'] = 1
        
        # Handle customer_id conversion (string to int)
        if 'customer_id' in processed_data:
            try:
                # Convert string customer ID to integer for the database
                customer_id_int = int(processed_data['customer_id'])
                processed_data['customer_id'] = customer_id_int
            except (ValueError, TypeError):
                processed_data['customer_id'] = 1  # Default customer
        else:
            processed_data['customer_id'] = 1
        
        # Handle company_id conversion 
        if 'company_id' in processed_data:
            try:
                # If it's a UUID string, convert to integer (simplified)
                company_id = processed_data['company_id']
                if isinstance(company_id, str) and len(company_id) > 10:
                    # It's likely a UUID, use default company ID
                    processed_data['company_id'] = 1
                else:
                    processed_data['company_id'] = int(company_id)
            except (ValueError, TypeError):
                processed_data['company_id'] = 1
        else:
            processed_data['company_id'] = 1
        
        # Determine status based on notes content properly
        status_value = "saved"  # Default to saved
        if 'notes' in processed_data and processed_data['notes']:
            # Check if notes start with [DRAFT] (case insensitive)
            notes_content = str(processed_data['notes']).strip()
            if notes_content.upper().startswith('[DRAFT]'):
                status_value = "draft"
        
        # Process items if present
        if 'items' in request_data and isinstance(request_data['items'], list):
            processed_data['items'] = []
            for item in request_data['items']:
                # Skip empty items
                if not item or not item.get('sku') or not item.get('description'):
                    continue
                    
                # Validate quantity fields
                has_quantity = False
                for qty_field in ['units', 'boxes', 'pieces', 'm2']:
                    if qty_field in item and item[qty_field] and float(item[qty_field]) > 0:
                        has_quantity = True
                        break
                        
                if has_quantity:
                    processed_data['items'].append(item)
                    
        # Check if po_id is provided and validate it
        if 'po_id' in processed_data and processed_data['po_id']:
            # Validate that the purchase order exists
            purchase_order = db.query(PurchaseOrder).filter(PurchaseOrder.id == processed_data['po_id']).first()
            if not purchase_order:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"Purchase order with ID {processed_data['po_id']} not found"
                )
        
        # Parse the processed data into the InvoiceCreate model
        invoice_data = InvoiceCreate(**processed_data)
        
        # Create invoice using current user's store type
        db_invoice = create_invoice(
            db=db, 
            invoice_data=invoice_data, 
            user_id=current_user.id,
            user_store_type=current_user.store_type.name if current_user.store_type else None
        )
        
        # Set the correct status based on our determination
        db_invoice.status = status_value
        
        db.commit()
        db.refresh(db_invoice)
        
        # Create a dictionary to override non-serializable attributes
        invoice_dict = {
            "id": str(db_invoice.id),
            "invoice_number": {
                "id": str(db_invoice.id),
                "formatted_number": db_invoice.invoice_number
            },
            "store_type_id": getattr(db_invoice, "store_type_id", 1),
            "store_type": {
                "id": getattr(db_invoice, "store_type_id", 1),
                "name": getattr(db_invoice, "invoice_type", "trade")
            },
            "sale_type": getattr(db_invoice, "invoice_type", "trade"),
            "mode_of_payment": "cash",  # Default
            "customer_id": str(db_invoice.customer_id),
            "company_id": str(db_invoice.company_id),
            "company": {
                "id": str(db_invoice.company_id),
                "name": "Builders Warehouse"  # Default company name
            },
            "date": db_invoice.invoice_date,
            "notes": db_invoice.notes,
            "status": db_invoice.status,  # Use the correctly set status
            "po_id": getattr(db_invoice, "po_id", None),
            "items": db_invoice.items.get("items", []) if hasattr(db_invoice, "items") and db_invoice.items else [],
            "total_order": getattr(db_invoice, "total_order", 0),
            "shipping": getattr(db_invoice, "shipping", 0),
            "total_gst": getattr(db_invoice, "total_gst", 0),
            "grand_total": getattr(db_invoice, "grand_total", 0),
            "created_at": getattr(db_invoice, "created_at", None),
            "updated_at": getattr(db_invoice, "updated_at", None)
        }
        
        # Log the action
        log_action(
            db=db,
            email=current_user.email,
            event_type="CREATE",
            status="success",
            details={
                "entity_type": "invoice",
                "entity_id": str(db_invoice.id),
                "dont_send_po": getattr(invoice_data, "dont_send_po", False),
                "po_id": getattr(db_invoice, "po_id", None),
                "is_draft": status_value == "draft"
            }
        )
        
        return invoice_dict
    
    except ValidationError as ve:
        logger.error(f"Validation error creating invoice: {str(ve)}")
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=str(ve)
        )
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error creating invoice: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create invoice: {str(e)}"
        )

# Get all invoices with filtering and pagination
@router.get("", response_model=InvoicePagination)
def read_invoices(
    db: Session = Depends(get_db),
    current_user: UserResponse = Depends(get_current_user),
    skip: int = Query(0, ge=0, description="Number of items to skip"),
    limit: int = Query(100, ge=1, le=100, description="Number of items to return"),
    customer_id: Optional[uuid.UUID] = Query(None, description="Filter by customer ID"),
    store_type: Optional[str] = Query(None, description="Filter by store type"),
    start_date: Optional[str] = Query(None, description="Filter by start date (YYYY-MM-DD)"),
    end_date: Optional[str] = Query(None, description="Filter by end date (YYYY-MM-DD)"),
    search: Optional[str] = Query(None, description="Search by invoice number or customer name"),
):
    """
    Get all invoices with filtering and pagination
    
    Parameters:
    - skip: Number of items to skip (for pagination)
    - limit: Maximum number of items to return
    - customer_id: Filter by customer ID
    - store_type: Filter by store type
    - start_date: Filter by start date (YYYY-MM-DD)
    - end_date: Filter by end date (YYYY-MM-DD)
    - search: Search by invoice number or customer company name
    """
    # If search parameter is provided, log the search action
    if search:
        create_audit_log(
            db=db,
            user_id=str(current_user.id) if current_user else None,
            action="search",
            resource_type="invoices",
            details={"search_term": search}
        )
    
    # Debug log the raw date parameters
    logger.debug(f"Raw date parameters: start_date={start_date}, end_date={end_date}")
    
    # Parse string dates to date objects if they exist
    parsed_start_date = None
    parsed_end_date = None
    
    if start_date:
        try:
            # Try to parse as YYYY-MM-DD
            parsed_start_date = date.fromisoformat(start_date)
            print(f"Parsed start_date: {parsed_start_date}")
        except ValueError as e:
            print(f"Error parsing start_date: {e}")
            raise HTTPException(
                status_code=400, 
                detail=f"Invalid start_date format. Expected YYYY-MM-DD, got {start_date}"
            )
    
    if end_date:
        try:
            # Try to parse as YYYY-MM-DD
            parsed_end_date = date.fromisoformat(end_date) 
            print(f"Parsed end_date: {parsed_end_date}")
        except ValueError as e:
            print(f"Error parsing end_date: {e}")
            raise HTTPException(
                status_code=400, 
                detail=f"Invalid end_date format. Expected YYYY-MM-DD, got {end_date}"
            )

    # Call the repository function with the parsed dates
    invoices, total = get_invoices(
        db, 
        skip=skip, 
        limit=limit,
        customer_id=customer_id,
        store_type=store_type,
        start_date=parsed_start_date,
        end_date=parsed_end_date,
        search=search
    )
    
    # Debug log the results
    print(f"Found {total} invoices with date range: {parsed_start_date} to {parsed_end_date}")
    
    return {
        "items": invoices,
        "total": total,
        "page": skip // limit + 1,
        "size": limit
    }

# Get a single invoice by ID
@router.get("/{invoice_id}", response_model=InvoiceOut)
def read_invoice(
    invoice_id: str = Path(..., description="The ID of the invoice to get"),
    db: Session = Depends(get_db),
    current_user: UserResponse = Depends(get_current_user)
):
    """Get an invoice by ID with details, including linked PO information"""
    # Get invoice as a dictionary
    invoice_dict = get_invoice(db, invoice_id=invoice_id)
    
    if invoice_dict is None:
        raise HTTPException(status_code=404, detail="Invoice not found")
    
    # Format invoice_number as expected by the schema
    formatted_invoice_number = {
        "id": str(invoice_dict["id"]),
        "formatted_number": invoice_dict["invoice_number"]
    }
    
    # Extract additional fields from items JSON if available
    items_data = invoice_dict.get("items", {})
    if isinstance(items_data, dict):
        metadata = items_data.get("metadata", {})
        items_list = items_data.get("items", [])
    else:
        metadata = {}
        items_list = []
    
    # Create a dictionary that matches the expected response schema
    result = {
        "id": str(invoice_dict["id"]),
        "invoice_number": formatted_invoice_number,
        "store_type_id": 1,  # Default store type ID
        "store_type": {
            "id": 1,
            "name": invoice_dict["invoice_type"] or "trade"
        },
        "sale_type": invoice_dict["invoice_type"] or "trade",
        "mode_of_payment": "cash",  # Default - could be enhanced to store actual payment mode
        "customer_id": str(invoice_dict["customer_id"]),
        "company_id": str(invoice_dict["company_id"]),
        "company": {
            "id": str(invoice_dict["company_id"]),
            "name": invoice_dict["customer"].get("name") or "Builders Warehouse"
        },
        "date": invoice_dict["invoice_date"],
        "notes": invoice_dict["notes"],
        "po_id": None,  # We don't have po_id in the returned data
        "items": items_list,
        "total_order": float(invoice_dict.get("grand_total", 0)) or 0,
        "shipping": 0,  # Default - could be enhanced to store actual shipping
        "total_gst": float(invoice_dict["total_gst"]) if invoice_dict["total_gst"] else 0,
        "grand_total": float(invoice_dict["grand_total"]) if invoice_dict["grand_total"] else 0,
        "credit_card_surcharge": "0",  # Default - could be enhanced
        "created_at": invoice_dict["created_at"],
        "updated_at": invoice_dict["updated_at"],
        # Add missing fields that the frontend expects
        "deliver_to_address": "",  # Default - could be enhanced to store actual address
        "purchase_order_number": "",  # Default - could be enhanced
        "dont_send_po": False,  # Default
        "linked_quote_id": None  # Default - could be enhanced
    }
    
    return result

# Update an invoice
@router.put("/{invoice_id}", response_model=InvoiceOut)
def update_invoice_by_id(
    invoice_update: InvoiceUpdate,
    invoice_id: str = Path(..., description="The ID of the invoice to update"),
    db: Session = Depends(get_db),
    current_user: UserResponse = Depends(get_current_user),
    request: Request = None
):
    """
    Update an invoice with PO and draft mail handling
    
    Will recalculate all financial totals if relevant fields change:
    - Items
    - Payment method (credit card surcharge)
    - Shipping
    
    If po_id is provided, it will validate that the purchase order exists and:
    1. Update the invoice to reference that PO
    2. Update any existing draft mails to reference the new PO
    
    If dont_send_po is changed:
    1. Any existing draft emails will be updated to match the new setting
    2. If no draft emails exist but a PO exists, a new draft email will be created
    
    The po_id field enables cross-referencing between invoices and purchase orders,
    allowing for improved tracking and reporting of order fulfillment and billing workflows.
    """
    # Call the repository function to update the invoice
    updated_invoice = update_invoice(
        db, 
        invoice_id=invoice_id, 
        invoice_update=invoice_update,
        user_id=current_user.id
    )
    
    if updated_invoice is None:
        raise HTTPException(status_code=404, detail="Invoice not found")
    
    # Format invoice_number as expected by the schema
    formatted_invoice_number = {
        "id": str(updated_invoice["id"]),
        "formatted_number": updated_invoice["invoice_number"]
    }
    
    # Create a dictionary that matches the expected response schema
    result = {
        "id": str(updated_invoice["id"]),
        "invoice_number": formatted_invoice_number,
        "store_type_id": 1,  # Default store type ID
        "store_type": {
            "id": 1,
            "name": updated_invoice["invoice_type"] or "trade"
        },
        "sale_type": updated_invoice["invoice_type"] or "trade",
        "mode_of_payment": updated_invoice.get("mode_of_payment", "cash"),
        "po_id": None,  # We don't have po_id in the returned data
        "customer_id": str(updated_invoice["customer_id"]),
        "company_id": str(updated_invoice["company_id"]),
        "company": {
            "id": str(updated_invoice["company_id"]),
            "name": updated_invoice["customer"].get("name") or "Builders Warehouse"
        },
        "date": updated_invoice["invoice_date"],
        "notes": updated_invoice["notes"],
        "items": updated_invoice["items"].get("items", []) if isinstance(updated_invoice["items"], dict) else [],
        "total_order": 0,  # Default
        "shipping": 0,  # Default
        "total_gst": updated_invoice["total_gst"] or 0,
        "grand_total": updated_invoice["grand_total"] or 0,
        "created_at": updated_invoice["created_at"],
        "updated_at": updated_invoice["updated_at"]
    }
    
    # Log the action
    log_action(
        db=db,
        email=current_user.email,
        event_type="UPDATE",
        status="success",
        details={
            "entity_type": "invoice",
            "entity_id": str(invoice_id),
            "po_id": None,  # We don't have po_id in the returned data
            "dont_send_po": getattr(invoice_update, "dont_send_po", None)
        },
        ip_address=request.client.host if request and request.client else None
    )
    
    return result 

# Direct implementation for listing invoices
@router.get("/direct")
async def direct_list_invoices(
    skip: int = 0,
    limit: int = 100,
    customer_id: Optional[str] = None,
    store_type: Optional[str] = None,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    search: Optional[str] = None,
    authorization: Optional[str] = Header(None)
):
    """Direct implementation for listing invoices"""
    try:
        # Log the request
        logger.info(f"Raw invoice listing endpoint called with skip={skip}, limit={limit}, search={search}")
        
        # Get DB session directly
        from src.database import SessionLocal
        db = SessionLocal()
        
        try:
            # Check if token is provided
            if not authorization or not authorization.startswith('Bearer '):
                logger.warning("No Authorization header or invalid Bearer token format")
                return JSONResponse(
                    status_code=401,
                    content={"detail": "Not authenticated"}
                )
                
            # Process the invoice listing
            try:
                # Import necessary components
                from src.repository.invoice import get_invoices
                from src.models.quote import Quote
                from sqlalchemy import or_
                from datetime import datetime, date
                
                # Convert date strings to date objects if provided
                start_date_obj = None
                end_date_obj = None
                
                if start_date:
                    start_date_obj = datetime.strptime(start_date, "%Y-%m-%d").date()
                    
                if end_date:
                    end_date_obj = datetime.strptime(end_date, "%Y-%m-%d").date()
                
                # Convert customer_id to UUID if provided
                customer_uuid = None
                if customer_id:
                    try:
                        import uuid
                        customer_uuid = uuid.UUID(customer_id)
                    except ValueError:
                        # If not a valid UUID, it will be None
                        pass
                
                # Get invoices
                invoices, total = get_invoices(
                    db=db,
                    skip=skip,
                    limit=limit,
                    customer_id=customer_uuid,
                    store_type=store_type,
                    start_date=start_date_obj,
                    end_date=end_date_obj,
                    search=search
                )

                # Log a few invoices for debugging
                logger.info(f"Got {len(invoices)} invoices from database")
                if invoices:
                    logger.info(f"First invoice fields: {list(invoices[0].keys())}")
                    logger.info(f"First invoice: {invoices[0]}")
                    
                    # Check for linked_quote_id specifically
                    for i, invoice in enumerate(invoices[:3]):
                        if 'linked_quote_id' in invoice:
                            logger.info(f"Invoice {i+1} has linked_quote_id: {invoice['linked_quote_id']}")
                        else:
                            logger.info(f"Invoice {i+1} does NOT have linked_quote_id")
                            
                    # Make sure linked_quote_id is explicitly added to each invoice object
                    for invoice in invoices:
                        if hasattr(invoice, 'linked_quote_id') and invoice.linked_quote_id:
                            invoice['linked_quote_id'] = str(invoice.linked_quote_id)
                            logger.info(f"Added linked_quote_id {invoice['linked_quote_id']} to invoice dict")
                        
                        # If invoice is a dictionary, check if it has _asdict method (SQLAlchemy result object)
                        if hasattr(invoice, '_asdict'):
                            # Convert to regular dictionary
                            invoice_dict = invoice._asdict()
                            if 'linked_quote_id' in invoice_dict and invoice_dict['linked_quote_id']:
                                invoice['linked_quote_id'] = str(invoice_dict['linked_quote_id'])
                                logger.info(f"Added linked_quote_id from _asdict: {invoice['linked_quote_id']}")
                
                # Make one final check and force the linked_quote_id into the response
                linked_quotes_count = 0
                for invoice in invoices:
                    # Convert to dictionary if not already
                    if not isinstance(invoice, dict):
                        try:
                            invoice = dict(invoice._mapping)
                        except (AttributeError, TypeError):
                            continue
                    
                    # Try to access linked_quote_id by string key
                    if 'Invoice_linked_quote_id' in invoice and invoice['Invoice_linked_quote_id']:
                        invoice['linked_quote_id'] = str(invoice['Invoice_linked_quote_id'])
                        linked_quotes_count += 1
                
                logger.info(f"Final linked quotes count: {linked_quotes_count}")
                
                # Additionally, get converted quotes that might not have corresponding invoices
                try:
                    # Query for quotes with status='converted'
                    from src.models.quote import Quote
                    
                    # Direct query approach that worked in the debug endpoint
                    converted_quotes_query = db.query(Quote).filter(Quote.status == "converted")
                    converted_quotes = converted_quotes_query.all()
                    
                    logger.info(f"Raw converted quotes found: {len(converted_quotes)}")
                    
                    # Format the converted quotes as invoices
                    for quote in converted_quotes:
                        # Check if already in invoices list (by linked_quote_id)
                        if any(str(inv.get('linked_quote_id', '')) == str(quote.id) for inv in invoices):
                            logger.info(f"Quote {quote.id} already in invoices list, skipping")
                            continue
                            
                        try:
                            # Get customer name directly from the relationship
                            customer_name = "Unknown"
                            if hasattr(quote, 'customer') and quote.customer is not None:
                                customer_name = quote.customer.company_name
                                
                            # Create an invoice-like dictionary
                            quote_invoice = {
                                "id": f"q-{quote.id}",  # Prefix with 'q-' to distinguish from real invoices
                                "invoice_number": {
                                    "id": f"q-{quote.id}",
                                    "formatted_number": f"CONV-{quote.quote_number}"
                                },
                                "invoice_no": f"CONV-{quote.quote_number}",
                                "customer_id": str(quote.customer_id) if quote.customer_id else "",
                                "customer_name": customer_name,
                                "company_id": str(quote.company_id) if quote.company_id else "1",
                                "company_name": "Default Company",
                                "store_type_id": 1,
                                "store_type_name": quote.store_type or "default",
                                "store_type": quote.store_type or "default",
                                "po_id": None,
                                "date": quote.quote_date.isoformat() if quote.quote_date else None,
                                "grand_total": float(quote.grand_total) if quote.grand_total else 0.0,
                                "notes": f"Converted from Quote {quote.quote_number}. {quote.notes or ''}",
                                "linked_quote_id": str(quote.id),
                                "is_from_quote": True
                            }
                            
                            # Add to invoices list
                            invoices.append(quote_invoice)
                            total += 1
                            logger.info(f"Added converted quote {quote.id} to invoices list")
                            
                        except Exception as quote_formatting_error:
                            logger.error(f"Error formatting quote {quote.id}: {str(quote_formatting_error)}")
                    
                except Exception as quote_error:
                    logger.error(f"Error fetching converted quotes: {str(quote_error)}")
                    import traceback
                    logger.error(f"Detailed error: {traceback.format_exc()}")
                    
                # Log the final count
                logger.info(f"Total invoices after adding converted quotes: {len(invoices)}")
                
                # Return paginated response
                return {
                    "items": invoices,
                    "total": total,
                    "page": (skip // limit) + 1 if limit > 0 else 1,
                    "size": limit
                }
                
            except Exception as e:
                logger.error(f"Error listing invoices: {str(e)}")
                import traceback
                logger.error(f"Detailed error: {traceback.format_exc()}")
                return JSONResponse(
                    status_code=500,
                    content={"detail": f"Internal server error: {str(e)}"}
                )
                
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"Exception in invoice listing endpoint: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={"detail": f"Server error: {str(e)}"}
        )

# Direct implementation to fix the linked quotes issue more directly
@router.get("/with-quotes")
async def direct_list_invoices_with_quotes(
    skip: int = 0,
    limit: int = 100,
    customer_id: Optional[str] = None,
    store_type: Optional[str] = None,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    search: Optional[str] = None,
    authorization: Optional[str] = Header(None)
):
    """Direct implementation for listing invoices including linked quotes"""
    try:
        # Log the request
        logger.info(f"Raw invoices with quotes endpoint called")
        
        # Get DB session directly
        from src.database import SessionLocal
        db = SessionLocal()
        
        try:
            # Check if token is provided
            if not authorization or not authorization.startswith('Bearer '):
                logger.warning("No Authorization header or invalid Bearer token format")
                return JSONResponse(
                    status_code=401,
                    content={"detail": "Not authenticated"}
                )
                
            # Import necessary components
            from src.models.invoice import Invoice
            from src.models.customer import Customer
            from sqlalchemy import or_, desc
            
            # Build the query
            query = db.query(Invoice, Customer).join(Customer, Invoice.customer_id == Customer.id)
            
            # Apply pagination
            query = query.order_by(desc(Invoice.invoice_date))
            
            # Get total count
            total_count = query.count()
            
            # Apply pagination
            results = query.offset(skip).limit(limit).all()
            
            # Format results
            invoices = []
            for invoice, customer in results:
                # Create invoice dict with all necessary fields
                invoice_dict = {
                    "id": invoice.id,
                    "invoice_number": {
                        "id": str(invoice.id),
                        "formatted_number": invoice.invoice_number
                    },
                    "invoice_no": invoice.invoice_number,
                    "customer_id": str(invoice.customer_id),
                    "customer_name": customer.company_name,
                    "company_id": str(invoice.company_id),
                    "company_name": "Builders Warehouse",
                    "store_type_id": 1,
                    "store_type_name": invoice.invoice_type,
                    "store_type": invoice.invoice_type,
                    "po_id": invoice.po_id,
                    "date": invoice.invoice_date.isoformat() if invoice.invoice_date else None,
                    "grand_total": float(invoice.grand_total) if invoice.grand_total else 0.0,
                    "notes": invoice.notes
                }
                
                # Explicitly add linked_quote_id
                if invoice.linked_quote_id:
                    invoice_dict["linked_quote_id"] = invoice.linked_quote_id
                    logger.info(f"Found invoice {invoice.invoice_number} linked to quote {invoice.linked_quote_id}")
                
                invoices.append(invoice_dict)
            
            # Return paginated response
            return {
                "items": invoices,
                "total": total_count,
                "page": (skip // limit) + 1 if limit > 0 else 1,
                "size": limit
            }
            
        except Exception as e:
            logger.error(f"Error listing invoices with quotes: {str(e)}")
            import traceback
            logger.error(f"Detailed error: {traceback.format_exc()}")
            return JSONResponse(
                status_code=500,
                content={"detail": f"Internal server error: {str(e)}"}
            )
            
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"Exception in invoices with quotes endpoint: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={"detail": f"Server error: {str(e)}"}
        ) 