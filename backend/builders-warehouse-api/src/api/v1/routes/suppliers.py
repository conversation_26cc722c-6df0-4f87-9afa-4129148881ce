from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status, Query, Path, Request, Header
from sqlalchemy.orm import Session
import logging
from sqlalchemy import text, or_, and_
import math

from src.database import get_db
from src.models.user import User
from src.models.supplier import Supplier
from src.repository import supplier as supplier_crud
from src.models.customer import PriceList
from src.schemas.supplier import SupplierCreate, SupplierOut, SupplierUpdate, PaginatedSupplierResponse
from src.api.v1.dependencies import get_current_active_user, get_current_user
from src.utils.audit_log import create_audit_log

router = APIRouter(prefix="/suppliers", tags=["suppliers"])
logger = logging.getLogger(__name__)

@router.post("/", response_model=SupplierOut)
def create_supplier(
    supplier: SupplierCreate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Create a new supplier
    
    This endpoint allows users to create a new supplier with detailed information including:
    - supplier_name: The name of the supplier
    - phone_no: Contact phone number
    - email: Contact email address
    - address: Physical address of the supplier
    - price_list: Optional list of price items
    """
    try:
        # Log the incoming data
        logger.info(f"Creating supplier with name: {supplier.supplier_name}")
        
        # Create the supplier with the data 
        db_supplier = supplier_crud.create_supplier(db, supplier, current_user.id)
        
        return db_supplier
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error creating supplier: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create supplier: {str(e)}"
        )

@router.get("/", response_model=Dict[str, Any])
async def read_suppliers(
    skip: int = Query(0, ge=0, description="Number of records to skip"),
    limit: int = Query(10, ge=1, le=100, description="Number of records per page"),
    search: Optional[str] = Query(None, description="Search by company_name, email, or phone"),
    is_active: Optional[bool] = None,
    authorization: Optional[str] = Header(None),
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user)
):
    """
    Get a list of suppliers with optional filtering and pagination
    
    This endpoint retrieves a list of suppliers with optional filtering by:
    - search: Search term to match against supplier name, email, or phone number
    - is_active: Filter by supplier active status
    - skip: Number of records to skip (for pagination)
    - limit: Maximum number of records per page (default: 10)
    
    Results are sorted alphabetically by supplier name.
    """
    try:
        # Build the query with filters
        query = db.query(Supplier)
        
        # Apply filters
        filters = []
        
        if search:
            search_filter = or_(
                Supplier.name.ilike(f"%{search}%"),
                Supplier.email.ilike(f"%{search}%"),
                Supplier.phone.ilike(f"%{search}%")
            )
            filters.append(search_filter)
            
            # Log search action
            create_audit_log(
                db=db,
                user_id=str(current_user.id) if current_user else 'unknown',
                action="search",
                resource_type="suppliers",
                details={"search_term": search}
            )
        
        if is_active is not None:
            filters.append(Supplier.is_active == is_active)
        
        # Apply all filters
        if filters:
            query = query.filter(and_(*filters))
        
        # Get total count before pagination
        total_count = query.count()
        
        # Apply pagination and ordering - ensure alphabetical order by name
        query = query.order_by(Supplier.name.asc())
        suppliers = query.offset(skip).limit(limit).all()
        
        # Format the results
        results = []
        for supplier in suppliers:
            # Format price list if available
            price_list = []
            if hasattr(supplier, 'price_list') and supplier.price_list:
                try:
                    # Handle price_list based on its type
                    if isinstance(supplier.price_list, list):
                        price_list = supplier.price_list
                    elif isinstance(supplier.price_list, str):
                        import json
                        price_list = json.loads(supplier.price_list)
                except Exception as e:
                    logger.error(f"Error parsing price list for supplier {supplier.id}: {str(e)}")
                    price_list = []
            
            supplier_data = {
                "id": supplier.id,
                "supplier_name": supplier.name,
                "name": supplier.name,  # For backward compatibility
                "email": supplier.email,
                "phone_no": supplier.phone,
                "phone": supplier.phone,  # For backward compatibility
                "address": supplier.address,
                "price_list": price_list,
                "is_active": supplier.is_active,
                "created_at": supplier.created_at,
                "updated_at": supplier.updated_at
            }
            results.append(supplier_data)
        
        # Calculate page number
        page = (skip // limit) + 1 if limit > 0 else 1
        
        return {
            "items": results,
            "total": total_count,
            "page": page,
            "pages": math.ceil(total_count / limit) if limit > 0 else 1
        }
    except Exception as e:
        logger.error(f"Error retrieving suppliers: {str(e)}")
        # Return empty result set instead of an error
        return {
            "items": [],
            "total": 0,
            "page": 1,
            "pages": 0
        }

@router.get("/{supplier_id}", response_model=SupplierOut)
async def read_supplier(
    supplier_id: int = Path(..., ge=1),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Get a specific supplier by ID
    
    This endpoint retrieves detailed information about a specific supplier including their price list.
    """
    try:
        logger.info(f"Getting supplier with ID: {supplier_id}")
        
        # Validate that the ID is a positive integer
        if supplier_id <= 0:
            logger.warning(f"Invalid supplier ID: {supplier_id}")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid supplier ID: {supplier_id}. ID must be a positive integer."
            )
        
        supplier = supplier_crud.get_supplier(db, supplier_id)
        
        if not supplier:
            logger.warning(f"Supplier with ID {supplier_id} not found")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Supplier with ID {supplier_id} not found"
            )
        
        # Ensure supplier data is properly formatted for frontend
        if not hasattr(supplier, 'supplier_name') or not supplier.supplier_name:
            supplier.supplier_name = supplier.name
            
        if not hasattr(supplier, 'phone_no') or not supplier.phone_no:
            supplier.phone_no = supplier.phone
            
        logger.info(f"Successfully retrieved supplier: {supplier.name}")
        return supplier
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error retrieving supplier with ID {supplier_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error retrieving supplier: {str(e)}"
        )

@router.put("/{supplier_id}", response_model=SupplierOut)
async def update_supplier(
    supplier_id: int,
    supplier_data: SupplierUpdate,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Update a supplier
    
    This endpoint allows users to update an existing supplier's information.
    """
    # Check if supplier exists
    db_supplier = supplier_crud.get_supplier(db, supplier_id)
    if not db_supplier:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Supplier with ID {supplier_id} not found"
        )
    
    # If updating supplier name, check if it would conflict with another supplier
    if supplier_data.supplier_name and supplier_data.supplier_name != getattr(db_supplier, 'name', ''):
        existing = supplier_crud.get_supplier_by_name(db, supplier_data.supplier_name)
        if existing and existing.id != supplier_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Supplier with name '{supplier_data.supplier_name}' already exists"
            )
    
    updated_supplier = supplier_crud.update_supplier(
        db, supplier_id, supplier_data, current_user.id
    )
    return updated_supplier

@router.delete("/{supplier_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_supplier(
    supplier_id: int,
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Delete a supplier
    
    This endpoint soft-deletes a supplier by setting is_active to False.
    """
    success = supplier_crud.delete_supplier(db, supplier_id, current_user.id)
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=f"Supplier with ID {supplier_id} not found"
        )
    return None

@router.post("/fix-table", status_code=status.HTTP_200_OK)
async def fix_supplier_table(
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Fix the supplier table structure
    
    This endpoint runs maintenance tasks to ensure proper structure.
    """
    try:
        # Import the fix function
        from fix_supplier_table import fix_supplier_table
        
        # Run the fix
        result = fix_supplier_table()
        
        if result:
            return {"status": "success", "message": "Supplier table fixed successfully"}
        else:
            return {"status": "error", "message": "Failed to fix supplier table"}
    except Exception as e:
        logger.error(f"Error fixing supplier table: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to fix supplier table: {str(e)}"
        ) 