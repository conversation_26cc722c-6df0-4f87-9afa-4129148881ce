from fastapi import APIRouter
from src.api.v1.routes.auth import router as auth_router
from src.api.v1.routes.users import router as users_router
from src.api.v1.routes.quotes import router as quotes_router
from src.api.v1.routes.customers import router as customers_router
from src.api.v1.routes.purchase_orders import router as purchase_orders_router
from src.api.v1.routes.invoices import router as invoices_router
from src.api.v1.routes.inventory import router as inventory_router
from src.api.v1.routes.store_types import router as store_types_router
from src.api.v1.routes.suppliers import router as suppliers_router
from src.api.v1.routes.inbound_delivery import router as inbound_delivery_router
from src.api.v1.routes.outbound_delivery import router as outbound_delivery_router
from src.api.v1.routes.companies import router as companies_router
from src.api.v1.routes.reports_v2 import router as reports_router
from src.api.v1.routes.price_lists import router as price_lists_router
from src.api.v1.routes.roles import router as roles_router
from src.api.v1.routes.search import router as search_router
from src.api.v1.routes.password_reset import router as password_reset_router
from src.api.v1.routes.debug import router as debug_router
from src.config import get_settings

# Get application settings
settings = get_settings()

# Main API router
router = APIRouter()

# Include all route modules
router.include_router(auth_router)
router.include_router(users_router)
router.include_router(quotes_router)
# Fix quotes router to work without trailing slash
quotes_router.prefix = quotes_router.prefix.rstrip('/')

# Use the customers router (which now uses the unified service)
router.include_router(customers_router)
# Also register an explicit route without trailing slash
customers_router.prefix = customers_router.prefix.rstrip('/')

router.include_router(purchase_orders_router)
# Fix purchase_orders router to work without trailing slash
purchase_orders_router.prefix = purchase_orders_router.prefix.rstrip('/')

router.include_router(invoices_router)
router.include_router(inventory_router)
# Fix inventory router to work without trailing slash
inventory_router.prefix = inventory_router.prefix.rstrip('/')

router.include_router(store_types_router)
# Fix store_types router to work without trailing slash
store_types_router.prefix = store_types_router.prefix.rstrip('/')

# Include suppliers router and ensure correct prefix
router.include_router(suppliers_router)
# Also register an explicit route without trailing slash for suppliers
suppliers_router.prefix = suppliers_router.prefix.rstrip('/')

router.include_router(inbound_delivery_router)
router.include_router(outbound_delivery_router)
router.include_router(companies_router)
router.include_router(reports_router)
router.include_router(price_lists_router)

# Add roles router with both formats (with and without trailing slash)
router.include_router(roles_router)
# Set prefix without trailing slash to ensure both routes work
roles_router.prefix = roles_router.prefix.rstrip('/')

router.include_router(search_router)
router.include_router(password_reset_router)

# Include debug router
router.include_router(debug_router)

__all__ = ["router"] 