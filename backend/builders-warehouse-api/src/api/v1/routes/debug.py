from typing import Optional
from fastapi import APIRouter, Header
from fastapi.responses import JSONResponse
import logging

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/debug", tags=["debug"])


# Testing endpoint to check converted quotes directly
@router.get("/converted-quotes")
async def debug_converted_quotes(
    authorization: Optional[str] = Header(None)
):
    """Debug endpoint to directly check for quotes with 'converted' status"""
    try:
        # Check if token is provided
        if not authorization or not authorization.startswith('Bearer '):
            logger.warning("No Authorization header or invalid Bearer token format")
            return JSONResponse(
                status_code=401,
                content={"detail": "Not authenticated"}
            )
            
        # Get DB session directly
        from src.database import SessionLocal
        db = SessionLocal()
        
        try:
            # Import necessary components
            from src.models.quote import Quote
            
            # Query for quotes with status='converted'
            converted_quotes_query = db.query(Quote).filter(Quote.status == "converted")
            converted_quotes = converted_quotes_query.all()
            
            logger.info(f"DEBUG: Found {len(converted_quotes)} quotes with status 'converted'")
            
            # Format the results
            result = []
            for quote in converted_quotes:
                customer_name = quote.customer.company_name if hasattr(quote, 'customer') and quote.customer else "Unknown"
                
                quote_data = {
                    "id": quote.id,
                    "quote_number": quote.quote_number,
                    "customer_id": quote.customer_id,
                    "customer_name": customer_name,
                    "company_id": quote.company_id,
                    "date": quote.quote_date.isoformat() if quote.quote_date else None,
                    "grand_total": float(quote.grand_total) if quote.grand_total else 0.0,
                    "notes": quote.notes,
                    "status": quote.status
                }
                
                result.append(quote_data)
            
            return {
                "total": len(result),
                "items": result
            }
            
        except Exception as e:
            logger.error(f"Error in debug endpoint: {str(e)}")
            import traceback
            logger.error(f"Detailed error: {traceback.format_exc()}")
            return JSONResponse(
                status_code=500,
                content={"detail": f"Internal server error: {str(e)}"}
            )
            
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"Exception in debug endpoint: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={"detail": f"Server error: {str(e)}"}
        )


# Testing endpoint to check linked_quote_id in Invoice table
@router.get("/linked-invoices")
async def debug_linked_invoices(
    authorization: Optional[str] = Header(None)
):
    """Debug endpoint to check linked_quote_id values in the Invoice table"""
    try:
        # Check if token is provided
        if not authorization or not authorization.startswith('Bearer '):
            logger.warning("No Authorization header or invalid Bearer token format")
            return JSONResponse(
                status_code=401,
                content={"detail": "Not authenticated"}
            )
            
        # Get DB session directly
        from src.database import SessionLocal
        from sqlalchemy import text
        db = SessionLocal()
        
        try:
            # Import necessary components
            from src.models.invoice import Invoice
            
            # Query for invoices with linked_quote_id
            linked_invoices_query = db.query(Invoice).filter(Invoice.linked_quote_id.isnot(None))
            linked_invoices = linked_invoices_query.all()
            
            logger.info(f"DEBUG: Found {len(linked_invoices)} invoices with linked_quote_id values")
            
            # Format the results
            result = []
            for invoice in linked_invoices:
                invoice_data = {
                    "id": invoice.id,
                    "invoice_number": invoice.invoice_number,
                    "linked_quote_id": invoice.linked_quote_id,
                    "customer_id": invoice.customer_id,
                    "date": invoice.invoice_date.isoformat() if invoice.invoice_date else None,
                    "grand_total": float(invoice.grand_total) if invoice.grand_total else 0.0,
                    "notes": invoice.notes
                }
                
                result.append(invoice_data)
            
            # Also check the database directly
            sql_query = text("SELECT id, invoice_number, linked_quote_id FROM \"Invoice\" WHERE linked_quote_id IS NOT NULL")
            direct_query = db.execute(sql_query)
            direct_results = [{"id": row[0], "invoice_number": row[1], "linked_quote_id": row[2]} for row in direct_query]
            
            return {
                "total": len(result),
                "items": result,
                "direct_query": {
                    "total": len(direct_results),
                    "items": direct_results
                }
            }
            
        except Exception as e:
            logger.error(f"Error in debug endpoint: {str(e)}")
            import traceback
            logger.error(f"Detailed error: {traceback.format_exc()}")
            return JSONResponse(
                status_code=500,
                content={"detail": f"Internal server error: {str(e)}"}
            )
            
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"Exception in debug endpoint: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={"detail": f"Server error: {str(e)}"}
        )


# Debug endpoint for direct invoice access
@router.get("/invoices-raw")
async def debug_invoices_raw(
    authorization: Optional[str] = Header(None)
):
    """Debug endpoint to directly access invoice data with linked_quote_id"""
    try:
        # Check if token is provided
        if not authorization or not authorization.startswith('Bearer '):
            logger.warning("No Authorization header or invalid Bearer token format")
            return JSONResponse(
                status_code=401,
                content={"detail": "Not authenticated"}
            )
            
        # Get DB session directly
        from src.database import SessionLocal
        db = SessionLocal()
        
        try:
            # Import necessary components
            from src.models.invoice import Invoice
            from src.models.customer import Customer
            
            # Query directly with minimum processing
            invoices = []
            
            # Direct query using model instances
            query = db.query(Invoice, Customer).join(Customer, Invoice.customer_id == Customer.id)
            results = query.limit(5).all()
            
            for invoice, customer in results:
                invoice_dict = {
                    "id": invoice.id,
                    "invoice_number": invoice.invoice_number,
                    "customer_name": customer.company_name,
                    "invoice_date": invoice.invoice_date.isoformat() if invoice.invoice_date else None,
                    "linked_quote_id": invoice.linked_quote_id,
                    "grand_total": float(invoice.grand_total) if invoice.grand_total else 0.0,
                    "notes": invoice.notes
                }
                invoices.append(invoice_dict)
            
            # Direct SQL approach
            from sqlalchemy import text
            sql = text("""
                SELECT i.id, i.invoice_number, i.linked_quote_id, c.company_name 
                FROM "Invoice" i 
                JOIN "Customer" c ON i.customer_id = c.id 
                LIMIT 5
            """)
            
            sql_results = []
            for row in db.execute(sql):
                sql_results.append({
                    "id": row[0],
                    "invoice_number": row[1],
                    "linked_quote_id": row[2],
                    "customer_name": row[3]
                })
            
            return {
                "invoices": invoices,
                "sql_results": sql_results
            }
            
        except Exception as e:
            logger.error(f"Error in debug invoices raw endpoint: {str(e)}")
            import traceback
            logger.error(f"Detailed error: {traceback.format_exc()}")
            return JSONResponse(
                status_code=500,
                content={"detail": f"Internal server error: {str(e)}"}
            )
            
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"Exception in debug invoices raw endpoint: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={"detail": f"Server error: {str(e)}"}
        )


# Debug endpoint to check all quotes
@router.get("/all-quotes")
async def debug_all_quotes(
    authorization: Optional[str] = Header(None)
):
    """Debug endpoint to check all quotes including draft ones"""
    try:
        # Check if token is provided
        if not authorization or not authorization.startswith('Bearer '):
            logger.warning("No Authorization header or invalid Bearer token format")
            return JSONResponse(
                status_code=401,
                content={"detail": "Not authenticated"}
            )
            
        # Get DB session directly
        from src.database import SessionLocal
        db = SessionLocal()
        
        try:
            # Import necessary components
            from src.models.quote import Quote
            
            # Query for all quotes, ordered by ID descending to see newest first
            all_quotes_query = db.query(Quote).order_by(Quote.id.desc())
            all_quotes = all_quotes_query.limit(20).all()  # Limit to last 20 quotes
            
            logger.info(f"DEBUG: Found {len(all_quotes)} quotes total")
            
            # Format the results
            result = []
            for quote in all_quotes:
                customer_name = quote.customer.company_name if hasattr(quote, 'customer') and quote.customer else "Unknown"
                
                quote_data = {
                    "id": quote.id,
                    "quote_number": quote.quote_number,
                    "customer_id": quote.customer_id,
                    "customer_name": customer_name,
                    "company_id": quote.company_id,
                    "date": quote.quote_date.isoformat() if quote.quote_date else None,
                    "grand_total": float(quote.grand_total) if quote.grand_total else 0.0,
                    "notes": quote.notes,
                    "status": quote.status,
                    "created_at": quote.created_at.isoformat() if quote.created_at else None
                }
                
                result.append(quote_data)
            
            return {
                "total": len(result),
                "items": result
            }
            
        except Exception as e:
            logger.error(f"Error in debug all quotes endpoint: {str(e)}")
            import traceback
            logger.error(f"Detailed error: {traceback.format_exc()}")
            return JSONResponse(
                status_code=500,
                content={"detail": f"Internal server error: {str(e)}"}
            )
            
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"Exception in debug all quotes endpoint: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={"detail": f"Server error: {str(e)}"}
        ) 