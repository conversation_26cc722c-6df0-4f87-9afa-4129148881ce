from fastapi import APIRouter, Depends, HTTPException, Query, status, Path, Header
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
from datetime import date, datetime
import logging
from sqlalchemy import or_, and_, func

from src.database import get_db
from src.models.purchase_order import PurchaseOrder
from src.models.supplier import Supplier
from src.schemas.purchase_order import (
    PurchaseOrderCreate, PurchaseOrderUpdate, PurchaseOrderOut, 
    PurchaseOrderEmailStatusUpdate, PurchaseOrderDetailNotesUpdate,
    PurchaseOrderDetailUpdate, PurchaseOrderPagination, PurchaseOrderStatus,
    PurchaseOrderDetailOut
)
from src.repository.purchase_order import (
    create_purchase_order, get_purchase_orders, get_purchase_order,
    update_purchase_order, delete_purchase_order, update_purchase_order_email_status,
    update_purchase_order_detail_notes, update_purchase_order_detail,
    get_purchase_order_detail
)
from src.utils.audit_log import create_audit_log
from src.models.user import User
from src.api.v1.dependencies import get_current_user, get_current_active_user
from src.services.email_service import EmailService

router = APIRouter(
    prefix="/purchase-orders",
    tags=["Purchase Orders"],
    responses={404: {"description": "Not found"}},
)
logger = logging.getLogger(__name__)

# Create a new purchase order with details
@router.post("", response_model=PurchaseOrderOut, status_code=status.HTTP_201_CREATED)
def create_new_purchase_order(
    po: PurchaseOrderCreate,
    db: Session = Depends(get_db)
):
    """Create a new purchase order with details"""
    return create_purchase_order(db=db, po=po)

# Get all purchase orders with filtering and pagination
@router.get("", response_model=Dict[str, Any])
async def read_purchase_orders(
    skip: int = 0,
    limit: int = 10,
    supplier_name: Optional[str] = None,
    store_type: Optional[str] = None,
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    status: Optional[str] = None,
    search: Optional[str] = None,
    current_user: dict = Depends(get_current_user),
    authorization: Optional[str] = Header(None),
    db: Session = Depends(get_db)
):
    """
    Get a list of purchase orders with pagination and filtering
    """
    # Build the query with joins
    query = db.query(PurchaseOrder).join(Supplier, PurchaseOrder.supplier_id == Supplier.id, isouter=True)
    
    # Apply filters
    filters = []
    
    if supplier_name:
        filters.append(Supplier.name.ilike(f"%{supplier_name}%"))
    
    # Note: store_type filtering is not supported as it's stored in JSON field
    # if store_type:
    #     filters.append(PurchaseOrder.store_type == store_type)
    
    if status:
        filters.append(PurchaseOrder.status == status)
    
    # Date filters
    if start_date:
        try:
            start_date_obj = datetime.strptime(start_date, "%Y-%m-%d")
            filters.append(PurchaseOrder.order_date >= start_date_obj)
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid start_date format. Use YYYY-MM-DD")
    
    if end_date:
        try:
            end_date_obj = datetime.strptime(end_date, "%Y-%m-%d")
            filters.append(PurchaseOrder.order_date <= end_date_obj)
        except ValueError:
            raise HTTPException(status_code=400, detail="Invalid end_date format. Use YYYY-MM-DD")
    
    # Search filter
    if search:
        search_filter = or_(
            PurchaseOrder.po_number.ilike(f"%{search}%"),
            Supplier.name.ilike(f"%{search}%")
        )
        filters.append(search_filter)
    
    # Apply all filters
    if filters:
        query = query.filter(and_(*filters))
    
    # Get total count before pagination
    total_count = query.count()
    
    # Apply pagination and ordering
    query = query.order_by(PurchaseOrder.order_date.desc())
    purchase_orders = query.offset(skip).limit(limit).all()
    
    # Format results
    results = []
    for po in purchase_orders:
        # Get supplier name from the relationship
        supplier_name = po.supplier.name if po.supplier else "Unknown"
        
        # Get items from the PurchaseOrder's details property
        items = po.details if hasattr(po, 'details') and po.details else []
        
        # Calculate totals using the correct field names that are actually stored
        total_quantity = sum(item.get('quantity_ordered', 0) for item in items)
        total_amount = sum(item.get('total', 0) for item in items)
        
        results.append({
            "id": po.id,
            "po_number": po.po_number,
            "date": po.order_date.strftime("%Y-%m-%d") if po.order_date else None,
            "supplier_id": po.supplier_id,
            "supplier_name": supplier_name,
            "status": po.status,
            "store_type": po.store_type,
            "email_sent": po.email_sent,
            "email_status": "SENT" if po.email_sent else "NOT_SENT",
            "notes": None,  # Notes are not stored directly in the model
            "total_quantity": total_quantity,
            "total_amount": float(po.total_amount) if po.total_amount else 0.0,
            "created_at": po.created_at,
            "updated_at": po.updated_at
        })
    
    return {
        "items": results,
        "total": total_count,
        "page": skip // limit + 1 if limit > 0 else 1,
        "pages": (total_count + limit - 1) // limit if limit > 0 else 1
    }

# Get a single purchase order by ID
@router.get("/{po_id}", response_model=Dict[str, Any])
async def read_purchase_order(
    po_id: int,
    current_user: dict = Depends(get_current_user),
    authorization: Optional[str] = Header(None),
    db: Session = Depends(get_db)
):
    """
    Get details of a specific purchase order by ID
    """
    # Query the purchase order
    purchase_order = db.query(PurchaseOrder).filter(PurchaseOrder.id == po_id).first()
    
    if not purchase_order:
        raise HTTPException(status_code=404, detail=f"Purchase order with ID {po_id} not found")
    
    # Get supplier details
    supplier = db.query(Supplier).filter(Supplier.id == purchase_order.supplier_id).first()
    supplier_name = supplier.name if supplier else "Unknown"
    
    # Get items from the PurchaseOrder's details property
    po_items = purchase_order.details if hasattr(purchase_order, 'details') else []
    
    # Format items - use the correct field names that are actually stored
    items = []
    for item in po_items:
        items.append({
            "id": item.get('id', None),
            "sku": item.get('sku', None),
            "description": item.get('description', None),
            "quantity_ordered": item.get('quantity_ordered', 0),
            "quantity_received": item.get('quantity_received', 0),
            "expected_delivery_date": item.get('expected_delivery_date', None),
            "total": item.get('total', 0),
            "notes": item.get('notes', None),
            # Also include legacy fields for backwards compatibility
            "quantity": item.get('quantity_ordered', 0),  # Legacy field
            "unit_price": item.get('unit_price', 0),      # Legacy field
            "total_price": item.get('total', 0)           # Legacy field
        })
    
    # Calculate totals using the correct field names
    total_quantity = sum(item.get('quantity_ordered', 0) for item in po_items)
    total_amount = sum(item.get('total', 0) for item in po_items)
    
    # Format response
    result = {
        "id": purchase_order.id,
        "po_number": purchase_order.po_number,
        "date": purchase_order.order_date.strftime("%Y-%m-%d") if purchase_order.order_date else None,
        "supplier_id": purchase_order.supplier_id,
        "supplier_name": supplier_name,
        "status": purchase_order.status,
        "store_type": purchase_order.store_type,
        "email_sent": purchase_order.email_sent,
        "email_status": "SENT" if purchase_order.email_sent else "NOT_SENT",
        "notes": None,  # Notes are not stored directly in the model
        "items": items,
        "details": items,  # Include both for compatibility
        "total_quantity": total_quantity,
        "total_amount": float(purchase_order.total_amount) if purchase_order.total_amount else 0.0,
        "created_at": purchase_order.created_at,
        "updated_at": purchase_order.updated_at
    }
    
    return result

# Update a purchase order
@router.put("/{po_id}", response_model=PurchaseOrderOut)
def update_purchase_order_by_id(
    po_update: PurchaseOrderUpdate,
    po_id: int = Path(..., description="The ID of the purchase order to update"),
    db: Session = Depends(get_db)
):
    """Update a purchase order"""
    db_po = update_purchase_order(db, po_id=po_id, po_update=po_update)
    if db_po is None:
        raise HTTPException(status_code=404, detail="Purchase order not found")
    return db_po

# Delete a purchase order
@router.delete("/{po_id}", status_code=status.HTTP_204_NO_CONTENT)
def delete_purchase_order_by_id(
    po_id: int = Path(..., description="The ID of the purchase order to delete"),
    db: Session = Depends(get_db)
):
    """Delete a purchase order"""
    deleted = delete_purchase_order(db, po_id=po_id)
    if not deleted:
        raise HTTPException(status_code=404, detail="Purchase order not found")
    return None

# Update purchase order email status
@router.patch("/{po_id}/email-status", response_model=PurchaseOrderOut)
def update_email_status(
    email_status: PurchaseOrderEmailStatusUpdate,
    po_id: int = Path(..., description="The ID of the purchase order to update"),
    db: Session = Depends(get_db)
):
    """Update the email status of a purchase order"""
    db_po = update_purchase_order_email_status(db, po_id=po_id, email_status=email_status)
    if db_po is None:
        raise HTTPException(status_code=404, detail="Purchase order not found")
    return db_po

# Update purchase order detail
@router.patch("/details/{detail_id}", response_model=PurchaseOrderDetailOut)
def update_detail(
    detail_update: PurchaseOrderDetailUpdate,
    detail_id: int = Path(..., description="The ID of the purchase order detail to update"),
    db: Session = Depends(get_db)
):
    """Update a purchase order detail"""
    db_detail = update_purchase_order_detail(db, detail_id=detail_id, detail_update=detail_update)
    if db_detail is None:
        raise HTTPException(status_code=404, detail="Purchase order detail not found")
    return db_detail

# Update purchase order detail notes
@router.patch("/details/{detail_id}/notes", response_model=PurchaseOrderDetailOut)
def update_detail_notes(
    notes_update: PurchaseOrderDetailNotesUpdate,
    detail_id: int = Path(..., description="The ID of the purchase order detail to update"),
    db: Session = Depends(get_db)
):
    """Update the notes of a purchase order detail"""
    db_detail = update_purchase_order_detail_notes(db, detail_id=detail_id, notes_update=notes_update)
    if db_detail is None:
        raise HTTPException(status_code=404, detail="Purchase order detail not found")
    return db_detail

# Send purchase order email to supplier
@router.post("/{po_id}/send-email", response_model=Dict[str, Any])
async def send_purchase_order_email(
    po_id: int = Path(..., description="The ID of the purchase order to send"),
    current_user: dict = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """Send purchase order details via email to the supplier"""
    try:
        # Get the purchase order
        purchase_order = db.query(PurchaseOrder).filter(PurchaseOrder.id == po_id).first()
        
        if not purchase_order:
            raise HTTPException(status_code=404, detail=f"Purchase order with ID {po_id} not found")
        
        # Check if email has already been sent for this PO
        if purchase_order.email_sent:
            return {
                "success": False,
                "message": f"Purchase order email has already been sent to the supplier",
                "email_sent": True,
                "email_status": "ALREADY_SENT",
                "supplier_email": None,
                "po_number": purchase_order.po_number,
                "already_sent": True
            }
        
        # Get supplier details
        supplier = db.query(Supplier).filter(Supplier.id == purchase_order.supplier_id).first()
        
        if not supplier:
            raise HTTPException(status_code=404, detail="Supplier not found for this purchase order")
        
        if not supplier.email:
            raise HTTPException(status_code=400, detail="Supplier email address not available")
        
        # Prepare purchase order data for email
        po_data = {
            "id": purchase_order.id,
            "po_number": purchase_order.po_number,
            "date": purchase_order.order_date.strftime("%Y-%m-%d") if purchase_order.order_date else None,
            "order_date": purchase_order.order_date.strftime("%Y-%m-%d") if purchase_order.order_date else None,
            "total_amount": float(purchase_order.total_amount) if purchase_order.total_amount else 0.0,
            "payment_terms": purchase_order.payment_terms or "Net 30 Days",
            "details": purchase_order.details if hasattr(purchase_order, 'details') else [],
            "items": purchase_order.details if hasattr(purchase_order, 'details') else []
        }
        
        # Send the email
        email_sent = await EmailService.send_purchase_order_email(
            to_email=supplier.email,
            supplier_name=supplier.name,
            po_data=po_data
        )
        
        if email_sent:
            # Update the email status in the database
            email_status = PurchaseOrderEmailStatusUpdate(
                email_sent=True,
                email_sent_message="Email sent successfully"
            )
            
            db_po = update_purchase_order_email_status(db, po_id=po_id, email_status=email_status)
            
            return {
                "success": True,
                "message": f"Purchase order email sent successfully to {supplier.email}",
                "email_sent": True,
                "email_status": "SENT",
                "supplier_email": supplier.email,
                "po_number": purchase_order.po_number
            }
        else:
            # Update the email status to indicate failure
            email_status = PurchaseOrderEmailStatusUpdate(
                email_sent=False,
                email_sent_message="Failed to send email"
            )
            
            update_purchase_order_email_status(db, po_id=po_id, email_status=email_status)
            
            raise HTTPException(
                status_code=500, 
                detail="Failed to send purchase order email. Please try again."
            )
            
    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        logger.error(f"Error sending purchase order email for PO {po_id}: {str(e)}")
        raise HTTPException(
            status_code=500,
            detail=f"Internal server error while sending email: {str(e)}"
        ) 