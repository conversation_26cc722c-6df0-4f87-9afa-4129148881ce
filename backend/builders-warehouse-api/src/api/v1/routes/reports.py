from fastapi import APIRouter, Depends, HTTPException, status, Query, File, UploadFile, BackgroundTasks, Response
from fastapi.responses import StreamingResponse
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
import logging
from datetime import date

from src.database import get_db
from src.models.report import SalesReport, POReport
from src.schemas.report import (
    SalesReportOut, POReportOut, ReportType, ReportDeleteRequest,
    ReportPagination, ReportCSVUploadResponse
)
from src.repository.report import (
    get_last_n_sales, get_last_n_pos, delete_sales_reports, delete_po_reports,
    generate_sales_csv, generate_po_csv, process_sales_csv, process_po_csv,
    sync_reports_from_db
)
from src.utils.audit_log import create_audit_log

# Setup logger
logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/reports",
    tags=["Reports"],
    responses={404: {"description": "Not found"}},
)

# Get last n sales and pos
@router.get("", response_model=Dict[str, Any])
def get_reports(
    report_type: Optional[ReportType] = Query(None, description="Type of report to get (sales or po)"),
    n: int = Query(30, ge=1, le=30, description="Number of records to get, maximum 30"),
    db: Session = Depends(get_db)
):
    """Get the last n sales and/or purchase order reports, limited to 30 entries max"""
    result = {}
    
    if report_type is None or report_type == ReportType.SALES:
        sales = get_last_n_sales(db, n)
        result["sales"] = sales
    
    if report_type is None or report_type == ReportType.PO:
        pos = get_last_n_pos(db, n)
        result["pos"] = pos
    
    return result

# Sync reports from database data
@router.post("/sync", response_model=Dict[str, int])
def sync_reports(
    db: Session = Depends(get_db)
):
    """Sync reports from actual invoice and purchase order data in the database"""
    sales_count, po_count = sync_reports_from_db(db)
    
    # Log the sync operation to audit log
    create_audit_log(
        db, 
        user_id=None,  # This would typically be the authenticated user's ID
        action="SYNC",
        resource_type="REPORTS",
        resource_id=None,
        details={
            "sales_synced": sales_count,
            "pos_synced": po_count
        }
    )
    
    return {"sales_synced": sales_count, "pos_synced": po_count}

# Download reports as CSV
@router.get("/download", response_class=StreamingResponse)
def download_reports_csv(
    report_type: ReportType = Query(..., description="Type of report to download (sales or po)"),
    n: int = Query(30, ge=1, le=100, description="Number of records to include"),
    db: Session = Depends(get_db)
):
    """Download reports as CSV file"""
    if report_type == ReportType.SALES:
        csv_data = generate_sales_csv(db, n)
        filename = f"sales_report_{date.today().strftime('%Y%m%d')}.csv"
    else:  # report_type == ReportType.PO
        csv_data = generate_po_csv(db, n)
        filename = f"po_report_{date.today().strftime('%Y%m%d')}.csv"
    
    # Log the download operation to audit log
    create_audit_log(
        db, 
        user_id=None,  # This would typically be the authenticated user's ID
        action="DOWNLOAD",
        resource_type="REPORTS",
        resource_id=None,
        details={
            "report_type": report_type,
            "records": n,
            "filename": filename
        }
    )
    
    # Create a StreamingResponse with the CSV data
    response = StreamingResponse(
        iter([csv_data]), 
        media_type="text/csv"
    )
    response.headers["Content-Disposition"] = f"attachment; filename={filename}"
    
    return response

# Upload report data via CSV
@router.post("/upload", response_model=ReportCSVUploadResponse)
async def upload_reports_csv(
    report_type: ReportType = Query(..., description="Type of report to upload (sales or po)"),
    file: UploadFile = File(..., description="CSV file to upload"),
    db: Session = Depends(get_db)
):
    """Upload report data via CSV file"""
    try:
        # Read the uploaded file
        contents = await file.read()
        csv_data = contents.decode('utf-8')
        
        # Process the CSV data based on report type
        if report_type == ReportType.SALES:
            success_count, failed_rows = process_sales_csv(db, csv_data)
        else:  # report_type == ReportType.PO
            success_count, failed_rows = process_po_csv(db, csv_data)
        
        # Log the upload operation to audit log
        create_audit_log(
            db, 
            user_id=None,  # This would typically be the authenticated user's ID
            action="UPLOAD",
            resource_type="REPORTS",
            resource_id=None,
            details={
                "report_type": report_type,
                "filename": file.filename,
                "success_count": success_count,
                "failed_count": len(failed_rows)
            }
        )
        
        return {
            "success": True,
            "message": f"Successfully processed {success_count} records with {len(failed_rows)} failures",
            "items_processed": success_count,
            "items_failed": len(failed_rows),
            "failed_items": failed_rows
        }
    
    except Exception as e:
        logger.error(f"Error processing CSV upload: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Error processing CSV file: {str(e)}"
        )

# Delete report entries
@router.delete("", response_model=Dict[str, int])
def delete_reports(
    delete_request: ReportDeleteRequest,
    db: Session = Depends(get_db)
):
    """Delete report entries by IDs and type"""
    if delete_request.report_type == ReportType.SALES:
        count = delete_sales_reports(db, delete_request.ids)
        resource_type = "SALES_REPORTS"
    else:  # delete_request.report_type == ReportType.PO
        count = delete_po_reports(db, delete_request.ids)
        resource_type = "PO_REPORTS"
    
    # Log the delete operation to audit log
    create_audit_log(
        db, 
        user_id=None,  # This would typically be the authenticated user's ID
        action="DELETE",
        resource_type=resource_type,
        resource_id=str(delete_request.ids),
        details={
            "report_type": delete_request.report_type,
            "deleted_count": count,
            "requested_ids": delete_request.ids
        }
    )
    
    return {"deleted_count": count} 