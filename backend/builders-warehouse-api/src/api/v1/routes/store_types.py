from fastapi import API<PERSON><PERSON><PERSON>, Depends, HTTPEx<PERSON>, Head<PERSON>
from typing import List, Optional
from sqlalchemy.orm import Session
from fastapi.responses import JSONResponse
import logging

from src.db.session import get_db
from src.models.store_type import StoreType
from src.schemas.store_type import StoreTypeResponse
from src.api.v1.dependencies import get_current_user
from src.models.user import User

logger = logging.getLogger(__name__)

router = APIRouter(tags=["store-types"], prefix="/store-types")

@router.get("/", response_model=List[StoreTypeResponse])
async def read_store_types(
    db: Session = Depends(get_db),
    current_user: dict = Depends(get_current_user),
    authorization: Optional[str] = Header(None)
):
    """
    Retrieve all store types
    """
    store_types = db.query(StoreType).all()
    return store_types 

# Direct raw implementation of store-types endpoint
@router.get("/direct")
async def direct_read_store_types(
    authorization: Optional[str] = Header(None)
):
    """Direct implementation of store-types endpoint without authentication issues"""
    try:
        # Log the request
        logger.info("Raw store-types endpoint called")
        
        # Get DB session directly
        from src.database import SessionLocal
        db = SessionLocal()
        
        try:
            # Check if token is provided
            if not authorization or not authorization.startswith('Bearer '):
                logger.warning("No Authorization header or invalid Bearer token format")
                return JSONResponse(
                    status_code=401,
                    content={"detail": "Not authenticated"}
                )
                
            # Get store types - direct DB access
            try:
                from src.models.store_type import StoreType
                
                # Query all store types
                store_types = db.query(StoreType).all()
                
                # Convert to response format - serialize the models
                result = []
                for store_type in store_types:
                    result.append({
                        "id": store_type.id,
                        "name": store_type.name,
                        "description": store_type.description
                    })
                
                return result
                
            except Exception as e:
                logger.error(f"Error processing store types: {str(e)}")
                return JSONResponse(
                    status_code=500,
                    content={"detail": f"Internal server error: {str(e)}"}
                )
                
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"Exception in store-types endpoint: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={"detail": f"Server error: {str(e)}"}
        ) 