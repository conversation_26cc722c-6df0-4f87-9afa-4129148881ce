from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List
import logging

from src.db.session import get_db
from src.models.store_type import StoreType
from src.schemas.store_type import StoreTypeResponse
from src.utils.auth import get_current_user

logger = logging.getLogger(__name__)

# Create router with prefix and tags
router = APIRouter(
    prefix="/store-types",
    tags=["StoreTypes"]
)

@router.get("/", response_model=List[StoreTypeResponse])
async def get_store_types(
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    Get all store types
    """
    store_types = db.query(StoreType).all()
    return store_types 

# Add a test endpoint without authentication for debugging
@router.get("/test", response_model=List[StoreTypeResponse])
async def test_store_types(
    db: Session = Depends(get_db)
):
    """
    Test endpoint to check if StoreType model works correctly
    """
    store_types = db.query(StoreType).all()
    return store_types 