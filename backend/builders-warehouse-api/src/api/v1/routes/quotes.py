from fastapi import APIRouter, Depends, HTTPException, status, Request, Query, Header
from fastapi.responses import JSONResponse
from sqlalchemy.orm import Session
from typing import Optional, List
from datetime import date
import logging

from src.db.session import get_db
from src.schemas.quote import QuoteCreate, QuoteUpdate, QuoteWithItems, QuotePagination
from src.controllers.quote_controller import QuoteController
from src.utils.auth import get_current_user
from src.utils.audit_log import create_audit_log

# Create router with prefix and tags
router = APIRouter(
    prefix="/quotes",
    tags=["quotes"],
    dependencies=[Depends(get_current_user)]
)

logger = logging.getLogger(__name__)

@router.post("/", status_code=status.HTTP_201_CREATED)
async def create_quote(
    quote: QuoteCreate,
    request: Request,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    Create a new quote with quote items
    """
    # Add debug logging
    print(f"Request body: {await request.json()}")
    print(f"Parsed quote data: {quote}")
    
    return await QuoteController.create_quote(
        db=db,
        quote_data=quote,
        request=request
    )

@router.get("/current", response_model=QuotePagination)
async def get_current_quotes(
    search: Optional[str] = Query(None, description="Search by quote_number or customer name"),
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    page: int = Query(1, ge=1),
    limit: int = Query(100, ge=1, le=100),
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    Get current (non-archived) quotes with pagination and filtering
    
    Parameters:
    - search: Search term for quote_number or customer company name
    - start_date: Filter by start date (YYYY-MM-DD)
    - end_date: Filter by end date (YYYY-MM-DD)
    - page: Page number for pagination
    - limit: Number of items per page
    """
    # If search parameter is provided, log the search action
    if search:
        create_audit_log(
            db=db,
            user_id=str(current_user.id),
            action="search",
            resource_type="quotes",
            details={"search_term": search}
        )
        
    # Debug log
    print(f"API - get_current_quotes called with: search={search}, start_date={start_date}, end_date={end_date}")
    
    # Parse dates if provided
    parsed_start_date = None
    parsed_end_date = None
    
    if start_date:
        try:
            parsed_start_date = date.fromisoformat(start_date)
            print(f"Parsed start_date: {parsed_start_date}")
        except ValueError as e:
            print(f"Error parsing start_date: {e}")
    
    if end_date:
        try:
            parsed_end_date = date.fromisoformat(end_date)
            print(f"Parsed end_date: {parsed_end_date}")
        except ValueError as e:
            print(f"Error parsing end_date: {e}")
    
    # Forward to controller
    return QuoteController.get_quotes(
        db=db,
        is_archived=False,
        search=search,
        start_date=parsed_start_date,
        end_date=parsed_end_date,
        page=page,
        limit=limit,
        current_user=current_user
    )

@router.get("/archived", response_model=QuotePagination)
async def get_archived_quotes(
    search: Optional[str] = Query(None, description="Search by quote_number or customer name"),
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    page: int = Query(1, ge=1),
    limit: int = Query(100, ge=1, le=100),
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    Get archived quotes with pagination and filtering
    
    Parameters:
    - search: Search term for quote_number or customer company name
    - start_date: Filter by start date (YYYY-MM-DD)
    - end_date: Filter by end date (YYYY-MM-DD)
    - page: Page number for pagination
    - limit: Number of items per page
    """
    # If search parameter is provided, log the search action
    if search:
        create_audit_log(
            db=db,
            user_id=str(current_user.id),
            action="search",
            resource_type="quotes",
            details={"search_term": search}
        )
        
    # Debug log
    print(f"API - get_archived_quotes called with: search={search}, start_date={start_date}, end_date={end_date}")
    
    # Parse dates if provided
    parsed_start_date = None
    parsed_end_date = None
    
    if start_date:
        try:
            parsed_start_date = date.fromisoformat(start_date)
            print(f"Parsed start_date: {parsed_start_date}")
        except ValueError as e:
            print(f"Error parsing start_date: {e}")
    
    if end_date:
        try:
            parsed_end_date = date.fromisoformat(end_date)
            print(f"Parsed end_date: {parsed_end_date}")
        except ValueError as e:
            print(f"Error parsing end_date: {e}")
    
    # Forward to controller
    return QuoteController.get_quotes(
        db=db,
        is_archived=True,
        search=search,
        start_date=parsed_start_date,
        end_date=parsed_end_date,
        page=page,
        limit=limit,
        current_user=current_user
    )

@router.get("/{quote_id}", response_model=QuoteWithItems)
async def get_quote(
    quote_id: int,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    Get a quote by ID
    """
    return QuoteController.get_quote(
        db=db,
        quote_id=quote_id
    )

@router.put("/{quote_id}")
async def update_quote(
    quote_id: int,
    quote_update: QuoteUpdate,
    request: Request,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    Update a quote
    """
    return await QuoteController.update_quote(
        db=db,
        quote_id=quote_id,
        quote_update=quote_update,
        request=request
    )

@router.put("/{quote_id}/convert")
async def convert_to_invoice(
    quote_id: int,
    request: Request,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    Convert a quote to invoice and move to archive
    """
    return await QuoteController.convert_to_invoice(
        db=db,
        quote_id=quote_id,
        request=request
    )

@router.put("/{quote_id}/renew", response_model=QuoteWithItems)
async def renew_quote(
    quote_id: int,
    request: Request,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    Renew an archived quote
    """
    return await QuoteController.renew_quote(
        db=db,
        quote_id=quote_id,
        request=request
    )

@router.put("/{quote_id}/archive", response_model=QuoteWithItems)
async def archive_quote(
    quote_id: int,
    request: Request,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    Archive a quote
    """
    # Create a quote update to change status to archived
    quote_update = QuoteUpdate(status="archived")
    
    return await QuoteController.update_quote(
        db=db,
        quote_id=quote_id,
        quote_update=quote_update,
        request=request
    )

@router.put("/{quote_id}/unarchive", response_model=QuoteWithItems)
async def unarchive_quote(
    quote_id: int,
    request: Request,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    Unarchive a quote
    """
    # Create a quote update to change status back to active/draft
    quote_update = QuoteUpdate(status="draft")
    
    return await QuoteController.update_quote(
        db=db,
        quote_id=quote_id,
        quote_update=quote_update,
        request=request
    )

@router.delete("/{quote_id}")
async def delete_quote(
    quote_id: int,
    request: Request,
    db: Session = Depends(get_db),
    current_user = Depends(get_current_user)
):
    """
    Soft delete a quote
    """
    return await QuoteController.delete_quote(
        db=db,
        quote_id=quote_id,
        request=request
    )

# Direct quotes endpoint implementation for quote creation
@router.post("/direct", status_code=status.HTTP_201_CREATED)
async def direct_create_quote(
    request: Request,
    authorization: Optional[str] = Header(None)
):
    """Direct implementation for creating a quote without requiring a trailing slash"""
    try:
        # Log the request
        logger.info("Raw quote creation endpoint called")
        
        # Get DB session directly
        from src.database import SessionLocal
        db = SessionLocal()
        
        try:
            # Check if token is provided
            if not authorization or not authorization.startswith('Bearer '):
                logger.warning("No Authorization header or invalid Bearer token format")
                return JSONResponse(
                    status_code=401,
                    content={"detail": "Not authenticated"}
                )
                
            # Process the quote creation
            try:
                # Import necessary components
                from src.controllers.quote_controller import QuoteController
                from src.schemas.quote import QuoteCreate
                
                # Get the request body
                request_body = await request.json()
                logger.info(f"Quote creation request body: {request_body}")
                
                # Parse the request into a QuoteCreate model
                quote_data = QuoteCreate.model_validate(request_body)
                
                # Forward to controller
                created_quote = await QuoteController.create_quote(
                    db=db,
                    quote_data=quote_data,
                    request=request
                )
                
                return created_quote
                
            except Exception as e:
                logger.error(f"Error creating quote: {str(e)}")
                return JSONResponse(
                    status_code=500,
                    content={"detail": f"Internal server error: {str(e)}"}
                )
                
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"Exception in quote creation endpoint: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={"detail": f"Server error: {str(e)}"}
        )

# Direct implementation for converting a quote to an invoice
@router.put("/{quote_id}/convert/direct")
async def direct_convert_to_invoice(
    quote_id: int,
    request: Request,
    authorization: Optional[str] = Header(None)
):
    """Direct implementation for converting a quote to invoice without requiring a trailing slash"""
    try:
        # Log the request
        logger.info(f"Raw quote conversion endpoint called with quote_id={quote_id}")
        
        # Get DB session directly
        from src.database import SessionLocal
        db = SessionLocal()
        
        try:
            # Check if token is provided
            if not authorization or not authorization.startswith('Bearer '):
                logger.warning("No Authorization header or invalid Bearer token format")
                return JSONResponse(
                    status_code=401,
                    content={"detail": "Not authenticated"}
                )
                
            # Process the quote conversion
            try:
                # Import necessary components
                from src.controllers.quote_controller import QuoteController
                from src.models.quote import Quote
                
                logger.info(f"Starting conversion of quote {quote_id} to invoice")
                
                # First, check if the quote exists and isn't already converted
                quote = db.query(Quote).filter(Quote.id == quote_id).first()
                if not quote:
                    logger.error(f"Quote with ID {quote_id} not found")
                    return JSONResponse(
                        status_code=404,
                        content={"detail": f"Quote with ID {quote_id} not found"}
                    )
                
                if quote.status == "converted":
                    logger.warning(f"Quote {quote_id} is already converted")
                    return JSONResponse(
                        status_code=400,
                        content={"detail": "Quote is already converted", "quote_status": quote.status}
                    )
                
                # Forward to controller
                logger.info(f"Converting quote {quote_id} to invoice via controller")
                converted_quote = await QuoteController.convert_to_invoice(
                    db=db,
                    quote_id=quote_id,
                    request=request
                )
                
                # Check if any new invoices were created as a result
                try:
                    from src.models.invoice import Invoice
                    
                    # Get the most recent invoice (should be the one just created)
                    latest_invoice = db.query(Invoice).order_by(Invoice.id.desc()).first()
                    
                    if latest_invoice:
                        logger.info(f"Latest invoice after conversion: ID={latest_invoice.id}, Number={latest_invoice.invoice_number}")
                    else:
                        logger.warning("No invoices found after conversion")
                except Exception as invoice_check_error:
                    logger.error(f"Error checking for new invoices: {str(invoice_check_error)}")
                
                logger.info(f"Successfully converted quote {quote_id} to invoice")
                return converted_quote
                
            except Exception as e:
                logger.error(f"Error converting quote to invoice: {str(e)}")
                # Get more detailed error information if possible
                import traceback
                logger.error(f"Detailed error: {traceback.format_exc()}")
                
                return JSONResponse(
                    status_code=500,
                    content={"detail": f"Internal server error: {str(e)}"}
                )
                
        finally:
            db.close()
            
    except Exception as e:
        logger.error(f"Exception in quote conversion endpoint: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={"detail": f"Server error: {str(e)}"}
        ) 