from fastapi import APIRouter, Depends, HTTPException, Query, status, Path, BackgroundTasks
from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
from datetime import date, datetime
import re
import logging

from src.database import get_db
from src.models.inbound_delivery import InboundDelivery
from src.models.purchase_order import PurchaseOrder
from src.schemas.inbound_delivery import (
    InboundDeliveryCreate, InboundDeliveryUpdate, InboundDeliveryOut,
    CalendarResponse, PagedInboundDeliveryResponse
)
from src.repository.inbound_delivery import (
    create_inbound_delivery, get_inbound_delivery, get_inbound_deliveries_by_po,
    get_calendar_highlights, update_inbound_delivery, update_ship_to_customer
)
from src.models.supplier import Supplier

router = APIRouter(
    prefix="/inbound-delivery",
    tags=["Inbound Delivery"],
    responses={404: {"description": "Not found"}},
)

@router.get("/calendar", response_model=CalendarResponse)
def get_calendar(
    month: int = Query(..., ge=1, le=12, description="Month (1-12)"),
    year: int = Query(..., ge=2000, le=2100, description="Year"),
    db: Session = Depends(get_db)
):
    """
    Get calendar highlights for a specific month/year
    
    This endpoint uses the purchase orders table to generate calendar data,
    showing dates with associated purchase orders and their delivery status.
    """
    try:
        days = get_calendar_highlights(db, month, year)
        
        # Ensure date is returned in the expected string format
        for day in days:
            if isinstance(day["date"], date):
                day["date"] = day["date"].isoformat()
                
        return {
            "month": month,
            "year": year,
            "days": days
        }
    except Exception as e:
        # Log error but don't throw exception to client
        logger = logging.getLogger(__name__)
        logger.error(f"Error fetching calendar data: {str(e)}")
        
        # Return empty data structure instead of error
        return {
            "month": month,
            "year": year,
            "days": []
        }

@router.get("/date/{date_str}", response_model=PagedInboundDeliveryResponse)
def get_deliveries_by_date(
    date_str: str = Path(..., description="Date to filter by (YYYY-MM-DD)"),
    supplier: Optional[str] = Query(None, description="Filter by supplier name"),
    page: int = Query(1, ge=1, description="Page number"),
    limit: int = Query(10, ge=1, le=100, description="Items per page"),
    db: Session = Depends(get_db)
):
    """
    Get deliveries for a specific date with pagination.
    
    This endpoint returns both InboundDelivery records and PurchaseOrder records 
    for the specified date, combined into a uniform response format.
    Each record includes:
    - PO number
    - Supplier information
    - SKU details
    - Quantity ordered and received
    - Invoice linkage
    - Expected dates
    """
    try:
        # Parse the date string
        delivery_date = datetime.strptime(date_str, "%Y-%m-%d").date()
        
        # Retrieve the data from the repository
        skip = (page - 1) * limit
        result = get_inbound_deliveries_by_po(db, None, supplier, delivery_date, skip, limit)
        
        # Add additional logging
        logger = logging.getLogger(__name__)
        logger.info(f"Date query for {date_str} returned {len(result['data'])} results")
        
        # Return the result regardless of whether there's data or not
        # This prevents 404 errors when no data is found
        return result
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid date format. Use YYYY-MM-DD format"
        )

@router.get("/po/{po_number}", response_model=PagedInboundDeliveryResponse)
def get_po_details(
    po_number: str = Path(..., description="The PO number to get details for"),
    supplier: Optional[str] = Query(None, description="Filter by supplier name"),
    page: int = Query(1, ge=1, description="Page number"),
    limit: int = Query(10, ge=1, le=100, description="Items per page"),
    db: Session = Depends(get_db)
):
    """
    Get detailed PO data with pagination.
    
    This endpoint returns detailed information about a Purchase Order, including:
    - PO number
    - Supplier information
    - SKU details
    - Quantity ordered and received
    - Invoice linkage
    - Expected dates
    - Ship-to-customer status
    """
    try:
        skip = (page - 1) * limit
        result = get_inbound_deliveries_by_po(db, po_number, supplier, None, skip, limit)
        
        # Add additional logging
        logger = logging.getLogger(__name__)
        logger.info(f"PO query for {po_number} returned {len(result['data'])} results")
        
        # Return the result regardless of whether there's data or not
        # This prevents 404 errors when no data is found
        return result
    except Exception as e:
        logger = logging.getLogger(__name__)
        logger.error(f"Error getting PO details for {po_number}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get PO details: {str(e)}"
        )

@router.patch("/ship/{po_number}", response_model=Dict[str, Any])
async def update_shipment_status(
    po_number: str = Path(..., description="The PO number to update"),
    ship_to_customer: bool = Query(..., description="Whether to ship to customer. Note: Once set to true, it cannot be changed back to false."),
    db: Session = Depends(get_db),
    background_tasks: BackgroundTasks = None
):
    """
    Update the shipment toggle for a PurchaseOrder to indicate it should be shipped to a customer.
    
    Once ship_to_customer is set to True, it cannot be changed back to False.
    
    This endpoint allows:
    - Setting ship_to_customer from False to True
    - Keeping ship_to_customer as True if already set
    - Keeping ship_to_customer as False if not yet set
    
    But does not allow:
    - Changing ship_to_customer from True back to False
    """
    logger = logging.getLogger(__name__)
    logger.info(f"Updating ship_to_customer for PO {po_number} to {ship_to_customer}")
    
    try:
        if not po_number:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="PO number is required"
            )
        
        # Check if the PurchaseOrder exists before updating
        existing_po = db.query(PurchaseOrder).filter(
            PurchaseOrder.po_number == po_number
        ).first()
        
        if existing_po:
            logger.info(f"Found PurchaseOrder with ID {existing_po.id}, current ship_to_customer: {existing_po.ship_to_customer}")
            
            # Pre-check for attempting to disable ship_to_customer
            if existing_po.ship_to_customer and not ship_to_customer:
                logger.warning(f"UI attempted to disable ship_to_customer for PO {po_number}, which is not allowed")
                return {
                    "success": False,
                    "message": "Ship to customer cannot be disabled once enabled",
                    "po_number": po_number,
                    "ship_to_customer": existing_po.ship_to_customer,  # Return the unchanged value
                    "toast": {
                        "show": True,
                        "message": "This delivery is already marked for shipping to customer and cannot be disabled.",
                        "type": "warning",
                        "title": "Ship to Customer Status"
                    }
                }
                
        else:
            logger.error(f"No PurchaseOrder found with number {po_number}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"No purchase order found for PO {po_number}"
            )
            
        # Call the repository function to update ship_to_customer
        purchase_orders = update_ship_to_customer(db, po_number, ship_to_customer)
        
        if not purchase_orders:
            logger.error(f"Failed to update ship_to_customer for PO {po_number}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to update ship_to_customer for PO {po_number}"
            )
        
        # Log the updated ship_to_customer values
        logger.info(f"After update, found {len(purchase_orders)} purchase orders")
        for i, po in enumerate(purchase_orders):
            logger.info(f"Updated PO {i+1} - id: {po.id}, ship_to_customer: {po.ship_to_customer}")
            
            # Note: ship_to_customer is a Python property stored in items JSON, not a database column
            # so we can't query it directly from the database
        
        # If we set ship_to_customer to True, we might trigger outbound delivery integration
        if ship_to_customer and background_tasks:
            # Example of how to schedule a background task for integration
            # background_tasks.add_task(trigger_outbound_delivery, po_number)
            logger.info(f"Ship to customer set to True for PO {po_number}")
        
        # Convert model objects to response format
        po = purchase_orders[0]  # Use the first PO since we're only updating one
        
        # Get supplier name
        supplier = db.query(Supplier).filter(Supplier.id == po.supplier_id).first()
        supplier_name = supplier.name if supplier else "Unknown Supplier"
        
        # Prepare toast message based on action
        toast_message = ""
        if ship_to_customer:
            toast_message = "This delivery has been marked for shipping to customer. This action cannot be undone."
        
        # Return the updated data with a toast message
        return {
            "success": True,
            "message": "Ship to customer status updated successfully",
            "po_number": po_number,
            "ship_to_customer": po.ship_to_customer,
            "toast": {
                "show": True,
                "message": toast_message,
                "type": "success" if ship_to_customer else "info",
                "title": "Ship to Customer Status"
            }
        }
    except HTTPException as e:
        # Re-raise HTTP exceptions with details
        logger.error(f"HTTP Exception when updating ship_to_customer: {e.detail}")
        raise
    except Exception as e:
        logger.error(f"Error updating ship_to_customer for PO {po_number}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update ship_to_customer: {str(e)}"
        )

@router.patch("/update-quantity", response_model=Dict[str, Any])
def update_quantity_received(
    po_data: Dict[str, Any],
    db: Session = Depends(get_db)
):
    """
    Update the quantity received for a Purchase Order item.
    
    Expected JSON body:
    {
        "po_number": "PO123",
        "sku": "ABC123",  # Optional, if multiple items in PO
        "quantity_received": 5,
        "expected_date": "2025-06-01"  # Optional, ISO formatted date
    }
    """
    try:
        logger = logging.getLogger(__name__)
        logger.info(f"Updating quantity received with data: {po_data}")
        
        po_number = po_data.get("po_number")
        if not po_number:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="PO number is required"
            )
        
        quantity_received = po_data.get("quantity_received")
        if quantity_received is None:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Quantity received is required"
            )
        
        # First try to find in InboundDelivery
        delivery = db.query(InboundDelivery).filter(
            InboundDelivery.delivery_number == po_number
        ).first()
        
        if delivery:
            logger.info(f"Found delivery with ID {delivery.id}")
            
            # Check if ship_to_customer is enabled - if so, prevent quantity updates
            if delivery.ship_to_customer:
                logger.warning(f"Attempted to update quantity for delivery {po_number} but ship_to_customer is enabled")
                raise HTTPException(
                    status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                    detail="Cannot update quantity received after ship to customer has been enabled. The delivery is locked for customer shipment."
                )
            
            if not delivery.items:
                delivery.items = {}
            
            delivery.items["quantity_received"] = quantity_received
            
            # Update expected date if provided
            if po_data.get("expected_date"):
                delivery.items["expected_date"] = po_data.get("expected_date")
            
            # Calculate new status
            if "quantity_ordered" in delivery.items:
                if quantity_received == 0:
                    delivery.status = "pending"
                elif quantity_received < delivery.items["quantity_ordered"]:
                    delivery.status = "partial"
                else:
                    delivery.status = "completed"
            
            db.commit()
            
            return {
                "success": True,
                "message": "Inbound delivery updated successfully",
                "delivery_id": delivery.id
            }
        
        # If not found in InboundDelivery, try PurchaseOrder
        po = db.query(PurchaseOrder).filter(
            PurchaseOrder.po_number == po_number
        ).first()
        
        if not po:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Purchase order with number {po_number} not found"
            )
        
        # Check if ship_to_customer is enabled - if so, prevent quantity updates
        if po.ship_to_customer:
            logger.warning(f"Attempted to update quantity for PO {po_number} but ship_to_customer is enabled")
            raise HTTPException(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail="Cannot update quantity received after ship to customer has been enabled. The delivery is locked for customer shipment."
            )
        
        logger.info(f"Found PO with ID {po.id}, current items: {po.items}")
        
        # Update quantity in PO items
        if po.items and isinstance(po.items, dict) and "items" in po.items:
            sku = po_data.get("sku")
            updated = False
            
            # Deep copy the items to avoid reference issues
            import copy
            updated_items = copy.deepcopy(po.items)
            
            for i, item in enumerate(updated_items["items"]):
                # If SKU is provided, update only that item, otherwise update first item
                if not sku or item.get("sku") == sku:
                    logger.info(f"Updating item: {item} with quantity_received: {quantity_received}")
                    updated_items["items"][i]["quantity_received"] = quantity_received
                    
                    # Update expected date if provided
                    if po_data.get("expected_date"):
                        updated_items["items"][i]["expected_delivery_date"] = po_data.get("expected_date")
                    
                    updated = True
                    break
            
            if not updated:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"Item with SKU {sku} not found in purchase order {po_number}"
                )
            
            # Update PO with the modified items
            # Force new assignment of the items column to trigger ORM update
            po.items = None
            db.flush()
            po.items = updated_items
            
            # Update PO status based on quantities
            all_received = all(
                item.get("quantity_received", 0) >= item.get("quantity_ordered", 0)
                for item in updated_items["items"]
            )
            
            any_received = any(
                item.get("quantity_received", 0) > 0
                for item in updated_items["items"]
            )
            
            if all_received:
                po.status = "delivered"
            elif any_received:
                po.status = "partial"
            else:
                po.status = "ordered"
            
            logger.info(f"Updated PO items: {po.items}, status: {po.status}")
            db.commit()
            
            # Verify the update
            db.refresh(po)
            logger.info(f"After commit, PO items: {po.items}")
            
            # Perform a direct database update as a fallback if the ORM update isn't persisting
            if po.items["items"][0]["quantity_received"] != quantity_received:
                logger.warning("ORM update failed, using direct SQL update")
                from sqlalchemy import text
                sql = text("""
                UPDATE "PurchaseOrder" 
                SET items = jsonb_set(items, '{items,0,quantity_received}', :quantity::jsonb)
                WHERE id = :id
                """)
                db.execute(sql, {"quantity": str(quantity_received), "id": po.id})
                db.commit()
                
                # Verify the SQL update
                db.refresh(po)
                logger.info(f"After SQL update, PO items: {po.items}")
            
            return {
                "success": True,
                "message": "Purchase order updated successfully",
                "po_id": po.id
            }
        
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Could not update quantity - invalid PO structure"
        )
    
    except HTTPException:
        raise
    except Exception as e:
        logger = logging.getLogger(__name__)
        logger.error(f"Error updating quantity: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update quantity: {str(e)}"
        )

@router.get("/ship-status/{po_number}", response_model=Dict[str, Any])
async def get_shipment_status(
    po_number: str = Path(..., description="The PO number to check"),
    db: Session = Depends(get_db)
):
    """
    Get the current ship_to_customer status for a PurchaseOrder.
    
    This endpoint is useful for the UI to:
    - Check if a PO is already marked for shipping to customer
    - Disable the toggle to prevent attempts to change it back
    - Show appropriate UI messaging
    
    Returns:
        dict: JSON with ship_to_customer status, can_disable flag, and toast messages
    """
    logger = logging.getLogger(__name__)
    
    try:
        # Find the purchase order
        po = db.query(PurchaseOrder).filter(
            PurchaseOrder.po_number == po_number
        ).first()
        
        if not po:
            logger.warning(f"No PurchaseOrder found with PO number {po_number}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"No purchase order found for PO {po_number}"
            )
        
        # Return status and whether it can be disabled
        return {
            "po_number": po_number,
            "ship_to_customer": po.ship_to_customer,
            "can_disable": not po.ship_to_customer,  # Can only disable if currently False
            "message": "Ship to customer cannot be disabled once enabled" if po.ship_to_customer else "",
            "toast": {
                "show": po.ship_to_customer,
                "message": "This delivery is already marked for shipping to customer and cannot be disabled.",
                "type": "warning",
                "title": "Ship to Customer Status"
            }
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error checking ship_to_customer status for PO {po_number}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to check ship_to_customer status: {str(e)}"
        ) 