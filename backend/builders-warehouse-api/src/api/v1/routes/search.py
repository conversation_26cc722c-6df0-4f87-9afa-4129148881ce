from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, Query, HTTPException, status
from sqlalchemy.orm import Session
from sqlalchemy import or_, text, cast, String
import logging

from src.database import get_db
from src.api.v1.dependencies import get_current_active_user
from src.models.user import User
from src.models.purchase_order import PurchaseOrder
from src.models.invoice import Invoice
from src.models.supplier import Supplier
from src.models.customer import Customer
from src.models.inventory import Inventory

# Setup logger for this module
logger = logging.getLogger(__name__)

router = APIRouter(
    prefix="/search",
    tags=["search"],
    dependencies=[Depends(get_current_active_user)],
)

@router.get("/", response_model=Dict[str, Any])
@router.get("", response_model=Dict[str, Any])  # Handle both /search/ and /search
async def global_search(
    query: str = Query(..., description="Search term to query across all modules"),
    page: int = Query(1, ge=1, description="Page number for pagination"),
    limit: int = Query(10, ge=1, le=100, description="Number of results per module"),
    db: Session = Depends(get_db),
    current_user: User = Depends(get_current_active_user)
):
    """
    Global search across multiple modules in the application.
    
    This endpoint searches for the provided query term across users, purchase orders,
    invoices, suppliers, customers, and inventory items.
    
    Results are grouped by module and paginated.
    """
    logger.info(f"Performing global search for query: {query}")
    
    if not query or len(query.strip()) < 2:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Search query must be at least 2 characters long"
        )
    
    # Calculate offset for pagination
    offset = (page - 1) * limit
    
    # Prepare search pattern for SQL LIKE/ILIKE
    search_pattern = f"%{query}%"
    
    # Search in Users
    users = db.query(User).filter(
        User.is_active == True,
        or_(
            User.user_name.ilike(search_pattern),
            User.email.ilike(search_pattern),
            User.mobile_number.ilike(search_pattern),
            User.role.ilike(search_pattern)
        )
    ).offset(offset).limit(limit).all()
    
    # Search in Purchase Orders
    purchase_orders = db.query(PurchaseOrder).filter(
        or_(
            cast(PurchaseOrder.id, String).ilike(search_pattern),
            PurchaseOrder.po_number.ilike(search_pattern),
            PurchaseOrder.status.ilike(search_pattern)
        )
    ).offset(offset).limit(limit).all()
    
    # Search in Invoices
    invoices = db.query(Invoice).filter(
        or_(
            Invoice.invoice_number.ilike(search_pattern),
            cast(Invoice.id, String).ilike(search_pattern),
            Invoice.status.ilike(search_pattern)
        )
    ).offset(offset).limit(limit).all()
    
    # Search in Suppliers
    suppliers = db.query(Supplier).filter(
        Supplier.is_active == True,
        or_(
            Supplier.name.ilike(search_pattern),
            Supplier.email.ilike(search_pattern),
            Supplier.phone.ilike(search_pattern)
        )
    ).offset(offset).limit(limit).all()
    
    # Search in Customer
    customers = db.query(Customer).filter(
        or_(
            Customer.company_name.ilike(search_pattern),
            Customer.contact_person.ilike(search_pattern),
            Customer.email.ilike(search_pattern),
            Customer.phone.ilike(search_pattern)
        )
    ).offset(offset).limit(limit).all()
    
    # Search in Inventory
    inventory_items = db.query(Inventory).join(Supplier, Inventory.supplier_id == Supplier.id, isouter=True).filter(
        or_(
            Inventory.sku_code.ilike(search_pattern),
            Inventory.style_code.ilike(search_pattern),
            Supplier.name.ilike(search_pattern),
            Inventory.carton_dimensions.ilike(search_pattern)
        )
    ).offset(offset).limit(limit).all()
    
    # Format results
    results = []
    
    if users:
        results.append({
            "module": "Users",
            "data": [{
                "id": str(user.id),
                "user_name": user.user_name,
                "email": user.email,
                "mobile_number": user.mobile_number,
                "role": user.role,
                "type": "user"
            } for user in users]
        })
    
    if purchase_orders:
        results.append({
            "module": "Purchase Orders",
            "data": [{
                "id": str(po.id),
                "po_number": po.po_number,
                "supplier_name": po.supplier.name if po.supplier else "Unknown Supplier",
                "issue_date": po.order_date.isoformat() if po.order_date else None,
                "status": po.status,
                "type": "purchase_order"
            } for po in purchase_orders]
        })
    
    if invoices:
        results.append({
            "module": "Invoices",
            "data": [{
                "id": str(invoice.id),
                "invoice_number": invoice.invoice_number,
                "customer_name": invoice.customer.company_name if invoice.customer else "Unknown Customer",
                "date": invoice.invoice_date.isoformat() if invoice.invoice_date else None,
                "total_amount": float(invoice.grand_total) if invoice.grand_total else 0,
                "status": invoice.status,
                "type": "invoice"
            } for invoice in invoices]
        })
    
    if suppliers:
        results.append({
            "module": "Suppliers",
            "data": [{
                "id": str(supplier.id),
                "name": supplier.name,
                "email": supplier.email,
                "phone": supplier.phone,
                "type": "supplier"
            } for supplier in suppliers]
        })
    
    if customers:
        results.append({
            "module": "Customer",
            "data": [{
                "id": str(customer.id),
                "company_name": customer.company_name,
                "contact_person": customer.contact_person,
                "email": customer.email,
                "phone": customer.phone,
                "type": "customer"
            } for customer in customers]
        })
    
    if inventory_items:
        results.append({
            "module": "Inventory",
            "data": [{
                "id": str(item.id),
                "sku_code": item.sku_code,
                "style_code": item.style_code,
                "supplier_name": item.supplier.name if item.supplier else "Unknown Supplier",
                "carton": item.carton,
                "units_per_carton": item.units_per_carton,
                "type": "inventory_item"
            } for item in inventory_items]
        })
    
    return {
        "query": query,
        "page": page,
        "limit": limit,
        "results": results
    } 