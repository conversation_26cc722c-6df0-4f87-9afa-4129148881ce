from typing import List, Optional, Any, Union
from fastapi import APIRouter, Depends, HTTPException, Query, UploadFile, File, status, BackgroundTasks, Header
from fastapi.responses import JSONResponse, StreamingResponse
from sqlalchemy.ext.asyncio import AsyncSession
from uuid import UUID
import io
import math
import logging
from jinja2 import Environment, FileSystemLoader
import weasyprint

from src.database import get_async_db
from src.models.customer import Customer
from src.models.invoice import Invoice
from src.models.user import User
from src.schemas.customer import (
    CustomerCreate, CustomerUpdate, CustomerOut, PaginatedCustomerResponse,
    InvoiceListParams
)
from src.schemas.invoice import InvoiceCreate, InvoiceOut as InvoiceSchema
from src.services import customer_service
from src.repository import invoice as invoice_repository
from src.utils.audit_log import create_audit_log
from src.api.v1.dependencies import get_current_user

logger = logging.getLogger(__name__)

router = APIRouter(prefix="/customers", tags=["customers"])


@router.get("/", response_model=PaginatedCustomerResponse)
async def list_customers(
    search: Optional[str] = Query(None, description="Search by company_name, email, or phone"),
    page: int = Query(1, ge=1),
    per_page: int = Query(10, ge=1, le=100),
    db: AsyncSession = Depends(get_async_db),
    current_user: Optional[User] = Depends(get_current_user)
):
    """
    List all customers with pagination and search functionality.
    
    Parameters:
    - search: Search term for company_name, email, or phone
    - page: Page number for pagination
    - per_page: Number of items per page
    """
    # If search parameter is provided, log the search action
    if search:
        create_audit_log(
            db=db,
            user_id=str(current_user.id) if current_user else None,
            action="search",
            resource_type="customers",
            details={"search_term": search}
        )
    
    skip = (page - 1) * per_page
    customers, total = await customer_service.list_customers(db, skip, per_page, search)
    
    pages = math.ceil(total / per_page) if total > 0 else 1
    
    # Convert dict items to Pydantic models if needed
    customer_models = []
    for customer in customers:
        if isinstance(customer, dict):
            # If repository returned dicts (from Prisma)
            # Check if id is string/UUID and handle explicitly to avoid validation errors
            customer_id = customer["id"]
            
            customer_out = CustomerOut(
                id=customer_id,
                company_name=customer["company_name"],
                contact_person=customer.get("contact_person"),
                email=customer.get("email", "<EMAIL>"),  # Provide default for required field
                phone=customer.get("phone"),
                is_account=customer.get("is_account", False),
                billing_address=customer.get("billing_address"),
                billing_suburb=customer.get("billing_suburb"),
                billing_postcode=customer.get("billing_postcode"),
                price_list_id=customer.get("price_list_id"),
                price_list=customer.get("price_list"),
                invoice_summary=customer.get("invoice_summary")
            )
            customer_models.append(customer_out)
        else:
            # If repository returned SQLAlchemy models
            # Convert to dict first to avoid validation issues
            customer_dict = {
                "id": customer.id,
                "company_name": customer.company_name,
                "contact_person": getattr(customer, "contact_person", None),
                "email": getattr(customer, "email", "<EMAIL>"),  # Provide default for required field
                "phone": getattr(customer, "phone", None),
                "is_account": getattr(customer, "is_account", False),
                "billing_address": getattr(customer, "billing_address", None),
                "billing_suburb": getattr(customer, "billing_suburb", None),
                "billing_postcode": getattr(customer, "billing_postcode", None),
                "price_list_id": getattr(customer, "price_list_id", None),
                "price_list": getattr(customer, "price_list", None),
                "invoice_summary": getattr(customer, "invoice_summary", None)
            }
            customer_models.append(CustomerOut(**customer_dict))
    
    return {
        "items": customer_models,
        "total": total,
        "page": page,
        "pages": pages,
        "per_page": per_page
    }


@router.post("/", response_model=CustomerOut, status_code=status.HTTP_201_CREATED)
async def create_customer(
    customer_in: CustomerCreate,
    db: AsyncSession = Depends(get_async_db)
):
    """
    Create a new customer.
    """
    try:
        # Check if customer with email already exists
        from sqlalchemy import select
        from src.models.customer import Customer
        
        # Check if email exists
        result = await db.execute(select(Customer).where(Customer.email == customer_in.email))
        existing = result.scalars().first()
        if existing:
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail="Customer with this email already exists"
            )
        
        # Create the customer directly without using service layer
        customer = Customer(
            company_name=customer_in.company_name,
            contact_person=customer_in.contact_person,
            email=customer_in.email,
            phone=customer_in.phone,
            is_account=customer_in.is_account,
            billing_address=customer_in.billing_address,
            billing_suburb=customer_in.billing_suburb,
            billing_postcode=customer_in.billing_postcode,
            price_list_id=customer_in.price_list_id
        )
        
        # Add to session and commit
        db.add(customer)
        await db.commit()
        await db.refresh(customer)
        
        # Initialize an empty invoice summary
        invoice_summary = {
            "invoice_count": 0,
            "total_amount": 0,
            "recent_invoices": []
        }
        
        # Convert SQLAlchemy model to Pydantic model
        customer_dict = {
            "id": customer.id,
            "company_name": customer.company_name,
            "contact_person": customer.contact_person,
            "email": customer.email,
            "phone": customer.phone,
            "is_account": customer.is_account,
            "billing_address": customer.billing_address,
            "billing_suburb": customer.billing_suburb,
            "billing_postcode": customer.billing_postcode,
            "price_list_id": customer.price_list_id,
            "price_list": None,  # We'll handle price_list relations separately if needed
            "invoice_summary": invoice_summary
        }
        
        # Return the customer
        return CustomerOut(**customer_dict)
        
    except HTTPException:
        # Re-raise HTTPExceptions
        raise
    except Exception as e:
        await db.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Error creating customer: {str(e)}"
        )


@router.get("/{customer_id}", response_model=CustomerOut)
async def get_customer(
    customer_id: Union[int, UUID],
    db: AsyncSession = Depends(get_async_db)
):
    """
    Get a specific customer by ID.
    """
    try:
        # Convert to int if it's a numeric string
        if isinstance(customer_id, str) and customer_id.isdigit():
            customer_id = int(customer_id)
        
        customer = await customer_service.get_customer(db, customer_id)
        if not customer:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Customer not found"
            )
        
        # Initialize an empty invoice summary
        invoice_summary = {
            "invoice_count": 0,
            "total_amount": 0,
            "recent_invoices": []
        }
        
        # Get the customer ID value
        customer_id_val = customer.id if hasattr(customer, 'id') else customer["id"]
        
        # Combine customer and summary data
        if isinstance(customer, dict):
            # If repository returned dict (from Prisma)
            result = CustomerOut(
                id=customer["id"],
                company_name=customer["company_name"],
                contact_person=customer.get("contact_person"),
                email=customer.get("email", "<EMAIL>"),  # Provide default for required field
                phone=customer.get("phone"),
                is_account=customer.get("is_account", False),
                billing_address=customer.get("billing_address"),
                billing_suburb=customer.get("billing_suburb"),
                billing_postcode=customer.get("billing_postcode"),
                price_list_id=customer.get("price_list_id"),
                price_list=customer.get("price_list"),
                invoice_summary=invoice_summary
            )
        else:
            # If repository returned SQLAlchemy model
            # Convert to dict first to avoid validation issues
            customer_dict = {
                "id": customer.id,
                "company_name": customer.company_name,
                "contact_person": getattr(customer, "contact_person", None),
                "email": getattr(customer, "email", "<EMAIL>"),  # Provide default for required field
                "phone": getattr(customer, "phone", None),
                "is_account": getattr(customer, "is_account", False),
                "billing_address": getattr(customer, "billing_address", None),
                "billing_suburb": getattr(customer, "billing_suburb", None),
                "billing_postcode": getattr(customer, "billing_postcode", None),
                "price_list_id": getattr(customer, "price_list_id", None),
                "price_list": getattr(customer, "price_list", None)
            }
            result = CustomerOut(**customer_dict)
            result.invoice_summary = invoice_summary
        
        return result
    except Exception as e:
        # Log the error
        logger.error(f"Error retrieving customer: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retrieve customer: {str(e)}"
        )


@router.put("/{customer_id}", response_model=CustomerOut)
async def update_customer(
    customer_id: Union[int, UUID],
    customer_in: CustomerUpdate,
    db: AsyncSession = Depends(get_async_db)
):
    """
    Update an existing customer.
    """
    # Check if customer exists
    customer = await customer_service.get_customer(db, customer_id)
    if not customer:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Customer not found"
        )
    
    try:
        updated_customer = await customer_service.update_customer(db, customer_id, customer_in)
        
        # Initialize an empty invoice summary
        invoice_summary = {
            "invoice_count": 0,
            "total_amount": 0,
            "recent_invoices": []
        }
        
        # Get the updated customer ID properly
        updated_id = updated_customer.id if hasattr(updated_customer, 'id') else updated_customer["id"]
        
        # Combine customer and summary data
        if isinstance(updated_customer, dict):
            # If repository returned dict (from Prisma)
            result = CustomerOut(
                id=updated_customer["id"],
                company_name=updated_customer["company_name"],
                contact_person=updated_customer.get("contact_person"),
                email=updated_customer.get("email", "<EMAIL>"),
                phone=updated_customer.get("phone"),
                is_account=updated_customer.get("is_account", False),
                billing_address=updated_customer.get("billing_address"),
                billing_suburb=updated_customer.get("billing_suburb"),
                billing_postcode=updated_customer.get("billing_postcode"),
                price_list_id=updated_customer.get("price_list_id"),
                price_list=updated_customer.get("price_list"),
                invoice_summary=invoice_summary
            )
        else:
            # If repository returned SQLAlchemy model
            # Convert to dict first to avoid validation issues
            customer_dict = {
                "id": updated_customer.id,
                "company_name": updated_customer.company_name,
                "contact_person": getattr(updated_customer, "contact_person", None),
                "email": getattr(updated_customer, "email", "<EMAIL>"),
                "phone": getattr(updated_customer, "phone", None),
                "is_account": getattr(updated_customer, "is_account", False),
                "billing_address": getattr(updated_customer, "billing_address", None),
                "billing_suburb": getattr(updated_customer, "billing_suburb", None),
                "billing_postcode": getattr(updated_customer, "billing_postcode", None),
                "price_list_id": getattr(updated_customer, "price_list_id", None),
                "price_list": getattr(updated_customer, "price_list", None)
            }
            result = CustomerOut(**customer_dict)
            result.invoice_summary = invoice_summary
        
        return result
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Error updating customer: {str(e)}"
        )


@router.delete("/{customer_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_customer(
    customer_id: Union[int, UUID],
    db: AsyncSession = Depends(get_async_db)
):
    """
    Delete a customer.
    """
    # Convert to int if it's a numeric string
    if isinstance(customer_id, str) and customer_id.isdigit():
        customer_id = int(customer_id)
        
    customer = await customer_service.get_customer(db, customer_id)
    if not customer:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Customer not found"
        )
    
    try:
        await customer_service.delete_customer(db, customer_id)
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error deleting customer: {str(e)}"
        )
    
    return None


@router.get("/{customer_id}/history")
async def list_customer_invoices(
    customer_id: Union[UUID, int],
    start_date: Optional[str] = None,
    end_date: Optional[str] = None,
    invoice_type: Optional[str] = None,
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=500),
    page: int = Query(1, ge=1),
    per_page: int = Query(10, ge=1, le=100),
    db: AsyncSession = Depends(get_async_db)
):
    """
    List invoices for a specific customer with optional filtering and pagination.
    Returns paginated response format consistent with other APIs.
    """
    # Convert to int if it's a numeric string
    if isinstance(customer_id, str) and customer_id.isdigit():
        customer_id = int(customer_id)
        
    # Verify customer exists
    customer = await customer_service.get_customer(db, customer_id)
    if not customer:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Customer not found"
        )
    
    # Always use per_page for pagination when provided
    skip = (page - 1) * per_page
    limit = per_page
    
    # Create params object
    params = InvoiceListParams(
        start_date=start_date,
        end_date=end_date,
        invoice_type=invoice_type,
        skip=skip,
        limit=limit
    )
    
    # Get invoices and total count
    invoices, total_count = await customer_service.list_customer_invoices(db, customer_id, params)
    
    # Calculate pagination info
    pages = math.ceil(total_count / per_page) if total_count > 0 else 1
    
    return {
        "items": invoices,
        "total": total_count,
        "page": page,
        "pages": pages,
        "per_page": per_page
    }


@router.post("/{customer_id}/history", response_model=InvoiceSchema, status_code=status.HTTP_201_CREATED)
async def create_customer_invoice(
    customer_id: Union[UUID, int],
    invoice_in: InvoiceCreate,
    db: AsyncSession = Depends(get_async_db)
):
    """
    Create a new invoice for a customer.
    """
    # Convert to int if it's a numeric string
    if isinstance(customer_id, str) and customer_id.isdigit():
        customer_id = int(customer_id)
        
    # Verify customer exists
    customer = await customer_service.get_customer(db, customer_id)
    if not customer:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Customer not found"
        )
    
    # Set customer ID in invoice data
    invoice_in.customer_id = customer_id
    # If company_id is not set, use customer_id as default
    if not hasattr(invoice_in, 'company_id'):
        invoice_in.company_id = customer_id
    
    try:
        # Use the invoice repository with proper parameters
        # Using default values for user_id and user_store_type since we don't have auth context here
        invoice = invoice_repository.create_invoice(db, invoice_in, user_id=1, user_store_type="DEFAULT")
        return invoice
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Error creating invoice: {str(e)}"
        )


@router.post("/{customer_id}/history/bulk_upload")
async def bulk_upload_customer_invoices(
    customer_id: Union[UUID, int],
    file: UploadFile = File(...),
    db: AsyncSession = Depends(get_async_db)
):
    """
    Bulk upload invoices from CSV or XLSX file.
    """
    # Convert to int if it's a numeric string
    if isinstance(customer_id, str) and customer_id.isdigit():
        customer_id = int(customer_id)
        
    # Verify customer exists
    customer = await customer_service.get_customer(db, customer_id)
    if not customer:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Customer not found"
        )
    
    # Get file extension
    filename = file.filename
    if not filename:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid filename"
        )
    
    file_extension = filename.split(".")[-1]
    if file_extension.lower() not in ["csv", "xlsx", "xls"]:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Unsupported file format. Please upload CSV or XLSX file."
        )
    
    # Read file content
    content = await file.read()
    
    try:
        # Process file
        success_count, error_count, error_messages = await customer_service.bulk_upload_invoices(
            db, customer_id, content, file_extension
        )
        
        return {
            "success_count": success_count,
            "error_count": error_count,
            "errors": error_messages
        }
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error processing file: {str(e)}"
        )


@router.get("/{customer_id}/history/{invoice_id}/pdf")
async def generate_invoice_pdf(
    customer_id: Union[UUID, int],
    invoice_id: UUID,
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_async_db)
):
    """
    Generate and download a PDF invoice.
    """
    # Convert to int if it's a numeric string
    if isinstance(customer_id, str) and customer_id.isdigit():
        customer_id = int(customer_id)
        
    # Verify customer exists
    customer = await customer_service.get_customer(db, customer_id)
    if not customer:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Customer not found"
        )
    
    # Get invoice
    invoice = await customer_service.get_invoice(db, invoice_id)
    if not invoice:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Invoice not found"
        )
    
    # Verify invoice belongs to customer
    if invoice.customer_id != customer_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Invoice does not belong to this customer"
        )
    
    try:
        # Set up Jinja2 environment
        env = Environment(
            loader=FileSystemLoader("templates"),
            autoescape=True
        )
        
        # Load template
        template = env.get_template("invoice.html")
        
        # Render template with invoice data
        html_content = template.render(
            invoice=invoice,
            customer=customer
        )
        
        # Generate PDF from HTML
        pdf_content = weasyprint.HTML(string=html_content).write_pdf()
        
        # Create in-memory buffer
        buffer = io.BytesIO(pdf_content)
        buffer.seek(0)
        
        # Add cleanup task to background tasks
        background_tasks.add_task(buffer.close)
        
        # Return PDF as streaming response
        filename = f"invoice_{invoice.invoice_no}.pdf"
        return StreamingResponse(
            buffer,
            media_type="application/pdf",
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Error generating PDF: {str(e)}"
        )


# Direct customer endpoint implementation for getting customer by ID
@router.get("/{customer_id}/direct")
async def direct_get_customer(
    customer_id: Union[int, str, UUID],
    authorization: Optional[str] = Header(None)
):
    """Direct implementation for getting a customer by ID without requiring a trailing slash"""
    try:
        # Log the request
        logger.info(f"Raw customer lookup endpoint called with customer_id={customer_id}")
        
        # Get DB session directly
        from src.database import SessionLocal
        db_session = SessionLocal()
        
        try:
            # Check if token is provided
            if not authorization or not authorization.startswith('Bearer '):
                logger.warning("No Authorization header or invalid Bearer token format")
                return JSONResponse(
                    status_code=401,
                    content={"detail": "Not authenticated"}
                )
                
            # Process the customer lookup
            try:
                # Import necessary components
                from src.models.customer import Customer
                
                # Convert customer_id to proper type if needed
                if isinstance(customer_id, str):
                    # Strip trailing slash if present
                    if customer_id.endswith('/'):
                        customer_id = customer_id[:-1]
                    
                    # Convert to int if numeric
                    if customer_id.isdigit():
                        customer_id = int(customer_id)
                
                # Fetch the customer
                customer = db_session.query(Customer).filter(Customer.id == customer_id).first()
                
                if not customer:
                    return JSONResponse(
                        status_code=404,
                        content={"detail": f"Customer with ID {customer_id} not found"}
                    )
                
                # Create the response
                customer_response = {
                    "id": customer.id,
                    "company_name": customer.company_name,
                    "contact_person": getattr(customer, "contact_person", None),
                    "email": getattr(customer, "email", None),
                    "phone": getattr(customer, "phone", None),
                    "is_account": getattr(customer, "is_account", False),
                    "billing_address": getattr(customer, "billing_address", None),
                    "billing_suburb": getattr(customer, "billing_suburb", None),
                    "billing_postcode": getattr(customer, "billing_postcode", None),
                    "price_list_id": getattr(customer, "price_list_id", None)
                }
                
                return customer_response
                
            except Exception as e:
                logger.error(f"Error getting customer: {str(e)}")
                return JSONResponse(
                    status_code=500,
                    content={"detail": f"Internal server error: {str(e)}"}
                )
                
        finally:
            db_session.close()
            
    except Exception as e:
        logger.error(f"Exception in customer lookup endpoint: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={"detail": f"Server error: {str(e)}"}
        ) 