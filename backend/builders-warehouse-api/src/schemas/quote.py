from pydantic import BaseModel, Field, ConfigDict, field_validator, UUID4
from typing import List, Optional, Dict, Any, Annotated, Union
from datetime import datetime, date
from enum import Enum
import uuid

class StoreTypeInfo(BaseModel):
    """Schema for store type information"""
    id: int
    name: str
    
    model_config = ConfigDict(from_attributes=True)

class CompanyInfo(BaseModel):
    """Schema for company information"""
    id: UUID4
    name: str
    
    model_config = ConfigDict(from_attributes=True)

class QuoteNumberInfo(BaseModel):
    """Schema for quote number information"""
    id: UUID4
    formatted_number: str
    
    model_config = ConfigDict(from_attributes=True)

# Base models
class QuoteBase(BaseModel):
    """Base schema for quote data"""
    company_id: Optional[UUID4] = None
    store_type_id: Optional[int] = None
    customer_id: Any  # Change Union to Any to avoid recursion issues
    date: Optional[Any] = Field(default_factory=lambda: date.today())
    notes: Optional[str] = None
    deliver_to_address: Optional[str] = None
    
    model_config = ConfigDict(
        populate_by_name=True,
        extra='allow'  # Allow extra fields to be passed but not used
    )
    
    @field_validator('date', mode='before')
    def parse_date(cls, v):
        """Parse date from string if needed"""
        if isinstance(v, str):
            try:
                return date.fromisoformat(v)
            except ValueError:
                raise ValueError(f"Invalid date format: {v}. Expected YYYY-MM-DD")
        return v
    
    @field_validator('customer_id', mode='before') 
    def parse_customer_uuid(cls, v):
        """Parse UUID from string if needed, or accept integer"""
        if isinstance(v, str):
            try:
                return uuid.UUID(v)
            except ValueError:
                # Try to convert to int if it's a string number
                try:
                    return int(v)
                except ValueError:
                    raise ValueError(f"Invalid UUID or integer format: {v}")
        # Allow integers to pass through
        return v
    
    @field_validator('company_id', mode='before') 
    def parse_company_uuid(cls, v):
        """Parse UUID from string if needed"""
        if v is None:
            return v
        if isinstance(v, str):
            try:
                return uuid.UUID(v)
            except ValueError:
                raise ValueError(f"Invalid UUID format: {v}")
        return v

# Input models
class QuoteItemInput(BaseModel):
    """Schema for quote item input during quote creation"""
    sku_code: str
    description: str = ""
    quantity_units: int = Field(ge=0, default=0)
    quantity_boxes: int = Field(ge=0, default=0)
    quantity_pieces: int = Field(ge=0, default=0)
    quantity_m2: float = Field(ge=0, default=0.0)
    unit_price: float = Field(ge=0, default=0.0)
    
    model_config = ConfigDict(
        extra='allow'
    )

class QuoteCreate(QuoteBase):
    """Schema for creating a quote"""
    items: List[QuoteItemInput] = Field(default_factory=list)
    quote_items: Optional[List[QuoteItemInput]] = Field(default=None, exclude=True)
    
    @field_validator('items', mode='before')
    def process_items(cls, v, values):
        """Process items to handle different field names"""
        if not v and 'quote_items' in values.data:
            return values.data.get('quote_items')
        return v
    
    model_config = ConfigDict(
        populate_by_name=True,
        extra='allow',
        json_schema_extra={
            "example": {
                "company_id": "8138b3a5-d70b-46b1-8359-7a56a6f6b827",
                "store_type_id": 1,
                "customer_id": "8138b3a5-d70b-46b1-8359-7a56a6f6b827",
                "date": "2023-03-01",
                "deliver_to_address": "123 Main St, City",
                "notes": "Priority delivery requested",
                "items": [
                    {
                        "sku_code": "ABC123",
                        "description": "Premium Lumber",
                        "quantity_units": 5,
                        "quantity_boxes": 0,
                        "quantity_pieces": 0,
                        "quantity_m2": 0,
                        "unit_price": 59.99
                    }
                ]
            }
        }
    )

class QuoteUpdate(BaseModel):
    """Schema for updating a quote"""
    company_id: Optional[UUID4] = None
    store_type_id: Optional[int] = None
    date: Optional[date] = None
    notes: Optional[str] = None
    deliver_to_address: Optional[str] = None
    
    @field_validator('date')
    def date_not_in_future(cls, v: Optional[date]) -> Optional[date]:
        if v is not None and v > date.today():
            raise ValueError('Date cannot be in the future')
        return v

# Output models - using dictionaries for nested data to avoid circular references
class QuoteDB(QuoteBase):
    """Schema for a quote from the database"""
    id: int
    status: str = "draft"  # Default status
    created_at: datetime
    updated_at: datetime
    
    model_config = ConfigDict(from_attributes=True)

class QuoteWithItems(QuoteDB):
    """Schema for a quote with its items"""
    id: int
    quote_number: QuoteNumberInfo
    company: CompanyInfo
    store_type: StoreTypeInfo
    items: Any = []  # Use Any type to avoid validation errors in the temporary solution
    
    # Virtual properties for backward compatibility
    @property
    def is_archived(self) -> bool:
        """Virtual property for backward compatibility - derives from status"""
        if hasattr(self, 'status'):
            return self.status in ["archived", "converted"]
        return False
        
    @property
    def converted_to_invoice(self) -> bool:
        """Virtual property for backward compatibility - derives from status"""
        if hasattr(self, 'status'):
            return self.status == "converted"
        return False
        
    @property
    def renewed_at(self) -> Optional[datetime]:
        """Virtual property for backward compatibility - placeholder for tests"""
        if hasattr(self, 'status') and self.status == "renewed":
            return self.updated_at
        return None
    
    model_config = ConfigDict(from_attributes=True)

class QuoteResponse(BaseModel):
    """Basic response schema for quote in list view"""
    id: int
    quote_number: Any  # More flexible type
    customer_id: Any  # More flexible type
    customer_name: str  # Populated from customer relation
    company_id: Any  # More flexible type
    company_name: str  # Populated from company relation
    store_type_id: int
    store_type_name: str  # Populated from store_type relation
    date: Any  # Accept any date format for flexibility
    grand_total: float
    notes: Optional[str] = None
    
    model_config = ConfigDict(
        from_attributes=True,
        extra='allow',  # Allow extra fields
        arbitrary_types_allowed=True  # Allow arbitrary types
    )

class QuotePagination(BaseModel):
    """Schema for paginated quotes"""
    total: int
    items: List[Any]  # Accept any item type for maximum flexibility
    page: int
    size: int
    
    model_config = ConfigDict(
        from_attributes=True,
        extra='allow',  # Allow extra fields
        arbitrary_types_allowed=True  # Allow arbitrary types
    ) 