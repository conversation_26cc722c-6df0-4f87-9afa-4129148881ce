from pydantic import BaseModel, Field, UUID4, ConfigDict
from typing import List, Optional, Dict, Any
from datetime import date, datetime
from enum import Enum

class SalesReportBase(BaseModel):
    """Base schema for sales report data"""
    date: date
    invoice_number: str  # This will be treated as an ID
    company_name: str  # This will be treated as an ID
    total_amount: float = Field(ge=0)
    mode_of_payment: str

class SalesReportCreate(SalesReportBase):
    """Schema for creating a sales report entry"""
    pass

class SalesReportOut(SalesReportBase):
    """Output schema for sales report data"""
    id: int
    created_at: datetime
    updated_at: datetime
    
    model_config = ConfigDict(from_attributes=True)

class POReportBase(BaseModel):
    """Base schema for purchase order report data"""
    date: date
    po_number: str  # This will be treated as an ID
    supplier_name: str  # This will be treated as an ID
    linked_invoice_number: Optional[str] = None  # This will be treated as an ID if applicable
    total_amount: float = Field(ge=0)

class POReportCreate(POReportBase):
    """Schema for creating a purchase order report entry"""
    pass

class POReportOut(POReportBase):
    """Output schema for purchase order report data"""
    id: int
    created_at: datetime
    updated_at: datetime
    
    model_config = ConfigDict(from_attributes=True)

class ReportType(str, Enum):
    """Enum for report types"""
    SALES = "sales"
    PO = "po"

class ReportDeleteRequest(BaseModel):
    """Schema for deleting report entries"""
    report_type: ReportType
    ids: List[int]  # List of IDs to delete

class ReportPagination(BaseModel):
    """Schema for paginated reports"""
    total: int
    items: List[Any]  # Can be either SalesReportOut or POReportOut
    page: int
    size: int

class ReportCSVUploadResponse(BaseModel):
    """Schema for CSV upload response"""
    success: bool
    message: str
    items_processed: int
    items_failed: int = 0
    failed_items: List[Dict[str, Any]] = [] 