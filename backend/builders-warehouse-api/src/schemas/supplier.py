from typing import Optional, Dict, Any, List, Union
from pydantic import BaseModel, Field, EmailStr, validator, field_validator
from datetime import datetime
import re

# SKU object schema
class SKU(BaseModel):
    """Schema for SKU object with ID and code"""
    id: int
    code: str

# Price list item schema
class PriceListItem(BaseModel):
    """Schema for price list items"""
    sku: str 
    description: Optional[str] = None
    unit_price: float

class PriceItem(BaseModel):
    """Schema for a single price item in the price list"""
    sku: SKU
    description: str
    style_code: str = ''
    rrp_ex_gst: float = 0.0
    bwa_buy_price_50_ex_gst: float = 0.0
    # Calculated fields - these will be auto-computed
    regional_retail_aud_incl: Optional[float] = None
    regional_trade_aud_incl: Optional[float] = None
    metro_retail_aud_incl: Optional[float] = None
    metro_trade_aud_incl: Optional[float] = None
    regional_retail_vip_aud_incl: Optional[float] = None
    regional_trade_vip1_aud_incl: Optional[float] = None
    regional_trade_vip2_aud_incl: Optional[float] = None
    metro_retail_vip_aud_incl: Optional[float] = None
    metro_trade_vip1_aud_incl: Optional[float] = None
    metro_trade_vip2_aud_incl: Optional[float] = None
    status: bool = True
    
    # Add validators to handle string values for numeric fields
    @field_validator('rrp_ex_gst', 'bwa_buy_price_50_ex_gst', mode='before')
    @classmethod
    def validate_input_float_fields(cls, v: Any) -> float:
        if v is None:
            return 0.0
        if isinstance(v, str):
            if v.strip() == '':
                return 0.0
            try:
                return float(v)
            except ValueError:
                return 0.0
        return v
    
    def model_post_init(self, __context: Any) -> None:
        """Calculate pricing fields based on input values"""
        if self.bwa_buy_price_50_ex_gst > 0:
            # Determine if product is a vanity based on description
            is_vanity = 'vanity' in self.description.lower() if self.description else False
            
            # Apply formulas with GST (1.1) and rounding
            # Regional Retail: 70% markup (80% for vanities)
            markup = 1.80 if is_vanity else 1.70
            self.regional_retail_aud_incl = round(self.bwa_buy_price_50_ex_gst * markup * 1.1, 0)
            
            # Regional Trade: 60% markup
            self.regional_trade_aud_incl = round(self.bwa_buy_price_50_ex_gst * 1.60 * 1.1, 0)
            
            # Metro Retail: 60% markup (70% for vanities)
            markup = 1.70 if is_vanity else 1.60
            self.metro_retail_aud_incl = round(self.bwa_buy_price_50_ex_gst * markup * 1.1, 0)
            
            # Metro Trade: 50% markup
            self.metro_trade_aud_incl = round(self.bwa_buy_price_50_ex_gst * 1.50 * 1.1, 0)
            
            # Regional Retail VIP: 65% markup (70% for vanities)
            markup = 1.70 if is_vanity else 1.65
            self.regional_retail_vip_aud_incl = round(self.bwa_buy_price_50_ex_gst * markup * 1.1, 0)
            
            # Regional Trade VIP1: 45% markup
            self.regional_trade_vip1_aud_incl = round(self.bwa_buy_price_50_ex_gst * 1.45 * 1.1, 0)
            
            # Regional Trade VIP2: 40% markup
            self.regional_trade_vip2_aud_incl = round(self.bwa_buy_price_50_ex_gst * 1.40 * 1.1, 0)
            
            # Metro Retail VIP: 55% markup (60% for vanities)
            markup = 1.60 if is_vanity else 1.55
            self.metro_retail_vip_aud_incl = round(self.bwa_buy_price_50_ex_gst * markup * 1.1, 0)
            
            # Metro Trade VIP1: 45% markup
            self.metro_trade_vip1_aud_incl = round(self.bwa_buy_price_50_ex_gst * 1.45 * 1.1, 0)
            
            # Metro Trade VIP2: 40% markup
            self.metro_trade_vip2_aud_incl = round(self.bwa_buy_price_50_ex_gst * 1.40 * 1.1, 0)

class SupplierBase(BaseModel):
    """Base schema for supplier data"""
    supplier_name: str = Field(..., min_length=1, max_length=100)
    phone_no: str = Field(..., min_length=8, max_length=20)
    email: Optional[EmailStr] = None
    address: Optional[str] = Field(None, max_length=255)
    
    @validator('phone_no')
    def validate_phone_number(cls, v):
        if not re.match(r'^\+?[0-9]+$', v):
            raise ValueError('Phone number must contain only digits with an optional + prefix')
        return v

    @validator('email', pre=True)
    def validate_email(cls, v):
        if v == '' or v is None:
            return None
        return v

class SupplierCreate(SupplierBase):
    """Schema for creating a new supplier"""
    price_list: Optional[List[PriceItem]] = None

class SupplierUpdate(BaseModel):
    """Schema for updating a supplier"""
    supplier_name: Optional[str] = Field(None, min_length=1, max_length=100)
    phone_no: Optional[str] = Field(None, min_length=8, max_length=20)
    email: Optional[EmailStr] = None
    address: Optional[str] = Field(None, max_length=255)
    price_list: Optional[List[PriceItem]] = None
    is_active: Optional[bool] = None
    
    @validator('phone_no')
    def validate_phone_number(cls, v):
        if v is not None and not re.match(r'^\+?[0-9]+$', v):
            raise ValueError('Phone number must contain only digits with an optional + prefix')
        return v

    @validator('email', pre=True)
    def validate_email(cls, v):
        if v == '' or v is None:
            return None
        return v

class SupplierOut(BaseModel):
    """Schema for Supplier output"""
    id: int
    name: str
    supplier_name: str = None  # Added field for frontend compatibility
    phone: Optional[str] = None
    phone_no: Optional[str] = None  # Added field for frontend compatibility
    email: Optional[str] = None
    address: Optional[str] = None
    price_list: Optional[List[PriceItem]] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    model_config = {
        "json_schema_extra": {
            "examples": [
                {
                    "id": 1,
                    "name": "ABC Supplier",
                    "supplier_name": "ABC Supplier",
                    "phone": "************",
                    "phone_no": "************",
                    "email": "<EMAIL>",
                    "address": "123 Main St, City, Country",
                    "price_list": [
                        {
                            "sku": {
                                "id": 1,
                                "code": "SKU001"
                            },
                            "description": "Product Description",
                            "style_code": "",
                            "rrp_ex_gst": 9.99,
                            "bwa_buy_price_50_ex_gst": 9.99,
                            "regional_retail_aud_incl": 10.99,
                            "regional_trade_aud_incl": 10.99,
                            "metro_retail_aud_incl": 10.99,
                            "metro_trade_aud_incl": 10.99,
                            "regional_retail_vip_aud_incl": 10.99,
                            "regional_trade_vip1_aud_incl": 10.99,
                            "regional_trade_vip2_aud_incl": 10.99,
                            "metro_retail_vip_aud_incl": 10.99,
                            "metro_trade_vip1_aud_incl": 10.99,
                            "metro_trade_vip2_aud_incl": 10.99,
                            "status": True
                        }
                    ]
                }
            ]
        }
    }
    
    # Add a validator to ensure both name and supplier_name have values
    @field_validator('supplier_name')
    def set_supplier_name(cls, v, info):
        # If supplier_name is not provided, use the name field
        if v is None and 'name' in info.data:
            return info.data['name']
        return v
    
    @field_validator('phone_no')
    def set_phone_no(cls, v, info):
        # If phone_no is not provided, use the phone field
        if v is None and 'phone' in info.data:
            return info.data['phone']
        return v

class PaginatedSupplierResponse(BaseModel):
    """Schema for paginated supplier responses"""
    items: List[SupplierOut]
    total: int
    page: int
    limit: int 