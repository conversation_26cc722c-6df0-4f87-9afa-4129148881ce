from typing import List, Optional, Union
from pydantic import BaseModel, UUID4, Field
from datetime import date, datetime
from enum import Enum


class CompanyHistoryItemType(str, Enum):
    """Type of history item"""
    INVOICE = "invoice"
    QUOTE = "quote"


class CompanyHistoryItem(BaseModel):
    """Base schema for history items (invoices and quotes)"""
    id: Union[int, UUID4]
    store_type: str
    date: Union[date, datetime]
    grand_total: float
    notes: Optional[str] = None
    type: CompanyHistoryItemType


class CompanyHistoryPagination(BaseModel):
    """Pagination information for company history"""
    total: int
    page: int
    limit: int
    items: List[CompanyHistoryItem]


class CompanyHistoryRequest(BaseModel):
    """Request schema for company history"""
    company_id: UUID4 = Field(..., description="UUID of the company", example="123e4567-e89b-12d3-a456-************")
    page: int = Field(1, ge=1, description="Page number, starts at 1")
    limit: int = Field(10, ge=1, le=100, description="Number of items per page")
    
    model_config = {
        "json_schema_extra": {
            "example": {
                "company_id": "123e4567-e89b-12d3-a456-************",
                "page": 1,
                "limit": 10
            }
        }
    }


class CompanyHistoryResponse(BaseModel):
    """Response schema for company history"""
    invoices: CompanyHistoryPagination
    quotes: CompanyHistoryPagination
    
    model_config = {
        "json_schema_extra": {
            "example": {
                "invoices": {
                    "total": 15,
                    "page": 1,
                    "limit": 10,
                    "items": [
                        {
                            "id": "1001",
                            "store_type": "Cranbourne",
                            "date": "2024-08-10",
                            "grand_total": 2500.00,
                            "notes": "Final installment",
                            "type": "invoice"
                        }
                    ]
                },
                "quotes": {
                    "total": 8,
                    "page": 1,
                    "limit": 10,
                    "items": [
                        {
                            "id": "204",
                            "store_type": "Sale",
                            "date": "2024-07-15",
                            "grand_total": 1850.00,
                            "notes": "Urgent order request",
                            "type": "quote"
                        }
                    ]
                }
            }
        }
    } 