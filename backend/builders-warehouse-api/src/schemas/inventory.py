from typing import Optional, List
from pydantic import BaseModel, Field, validator
from datetime import datetime

# Base model for inventory data
class InventoryBase(BaseModel):
    sku_code: str = Field(..., description="Unique SKU Code")
    style_code: str = Field(..., description="Style Code")
    supplier_id: int = Field(..., description="Supplier ID (foreign key to Supplier table)")
    carton: int = Field(1, description="Carton number", ge=1)
    units_per_carton: int = Field(..., description="Units per carton", gt=0)
    carton_dimensions: Optional[str] = Field(None, description="Carton Dimensions (e.g. 40x30x25 cm)")
    weight_per_unit: float = Field(..., description="Weight per unit in kg", gt=0)
    weight_per_carton: float = Field(..., description="Weight per carton in kg", gt=0)
    units_per_pallet: Optional[int] = Field(None, description="Number of units per pallet", gt=0)
    pallet_weight: Optional[float] = Field(None, description="Weight of pallet in kg", gt=0)
    notes: Optional[str] = Field(None, description="Additional notes or comments about the inventory item")

    @validator('weight_per_carton')
    def validate_weight_per_carton(cls, v, values):
        if 'weight_per_unit' in values and 'units_per_carton' in values:
            expected_weight = round(values['weight_per_unit'] * values['units_per_carton'], 2)
            if abs(v - expected_weight) > 0.1:  # Allow small difference due to packaging
                raise ValueError(f"Weight per carton should be approximately {expected_weight} " +
                                f"(weight_per_unit * units_per_carton)")
        return v

# Model for creating new inventory
class InventoryCreate(InventoryBase):
    pass

# Model for updating inventory
class InventoryUpdate(BaseModel):
    sku_code: Optional[str] = None
    style_code: Optional[str] = None
    supplier_id: Optional[int] = None
    carton: Optional[int] = None
    units_per_carton: Optional[int] = None
    carton_dimensions: Optional[str] = None
    weight_per_unit: Optional[float] = None
    weight_per_carton: Optional[float] = None
    units_per_pallet: Optional[int] = None
    pallet_weight: Optional[float] = None
    notes: Optional[str] = None

# Model for responses
class Inventory(InventoryBase):
    id: int
    created_at: Optional[datetime] = None  # Make datetime fields optional
    updated_at: Optional[datetime] = None  # Make datetime fields optional
    created_by: Optional[str] = None       # Change from int to str for UUID
    updated_by: Optional[str] = None       # Change from int to str for UUID

    @validator('created_at', 'updated_at', pre=True)
    def parse_datetime(cls, value):
        # Handle None values or other invalid datetime formats
        if value is None:
            return None
        if isinstance(value, datetime):
            return value
        try:
            return datetime.fromisoformat(str(value))
        except (ValueError, TypeError):
            return None
            
    @validator('created_by', 'updated_by', pre=True)
    def parse_uuid(cls, value):
        # Handle None values or convert UUID to string
        if value is None:
            return None
        # Convert UUID objects to strings
        return str(value)

    class Config:
        from_attributes = True
        # Allow for arbitrary types (like None for datetime)
        arbitrary_types_allowed = True

# Model for bulk upload
class InventoryBulkUpload(BaseModel):
    items: List[InventoryCreate]

# Model for CSV import
class CSVImportResult(BaseModel):
    success: int = 0
    errors: int = 0
    error_details: List[str] = [] 
