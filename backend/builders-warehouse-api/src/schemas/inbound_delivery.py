from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import datetime, date

class InboundDeliveryBase(BaseModel):
    """Base schema for inbound delivery data"""
    delivery_number: Optional[str] = None
    supplier_name: str
    delivery_date: datetime
    items: Optional[Dict[str, Any]] = None
    status: str = "pending"
    ship_to_customer: bool = False

class InboundDeliveryCreate(InboundDeliveryBase):
    """Schema for creating a new inbound delivery"""
    pass

class InboundDeliveryUpdate(BaseModel):
    """Schema for updating an inbound delivery"""
    supplier_name: Optional[str] = None
    delivery_date: Optional[datetime] = None
    items: Optional[Dict[str, Any]] = None
    status: Optional[str] = None
    ship_to_customer: Optional[bool] = None

class InboundDeliveryOut(BaseModel):
    """Schema for inbound delivery response"""
    id: int
    po_number: str  # Use delivery_number as po_number for consistency with frontend
    supplier_name: str
    delivery_date: datetime
    status: str
    created_at: datetime
    updated_at: datetime
    # Additional fields required by frontend
    sku: Optional[str] = None
    quantity_ordered: Optional[int] = None
    quantity_received: Optional[int] = 0
    ordered_by: Optional[str] = None
    balance_order: Optional[int] = None
    linked_invoice: Optional[str] = None
    ship_to_customer: bool = False
    po_date: Optional[str] = None  # ISO formatted date string
    new_expected_date: Optional[str] = None  # ISO formatted date string
    items: Optional[Dict[str, Any]] = None

    class Config:
        from_attributes = True
        arbitrary_types_allowed = True

class CalendarDay(BaseModel):
    """Schema for a calendar day with delivery data"""
    date: Any  # Could be date, string, or datetime
    po_numbers: List[str] = []
    status: str = "pending"

class CalendarResponse(BaseModel):
    """Schema for calendar highlights response"""
    month: int
    year: int
    days: List[CalendarDay] = []

class PagedInboundDeliveryResponse(BaseModel):
    """Schema for paginated inbound delivery response"""
    po_number: str
    delivery_date: Optional[str] = None  # ISO formatted date string
    data: List[Dict[str, Any]]  # Using Dict here to allow for flexible structure
    pagination: dict = {
        "total": 0,
        "page": 1,
        "limit": 10
    } 