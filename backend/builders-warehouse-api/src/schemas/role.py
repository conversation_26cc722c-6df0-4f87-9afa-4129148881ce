from pydantic import BaseModel
from typing import List, Optional
from datetime import datetime
import uuid

class RoleBase(BaseModel):
    """Base schema for role data"""
    name: str

class RoleCreate(RoleBase):
    """Schema for creating a new role"""
    pass

class RoleUpdate(BaseModel):
    """Schema for updating a role"""
    name: str

class RoleResponse(RoleBase):
    """Schema for role response data"""
    id: int
    created_at: datetime
    updated_at: Optional[datetime] = None
    
    class Config:
        from_attributes = True

class RoleInUserResponse(BaseModel):
    """Schema for nested role object in user responses"""
    id: int
    name: str
    
    class Config:
        from_attributes = True

class RoleListResponse(BaseModel):
    """Schema for paginated role list response"""
    items: List[RoleResponse]
    total: int 