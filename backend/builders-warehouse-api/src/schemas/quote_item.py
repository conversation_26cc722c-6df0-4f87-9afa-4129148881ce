from pydantic import BaseModel, Field, ConfigDict
from typing import Optional, Dict, Any
from datetime import datetime
from decimal import Decimal

# Base model for common fields
class QuoteItemBase(BaseModel):
    """Base schema for quote item data"""
    sku_code: str
    description: str = ""
    quantity_units: int = Field(ge=0, default=0)
    quantity_boxes: int = Field(ge=0, default=0)
    quantity_pieces: int = Field(ge=0, default=0)
    quantity_m2: float = Field(ge=0, default=0.0)
    unit_price: float = Field(ge=0, default=0.0)

# Input models
class QuoteItemCreate(QuoteItemBase):
    """Schema for creating a quote item"""
    pass

class QuoteItemUpdate(BaseModel):
    """Schema for updating a quote item"""
    sku_code: Optional[str] = None
    description: Optional[str] = None
    quantity_units: Optional[int] = Field(None, ge=0)
    quantity_boxes: Optional[int] = Field(None, ge=0)
    quantity_pieces: Optional[int] = Field(None, ge=0)
    quantity_m2: Optional[float] = Field(None, ge=0)
    unit_price: Optional[float] = Field(None, ge=0)

# Output models
class QuoteItemDB(QuoteItemBase):
    """Schema for a quote item from the database"""
    id: int
    total_price: float
    quote_id: int
    created_at: datetime
    updated_at: datetime
    
    model_config = ConfigDict(from_attributes=True)

# Function to convert a database model to a dictionary
def quote_item_to_dict(item: Any) -> Dict[str, Any]:
    """Convert a quote item model to a dictionary for safe serialization"""
    if not item:
        return {}
        
    return {
        "id": item.id,
        "sku_code": item.sku_code,
        "description": item.description,
        "quantity_units": item.quantity_units,
        "quantity_boxes": item.quantity_boxes,
        "quantity_pieces": item.quantity_pieces,
        "quantity_m2": float(item.quantity_m2) if hasattr(item.quantity_m2, '__float__') else item.quantity_m2,
        "unit_price": float(item.unit_price) if hasattr(item.unit_price, '__float__') else item.unit_price,
        "total_price": float(item.total_price) if hasattr(item.total_price, '__float__') else item.total_price,
        "quote_id": item.quote_id,
        "created_at": item.created_at.isoformat() if hasattr(item.created_at, 'isoformat') else str(item.created_at),
        "updated_at": item.updated_at.isoformat() if hasattr(item.updated_at, 'isoformat') else str(item.updated_at)
    } 