from __future__ import annotations

# Order matters for circular dependencies
from src.schemas.user import (
    UserBase, UserCreate, UserUpdate, UserResponse, 
    UserLogin, Token, TokenData, ManagerListItem
)
from src.schemas.audit_log import (
    AuditLogBase, AuditLogCreate, AuditLogResponse, AuditLogFilter
)

# First import the base models and helper functions
from src.schemas.quote_item import (
    QuoteItemBase, QuoteItemCreate, QuoteItemUpdate, 
    QuoteItemDB, quote_item_to_dict
)

# Then import models that depend on the above
from src.schemas.quote import (
    StoreTypeInfo, CompanyInfo, QuoteNumberInfo, QuoteBase, QuoteItemInput, QuoteCreate, QuoteUpdate,
    QuoteDB, QuoteWithItems, QuoteResponse, QuotePagination
)

# Purchase Order schemas
from src.schemas.purchase_order import (
    PurchaseOrderStatus, PurchaseOrderBase, PurchaseOrderDetailBase,
    PurchaseOrderCreate, PurchaseOrderDetailCreate,
    PurchaseOrderUpdate, PurchaseOrderDetailUpdate,
    PurchaseOrderOut, PurchaseOrderDetailOut,
    PurchaseOrderEmailStatusUpdate, PurchaseOrderDetailNotesUpdate,
    PurchaseOrderPagination
)

# Invoice schemas
from src.schemas.invoice import (
    SaleTypeEnum, PaymentModeEnum, StoreTypeInfo as InvoiceStoreTypeInfo,
    CompanyInfo as InvoiceCompanyInfo, InvoiceNumberInfo,
    InvoiceItemBase, InvoiceBase,
    InvoiceItemCreate, InvoiceCreate,
    InvoiceItemUpdate, InvoiceUpdate,
    InvoiceItemOut, InvoiceOut, InvoiceResponse,
    InvoicePagination
)

# Customer schemas
from src.schemas.customer import (
    CustomerBase, CustomerCreate, CustomerUpdate, CustomerOut, InvoiceSummary
)

# Inventory schemas
from src.schemas.inventory import (
    InventoryBase, InventoryCreate, InventoryUpdate, 
    Inventory, InventoryBulkUpload, CSVImportResult
)

# Supplier schemas
from src.schemas.supplier import (
    SupplierBase, SupplierCreate, SupplierUpdate, SupplierOut, PriceItem
)

# Report schemas
from src.schemas.report import (
    SalesReportBase, SalesReportCreate, SalesReportOut,
    POReportBase, POReportCreate, POReportOut,
    ReportType, ReportDeleteRequest, ReportPagination, ReportCSVUploadResponse
)

__all__ = [
    "UserBase", "UserCreate", "UserUpdate", "UserResponse", 
    "UserLogin", "Token", "TokenData",
    "AuditLogBase", "AuditLogCreate", "AuditLogResponse", "AuditLogFilter",
    "QuoteItemBase", "QuoteItemCreate", "QuoteItemUpdate", "QuoteItemDB", "quote_item_to_dict",
    "StoreTypeInfo", "CompanyInfo", "QuoteNumberInfo", "QuoteBase", "QuoteItemInput", "QuoteCreate", "QuoteUpdate",
    "QuoteDB", "QuoteWithItems", "QuoteResponse", "QuotePagination",
    "PurchaseOrderStatus", "PurchaseOrderBase", "PurchaseOrderDetailBase",
    "PurchaseOrderCreate", "PurchaseOrderDetailCreate",
    "PurchaseOrderUpdate", "PurchaseOrderDetailUpdate",
    "PurchaseOrderOut", "PurchaseOrderDetailOut",
    "PurchaseOrderEmailStatusUpdate", "PurchaseOrderDetailNotesUpdate",
    "PurchaseOrderPagination",
    "SaleTypeEnum", "PaymentModeEnum", "InvoiceStoreTypeInfo",
    "InvoiceCompanyInfo", "InvoiceNumberInfo",
    "InvoiceItemBase", "InvoiceBase",
    "InvoiceItemCreate", "InvoiceCreate",
    "InvoiceItemUpdate", "InvoiceUpdate",
    "InvoiceItemOut", "InvoiceOut", "InvoiceResponse",
    "InvoicePagination",
    "InventoryBase", "InventoryCreate", "InventoryUpdate", 
    "Inventory", "InventoryBulkUpload", "CSVImportResult",
    "CustomerBase", "CustomerCreate", "CustomerUpdate", "CustomerOut", "InvoiceSummary",
    "ManagerListItem",
    "SupplierBase",
    "SupplierCreate",
    "SupplierUpdate",
    "SupplierOut",
    "PriceItem",
    "SalesReportBase", "SalesReportCreate", "SalesReportOut",
    "POReportBase", "POReportCreate", "POReportOut",
    "ReportType", "ReportDeleteRequest", "ReportPagination", "ReportCSVUploadResponse"
] 