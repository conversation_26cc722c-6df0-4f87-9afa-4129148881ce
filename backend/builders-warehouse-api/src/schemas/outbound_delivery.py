from pydantic import BaseModel, Field
from typing import List, Optional
from datetime import date, datetime

class OutboundDeliveryBase(BaseModel):
    invoice_no: str
    invoice_date: date
    linked_po: str
    sku: str
    description: str
    status: str = "New"
    notes: Optional[str] = None

class OutboundDeliveryCreate(OutboundDeliveryBase):
    pass

class OutboundDeliveryUpdate(BaseModel):
    status: str
    notes: Optional[str] = None

class OutboundDeliveryOut(OutboundDeliveryBase):
    model_config = {
        "from_attributes": True
    }

class DeliveredOrderBase(BaseModel):
    invoice_no: str
    invoice_date: date
    linked_po: str
    sku: str
    description: str
    delivered_date: datetime
    notes: Optional[str] = None

class DeliveredOrderCreate(DeliveredOrderBase):
    pass

class DeliveredOrderOut(DeliveredOrderBase):
    model_config = {
        "from_attributes": True
    }

class Pagination(BaseModel):
    total: int
    page: int
    limit: int

class PagedOutboundDeliveryResponse(BaseModel):
    data: List[OutboundDeliveryOut]
    pagination: Pagination

class PagedDeliveredOrderResponse(BaseModel):
    data: List[DeliveredOrderOut]
    pagination: Pagination 