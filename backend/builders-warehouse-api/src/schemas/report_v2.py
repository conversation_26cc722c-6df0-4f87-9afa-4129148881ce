from pydantic import BaseModel, Field
from typing import List, Optional, Dict, Any
from datetime import date, datetime
from enum import Enum

class SaleReport(BaseModel):
    """Schema for invoice report data (last 30 sales)"""
    date: date
    invoice_number: str
    company_name: str
    total_amount: float = Field(ge=0)
    mode_of_payment: str

class PurchaseOrderReport(BaseModel):
    """Schema for purchase order report data (last 30 POs)"""
    date: date
    po_number: str
    supplier_name: str
    linked_invoice_number: Optional[str] = None
    total_amount: float = Field(ge=0)

class ReportResponse(BaseModel):
    """Schema for reports response with pagination metadata"""
    total_sales: int
    total_pos: int
    data: Dict[str, Any] = {
        "last_30_sales": List[SaleReport],
        "last_30_pos": List[PurchaseOrderReport]
    }

class ReportType(str, Enum):
    """Enum for report types"""
    SALES = "sales"
    PO = "po"

class PaginatedSalesResponse(BaseModel):
    """Schema for paginated sales reports response"""
    total: int
    page: int
    limit: int
    items: List[SaleReport]

class PaginatedPurchaseOrdersResponse(BaseModel):
    """Schema for paginated purchase orders reports response"""
    total: int
    page: int
    limit: int
    items: List[PurchaseOrderReport]

class CSVDownloadRequest(BaseModel):
    """Schema for CSV download request"""
    report_type: ReportType
    page: int = Field(1, ge=1, description="Page number, starting from 1")
    limit: int = Field(30, ge=1, le=1000, description="Number of records per page")
    search: Optional[str] = None 