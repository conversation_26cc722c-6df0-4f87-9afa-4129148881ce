from pydantic import BaseModel
from typing import Optional, Dict, Any, Union
from datetime import datetime
import uuid

class AuditLogBase(BaseModel):
    """Base schema for audit log data"""
    table_name: str
    operation: str
    entity_id: str
    user_id: Optional[uuid.UUID] = None
    ip_address: Optional[str] = None

class AuditLogCreate(AuditLogBase):
    """Schema for creating a new audit log entry"""
    changes: Optional[Union[Dict[str, Any], str]] = None

class AuditLogResponse(AuditLogBase):
    """Schema for audit log response data"""
    id: int
    timestamp: datetime
    changes: Optional[str] = None
    
    class Config:
        from_attributes = True

class AuditLogFilter(BaseModel):
    """Schema for filtering audit logs"""
    table_name: Optional[str] = None
    operation: Optional[str] = None
    entity_id: Optional[str] = None
    user_id: Optional[uuid.UUID] = None
    from_date: Optional[datetime] = None
    to_date: Optional[datetime] = None 