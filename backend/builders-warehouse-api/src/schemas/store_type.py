from pydantic import BaseModel
from typing import Optional

class StoreTypeBase(BaseModel):
    """Base schema for store type data"""
    name: str
    description: Optional[str] = None

class StoreTypeCreate(StoreTypeBase):
    """Schema for creating a new store type"""
    pass

class StoreTypeResponse(StoreTypeBase):
    """Schema for store type response data"""
    id: int
    
    class Config:
        from_attributes = True 