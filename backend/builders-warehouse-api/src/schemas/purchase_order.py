from pydantic import BaseModel, Field, ConfigDict, field_validator, UUID4
from typing import List, Optional, Dict, Any, Annotated, Union
from datetime import datetime, date, timezone
from enum import Enum

class PurchaseOrderStatus(str, Enum):
    """Enum for purchase order status"""
    DRAFT = "draft"
    ISSUED = "issued"
    RECEIVED = "received"
    CANCELLED = "cancelled"
    DELIVERED = "delivered"

# Base models
class PurchaseOrderDetailBase(BaseModel):
    """Base schema for purchase order detail data"""
    sku: str
    description: str
    quantity_ordered: int = Field(ge=0)
    quantity_received: int = Field(ge=0, default=0)
    expected_delivery_date: Optional[date] = None
    total: float = Field(ge=0)
    notes: Optional[str] = None

class PurchaseOrderBase(BaseModel):
    """Base schema for purchase order data"""
    supplier_name: Optional[str] = None
    supplier_id: Annotated[int, Field(gt=0)]
    store_type: Optional[str] = None
    invoice_id: Optional[str] = None
    issue_date: date = Field(default_factory=lambda: datetime.now().date())
    status: PurchaseOrderStatus = PurchaseOrderStatus.DRAFT

# Create models
class PurchaseOrderDetailCreate(PurchaseOrderDetailBase):
    """Schema for creating a purchase order detail"""
    pass

class PurchaseOrderCreate(PurchaseOrderBase):
    """Schema for creating a purchase order"""
    details: List[PurchaseOrderDetailCreate] = []
    payment_terms: Optional[str] = None

# Update models
class PurchaseOrderDetailUpdate(BaseModel):
    """Schema for updating a purchase order detail"""
    sku: Optional[str] = None
    description: Optional[str] = None
    quantity_ordered: Optional[int] = Field(ge=0, default=None)
    quantity_received: Optional[int] = Field(ge=0, default=None)
    expected_delivery_date: Optional[date] = None
    total: Optional[float] = Field(ge=0, default=None)
    notes: Optional[str] = None

class PurchaseOrderUpdate(BaseModel):
    """Schema for updating a purchase order"""
    supplier_name: Optional[str] = None
    supplier_id: Optional[int] = None
    store_type: Optional[str] = None
    invoice_id: Optional[str] = None
    issue_date: Optional[date] = None
    status: Optional[PurchaseOrderStatus] = None
    email_sent: Optional[bool] = None
    email_sent_message: Optional[str] = None
    payment_terms: Optional[str] = None

# Output models for details stored in JSONB
class PurchaseOrderDetailJSON(PurchaseOrderDetailBase):
    """Schema for purchase order detail stored in JSON"""
    id: Optional[str] = None
    
    model_config = ConfigDict(extra="allow")

# The traditional full-featured detail model for database-backed details
class PurchaseOrderDetailOut(PurchaseOrderDetailBase):
    """Output schema for purchase order detail from database"""
    id: int
    purchase_order_id: int
    created_at: datetime
    updated_at: datetime
    
    model_config = ConfigDict(from_attributes=True)

class PurchaseOrderOut(PurchaseOrderBase):
    """Output schema for purchase order"""
    id: int
    po_number: str
    email_sent: bool
    email_sent_message: Optional[str] = None
    created_at: datetime
    updated_at: datetime
    details: List[PurchaseOrderDetailJSON] = []  # Changed to use JSON schema
    payment_terms: Optional[str] = None
    total_amount: float
    
    model_config = ConfigDict(from_attributes=True)

# Email status update schema
class PurchaseOrderEmailStatusUpdate(BaseModel):
    """Schema for updating the email status of a purchase order"""
    email_sent: bool
    email_sent_message: Optional[str] = None

# Note update schema for purchase order details
class PurchaseOrderDetailNotesUpdate(BaseModel):
    """Schema for updating the notes of a purchase order detail"""
    notes: str

# Pagination
class PurchaseOrderPagination(BaseModel):
    """Schema for paginated purchase orders"""
    total: int
    items: List[PurchaseOrderOut]
    page: int
    size: int 