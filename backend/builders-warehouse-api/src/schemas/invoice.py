from pydantic import BaseModel, Field, ConfigDict, field_validator, UUID4
import typing
from datetime import datetime, date
from enum import Enum
from decimal import Decimal

class SaleTypeEnum(str, Enum):
    """Enum for sale type options"""
    TRADE = "trade"
    RETAIL = "retail"
    STORE = "store"

class PaymentModeEnum(str, Enum):
    """Enum for payment mode options"""
    CASH = "cash"
    BANK_TRANSFER = "bank_transfer"
    CREDIT_CARD = "credit_card"

class StoreTypeInfo(BaseModel):
    """Schema for store type information"""
    id: int
    name: str
    
    model_config = ConfigDict(from_attributes=True)

class CompanyInfo(BaseModel):
    """Schema for company information"""
    id: typing.Union[UUID4, int, str]
    name: str
    
    model_config = ConfigDict(
        from_attributes=True,
        arbitrary_types_allowed=True
    )

class InvoiceNumberInfo(BaseModel):
    """Schema for invoice number information"""
    id: str
    formatted_number: str
    
    model_config = ConfigDict(from_attributes=True)

# Base models
class InvoiceItemBase(BaseModel):
    """Base schema for invoice item data"""
    model_config = ConfigDict(extra="ignore")
    
    sku: str
    description: str
    units: typing.Annotated[int, Field(ge=0, default=0)]
    boxes: typing.Annotated[int, Field(ge=0, default=0)]
    pieces: typing.Annotated[int, Field(ge=0, default=0)]
    m2: typing.Annotated[Decimal, Field(ge=0, default=0)]
    unit_price: typing.Annotated[Decimal, Field(ge=0)]
    
    @field_validator('unit_price')
    def validate_unit_price(cls, v):
        if v < 0:
            raise ValueError('Unit price must be non-negative')
        return v

class InvoiceBase(BaseModel):
    """Base schema for invoice data"""
    model_config = ConfigDict(extra="ignore")
    
    store_type_id: int
    sale_type: SaleTypeEnum
    mode_of_payment: PaymentModeEnum
    purchase_order_number: typing.Optional[str] = None
    po_id: typing.Optional[int] = None  # Add reference to purchase order ID
    customer_id: typing.Union[UUID4, int]  # Accept both UUID and int
    company_id: typing.Union[UUID4, int]  # Accept both UUID and int
    deliver_to_address: typing.Optional[str] = None
    date: typing.Annotated[date, Field(default_factory=date.today)]
    dont_send_po: bool = False
    linked_quote_id: typing.Optional[UUID4] = None
    notes: typing.Optional[str] = None
    shipping: typing.Annotated[Decimal, Field(ge=0, default=0)]
    
    @field_validator('date')
    def date_not_in_future(cls, v: date) -> date:
        if v > date.today():
            raise ValueError('Date cannot be in the future')
        return v

# Create models
class InvoiceItemCreate(InvoiceItemBase):
    """Schema for creating an invoice item"""
    model_config = ConfigDict(extra="ignore")
    pass

class InvoiceCreate(InvoiceBase):
    """Schema for creating an invoice"""
    items: typing.List[InvoiceItemCreate] = []
    model_config = ConfigDict(extra="ignore")

# Update models
class InvoiceItemUpdate(BaseModel):
    """Schema for updating an invoice item"""
    sku: typing.Optional[str] = None
    description: typing.Optional[str] = None
    units: typing.Optional[typing.Annotated[int, Field(ge=0)]] = None
    boxes: typing.Optional[typing.Annotated[int, Field(ge=0)]] = None
    pieces: typing.Optional[typing.Annotated[int, Field(ge=0)]] = None
    m2: typing.Optional[typing.Annotated[Decimal, Field(ge=0)]] = None
    unit_price: typing.Optional[typing.Annotated[Decimal, Field(ge=0)]] = None
    model_config = ConfigDict(extra="ignore")

class InvoiceUpdate(BaseModel):
    """Schema for updating an invoice"""
    store_type_id: typing.Optional[int] = None
    sale_type: typing.Optional[SaleTypeEnum] = None
    mode_of_payment: typing.Optional[PaymentModeEnum] = None
    purchase_order_number: typing.Optional[str] = None
    po_id: typing.Optional[int] = None  # Add reference to purchase order ID
    customer_id: typing.Optional[typing.Union[UUID4, int]] = None
    company_id: typing.Optional[typing.Union[UUID4, int]] = None
    deliver_to_address: typing.Optional[str] = None
    date: typing.Optional[date] = None
    dont_send_po: typing.Optional[bool] = None
    linked_quote_id: typing.Optional[UUID4] = None
    notes: typing.Optional[str] = None
    shipping: typing.Optional[typing.Annotated[Decimal, Field(ge=0)]] = None
    items: typing.Optional[typing.List[InvoiceItemCreate]] = None
    model_config = ConfigDict(extra="ignore")
    
    @field_validator('date')
    def date_not_in_future(cls, v: typing.Optional[date]) -> typing.Optional[date]:
        if v is not None and v > date.today():
            raise ValueError('Date cannot be in the future')
        return v

# Output models
class InvoiceItemOut(BaseModel):
    """Output schema for invoice item with flexible types"""
    id: typing.Union[UUID4, str, int]
    invoice_id: typing.Union[UUID4, str, int]
    sku: str
    description: str
    units: typing.Union[int, float, None] = 0
    boxes: typing.Union[int, float, None] = 0
    pieces: typing.Union[int, float, None] = 0
    m2: typing.Union[Decimal, float, None] = 0
    unit_price: typing.Union[Decimal, float]
    total_price: typing.Union[Decimal, float]
    created_at: typing.Optional[datetime] = None
    updated_at: typing.Optional[datetime] = None
    
    model_config = ConfigDict(
        from_attributes=True,
        arbitrary_types_allowed=True,
        extra="allow"
    )

class InvoiceOut(BaseModel):
    """Output schema for invoice"""
    # Use Union types to handle different data formats
    id: typing.Union[UUID4, int, str]
    invoice_number: typing.Union[InvoiceNumberInfo, str, dict]
    store_type_id: typing.Optional[int] = None
    store_type: typing.Union[StoreTypeInfo, str, dict, None] = None
    sale_type: typing.Union[SaleTypeEnum, str, None] = None
    mode_of_payment: typing.Union[PaymentModeEnum, str, None] = None
    purchase_order_number: typing.Optional[str] = None
    po_id: typing.Optional[int] = None  # Add reference to purchase order ID
    customer_id: typing.Union[UUID4, int, str]
    company_id: typing.Union[UUID4, int, str]
    company: typing.Union[CompanyInfo, dict, None] = None
    deliver_to_address: typing.Optional[str] = None
    date: typing.Union[date, datetime, str, None] = None
    dont_send_po: typing.Optional[bool] = False
    linked_quote_id: typing.Optional[typing.Union[UUID4, str]] = None
    notes: typing.Optional[str] = None
    shipping: typing.Union[Decimal, float, int, None] = None
    total_order: typing.Union[Decimal, float, int, None] = None
    credit_card_surcharge: typing.Union[Decimal, float, int, None] = None
    total_gst: typing.Union[Decimal, float, int, None] = None
    grand_total: typing.Union[Decimal, float, int, None] = None
    created_at: typing.Optional[datetime] = None
    updated_at: typing.Optional[datetime] = None
    items: typing.Union[typing.List[InvoiceItemOut], typing.Dict[str, typing.Any], typing.Any] = []
    
    model_config = ConfigDict(
        from_attributes=True,
        arbitrary_types_allowed=True,
        extra="allow"
    )
    
    @field_validator('sale_type', mode='before')
    def validate_sale_type(cls, v):
        # Handle enum objects from database model
        if hasattr(v, 'value'):
            return v.value
        return v
    
    @field_validator('mode_of_payment', mode='before')
    def validate_payment_mode(cls, v):
        # Handle enum objects from database model
        if hasattr(v, 'value'):
            return v.value
        return v
        
    @field_validator('company', mode='before')
    def validate_company(cls, v):
        # Handle company objects from database model
        if hasattr(v, '__dict__'):
            # Convert ORM model to dict
            return {
                'id': getattr(v, 'id', None),
                'name': getattr(v, 'name', None)
            }
        return v

class InvoiceResponse(BaseModel):
    """Basic response schema for invoice in list view"""
    id: str
    invoice_number: typing.Union[InvoiceNumberInfo, str, None] = None
    invoice_no: typing.Optional[str] = None  # For backward compatibility
    customer_id: typing.Optional[str] = None
    customer_name: typing.Optional[str] = None  # Populated from customer relation
    company_id: typing.Optional[str] = None
    company_name: typing.Optional[str] = None  # Populated from company relation
    store_type_id: typing.Optional[int] = None
    store_type_name: typing.Optional[str] = None  # Populated from store_type relation
    store_type: typing.Optional[str] = None  # For backward compatibility
    po_id: typing.Optional[int] = None  # Add purchase order reference
    status: typing.Optional[str] = None  # Add status field for draft/saved invoices
    date: date
    grand_total: Decimal
    notes: typing.Optional[str] = None
    
    model_config = ConfigDict(from_attributes=True, populate_by_name=True)

# Pagination
class InvoicePagination(BaseModel):
    """Schema for paginated invoices"""
    total: int
    items: typing.List[InvoiceResponse]
    page: int
    size: int
    
    model_config = ConfigDict(from_attributes=True) 