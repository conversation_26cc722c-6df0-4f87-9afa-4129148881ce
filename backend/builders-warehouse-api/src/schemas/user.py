from pydantic import BaseModel, EmailStr, Field, validator, model_validator, field_validator
from typing import Optional, List, Union
from datetime import datetime
import re
import uuid
import typing

# ======================================================================
# TEMPORARY FIX: Store type and manager validations have been disabled
# to allow more flexibility in user creation and updates. This is a
# temporary measure and should be reverted once the frontend is updated
# to properly handle these requirements.
#
# Changes:
# 1. Commented out validation requiring store_type_id for Manager and Staff
# 2. Commented out validation requiring manager_id for Staff when created by Admin
# ======================================================================

from src.models.user import UserRole
from src.models.store_type import StoreType

class ManagerInfo(BaseModel):
    """Schema for manager information in user response"""
    id: Union[str, uuid.UUID]  # Accept either string or UUID
    user_name: str
    
    @field_validator('id')
    @classmethod
    def validate_id(cls, v):
        if isinstance(v, uuid.UUID):
            return str(v)  # Convert UUID to string
        return v

class StoreTypeInfo(BaseModel):
    """Schema for store type information in user response"""
    id: int
    name: str

class UserBase(BaseModel):
    """Base schema for user data"""
    user_name: str
    email: EmailStr
    mobile_number: str
    
    @validator('mobile_number')
    def validate_mobile_number(cls, v):
        # Basic mobile number validation (can be adjusted as needed)
        if not re.match(r'^\+?[0-9]{10,15}$', v):
            raise ValueError('Invalid mobile number format. Should be 10-15 digits with optional + prefix')
        return v

class UserCreate(UserBase):
    """Schema for creating a new user"""
    password: str
    role: UserRole = UserRole.STAFF
    store_type_id: Optional[int] = None
    manager_id: Optional[str] = None
    
    @validator('password')
    def validate_password(cls, v):
        # Password must have at least 8 chars, one uppercase, one lowercase, one number, one special char
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters')
        if not re.search(r'[A-Z]', v):
            raise ValueError('Password must contain at least one uppercase letter')
        if not re.search(r'[a-z]', v):
            raise ValueError('Password must contain at least one lowercase letter')
        if not re.search(r'[0-9]', v):
            raise ValueError('Password must contain at least one number')
        if not re.search(r'[!@#$%^&*(),.?":{}|<>]', v):
            raise ValueError('Password must contain at least one special character')
        return v
    
    @model_validator(mode='after')
    def validate_role_based_fields(self):
        role = self.role
        store_type_id = self.store_type_id
        
        # Rule 1: Admin must not have a store_type
        if role == UserRole.ADMIN and store_type_id is not None:
            self.store_type_id = None
        
        # TEMPORARY FIX: Make store_type_id optional for all roles
        # Rule 3: Manager and Staff must have a store_type
        # if role in [UserRole.MANAGER, UserRole.STAFF] and store_type_id is None:
        #     raise ValueError('store_type_id is required for Manager and Staff roles')
            
        # Note: Rule 4 (Manager requirement for Staff) will be handled in the service layer
        # since it depends on who's creating the user
            
        return self
    
    model_config = {
        "json_schema_extra": {
            "example": {
                "user_name": "johnsmith",
                "email": "<EMAIL>",
                "mobile_number": "+1234567890",
                "password": "Password123!",
                "role": "staff",
                "store_type_id": 1,
                "manager_id": "2"
            }
        }
    }

class UserUpdate(BaseModel):
    """Schema for updating an existing user"""
    user_name: Optional[str] = None
    email: Optional[EmailStr] = None
    mobile_number: Optional[str] = None
    password: Optional[str] = None
    is_active: Optional[bool] = None
    role: Optional[UserRole] = None
    store_type_id: Optional[int] = None
    manager_id: Optional[str] = None
    
    @validator('mobile_number')
    def validate_mobile_number(cls, v):
        if v is None:
            return v
        # Basic mobile number validation
        if not re.match(r'^\+?[0-9]{10,15}$', v):
            raise ValueError('Invalid mobile number format. Should be 10-15 digits with optional + prefix')
        return v
    
    @validator('password')
    def validate_password(cls, v):
        if v is None:
            return v
        # Password validation
        if len(v) < 8:
            raise ValueError('Password must be at least 8 characters')
        if not re.search(r'[A-Z]', v):
            raise ValueError('Password must contain at least one uppercase letter')
        if not re.search(r'[a-z]', v):
            raise ValueError('Password must contain at least one lowercase letter')
        if not re.search(r'[0-9]', v):
            raise ValueError('Password must contain at least one number')
        if not re.search(r'[!@#$%^&*(),.?":{}|<>]', v):
            raise ValueError('Password must contain at least one special character')
        return v
        
    @model_validator(mode='after')
    def validate_role_based_fields(self):
        # Only process if role is being updated
        role = self.role
        if role is None:
            return self
            
        store_type_id = self.store_type_id
        
        # Rule 1 & 2: Admin must not have a store_type
        if role == UserRole.ADMIN and store_type_id is not None:
            self.store_type_id = None
        
        # TEMPORARY FIX: Make store_type_id optional for all roles
        # Rule 3: Manager and Staff must have a store_type if being set
        # if role in [UserRole.MANAGER, UserRole.STAFF] and store_type_id is None:
        #     # Note: We can't raise an error here because the store_type might not be 
        #     # changing in this update. This will be handled in the service layer.
        #     pass
            
        return self
    
    model_config = {
        "json_schema_extra": {
            "example": {
                "user_name": "johnsmith",
                "email": "<EMAIL>",
                "mobile_number": "+1234567890",
                "password": "Password123!",
                "role": "staff",
                "store_type_id": 1,
                "manager_id": "2"
            }
        }
    }

class UserLogin(BaseModel):
    """Schema for email/password login"""
    email: EmailStr
    password: str

class UserResponse(BaseModel):
    """Schema for user response data"""
    id: Union[str, uuid.UUID]  # Accept either string or UUID
    user_name: str
    email: EmailStr
    mobile_number: Optional[str] = ""  # Allow None and default to empty string
    is_active: bool
    role: UserRole
    created_at: datetime
    updated_at: Optional[datetime] = None
    store_type: Optional[StoreTypeInfo] = None
    manager: Optional[ManagerInfo] = None
    
    # Model configurator with model_validator to handle null mobile_number and UUID ids
    @field_validator('id')
    @classmethod
    def validate_id(cls, v):
        if isinstance(v, uuid.UUID):
            return str(v)  # Convert UUID to string
        return v
    
    @field_validator('mobile_number')
    @classmethod
    def validate_mobile_number(cls, v):
        if v is None:
            return ""  # Convert None to empty string
        return v
    
    model_config = {
        "from_attributes": True
    }

class ManagerListItem(BaseModel):
    """Schema for manager dropdown items"""
    id: typing.Union[str, uuid.UUID]  # Accept either string or UUID
    user_name: str
    store_type: Optional[StoreTypeInfo] = None  # Add store_type info
    
    @field_validator('id')
    @classmethod
    def validate_id(cls, v):
        if isinstance(v, uuid.UUID):
            return str(v)  # Convert UUID to string
        return v
    
    model_config = {
        "from_attributes": True
    }

class TokenData(BaseModel):
    """Schema for data stored in JWT token"""
    user_id: Optional[str] = None
    email: Optional[str] = None
    username: Optional[str] = None  # Kept for backward compatibility
    role: Optional[str] = None
    store_type: Optional[str] = None

class Token(BaseModel):
    """Schema for JWT token response"""
    access_token: str
    token_type: str
    user: TokenData