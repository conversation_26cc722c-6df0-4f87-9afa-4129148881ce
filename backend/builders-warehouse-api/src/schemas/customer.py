from typing import List, Optional, Union, Annotated, Any
from datetime import date, datetime
from decimal import Decimal
from uuid import UUID
from pydantic import BaseModel, EmailStr, validator, Field, StringConstraints, ConfigDict, field_validator


class PriceListBase(BaseModel):
    name: str
    currency: str = "AUD"


class PriceListCreate(PriceListBase):
    pass


class PriceListUpdate(PriceListBase):
    name: Optional[str] = None
    currency: Optional[str] = None


class PriceList(PriceListBase):
    id: Union[UUID, int]
    model_config = ConfigDict(from_attributes=True)


class CustomerBase(BaseModel):
    company_name: str
    contact_person: Optional[str] = None
    email: EmailStr
    phone: Optional[Annotated[str, StringConstraints(pattern=r'^\+?\d{7,15}$')]]
    is_account: bool = False
    billing_address: Optional[str] = None
    billing_suburb: Optional[str] = None
    billing_postcode: Optional[str] = None
    price_list_id: Optional[Union[UUID, int]] = None


class CustomerCreate(CustomerBase):
    @field_validator('email')
    def email_must_be_valid(cls, v):
        # Additional custom validation if needed
        return v


class CustomerUpdate(BaseModel):
    company_name: Optional[str] = None
    contact_person: Optional[str] = None
    email: Optional[EmailStr] = None
    phone: Optional[Annotated[str, StringConstraints(pattern=r'^\+?\d{7,15}$')]] = None
    is_account: Optional[bool] = None
    billing_address: Optional[str] = None
    billing_suburb: Optional[str] = None
    billing_postcode: Optional[str] = None
    price_list_id: Optional[Union[UUID, int]] = None


class InvoiceSummary(BaseModel):
    invoice_count: int = 0
    total_amount: Union[Decimal, float] = 0
    recent_invoices: Optional[List[Any]] = None
    
    model_config = ConfigDict(arbitrary_types_allowed=True)


class CustomerOut(CustomerBase):
    id: Union[UUID, int]
    price_list: Optional[PriceList] = None
    invoice_summary: Optional[InvoiceSummary] = None
    model_config = ConfigDict(from_attributes=True)


class InvoiceListParams(BaseModel):
    start_date: Optional[date] = None
    end_date: Optional[date] = None
    invoice_type: Optional[str] = None
    skip: int = 0
    limit: int = 10


class PaginatedCustomerResponse(BaseModel):
    items: List[CustomerOut]
    total: int
    page: int
    pages: int
    per_page: int 