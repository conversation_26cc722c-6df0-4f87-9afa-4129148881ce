from pydantic import BaseModel, ConfigDict, Field, field_validator
from typing import Optional, Union
from datetime import datetime
import uuid

# Base model for shared fields
class DraftMailBase(BaseModel):
    """Base schema for draft mail with common fields"""
    invoice_id: int
    po_id: int
    subject: str
    body: str
    ready_to_send: bool = False

# Create model
class DraftMailCreate(DraftMailBase):
    """Schema for creating a draft mail"""
    pass

# Update model
class DraftMailUpdate(BaseModel):
    """Schema for updating a draft mail"""
    subject: Optional[str] = None
    body: Optional[str] = None
    ready_to_send: Optional[bool] = None
    sent: Optional[bool] = None

# Response model
class DraftMailOut(DraftMailBase):
    """Output schema for draft mail"""
    id: int
    created_by: uuid.UUID
    sent: bool = False
    sent_at: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime
    
    # Handle UUID conversion from string if needed
    @field_validator('created_by')
    @classmethod
    def validate_uuid(cls, v):
        if isinstance(v, str):
            return uuid.UUID(v)
        return v
    
    model_config = ConfigDict(
        from_attributes=True,
        arbitrary_types_allowed=True
    )

# Pagination model
class DraftMailPagination(BaseModel):
    """Schema for paginated draft mails"""
    total: int
    items: list[DraftMailOut]
    page: int
    size: int
    
    model_config = ConfigDict(from_attributes=True) 