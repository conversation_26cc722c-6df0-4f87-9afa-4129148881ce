from sqlalchemy.orm import Session, joinedload
from sqlalchemy import desc, asc, and_, or_, func, between
from datetime import datetime, date, timedelta
from typing import List, Optional, Dict, Any, Tuple, Union
from fastapi import HTTPException, status

from src.models.purchase_order import PurchaseOrder
from src.schemas.purchase_order import PurchaseOrderCreate, PurchaseOrderUpdate, PurchaseOrderDetailUpdate, PurchaseOrderEmailStatusUpdate, PurchaseOrderDetailNotesUpdate

# Purchase Order CRUD operations
def create_purchase_order(db: Session, po: PurchaseOrderCreate) -> PurchaseOrder:
    """Create a new purchase order with details"""
    # Create the purchase order
    db_po = PurchaseOrder(
        supplier_name=po.supplier_name,
        issue_date=po.issue_date,
        status=po.status,
        email_sent=False,
        email_sent_message=None
    )
    
    db.add(db_po)
    db.flush()  # Flush to get the ID
    
    # Add all details
    for item in po.details:
        detail = PurchaseOrderDetail(
            purchase_order_id=db_po.id,
            sku=item.sku,
            description=item.description,
            quantity_ordered=item.quantity_ordered,
            quantity_received=item.quantity_received,
            expected_delivery_date=item.expected_delivery_date,
            total=item.total,
            notes=item.notes
        )
        db.add(detail)
    
    db.commit()
    db.refresh(db_po)
    return db_po

def get_purchase_orders(
    db: Session, 
    skip: int = 0, 
    limit: int = 100,
    supplier_name: Optional[str] = None,
    start_date: Optional[date] = None,
    end_date: Optional[date] = None,
    status: Optional[str] = None,
    include_deleted: bool = False
) -> Tuple[List[PurchaseOrder], int]:
    """Get all purchase orders with optional filtering"""
    query = db.query(PurchaseOrder)
    
    # Apply filters
    if not include_deleted:
        query = query.filter(PurchaseOrder.is_deleted == False)
    
    if supplier_name:
        query = query.filter(PurchaseOrder.supplier_name.ilike(f"%{supplier_name}%"))
    
    if start_date and end_date:
        # Add time component to make it inclusive of the full day
        start_datetime = datetime.combine(start_date, datetime.min.time())
        end_datetime = datetime.combine(end_date, datetime.max.time())
        query = query.filter(between(PurchaseOrder.issue_date, start_datetime, end_datetime))
    elif start_date:
        start_datetime = datetime.combine(start_date, datetime.min.time())
        query = query.filter(PurchaseOrder.issue_date >= start_datetime)
    elif end_date:
        end_datetime = datetime.combine(end_date, datetime.max.time())
        query = query.filter(PurchaseOrder.issue_date <= end_datetime)
    
    if status:
        query = query.filter(PurchaseOrder.status == status)
    
    # Get total count for pagination
    total = query.count()
    
    # Apply pagination and sorting (newest first)
    purchase_orders = query.order_by(desc(PurchaseOrder.issue_date)).offset(skip).limit(limit).all()
    
    return purchase_orders, total

def get_purchase_order(db: Session, po_id: int, include_deleted: bool = False) -> Optional[PurchaseOrder]:
    """Get a purchase order by ID with details"""
    query = db.query(PurchaseOrder).options(joinedload(PurchaseOrder.details)).filter(PurchaseOrder.id == po_id)
    
    if not include_deleted:
        query = query.filter(PurchaseOrder.is_deleted == False)
    
    return query.first()

def update_purchase_order(db: Session, po_id: int, po_update: PurchaseOrderUpdate) -> Optional[PurchaseOrder]:
    """Update a purchase order"""
    db_po = get_purchase_order(db, po_id)
    if not db_po:
        return None
    
    # Update only the provided fields
    update_data = po_update.model_dump(exclude_unset=True)
    for key, value in update_data.items():
        setattr(db_po, key, value)
    
    db.commit()
    db.refresh(db_po)
    return db_po

def delete_purchase_order(db: Session, po_id: int) -> bool:
    """Soft delete a purchase order"""
    db_po = get_purchase_order(db, po_id)
    if not db_po:
        return False
    
    db_po.is_deleted = True
    db.commit()
    return True

def update_purchase_order_email_status(db: Session, po_id: int, email_status: PurchaseOrderEmailStatusUpdate) -> Optional[PurchaseOrder]:
    """Update the email status of a purchase order"""
    db_po = get_purchase_order(db, po_id)
    if not db_po:
        return None
    
    db_po.email_sent = email_status.email_sent
    db_po.email_sent_message = email_status.email_sent_message
    
    db.commit()
    db.refresh(db_po)
    return db_po

# Purchase Order Detail CRUD operations
def get_purchase_order_detail(db: Session, detail_id: int) -> Optional[PurchaseOrderDetail]:
    """Get a purchase order detail by ID"""
    return db.query(PurchaseOrderDetail).filter(PurchaseOrderDetail.id == detail_id).first()

def update_purchase_order_detail(db: Session, detail_id: int, detail_update: PurchaseOrderDetailUpdate) -> Optional[PurchaseOrderDetail]:
    """Update a purchase order detail"""
    db_detail = get_purchase_order_detail(db, detail_id)
    if not db_detail:
        return None
    
    # Update only the provided fields
    update_data = detail_update.model_dump(exclude_unset=True)
    for key, value in update_data.items():
        setattr(db_detail, key, value)
    
    db.commit()
    db.refresh(db_detail)
    return db_detail

def update_purchase_order_detail_notes(db: Session, detail_id: int, notes_update: PurchaseOrderDetailNotesUpdate) -> Optional[PurchaseOrderDetail]:
    """Update the notes of a purchase order detail"""
    db_detail = get_purchase_order_detail(db, detail_id)
    if not db_detail:
        return None
    
    db_detail.notes = notes_update.notes
    
    db.commit()
    db.refresh(db_detail)
    return db_detail 