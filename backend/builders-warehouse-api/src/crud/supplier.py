from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import or_, desc
import json
import logging

from src.models.supplier import Supplier
from src.schemas.supplier import SupplierCreate, SupplierUpdate
from src.models.audit_log import AuditLog

logger = logging.getLogger(__name__)

def create_supplier(db: Session, supplier_data: SupplierCreate, user_id: Optional[int] = None) -> Supplier:
    """
    Create a new supplier
    """
    # Convert price list to json if provided
    price_list_json = None
    if supplier_data.price_list:
        price_list_json = [item.model_dump() for item in supplier_data.price_list]
    
    # Create supplier object
    db_supplier = Supplier(
        supplier_name=supplier_data.supplier_name,
        phone_no=supplier_data.phone_no,
        email=supplier_data.email,
        address=supplier_data.address,
        price_list=price_list_json,
        created_by=user_id,
        updated_by=user_id
    )
    
    db.add(db_supplier)
    db.flush()
    
    # Audit logging
    entity_data = {
        key: getattr(db_supplier, key) 
        for key in db_supplier.__table__.columns.keys()
        if key not in ['created_at', 'updated_at', 'created_by', 'updated_by']
    }
    
    log_entry = AuditLog(
        event_type="CREATE",
        status="success",
        email=None,  # Could use supplier email if appropriate
        additional_info=json.dumps({
            "entity_type": "suppliers",
            "entity_id": str(db_supplier.id),
            "user_id": user_id,
            "entity_data": entity_data
        })
    )
    
    db.add(log_entry)
    db.commit()
    db.refresh(db_supplier)
    
    logger.info(f"Created supplier: {db_supplier.supplier_name}")
    return db_supplier

def get_supplier(db: Session, supplier_id: int) -> Optional[Supplier]:
    """
    Get a supplier by ID
    """
    return db.query(Supplier).filter(Supplier.id == supplier_id).first()

def get_supplier_by_name(db: Session, supplier_name: str) -> Optional[Supplier]:
    """
    Get a supplier by name
    """
    return db.query(Supplier).filter(Supplier.supplier_name == supplier_name).first()

def get_suppliers(
    db: Session, 
    skip: int = 0, 
    limit: int = 100,
    search: Optional[str] = None,
    is_active: Optional[bool] = None
) -> List[Supplier]:
    """
    Get all suppliers with optional filtering
    """
    query = db.query(Supplier)
    
    # Apply search filter
    if search:
        search_term = f"%{search}%"
        query = query.filter(
            or_(
                Supplier.supplier_name.ilike(search_term),
                Supplier.email.ilike(search_term),
                Supplier.phone_no.ilike(search_term)
            )
        )
    
    # Apply active filter
    if is_active is not None:
        query = query.filter(Supplier.is_active == is_active)
    
    # Order by supplier name
    return query.order_by(Supplier.supplier_name).offset(skip).limit(limit).all()

def update_supplier(
    db: Session, 
    supplier_id: int, 
    supplier_data: SupplierUpdate, 
    user_id: Optional[int] = None
) -> Optional[Supplier]:
    """
    Update an existing supplier
    """
    db_supplier = get_supplier(db, supplier_id)
    if not db_supplier:
        return None

    # Keep track of old values for audit logging
    old_values = {
        key: getattr(db_supplier, key) 
        for key in db_supplier.__table__.columns.keys()
        if key not in ['created_at', 'updated_at', 'created_by', 'updated_by']
    }
    
    # Get update data excluding None values
    update_data = supplier_data.model_dump(exclude_unset=True)
    
    # Convert price list to json if provided
    if 'price_list' in update_data and update_data['price_list'] is not None:
        update_data['price_list'] = [item.model_dump() for item in update_data['price_list']]
    
    # Track which fields were actually changed
    changed_fields = {}
    
    for key, value in update_data.items():
        if hasattr(db_supplier, key) and getattr(db_supplier, key) != value:
            changed_fields[key] = value
            setattr(db_supplier, key, value)
    
    # Set updated_by
    if user_id:
        db_supplier.updated_by = user_id
    
    # Only create audit log if something actually changed
    if changed_fields:
        new_values = {k: changed_fields[k] for k in changed_fields.keys()}
        
        log_entry = AuditLog(
            event_type="UPDATE",
            status="success",
            email=None,
            additional_info=json.dumps({
                "entity_type": "suppliers",
                "entity_id": str(db_supplier.id),
                "user_id": user_id,
                "before": {k: old_values[k] for k in changed_fields.keys()},
                "after": new_values
            })
        )
        
        db.add(log_entry)
        logger.info(f"Updated supplier: {db_supplier.supplier_name}")
    
    db.commit()
    db.refresh(db_supplier)
    return db_supplier

def delete_supplier(db: Session, supplier_id: int, user_id: Optional[int] = None) -> bool:
    """
    Delete a supplier (soft-delete by setting is_active=False)
    """
    db_supplier = get_supplier(db, supplier_id)
    if not db_supplier:
        return False
    
    # Soft delete by setting is_active to False
    old_is_active = db_supplier.is_active
    db_supplier.is_active = False
    db_supplier.updated_by = user_id
    
    # Audit logging
    if old_is_active != False:  # Only log if there was a change
        log_entry = AuditLog(
            event_type="DELETE",
            status="success",
            email=None,
            additional_info=json.dumps({
                "entity_type": "suppliers",
                "entity_id": str(supplier_id),
                "user_id": user_id,
                "changes": {
                    "is_active": {"old": old_is_active, "new": False}
                }
            })
        )
        
        db.add(log_entry)
        logger.info(f"Soft-deleted supplier: {supplier_id}")
    
    db.commit()
    return True 