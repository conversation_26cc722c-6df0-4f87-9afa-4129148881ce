"""
DEPRECATED: The `crud` module is deprecated and will be removed in future versions.

This module was originally used for CRUD operations directly on database models.
The application has since migrated to a more structured Repository pattern in
the `repository` module.

New code should use the Repository pattern instead of these direct CRUD functions.
Example usage:

```python
# Instead of this:
from src.crud import inventory
items = inventory.get_all_inventory(db)

# Use this:
from src.repository import inventory
items = inventory.get_inventories(db)
```

This module is maintained for backward compatibility only.
"""

# Import modules for backward compatibility
from .inventory import *
from .invoice import *
from .purchase_order import *
from .supplier import *

# Define the public API 
__all__ = [
    # Inventory
    "create_inventory",
    "get_inventory",
    "get_inventory_by_sku",
    "get_all_inventory",
    "update_inventory",
    "delete_inventory",
    
    # Invoice
    "create_invoice",
    "get_invoice",
    "get_invoices",
    "update_invoice",
    "delete_invoice",
    
    # Purchase Order
    "create_purchase_order",
    "get_purchase_order",
    "get_purchase_orders",
    "update_purchase_order",
    "delete_purchase_order",
    
    # Supplier
    "create_supplier",
    "get_supplier",
    "get_supplier_by_name",
    "get_suppliers",
    "update_supplier",
    "delete_supplier",
] 