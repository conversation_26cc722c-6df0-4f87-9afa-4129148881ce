from sqlalchemy.orm import Session, joinedload
from sqlalchemy import desc, asc, and_, or_, func, between
from datetime import datetime, date, timedelta
from typing import List, Optional, Dict, Any, Tuple, Union
from fastapi import HTTPException, status
import uuid

from src.models.invoice import Invoice, InvoiceItem, SaleType, PaymentMode
from src.models.customer import Customer
from src.models.purchase_order import PurchaseOrder
from src.schemas.invoice import InvoiceCreate, InvoiceUpdate, InvoiceItemCreate

def generate_invoice_number(db: Session) -> str:
    """Generate a unique invoice number with format INV-YYYYMMDD-XXX"""
    today = datetime.now()
    date_part = today.strftime("%Y%m%d")
    
    # Find the latest invoice number for today
    latest_invoice = db.query(Invoice).filter(
        Invoice.invoice_no.like(f"INV-{date_part}-%")
    ).order_by(desc(Invoice.invoice_no)).first()
    
    if latest_invoice:
        # Extract sequence number and increment
        try:
            seq_str = latest_invoice.invoice_no.split('-')[-1]
            seq_num = int(seq_str) + 1
        except (ValueError, IndexError):
            seq_num = 1
    else:
        seq_num = 1
    
    # Format with leading zeros for the sequence
    return f"INV-{date_part}-{seq_num:03d}"

def create_invoice(
    db: Session, 
    invoice_data: InvoiceCreate, 
    user_id: int,
    user_store_type: str
) -> Invoice:
    """Create a new invoice with items"""
    # Generate invoice number
    invoice_no = generate_invoice_number(db)
    
    # If po_id is provided, ensure it exists
    if invoice_data.po_id is not None:
        po_exists = db.query(PurchaseOrder).filter(PurchaseOrder.id == invoice_data.po_id).first()
        if not po_exists:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Purchase order with ID {invoice_data.po_id} not found"
            )
    
    # Create invoice
    db_invoice = Invoice(
        invoice_no=invoice_no,
        # Auto-fill store_type based on logged-in user
        store_type=user_store_type,
        sale_type=SaleType(invoice_data.sale_type.value if hasattr(invoice_data.sale_type, 'value') else invoice_data.sale_type),
        mode_of_payment=PaymentMode(invoice_data.mode_of_payment.value if hasattr(invoice_data.mode_of_payment, 'value') else invoice_data.mode_of_payment),
        purchase_order_number=invoice_data.purchase_order_number,
        po_id=invoice_data.po_id,
        customer_id=invoice_data.customer_id,
        deliver_to_address=invoice_data.deliver_to_address,
        date=invoice_data.date,
        dont_send_po=invoice_data.dont_send_po,
        linked_quote_id=invoice_data.linked_quote_id,
        notes=invoice_data.notes,
        shipping=invoice_data.shipping
    )
    
    db.add(db_invoice)
    db.flush()  # Flush to get the ID
    
    # Add all items
    for item_data in invoice_data.items:
        item = InvoiceItem(
            invoice_id=db_invoice.id,
            sku=item_data.sku,
            description=item_data.description,
            units=item_data.units,
            boxes=item_data.boxes,
            pieces=item_data.pieces,
            m2=item_data.m2,
            unit_price=item_data.unit_price
        )
        db.add(item)
    
    db.flush()
    
    # Calculate totals
    db_invoice.calculate_totals()
    
    db.commit()
    db.refresh(db_invoice)
    return db_invoice

def get_invoices(
    db: Session, 
    skip: int = 0, 
    limit: int = 100,
    customer_id: Optional[uuid.UUID] = None,
    store_type: Optional[str] = None,
    start_date: Optional[date] = None,
    end_date: Optional[date] = None,
) -> Tuple[List[Dict[str, Any]], int]:
    """Get all invoices with optional filtering"""
    query = db.query(Invoice, Customer.company_name.label("customer_name")).join(
        Customer, Invoice.customer_id == Customer.id
    )
    
    # Apply filters
    if customer_id:
        query = query.filter(Invoice.customer_id == customer_id)
    
    if store_type:
        query = query.filter(Invoice.store_type == store_type)
    
    if start_date and end_date:
        query = query.filter(Invoice.date.between(start_date, end_date))
    elif start_date:
        query = query.filter(Invoice.date >= start_date)
    elif end_date:
        query = query.filter(Invoice.date <= end_date)
    
    # Get total count for pagination
    total = query.count()
    
    # Get results with pagination
    results = query.order_by(desc(Invoice.date), desc(Invoice.created_at)).offset(skip).limit(limit).all()
    
    # Format results
    invoices = []
    for invoice, customer_name in results:
        invoice_dict = {
            "id": invoice.id,
            "invoice_no": invoice.invoice_no,
            "customer_name": customer_name,
            "store_type": invoice.store_type,
            "date": invoice.date,
            "grand_total": invoice.grand_total,
            "notes": invoice.notes,
            "po_id": getattr(invoice, "po_id", None)  # Include po_id in response
        }
        invoices.append(invoice_dict)
    
    return invoices, total

def get_invoice(db: Session, invoice_id: uuid.UUID) -> Optional[Invoice]:
    """Get a invoice by ID with details"""
    return db.query(Invoice).options(
        joinedload(Invoice.items),
        joinedload(Invoice.customer)
    ).filter(Invoice.id == invoice_id).first()

def update_invoice(
    db: Session, 
    invoice_id: uuid.UUID, 
    invoice_update: InvoiceUpdate,
    user_id: int
) -> Optional[Invoice]:
    """Update an invoice"""
    # Get current invoice
    db_invoice = get_invoice(db, invoice_id)
    if not db_invoice:
        return None
    
    # Update invoice fields
    update_data = invoice_update.model_dump(exclude_unset=True)
    
    # If po_id is provided in the update, ensure it exists
    if 'po_id' in update_data and update_data['po_id'] is not None:
        po_exists = db.query(PurchaseOrder).filter(PurchaseOrder.id == update_data['po_id']).first()
        if not po_exists:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Purchase order with ID {update_data['po_id']} not found"
            )
    
    # Handle mode_of_payment enum conversion if provided
    if 'mode_of_payment' in update_data:
        mode_value = update_data['mode_of_payment']
        if hasattr(mode_value, 'value'):
            # It's an enum object, get the value
            update_data['mode_of_payment'] = PaymentMode(mode_value.value)
        else:
            # It's already a string value, convert directly
            update_data['mode_of_payment'] = PaymentMode(mode_value)
    
    # Handle sale_type enum conversion if provided
    if 'sale_type' in update_data:
        sale_value = update_data['sale_type']
        if hasattr(sale_value, 'value'):
            # It's an enum object, get the value
            update_data['sale_type'] = SaleType(sale_value.value)
        else:
            # It's already a string value, convert directly
            update_data['sale_type'] = SaleType(sale_value)
    
    # Handle items if provided
    items_data = update_data.pop('items', None)
    
    # Update invoice fields
    for key, value in update_data.items():
        setattr(db_invoice, key, value)
    
    # Update invoice items if provided
    if items_data is not None:
        # Remove existing items
        for item in db_invoice.items:
            db.delete(item)
        
        # Add new items
        for item_data in items_data:
            item = InvoiceItem(
                invoice_id=db_invoice.id,
                sku=item_data.sku,
                description=item_data.description,
                units=item_data.units,
                boxes=item_data.boxes,
                pieces=item_data.pieces,
                m2=item_data.m2,
                unit_price=item_data.unit_price
            )
            db.add(item)
    
    db.flush()
    
    # Recalculate totals
    db_invoice.calculate_totals()
    
    db.commit()
    db.refresh(db_invoice)
    return db_invoice 