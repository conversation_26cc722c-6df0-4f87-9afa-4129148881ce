from pydantic_settings import BaseSettings
from typing import List
import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

class Settings(BaseSettings):
    """
    Application settings loaded from environment variables
    with fallback to default values
    """
    # App details
    app_name: str = "Builders Warehouse API"
    app_version: str = "0.1.0"
    app_description: str = "API for Builders Warehouse"
    
    # Database
    database_url: str = os.getenv("DATABASE_URL", "sqlite:///./test.db")
    
    # JWT Authentication
    secret_key: str = os.getenv("SECRET_KEY", "09d25e094faa6ca2556c818166b7a9563b93f7099f6f0f4caa6cf63b88e8d3e7")
    algorithm: str = os.getenv("ALGORITHM", "HS256")
    access_token_expire_minutes: int = int(os.getenv("ACCESS_TOKEN_EXPIRE_MINUTES", "30"))
    
    # CORS
    cors_origins: List[str] = []
    
    # Logging
    log_level: str = os.getenv("LOG_LEVEL", "INFO")
    log_retention_days: int = int(os.getenv("LOG_RETENTION_DAYS", "30"))
    logs_dir: str = os.getenv("LOGS_DIR", "logs")
    app_log_filename: str = os.getenv("APP_LOG_FILENAME", "app.log")
    
    # Audit log
    audit_log_path: str = os.getenv("AUDIT_LOG_PATH", "audit.log")
    
    # API docs
    docs_url: str = "/docs"
    redoc_url: str = "/redoc"
    openapi_url: str = "/openapi.json"
    
    def __init__(self, **data):
        super().__init__(**data)
        # Parse CORS origins from environment variable
        cors_origins_str = os.getenv("CORS_ORIGINS", "http://localhost:4200")
        self.cors_origins = cors_origins_str.split(",")
    
    class Config:
        env_file = ".env"
        case_sensitive = True
        extra = "allow"  # Allow extra fields from environment variables

# Create settings instance to be imported throughout the application
settings = Settings() 