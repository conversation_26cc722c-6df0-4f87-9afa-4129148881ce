from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
import logging

from src.config import settings
from src.database import Base, engine

logger = logging.getLogger(__name__)

def create_tables():
    """Create database tables if they don't exist yet."""
    from sqlalchemy import inspect
    
    try:
        # Check if tables already exist
        inspector = inspect(engine)
        existing_tables = inspector.get_table_names()
        
        # Create only tables that don't exist yet
        for table in Base.metadata.sorted_tables:
            if table.name not in existing_tables:
                table.create(engine)
                logger.info(f"Created table: {table.name}")
            else:
                logger.debug(f"Table {table.name} already exists, skipping")
                
        logger.info("Database tables checked/created successfully")
    except Exception as e:
        logger.error(f"Error checking/creating database tables: {e}")
        # Don't raise, just log the error and continue
        # This allows the application to start even if there are issues with table creation

def run_migrations():
    """Run database migrations."""
    try:
        logger.info("Running database migrations...")
        # We'll implement this later if needed
        logger.info("Migrations completed successfully.")
    except Exception as e:
        logger.error(f"Error running migrations: {e}")
        # Continue anyway, as some tables might already exist

def create_application() -> FastAPI:
    """
    Create and configure the FastAPI application
    """
    app_name = "Builder's Warehouse API"
    app_description = "API for managing builder resources"
    app_version = "0.1.0"
    docs_url = "/docs"
    redoc_url = "/redoc"
    openapi_url = "/openapi.json"
    
    application = FastAPI(
        title=app_name,
        description=app_description,
        version=app_version,
        docs_url=docs_url,
        redoc_url=redoc_url,
        openapi_url=openapi_url,
    )

    # Add CORS middleware
    logger.info(f"Setting up CORS with allowed origins: {settings.CORS_ORIGINS}")
    application.add_middleware(
        CORSMiddleware,
        allow_origins=settings.CORS_ORIGINS,
        allow_origin_regex=".*",  # Temporarily allow all origins for debugging
        allow_credentials=True,
        allow_methods=["*"],
        allow_headers=["*"],
        expose_headers=["*"],
    )
    
    # Add request logging middleware if available
    try:
        from src.middleware import RequestLoggingMiddleware
        application.add_middleware(RequestLoggingMiddleware)
    except ImportError:
        logger.warning("RequestLoggingMiddleware not available")
    
    return application

def setup_routers(app: FastAPI):
    """
    Register all API routers
    """
    try:
        # Import here to avoid circular imports
        from src.api.v1.routes import router as api_v1_router
        
        # Include routers
        app.include_router(api_v1_router, prefix="/api/v1")
    except ImportError as e:
        logger.warning(f"Error importing routes: {e}")
    
    # Add health check endpoint
    @app.get("/", tags=["Health"])
    def health_check():
        return {"status": "healthy", "message": "Builder's Warehouse API is running"} 