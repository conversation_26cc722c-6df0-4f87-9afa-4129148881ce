"""
Core application initialization module.

This module contains functions for initializing different
components of the application at startup.
"""
import logging
import os
import sys
from .app import create_application, setup_routers, create_tables, run_migrations

logger = logging.getLogger(__name__)

def initialize_database():
    """Initialize database components"""
    logger.info("Initializing database components")
    
    # Run database migrations to ensure tables exist
    try:
        # Run regular migrations
        run_migrations()
        logger.info("Database migrations completed successfully")
        
        # Additional database schema checks and fixes
        try:
            from sqlalchemy import text, inspect
            from src.db.session import engine
            
            inspector = inspect(engine)
            
            # Check if Supplier table exists and has price_list column
            if 'Supplier' in inspector.get_table_names():
                columns = [c['name'] for c in inspector.get_columns('Supplier')]
                price_list_exists = 'price_list' in columns
                
                # If price_list column doesn't exist, try to add it
                if not price_list_exists:
                    logger.warning("price_list column missing from Supplier table. Attempting to add it...")
                    with engine.connect() as conn:
                        try:
                            conn.execute(text('ALTER TABLE "Supplier" ADD COLUMN price_list JSONB DEFAULT \'[]\'::jsonb'))
                            conn.commit()
                            logger.info("price_list column added successfully")
                        except Exception as e:
                            logger.error(f"Error adding price_list column: {e}")
            
            # Check if Inventory table exists and has required columns
            if 'Inventory' in inspector.get_table_names():
                columns = {c['name']: c for c in inspector.get_columns('Inventory')}
                
                # Check for supplier_id column - it should exist
                supplier_id_exists = 'supplier_id' in columns
                if not supplier_id_exists:
                    logger.warning("supplier_id column missing from Inventory table. This is a critical issue.")
                
                # Check if supplier_name column needs to be removed
                supplier_name_exists = 'supplier_name' in columns
                if supplier_name_exists:
                    logger.info("Found deprecated supplier_name column in Inventory table. Will be migrated to use supplier_id.")
                    
                    # First, make sure all inventory items have a valid supplier_id
                    with engine.connect() as conn:
                        try:
                            # Update inventory items that have supplier_name but no supplier_id
                            # Find the supplier_id for each supplier_name and update
                            conn.execute(text('''
                                UPDATE "Inventory" i
                                SET supplier_id = s.id
                                FROM "Supplier" s
                                WHERE i.supplier_name = s.name
                                AND i.supplier_id IS NULL
                            '''))
                            conn.commit()
                            logger.info("Updated inventory items with supplier_id based on supplier_name")
                            
                            # Check if any records still have null supplier_id
                            result = conn.execute(text('SELECT COUNT(*) FROM "Inventory" WHERE supplier_id IS NULL'))
                            null_count = result.scalar()
                            
                            if null_count > 0:
                                logger.warning(f"Found {null_count} inventory items with no supplier_id. These may need manual fixing.")
                            
                            # Now drop the supplier_name column
                            conn.execute(text('ALTER TABLE "Inventory" DROP COLUMN supplier_name'))
                            conn.commit()
                            logger.info("Removed supplier_name column from Inventory table")
                        except Exception as e:
                            logger.error(f"Error migrating supplier_name to supplier_id: {e}")
            
            # Check if required columns exist in Inventory
            inventory_columns_missing = False
            required_inventory_columns = ['sku_code', 'style_code', 'supplier_id']
            
            if 'Inventory' in inspector.get_table_names():
                existing_columns = [c['name'].lower() for c in inspector.get_columns('Inventory')]
                for col in required_inventory_columns:
                    if col.lower() not in existing_columns:
                        inventory_columns_missing = True
                        break
            else:
                inventory_columns_missing = True
            
            # If inventory columns are missing, run the fix script
            if inventory_columns_missing:
                logger.warning("Required inventory columns missing. Running fix script...")
                # Import and run the fix script
                try:
                    # Determine the script path - try different locations
                    script_paths = [
                        os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))), 
                                   "fix_inventory_schema.py"),
                        os.path.join(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))), 
                                   "scripts", "fix_inventory_schema.py")
                    ]
                    
                    script_found = False
                    for script_path in script_paths:
                        if os.path.exists(script_path):
                            script_found = True
                            logger.info(f"Found fix script at: {script_path}")
                            
                            # Add the script directory to the Python path
                            script_dir = os.path.dirname(script_path)
                            if script_dir not in sys.path:
                                sys.path.insert(0, script_dir)
                                
                            # Run the script directly
                            import subprocess
                            result = subprocess.run([sys.executable, script_path], check=True)
                            if result.returncode == 0:
                                logger.info("Successfully fixed inventory schema")
                            else:
                                logger.error("Failed to fix inventory schema")
                            break
                    
                    if not script_found:
                        logger.error(f"Fix script not found at any of the expected locations")
                        
                except Exception as e:
                    logger.error(f"Error running inventory fix script: {e}")
                    
        except Exception as e:
            logger.error(f"Error checking for database schema issues: {e}")
    except Exception as e:
        logger.error(f"Error running database migrations: {e}")
        logger.warning("Application may not function correctly due to database schema issues")

__all__ = [
    'create_application',
    'setup_routers',
    'create_tables',
    'run_migrations',
    'initialize_database'
] 