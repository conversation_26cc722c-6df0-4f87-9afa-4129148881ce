from fastapi import HTT<PERSON>Ex<PERSON>, status, Request
from sqlalchemy.orm import Session
from typing import Optional
from datetime import datetime, timedelta
import logging
import uuid

from src.utils.auth import verify_password, create_access_token
from src.models.user import User
from src.schemas.user import Token, UserLogin, TokenData
from src.services.user import UserService
from src.services.auditlog import Audit<PERSON>ogger
from src.core.config import settings
from src.models.store_type import StoreType
from src.models.role import Role

logger = logging.getLogger(__name__)

class AuthService:
    """
    Service for authentication-related operations
    """
    
    @staticmethod
    async def authenticate_by_email(
        db: Session, 
        email: str, 
        password: str
    ) -> Optional[User]:
        """
        Authenticate a user with email and password
        
        Args:
            db: Database session
            email: Email address for authentication
            password: Plain text password
            
        Returns:
            User object if authentication successful, None otherwise
        """
        user = UserService.get_user_by_email(db, email)
        if not user or not verify_password(password, user.hashed_password) or not user.is_active or user.is_deleted:
            return None
        return user
    
    @classmethod
    async def login_by_email(
        cls,
        db: Session,
        login_data: UserLogin,
        request: Optional[Request] = None
    ) -> Token:
        """
        Process a login attempt using email/password and return a JWT token
        
        Args:
            db: Database session
            login_data: Email login data
            request: FastAPI request object (for audit logging)
            
        Returns:
            Token object with access_token and token_type
            
        Raises:
            HTTPException: If authentication fails
        """
        user = await cls.authenticate_by_email(db, login_data.email, login_data.password)
        
        # If authentication failed
        if not user:
            # Try to get user by email to log the attempt
            db_user = UserService.get_user_by_email(db, login_data.email)
            
            if db_user:
                try:
                    # Log failed login attempt, but don't let it break the flow
                    await AuditLogger.log_change(
                        db=db,
                        event_type="LOGIN",
                        status="failure",
                        email=login_data.email,
                        additional_info={"reason": "Invalid credentials"},
                        request=request
                    )
                except Exception as e:
                    # Log the error but continue
                    logger.error(f"Error logging failed login attempt: {e}")
                    # Make sure transaction is rolled back
                    db.rollback()
            
            # Raise exception with generic error
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Incorrect email or password",
                headers={"WWW-Authenticate": "Bearer"},
            )
        
        # Get store type details for token
        store_type_name = None
        if user.store_type_id:
            store = db.query(StoreType).filter(StoreType.id == user.store_type_id).first()
            if store:
                store_type_name = store.name
        
        # Get role details for token
        role_name = "unknown"
        if user.role_id is not None:
            role = db.query(Role).filter(Role.id == user.role_id).first()
            if role:
                role_name = role.name
        elif user.role is not None:
            # Legacy support for the old role column
            role_name = user.role
        
        # Create access token with all user claims
        access_token_expires = timedelta(minutes=int(settings.ACCESS_TOKEN_EXPIRE_MINUTES))
        token_data = TokenData(
            user_id=str(user.id),
            username=user.user_name,
            email=user.email,
            role=role_name,
            store_type=store_type_name
        )
        access_token = create_access_token(
            data=token_data.model_dump(), 
            expires_delta=access_token_expires
        )
        
        # Create token response
        token_response = Token(
            access_token=access_token,
            token_type="bearer",
            user=token_data
        )
        
        # Log successful login
        try:
            await AuditLogger.log_change(
                db=db,
                event_type="LOGIN",
                status="success",
                email=user.email,
                user_id=user.id,
                additional_info={"role": role_name, "store_type": store_type_name},
                request=request
            )
        except Exception as e:
            # Log the error but continue
            logger.error(f"Error logging successful login: {e}")
            # Make sure transaction is rolled back
            db.rollback()
        
        logger.info(f"User login successful: {user.email}")
        return token_response