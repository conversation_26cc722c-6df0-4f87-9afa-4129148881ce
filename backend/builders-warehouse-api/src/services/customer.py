from typing import List, Optional, Dict, Any, Tuple
from uuid import UUID
from datetime import date
from decimal import Decimal
import pandas as pd
from sqlalchemy import select, func, and_, or_
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.orm import selectinload

from src.models.customer import Customer, PriceList
from src.models.invoice import Invoice
from src.models.quote import Quote
from src.schemas.customer import CustomerCreate, CustomerUpdate, InvoiceListParams


async def create_customer(db: AsyncSession, obj_in: CustomerCreate) -> Customer:
    """Create a new customer"""
    db_obj = Customer(
        company_name=obj_in.company_name,
        contact_person=obj_in.contact_person,
        email=obj_in.email,
        phone=obj_in.phone,
        is_account=obj_in.is_account,
        billing_address=obj_in.billing_address,
        billing_suburb=obj_in.billing_suburb,
        billing_postcode=obj_in.billing_postcode,
        price_list_id=obj_in.price_list_id,
    )
    db.add(db_obj)
    await db.commit()
    await db.refresh(db_obj)
    return db_obj


async def get_customer(db: AsyncSession, customer_id: UUID) -> Optional[Customer]:
    """Get a customer by ID"""
    result = await db.execute(
        select(Customer)
        .options(selectinload(Customer.price_list))
        .where(Customer.id == customer_id)
    )
    return result.scalars().first()


async def get_customer_with_invoices(db: AsyncSession, customer_id: UUID) -> Optional[Customer]:
    """Get a customer by ID with related invoices"""
    result = await db.execute(
        select(Customer)
        .options(
            selectinload(Customer.price_list),
            selectinload(Customer.invoices)
        )
        .where(Customer.id == customer_id)
    )
    return result.scalars().first()


async def get_customer_by_email(db: AsyncSession, email: str) -> Optional[Customer]:
    """Get a customer by email"""
    result = await db.execute(select(Customer).where(Customer.email == email))
    return result.scalars().first()


async def list_customers(
    db: AsyncSession, 
    skip: int = 0, 
    limit: int = 100, 
    search: Optional[str] = None
) -> Tuple[List[Customer], int]:
    """List customers with optional search and pagination"""
    query = select(Customer)
    
    # Apply search filter if provided
    if search:
        search_filter = or_(
            Customer.company_name.ilike(f"%{search}%"),
            Customer.email.ilike(f"%{search}%"),
            Customer.contact_person.ilike(f"%{search}%"),
            Customer.phone.ilike(f"%{search}%")
        )
        query = query.where(search_filter)
    
    # Get total count
    count_query = select(func.count()).select_from(query.subquery())
    result = await db.execute(count_query)
    total_count = result.scalar_one()
    
    # Apply alphabetical ordering and pagination
    query = query.order_by(func.lower(Customer.company_name)).offset(skip).limit(limit)
    
    # Execute query
    result = await db.execute(query)
    customers = result.scalars().all()
    
    return customers, total_count


async def update_customer(
    db: AsyncSession, 
    db_obj: Customer, 
    obj_in: CustomerUpdate
) -> Customer:
    """Update a customer"""
    update_data = obj_in.dict(exclude_unset=True)
    
    for field, value in update_data.items():
        setattr(db_obj, field, value)
    
    await db.commit()
    await db.refresh(db_obj)
    return db_obj


async def delete_customer(db: AsyncSession, customer_id: UUID) -> bool:
    """Delete a customer"""
    db_obj = await get_customer(db, customer_id)
    if not db_obj:
        return False
    
    await db.delete(db_obj)
    await db.commit()
    return True


async def get_invoice(db: AsyncSession, invoice_id: UUID) -> Optional[Invoice]:
    """Get an invoice by ID"""
    result = await db.execute(
        select(Invoice)
        .where(Invoice.id == invoice_id)
    )
    return result.scalars().first()


async def list_customer_invoices(
    db: AsyncSession,
    customer_id: UUID,
    params: InvoiceListParams
) -> Tuple[List[Dict[str, Any]], int]:
    """List invoices and quotes for a customer with filters and pagination"""
    # Query invoices
    invoice_query = select(Invoice).where(Invoice.customer_id == customer_id)
    
    # Apply date filters for invoices
    if params.start_date:
        invoice_query = invoice_query.where(Invoice.invoice_date >= params.start_date)
    if params.end_date:
        invoice_query = invoice_query.where(Invoice.invoice_date <= params.end_date)
    
    # Apply invoice type filter if it exists
    if params.invoice_type:
        invoice_query = invoice_query.where(Invoice.store_type_name == params.invoice_type)
    
    # Query quotes
    quote_query = select(Quote).where(Quote.customer_id == customer_id)
    
    # Apply date filters for quotes
    if params.start_date:
        quote_query = quote_query.where(Quote.quote_date >= params.start_date)
    if params.end_date:
        quote_query = quote_query.where(Quote.quote_date <= params.end_date)
    
    # Apply store type filter for quotes
    if params.invoice_type:
        quote_query = quote_query.where(Quote.store_type == params.invoice_type)
    
    # Get total counts
    invoice_count_query = select(func.count()).select_from(invoice_query.subquery())
    quote_count_query = select(func.count()).select_from(quote_query.subquery())
    
    invoice_count_result = await db.execute(invoice_count_query)
    quote_count_result = await db.execute(quote_count_query)
    
    invoice_count = invoice_count_result.scalar_one()
    quote_count = quote_count_result.scalar_one()
    total_count = invoice_count + quote_count
    
    # Execute queries to get actual data
    invoice_result = await db.execute(invoice_query.order_by(Invoice.invoice_date.desc()))
    quote_result = await db.execute(quote_query.order_by(Quote.quote_date.desc()))
    
    invoices = invoice_result.scalars().all()
    quotes = quote_result.scalars().all()
    
    # Combine and transform results
    combined_results = []
    
    # Add invoices
    for invoice in invoices:
        combined_results.append({
            "id": invoice.id,
            "invoice_number": invoice.invoice_number,
            "customer_id": invoice.customer_id,
            "company_id": invoice.company_id,
            "invoice_date": invoice.invoice_date,
            "due_date": invoice.due_date,
            "status": invoice.status,
            "invoice_type": invoice.invoice_type,
            "grand_total": invoice.grand_total,
            "total_gst": invoice.total_gst,
            "notes": invoice.notes,
            "created_at": invoice.created_at,
            "updated_at": invoice.updated_at,
            "store_type_name": invoice.store_type_name or '',
            "mode_of_payment": invoice.mode_of_payment.value if invoice.mode_of_payment else '',
            "po_number": invoice.po_number or '',
            "dont_send_po": invoice.dont_send_po or False,
            "total_order": invoice.total_order or 0,
            "credit_card_surcharge": invoice.credit_card_surcharge or 0,
            "shipping": invoice.shipping or 0,
            "type": "invoice"
        })
    
    # Add quotes
    for quote in quotes:
        combined_results.append({
            "id": quote.id,
            "invoice_number": quote.quote_number,  # Use quote_number as invoice_number for consistency
            "customer_id": quote.customer_id,
            "company_id": quote.company_id,
            "invoice_date": quote.quote_date,  # Use quote_date as invoice_date for consistency
            "due_date": quote.valid_until,
            "status": quote.status,
            "invoice_type": "quote",
            "grand_total": quote.grand_total,
            "total_gst": quote.total_gst,
            "notes": quote.notes,
            "created_at": quote.created_at,
            "updated_at": quote.updated_at,
            "store_type_name": quote.store_type or '',
            "mode_of_payment": '',  # Quotes don't have payment mode
            "po_number": '',  # Quotes don't have PO number
            "dont_send_po": False,  # Quotes don't have this flag
            "total_order": quote.grand_total,  # Use grand_total as total_order
            "credit_card_surcharge": 0,  # Quotes don't have surcharge
            "shipping": 0,  # Quotes don't have shipping
            "type": "quote"
        })
    
    # Sort combined results by date (most recent first)
    combined_results.sort(key=lambda x: x["invoice_date"], reverse=True)
    
    # Apply pagination to combined results
    start_index = params.skip
    end_index = start_index + params.limit
    paginated_results = combined_results[start_index:end_index]
    
    return paginated_results, total_count


async def get_customer_invoice_summary(db: AsyncSession, customer_id: UUID) -> Dict[str, Any]:
    """Get summary of customer invoices"""
    query = select(
        func.count(Invoice.id).label("total_invoices"),
        func.sum(Invoice.grand_total).label("total_value")
    ).where(Invoice.customer_id == customer_id)
    
    result = await db.execute(query)
    row = result.one_or_none()
    
    if not row:
        return {"total_invoices": 0, "total_value": Decimal("0.00")}
    
    return {
        "total_invoices": row.total_invoices or 0,
        "total_value": row.total_value or Decimal("0.00")
    }


async def bulk_upload_invoices(
    db: AsyncSession, 
    customer_id: UUID, 
    file_content: bytes,
    file_extension: str
) -> Tuple[int, int, List[str]]:
    """Bulk upload invoices from CSV or XLSX file"""
    # Check if customer exists
    customer = await get_customer(db, customer_id)
    if not customer:
        raise ValueError(f"Customer with ID {customer_id} not found")
    
    # Parse file based on extension
    try:
        if file_extension.lower() == "csv":
            df = pd.read_csv(file_content)
        elif file_extension.lower() in ["xlsx", "xls"]:
            df = pd.read_excel(file_content)
        else:
            raise ValueError(f"Unsupported file extension: {file_extension}")
    except Exception as e:
        raise ValueError(f"Error parsing file: {str(e)}")
    
    # Validate required columns
    required_columns = ["invoice_no", "store_type", "date", "total_gst", "grand_total"]
    missing_columns = [col for col in required_columns if col not in df.columns]
    if missing_columns:
        raise ValueError(f"Missing required columns: {', '.join(missing_columns)}")
    
    # Process each row
    success_count = 0
    error_count = 0
    error_messages = []
    
    for _, row in df.iterrows():
        try:
            # Convert row to dict
            invoice_data = row.to_dict()
            
            # Parse date if it's a string
            if isinstance(invoice_data["date"], str):
                try:
                    invoice_data["date"] = date.fromisoformat(invoice_data["date"])
                except ValueError:
                    raise ValueError(f"Invalid date format: {invoice_data['date']}")
            
            # Create invoice
            # Note: This will need to be updated to use the new Invoice model structure
            # with proper ID references instead of string values
            invoice = Invoice(
                # Use attributes that still exist in the Invoice model
                invoice_no=invoice_data["invoice_no"],
                store_type_name=invoice_data["store_type"],
                date=invoice_data["date"],
                total_gst=Decimal(str(invoice_data["total_gst"])),
                grand_total=Decimal(str(invoice_data["grand_total"])),
                notes=invoice_data.get("notes", None),
                customer_id=customer_id,
                company_id=customer_id,  # Using customer_id as company_id for now
                # Default values for required fields that might not be in the CSV
                sale_type="trade",
                mode_of_payment="bank_transfer"
            )
            
            db.add(invoice)
            success_count += 1
        except Exception as e:
            error_count += 1
            error_messages.append(f"Error in row {success_count + error_count}: {str(e)}")
            # Continue with next row instead of failing entire batch
            continue
    
    if success_count > 0:
        await db.commit()
    
    return success_count, error_count, error_messages 