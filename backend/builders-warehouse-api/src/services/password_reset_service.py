import secrets
import logging
from datetime import datetime, timedelta, timezone
from typing import Optional
from sqlalchemy.orm import Session
from fastapi import HTT<PERSON><PERSON><PERSON><PERSON>, status

from src.models.user import User
from src.models.password_reset_token import PasswordResetToken
from src.services.email_service import EmailService
from src.utils.auth import get_password_hash
from src.schemas.password_reset import (
    PasswordResetRequest, 
    PasswordResetResponse,
    PasswordResetConfirm,
    PasswordResetConfirmResponse
)

logger = logging.getLogger(__name__)

class PasswordResetService:
    """
    Service for password reset operations
    """
    
    @staticmethod
    def generate_reset_token() -> str:
        """Generate a secure random token for password reset"""
        return secrets.token_urlsafe(32)
    
    @staticmethod
    async def request_password_reset(
        db: Session,
        request_data: PasswordResetRequest,
        frontend_url: str = "http://localhost:4200"
    ) -> PasswordResetResponse:
        """
        Process password reset request
        
        Args:
            db: Database session
            request_data: Password reset request data
            frontend_url: Frontend URL for reset link
            
        Returns:
            PasswordResetResponse with success status and message
        """
        try:
            # Check if user exists with this email - direct query instead of UserService
            user = db.query(User).filter(
                User.email == request_data.email,
                User.is_deleted == False
            ).first()
            
            if not user:
                # For security, don't reveal if email exists or not
                logger.warning(f"Password reset requested for non-existent email: {request_data.email}")
                return PasswordResetResponse(
                    message="If an account with this email exists, you will receive a password reset link shortly.",
                    success=True
                )
            
            if not user.is_active or user.is_deleted:
                # For security, don't reveal if account is inactive
                logger.warning(f"Password reset requested for inactive/deleted user: {request_data.email}")
                return PasswordResetResponse(
                    message="If an account with this email exists, you will receive a password reset link shortly.",
                    success=True
                )
            
            # Invalidate any existing tokens for this user
            existing_tokens = db.query(PasswordResetToken).filter(
                PasswordResetToken.user_id == user.id,
                PasswordResetToken.is_used == False
            ).all()
            
            for token in existing_tokens:
                token.is_used = True
            
            # Generate new reset token
            reset_token = PasswordResetService.generate_reset_token()
            
            # Create token record with 1 hour expiration
            token_record = PasswordResetToken(
                token=reset_token,
                user_id=user.id,
                expires_at=datetime.now(timezone.utc) + timedelta(hours=1)
            )
            
            db.add(token_record)
            db.commit()
            
            # Send password reset email
            email_sent = await EmailService.send_password_reset_email(
                to_email=user.email,
                user_name=user.user_name,
                reset_token=reset_token,
                frontend_url=frontend_url
            )
            
            if email_sent:
                logger.info(f"Password reset email sent successfully to {user.email}")
                return PasswordResetResponse(
                    message="If an account with this email exists, you will receive a password reset link shortly.",
                    success=True
                )
            else:
                # Mark token as used if email failed to send
                token_record.is_used = True
                db.commit()
                
                logger.error(f"Failed to send password reset email to {user.email}")
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail="Failed to send password reset email. Please try again later."
                )
                
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error processing password reset request: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="An error occurred while processing your request. Please try again later."
            )
    
    @staticmethod
    async def confirm_password_reset(
        db: Session,
        confirm_data: PasswordResetConfirm
    ) -> PasswordResetConfirmResponse:
        """
        Confirm password reset and update user password
        
        Args:
            db: Database session
            confirm_data: Password reset confirmation data
            
        Returns:
            PasswordResetConfirmResponse with success status and message
        """
        try:
            # Find the token
            token_record = db.query(PasswordResetToken).filter(
                PasswordResetToken.token == confirm_data.token
            ).first()
            
            if not token_record:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invalid or expired reset token."
                )
            
            # Check if token is valid
            if not token_record.is_valid():
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invalid or expired reset token."
                )
            
            # Get the user
            user = db.query(User).filter(User.id == token_record.user_id).first()
            if not user or not user.is_active or user.is_deleted:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Invalid or expired reset token."
                )
            
            # Update user password
            user.hashed_password = get_password_hash(confirm_data.new_password)
            
            # Mark token as used
            token_record.is_used = True
            
            # Invalidate all other tokens for this user
            other_tokens = db.query(PasswordResetToken).filter(
                PasswordResetToken.user_id == user.id,
                PasswordResetToken.id != token_record.id,
                PasswordResetToken.is_used == False
            ).all()
            
            for token in other_tokens:
                token.is_used = True
            
            db.commit()
            
            logger.info(f"Password reset completed successfully for user: {user.email}")
            
            return PasswordResetConfirmResponse(
                message="Your password has been reset successfully. You can now log in with your new password.",
                success=True
            )
            
        except HTTPException:
            raise
        except Exception as e:
            logger.error(f"Error confirming password reset: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="An error occurred while resetting your password. Please try again later."
            ) 