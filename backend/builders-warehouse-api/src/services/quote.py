from fastapi import HTT<PERSON>Exception, status, Request
from sqlalchemy.orm import Session
from sqlalchemy import desc, or_
from typing import List, Optional, Dict, Any
import logging
from datetime import datetime, date, timedelta
import uuid
import hashlib

from src.models.quote import Quote
from src.models.customer import Customer
from src.schemas.quote import QuoteCreate, QuoteUpdate, QuoteItemInput
from src.services.auditlog import AuditLogger
from sqlalchemy.orm import joinedload

logger = logging.getLogger(__name__)

class QuoteService:
    """Service for quote-related operations"""
    
    @staticmethod
    async def generate_quote_number(db: Session) -> str:
        """Generate a unique sequential quote number"""
        # Find the highest quote number and increment
        last_quote = db.query(Quote).order_by(desc(Quote.id)).first()
        
        if last_quote and last_quote.quote_number and last_quote.quote_number.startswith("QUO-"):
            try:
                last_number = int(last_quote.quote_number.split("-")[1])
                new_number = last_number + 1
            except (ValueError, IndexError):
                # If the last quote_number doesn't follow the format, start from 1
                new_number = 1
        else:
            new_number = 1
            
        return f"QUO-{new_number:03d}"
    
    @staticmethod
    async def create_quote(
        db: Session,
        quote_data: QuoteCreate,
        request: Optional[Request] = None
    ) -> Quote:
        """Create a new quote with items"""
        # Debug info
        logger.debug(f"Creating quote with data: {quote_data}")
        print(f"Creating quote with data: {quote_data}")
        
        # Verify customer exists - accept various customer_id formats
        customer_id = quote_data.customer_id
        logger.debug(f"Looking up customer with ID: {customer_id}")
        print(f"Looking up customer with ID: {customer_id}")
        
        try:
            # Handle different types of customer_id
            if isinstance(customer_id, uuid.UUID):
                # If UUID, convert to integer if possible
                print("Customer ID is UUID, trying to convert to int")
                try:
                    # Query by string representation of UUID
                    customer = db.query(Customer).filter(Customer.id == int(customer_id)).first()
                except (ValueError, TypeError):
                    # If can't convert to int, query by UUID string
                    print(f"Could not convert UUID to int, querying by string: {str(customer_id)}")
                    customer = db.query(Customer).filter(Customer.id == str(customer_id)).first()
            elif isinstance(customer_id, int):
                # Direct query by integer
                print(f"Customer ID is int: {customer_id}")
                customer = db.query(Customer).filter(Customer.id == customer_id).first()
            else:
                # Try to convert string to int
                print(f"Customer ID is string: {customer_id}")
                try:
                    customer_id_int = int(customer_id)
                    customer = db.query(Customer).filter(Customer.id == customer_id_int).first()
                except (ValueError, TypeError):
                    # If can't convert to int, query by string
                    print(f"Could not convert string to int, querying as is: {customer_id}")
                    customer = db.query(Customer).filter(Customer.id == customer_id).first()
                
            if not customer:
                print(f"Customer with ID {customer_id} not found")
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"Customer with ID {customer_id} not found"
                )
            print(f"Found customer: {customer}")
        except Exception as e:
            print(f"Error looking up customer: {str(e)}")
            logger.error(f"Error looking up customer: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, 
                detail=f"Error looking up customer: {str(e)}"
            )
        
        # Generate quote number
        print("Generating quote number")
        quote_no = await QuoteService.generate_quote_number(db)
        print(f"Generated quote number: {quote_no}")
        
        # Handle company_id - use default if not provided
        company_id = 1  # Default company ID
        if quote_data.company_id:
            try:
                if isinstance(quote_data.company_id, uuid.UUID):
                    company_id = 1  # Default if conversion fails
                else:
                    company_id = int(quote_data.company_id)
            except (ValueError, TypeError):
                print(f"Could not convert company_id to int, using default")
        
        # Default store_type if not provided
        store_type = "Cranbourne"
        if quote_data.store_type_id:
            store_type = str(quote_data.store_type_id)
            
        # Process items for JSON storage
        items_data = quote_data.items
        if not items_data:
            logger.warning("No quote items provided")
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="At least one quote item is required"
            )
        
        # Calculate totals and prepare items for JSON storage
        items_json = {"items": []}
        grand_total = 0.0
        for item in items_data:
            # Calculate item total price
            total_quantity = (item.quantity_units or 0) + (item.quantity_boxes or 0) + \
                           (item.quantity_pieces or 0) + float(item.quantity_m2 or 0)
            total_price = float(item.unit_price or 0) * total_quantity
            grand_total += total_price
            
            # Add formatted item to JSON array
            items_json["items"].append({
                "sku_code": item.sku_code,
                "description": item.description,
                "quantity_units": item.quantity_units,
                "quantity_boxes": item.quantity_boxes,
                "quantity_pieces": item.quantity_pieces,
                "quantity_m2": float(item.quantity_m2),
                "unit_price": float(item.unit_price),
                "totalPrice": float(total_price)
            })
            
        # Create quote with items JSON
        try:
            db_quote = Quote(
                quote_number=quote_no,
                company_id=company_id,
                store_type=store_type,
                quote_date=quote_data.date or datetime.now().date(),
                valid_until=quote_data.date or datetime.now().date(),  # Setting to same as quote_date
                notes=quote_data.notes or "",
                deliver_to_address=quote_data.deliver_to_address or "",
                customer_id=customer.id,  # Use the customer ID we verified
                status="draft",
                grand_total=grand_total,  
                total_gst=grand_total * 0.15,  # Assuming 15% GST
                items=items_json  # Store items as JSON
            )
            print(f"Created quote object: {db_quote}")
        except Exception as e:
            print(f"Error creating quote object: {str(e)}")
            logger.error(f"Error creating quote object: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error creating quote: {str(e)}"
            )
        
        # Add quote to session and commit
        try:
            db.add(db_quote)
            db.commit()
            db.refresh(db_quote)
        except Exception as e:
            db.rollback()
            print(f"Error saving quote: {str(e)}")
            logger.error(f"Error saving quote: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error saving quote: {str(e)}"
            )
        
        # Log quote creation in audit log
        await AuditLogger.log_create(
            db=db,
            entity_type="quotes",
            entity_id=db_quote.id,
            entity=db_quote,
            request=request
        )
        
        logger.info(f"Quote created: {db_quote.quote_number}")
        return db_quote
    
    @staticmethod
    def get_quotes(
        db: Session,
        is_archived: bool = False,
        search: Optional[str] = None,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
        page: int = 1,
        limit: int = 100,
        current_user = None
    ) -> Dict[str, Any]:
        """Get quotes with filtering and pagination"""
        # Base query
        query = db.query(Quote).options(joinedload(Quote.customer), joinedload(Quote.company))
        
        # Debug log the filter parameters
        print(f"Filter params - is_archived: {is_archived}, search: {search}, start_date: {start_date}, end_date: {end_date}")
        
        # Apply store type filtering for staff and manager users
        if current_user and hasattr(current_user, 'role') and hasattr(current_user, 'store_type'):
            user_role = current_user.role.lower() if current_user.role else ""
            if user_role in ['staff', 'manager'] and current_user.store_type:
                # Filter quotes by the user's store type
                user_store_name = current_user.store_type.name if hasattr(current_user.store_type, 'name') else str(current_user.store_type)
                print(f"Filtering quotes by store type: {user_store_name} for user role: {user_role}")
                query = query.filter(Quote.store_type == user_store_name)
        
        # Filter based on date instead of status
        # Calculate the date 30 days ago
        thirty_days_ago = datetime.now().date() - timedelta(days=30)
        
        if is_archived:
            # Archived: quotes older than 30 days (excluding deleted quotes only)
            query = query.filter(
                Quote.quote_date < thirty_days_ago,
                Quote.status != "deleted"
            )
            print(f"Filtering archived quotes: older than {thirty_days_ago}")
        else:
            # Current: quotes from last 30 days (excluding deleted quotes only)
            query = query.filter(
                Quote.quote_date >= thirty_days_ago,
                Quote.status != "deleted"
            )
            print(f"Filtering current quotes: from {thirty_days_ago} to today (excluding deleted only)")
        
        # Apply search filter if provided
        if search:
            search_term = f"%{search}%"
            query = query.filter(Quote.quote_number.ilike(search_term))
        
        # Enable date filtering
        if start_date:
            print(f"Filtering by start_date: {start_date}")
            query = query.filter(Quote.quote_date >= start_date)
        if end_date:
            print(f"Filtering by end_date: {end_date}")
            query = query.filter(Quote.quote_date <= end_date)
        
        # Count total quotes that match the filter
        total = query.count()
        
        # Order by date descending and apply pagination
        offset = (page - 1) * limit
        quotes = query.order_by(desc(Quote.quote_date)).offset(offset).limit(limit).all()
        
        # Debug log to ensure quotes are found
        print(f"Found {len(quotes)} quotes")
        for quote in quotes:
            print(f"Quote ID: {quote.id}, Quote No: {quote.quote_number}, Date: {quote.quote_date}, Status: {quote.status}")
            # Debug customer loading
            if hasattr(quote, 'customer') and quote.customer:
                print(f"Customer loaded: ID={quote.customer_id}, Name={quote.customer.company_name if hasattr(quote.customer, 'company_name') else 'Unknown'}")
            else:
                print(f"Customer not loaded for quote ID: {quote.id}")
                # Try to load customer explicitly if the relationship didn't work
                customer = db.query(Customer).filter(Customer.id == quote.customer_id).first()
                if customer:
                    print(f"Customer loaded explicitly: ID={customer.id}, Name={customer.company_name}")
                    quote.customer = customer
        
        return {
            "data": quotes,
            "total": total,
            "page": page,
            "limit": limit
        }
    
    @staticmethod
    def get_quote_by_id(db: Session, quote_id: int) -> Optional[Quote]:
        """Get a quote by ID"""
        return db.query(Quote).filter(Quote.id == quote_id).first()
    
    @staticmethod
    def get_quote_by_number(db: Session, quote_number: str) -> Optional[Quote]:
        """Get a quote by quote number"""
        return db.query(Quote).filter(Quote.quote_number == quote_number).first()
    
    @staticmethod
    async def update_quote(
        db: Session,
        quote_id: int,
        quote_update: QuoteUpdate,
        request: Optional[Request] = None
    ) -> Optional[Quote]:
        """Update a quote"""
        # Get quote
        db_quote = QuoteService.get_quote_by_id(db, quote_id)
        if not db_quote:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Quote with ID {quote_id} not found"
            )
        
        # Store original values for audit log
        original_data = {
            "notes": db_quote.notes,
            "deliver_to_address": db_quote.deliver_to_address,
            "status": db_quote.status
        }
        
        # Update quote fields
        update_data = quote_update.model_dump(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_quote, field, value)
        
        # Commit changes
        db.commit()
        db.refresh(db_quote)
        
        # Log quote update in audit log
        changed_fields = {k: v for k, v in update_data.items() if k in original_data and original_data[k] != v}
        if changed_fields:
            changes = {
                "before": {k: original_data[k] for k in changed_fields},
                "after": changed_fields
            }
            
            await AuditLogger.log_update(
                db=db,
                entity_type="quotes",
                entity_id=db_quote.id,
                old_values={k: original_data[k] for k in changed_fields},
                new_values=changed_fields,
                request=request
            )
        
        logger.info(f"Quote updated: {db_quote.quote_number}")
        return db_quote
    
    @staticmethod
    async def convert_to_invoice(
        db: Session,
        quote_id: int,
        request: Optional[Request] = None
    ) -> Optional[Quote]:
        """Convert a quote to invoice - Create invoice and mark quote as converted"""
        # Import here to avoid circular imports
        from src.services.invoice import InvoiceService
        
        # Get quote
        db_quote = QuoteService.get_quote_by_id(db, quote_id)
        if not db_quote:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Quote with ID {quote_id} not found"
            )
        
        # Store original status for audit log
        original_status = db_quote.status
        
        try:
            # Create invoice from quote
            db_invoice = await InvoiceService.create_invoice_from_quote(
                db=db,
                quote=db_quote,
                request=request
            )
            
            logger.info(f"Invoice {db_invoice.invoice_number} created from quote {db_quote.quote_number}")
            
            # Update quote status to "converted" instead of deleting
            db_quote.status = "converted"
            db.commit()
            db.refresh(db_quote)
            
            # Log the conversion in audit log
            await AuditLogger.log_change(
                db=db,
                event_type="CONVERT_TO_INVOICE",
                status="success",
                additional_info={
                    "entity_type": "quotes",
                    "entity_id": str(db_quote.id),
                    "before": {"status": original_status},
                    "after": {"status": "converted", "converted_to_invoice": db_invoice.invoice_number}
                },
                request=request
            )
            
            logger.info(f"Quote {db_quote.quote_number} marked as converted to invoice {db_invoice.invoice_number}")
            
            # Return the updated quote object
            return db_quote
            
        except Exception as e:
            # If anything fails, rollback and raise error
            db.rollback()
            logger.error(f"Error converting quote to invoice: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error converting quote to invoice: {str(e)}"
            )
    
    @staticmethod
    async def renew_quote(
        db: Session,
        quote_id: int,
        request: Optional[Request] = None
    ) -> Optional[Quote]:
        """Renew a quote - Update status and valid_until date"""
        # Get quote
        db_quote = QuoteService.get_quote_by_id(db, quote_id)
        if not db_quote:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Quote with ID {quote_id} not found"
            )
        
        # Store original values for audit log
        original_data = {
            "status": db_quote.status,
            "valid_until": db_quote.valid_until,
            "quote_date": db_quote.quote_date
        }
        
        # Update quote - set quote_date to today and extend validity by 30 days
        # This will move the quote from archived to current
        today = datetime.now().date()
        db_quote.quote_date = today
        db_quote.valid_until = today + timedelta(days=30)
        db_quote.status = "renewed"
        
        # Commit changes
        db.commit()
        db.refresh(db_quote)
        
        # Log quote renewal in audit log
        await AuditLogger.log_update(
            db=db,
            entity_type="quotes",
            entity_id=db_quote.id,
            old_values=original_data,
            new_values={
                "status": db_quote.status,
                "valid_until": db_quote.valid_until,
                "quote_date": db_quote.quote_date
            },
            request=request
        )
        
        logger.info(f"Quote renewed: {db_quote.quote_number}")
        return db_quote
    
    @staticmethod
    async def soft_delete_quote(
        db: Session,
        quote_id: int,
        request: Optional[Request] = None
    ) -> bool:
        """Soft delete a quote by changing its status"""
        # Get quote
        db_quote = QuoteService.get_quote_by_id(db, quote_id)
        if not db_quote:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Quote with ID {quote_id} not found"
            )
        
        # Update quote status instead of setting is_deleted flag
        original_status = db_quote.status
        db_quote.status = "deleted"
        
        # Commit changes
        db.commit()
        
        # Log quote deletion in audit log
        await AuditLogger.log_delete(
            db=db,
            entity_type="quotes",
            entity_id=db_quote.id,
            entity=db_quote,
            request=request
        )
        
        logger.info(f"Quote soft deleted: {db_quote.quote_number}")
        return True 