import json
import logging
import uuid
from datetime import datetime
from typing import Any, Dict, Optional, Union
from sqlalchemy.orm import Session
from fastapi import Request

from src.models.audit_log import AuditLog

logger = logging.getLogger(__name__)

# Custom JSON encoder to handle UUID serialization
class CustomJSONEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, uuid.UUID):
            return str(obj)
        elif isinstance(obj, datetime):
            return obj.isoformat()
        return super().default(obj)

class AuditLogger:
    """
    Service for tracking database changes (create, update, delete operations)
    """
    
    @staticmethod
    async def log_change(
        db: Session,
        event_type: str,
        status: str = "success",
        email: Optional[str] = None,
        additional_info: Optional[Dict[str, Any]] = None,
        request: Optional[Request] = None
    ) -> AuditLog:
        """
        Log a database change with timestamp
        
        Args:
            db: Database session
            event_type: Type of event (LOGIN, CREATE, UPDATE, DELETE)
            status: Status of the operation (success, failure)
            email: Email of the user performing the operation
            additional_info: JSON-compatible dictionary of additional details
            request: FastAPI request object for getting IP address
            
        Returns:
            The created AuditLog object
        """
        try:
            # Get client IP address if request is provided
            ip_address = None
            if request:
                ip_address = request.client.host if request.client else None
                
            # Convert additional_info to JSON string if necessary
            info_str = None
            if additional_info:
                if isinstance(additional_info, dict):
                    info_str = json.dumps(additional_info, cls=CustomJSONEncoder)
                else:
                    info_str = str(additional_info)
            
            # Create audit log entry - ONLY include columns known to exist in the database
            # Avoid using columns that might not exist yet (table_name, operation, entity_id, user_id)
            audit_log = AuditLog(
                timestamp=datetime.utcnow(),
                event_type=event_type,
                email=email,
                status=status,
                additional_info=info_str,
                ip_address=ip_address
            )
            
            # Add to database
            db.add(audit_log)
            db.commit()
            db.refresh(audit_log)
            
            logger.info(f"Audit log created: {event_type} by {email or 'unknown'} - {status}")
            return audit_log
            
        except Exception as e:
            logger.error(f"Error creating audit log: {e}")
            # Explicitly rollback the transaction
            db.rollback()
            # Don't raise the exception - audit logging should not break the main flow
            return None
    
    @classmethod
    async def log_create(
        cls, 
        db: Session, 
        entity_type: str, 
        entity_id: Union[str, int], 
        email: Optional[str] = None,
        entity: Optional[Any] = None,
        request: Optional[Request] = None
    ) -> Optional[AuditLog]:
        """Log a create operation"""
        additional_info = {
            "entity_type": entity_type,
            "entity_id": str(entity_id)
        }
        
        if entity:
            # If entity is provided, we'll log its initial state
            if hasattr(entity, "__dict__"):
                # Convert SQLAlchemy model to dict, excluding SQLAlchemy internal attributes
                entity_data = {k: v for k, v in entity.__dict__.items() 
                          if not k.startswith('_')}
                additional_info["entity_data"] = entity_data
        
        # Use log_change but don't rely on table_name, operation columns
        return await cls.log_change(
            db=db,
            event_type="CREATE",
            status="success",
            email=email,
            additional_info=additional_info,
            request=request
        )
    
    @classmethod
    async def log_update(
        cls,
        db: Session,
        entity_type: str,
        entity_id: Union[str, int],
        email: Optional[str] = None,
        old_values: Optional[Dict[str, Any]] = None,
        new_values: Optional[Dict[str, Any]] = None,
        request: Optional[Request] = None
    ) -> Optional[AuditLog]:
        """Log an update operation with before/after values"""
        additional_info = {
            "entity_type": entity_type,
            "entity_id": str(entity_id),
            "operation": "UPDATE"  # Include operation in additional_info instead
        }
        
        if old_values and new_values:
            additional_info["before"] = old_values
            additional_info["after"] = new_values
        elif new_values:
            additional_info["after"] = new_values
        
        # Use log_change but don't rely on table_name, operation columns
        return await cls.log_change(
            db=db,
            event_type="UPDATE",
            status="success",
            email=email,
            additional_info=additional_info,
            request=request
        )
    
    @classmethod
    async def log_delete(
        cls,
        db: Session,
        entity_type: str,
        entity_id: Union[str, int],
        email: Optional[str] = None,
        entity: Optional[Any] = None,
        request: Optional[Request] = None
    ) -> Optional[AuditLog]:
        """Log a delete operation"""
        additional_info = {
            "entity_type": entity_type,
            "entity_id": str(entity_id),
            "operation": "DELETE"  # Include operation in additional_info instead
        }
        
        if entity:
            # If entity is provided, we'll log its state before deletion
            if hasattr(entity, "__dict__"):
                # Convert SQLAlchemy model to dict, excluding SQLAlchemy internal attributes
                entity_data = {k: v for k, v in entity.__dict__.items() 
                          if not k.startswith('_')}
                additional_info["entity_data"] = entity_data
        
        # Use log_change but don't rely on table_name, operation columns
        return await cls.log_change(
            db=db,
            event_type="DELETE",
            status="success",
            email=email,
            additional_info=additional_info,
            request=request
        )

def log_action(
    db: Session,
    email: Optional[str],
    event_type: str,
    status: str = "success",
    details: Optional[Dict[str, Any]] = None,
    ip_address: Optional[str] = None
) -> Optional[AuditLog]:
    """
    Create an audit log entry for user actions
    
    Args:
        db: Database session
        email: Email of the user performing the action
        event_type: Type of event (LOGIN, VIEW, etc.)
        status: Status of the operation (success, failure)
        details: Optional additional details
        ip_address: Optional IP address of the user
        
    Returns:
        The created AuditLog object
    """
    try:
        # Create the audit log entry - ONLY include columns known to exist in the database
        # Avoid using columns that might not exist yet (table_name, operation, entity_id, user_id)
        audit_log = AuditLog(
            email=email,
            event_type=event_type,
            status=status,
            additional_info=json.dumps(details, cls=CustomJSONEncoder) if details else None,
            ip_address=ip_address or "0.0.0.0",
            timestamp=datetime.utcnow()
        )
        
        # Add to database
        db.add(audit_log)
        db.commit()
        db.refresh(audit_log)
        
        logger.info(f"Audit log created: {email or 'anonymous'} {event_type} - {status}")
        return audit_log
        
    except Exception as e:
        logger.error(f"Error creating audit log: {e}")
        # Explicitly rollback the transaction to avoid breaking application flow
        db.rollback()
        # Don't raise the exception - logging should not break application flow
        # Just return None if there was an error
        return None 