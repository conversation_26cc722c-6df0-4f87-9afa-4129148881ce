from fastapi import HTTPException, status
from sqlalchemy.orm import Session
from typing import List, Optional
import logging
import uuid

from src.models.role import Role
from src.schemas.role import RoleCreate, RoleUpdate

logger = logging.getLogger(__name__)

class RoleService:
    """
    Service for role-related operations
    """
    
    @staticmethod
    def create_role(db: Session, role_data: RoleCreate) -> Role:
        """
        Create a new role
        
        Args:
            db: Database session
            role_data: Role creation data
            
        Returns:
            The created role object
            
        Raises:
            HTTPException: If a role with the same name already exists
        """
        # Check if role name already exists
        existing_role = db.query(Role).filter(Role.name == role_data.name).first()
        if existing_role:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, 
                detail=f"Role with name '{role_data.name}' already exists"
            )
        
        # Create new role
        db_role = Role(name=role_data.name)
        
        # Save to database
        db.add(db_role)
        db.commit()
        db.refresh(db_role)
        
        logger.info(f"Role created: {db_role.name}")
        return db_role
    
    @staticmethod
    def get_roles(db: Session, skip: int = 0, limit: int = 100) -> List[Role]:
        """
        Get all roles with pagination
        
        Args:
            db: Database session
            skip: Number of records to skip
            limit: Maximum number of records to return
            
        Returns:
            List of role objects
        """
        roles = db.query(Role).offset(skip).limit(limit).all()
        return roles
    
    @staticmethod
    def get_roles_count(db: Session) -> int:
        """
        Get total count of roles
        
        Args:
            db: Database session
            
        Returns:
            Total count of roles
        """
        return db.query(Role).count()
    
    @staticmethod
    def get_role_by_id(db: Session, role_id: int) -> Optional[Role]:
        """
        Get a role by ID
        
        Args:
            db: Database session
            role_id: ID of the role to find
            
        Returns:
            Role object if found, None otherwise
        """
        try:
            # Ensure role_id is an integer
            if isinstance(role_id, str):
                try:
                    role_id = int(role_id)
                except ValueError:
                    # If it's a UUID string, it can't be converted to int
                    raise HTTPException(
                        status_code=status.HTTP_404_NOT_FOUND,
                        detail=f"Role with id {role_id} not found - invalid ID format"
                    )
            elif isinstance(role_id, uuid.UUID):
                # If it's a UUID, we can't use it directly
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"Role ID should be an integer, not UUID"
                )
            
            role = db.query(Role).filter(Role.id == role_id).first()
            if not role:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail=f"Role with id {role_id} not found"
                )
            return role
        except Exception as e:
            # Log the error but return a generic message
            print(f"Error getting role by ID: {e}")
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Role not found or invalid ID format"
            )
    
    @staticmethod
    def get_role_by_name(db: Session, name: str) -> Optional[Role]:
        """
        Get a role by name
        
        Args:
            db: Database session
            name: Name of the role to find
            
        Returns:
            Role object if found, None otherwise
        """
        return db.query(Role).filter(Role.name == name).first()
    
    @staticmethod
    def update_role(db: Session, role_id: int, role_update: RoleUpdate) -> Role:
        """
        Update a role by ID
        
        Args:
            db: Database session
            role_id: ID of the role to update
            role_update: Role update data
            
        Returns:
            The updated role object
            
        Raises:
            HTTPException: If role is not found or name is already taken
        """
        # Get role by ID
        db_role = RoleService.get_role_by_id(db, role_id)
        
        # Check if name is being changed and if new name is available
        if role_update.name != db_role.name:
            existing_role = RoleService.get_role_by_name(db, role_update.name)
            if existing_role:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Role with name '{role_update.name}' already exists"
                )
        
        # Update role fields
        db_role.name = role_update.name
        
        # Save to database
        db.commit()
        db.refresh(db_role)
        
        logger.info(f"Role updated: {db_role.name}")
        return db_role
    
    @staticmethod
    def delete_role(db: Session, role_id: int) -> None:
        """
        Delete a role by ID
        
        Args:
            db: Database session
            role_id: ID of the role to delete
            
        Raises:
            HTTPException: If role is not found or is in use by users
        """
        # Get role by ID
        db_role = RoleService.get_role_by_id(db, role_id)
        
        # Check if role is used by any users
        if len(db_role.users) > 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Cannot delete role that is assigned to users"
            )
        
        # Delete from database
        db.delete(db_role)
        db.commit()
        
        logger.info(f"Role deleted: {db_role.name}")
    
    @staticmethod
    def seed_default_roles(db: Session) -> None:
        """
        Seed default roles if they don't exist
        
        Args:
            db: Database session
        """
        default_roles = ["admin", "manager", "staff"]
        
        for role_name in default_roles:
            existing_role = RoleService.get_role_by_name(db, role_name)
            if not existing_role:
                db_role = Role(name=role_name)
                db.add(db_role)
                logger.info(f"Default role created: {role_name}")
        
        db.commit() 