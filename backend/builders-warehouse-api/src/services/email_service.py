import requests
import logging
import os
from typing import Optional
from src.core.config import settings

logger = logging.getLogger(__name__)

class EmailService:
    """
    Service for sending emails using Postmark API
    """
    
    POSTMARK_API_URL = "https://api.postmarkapp.com/email"
    POSTMARK_SERVER_TOKEN = os.getenv("POSTMARK_SERVER_TOKEN", "************************************")
    FROM_EMAIL = os.getenv("POSTMARK_FROM_EMAIL", "<EMAIL>")
    
    @classmethod
    async def send_password_reset_email(
        cls,
        to_email: str,
        user_name: str,
        reset_token: str,
        frontend_url: str = None
    ) -> bool:
        """
        Send password reset email using Postmark API
        
        Args:
            to_email: Recipient email address
            user_name: User's name for personalization
            reset_token: Password reset token
            frontend_url: Frontend URL for reset link
            
        Returns:
            bool: True if email sent successfully, False otherwise
        """
        if frontend_url is None:
            frontend_url = os.getenv("FRONTEND_URL", "http://localhost:4200")
            
        try:
            # Create reset link
            reset_link = f"{frontend_url}/reset-password?token={reset_token}"
            
            # Email content with improved styling and correct image path
            html_body = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Password Reset - Builders Warehouse</title>
                <style>
                    * {{
                        margin: 0;
                        padding: 0;
                        box-sizing: border-box;
                    }}
                    
                    body {{
                        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                        line-height: 1.6;
                        color: #333;
                        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
                        margin: 0;
                        padding: 20px;
                    }}
                    
                    .email-wrapper {{
                        max-width: 600px;
                        margin: 0 auto;
                        background-color: #ffffff;
                        border-radius: 20px;
                        overflow: hidden;
                        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
                    }}
                    
                    .header {{
                        background: linear-gradient(135deg, #042B41 0%, #0a3d5c 100%);
                        color: white;
                        text-align: center;
                        padding: 40px 20px;
                        position: relative;
                        overflow: hidden;
                    }}
                    
                    .header::before {{
                        content: '';
                        position: absolute;
                        top: -50%;
                        left: -50%;
                        width: 200%;
                        height: 200%;
                        background: radial-gradient(circle, rgba(255,255,255,0.1) 0%, transparent 70%);
                        animation: shimmer 3s infinite;
                    }}
                    
                    @keyframes shimmer {{
                        0% {{ transform: rotate(0deg); }}
                        100% {{ transform: rotate(360deg); }}
                    }}
                    
                    .logo {{
                        margin-bottom: 20px;
                        position: relative;
                        z-index: 2;
                    }}
                    
                    .logo img {{
                        max-width: 220px;
                        height: auto;
                        filter: brightness(0) invert(1);
                        transition: transform 0.3s ease;
                    }}
                    
                    .title {{
                        font-size: 32px;
                        font-weight: 700;
                        margin-bottom: 10px;
                        position: relative;
                        z-index: 2;
                        text-shadow: 0 2px 4px rgba(0,0,0,0.3);
                    }}
                    
                    .subtitle {{
                        font-size: 18px;
                        font-weight: 300;
                        opacity: 0.9;
                        position: relative;
                        z-index: 2;
                    }}
                    
                    .content {{
                        padding: 50px 40px;
                        background-color: #ffffff;
                    }}
                    
                    .greeting {{
                        font-size: 20px;
                        color: #042B41;
                        margin-bottom: 25px;
                        font-weight: 600;
                    }}
                    
                    .message {{
                        font-size: 16px;
                        color: #555;
                        margin-bottom: 35px;
                        line-height: 1.8;
                    }}
                    
                    .button-container {{
                        text-align: center;
                        margin: 40px 0;
                    }}
                    
                    .reset-button {{
                        display: inline-block;
                        background: linear-gradient(135deg, #042B41 0%, #0a3d5c 100%);
                        color: white;
                        padding: 18px 40px;
                        text-decoration: none;
                        border-radius: 50px;
                        font-weight: 600;
                        font-size: 16px;
                        letter-spacing: 0.5px;
                        transition: all 0.3s ease;
                        box-shadow: 0 8px 25px rgba(4, 43, 65, 0.3);
                        text-transform: uppercase;
                    }}
                    
                    .reset-button:hover {{
                        transform: translateY(-2px);
                        box-shadow: 0 12px 35px rgba(4, 43, 65, 0.4);
                        background: linear-gradient(135deg, #031f30 0%, #08344a 100%);
                    }}
                    
                    .warning {{
                        background: linear-gradient(135deg, #fff8e1 0%, #ffecb3 100%);
                        border-left: 4px solid #ff9800;
                        color: #e65100;
                        padding: 20px;
                        border-radius: 10px;
                        margin: 30px 0;
                        font-size: 15px;
                        box-shadow: 0 4px 15px rgba(255, 152, 0, 0.1);
                    }}
                    
                    .warning strong {{
                        color: #e65100;
                        font-weight: 700;
                    }}
                    
                    .security-note {{
                        background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
                        border-left: 4px solid #4caf50;
                        color: #2e7d32;
                        padding: 20px;
                        border-radius: 10px;
                        margin: 25px 0;
                        font-size: 15px;
                        box-shadow: 0 4px 15px rgba(76, 175, 80, 0.1);
                    }}
                    
                    .link-section {{
                        background-color: #f8f9fa;
                        padding: 20px;
                        border-radius: 10px;
                        margin: 25px 0;
                        border: 1px solid #e9ecef;
                    }}
                    
                    .link-section p {{
                        margin-bottom: 10px;
                        font-size: 14px;
                        color: #666;
                    }}
                    
                    .backup-link {{
                        word-break: break-all;
                        color: #042B41;
                        font-family: monospace;
                        background-color: #ffffff;
                        padding: 10px;
                        border-radius: 5px;
                        border: 1px solid #dee2e6;
                        font-size: 13px;
                    }}
                    
                    .footer {{
                        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
                        padding: 30px 40px;
                        text-align: center;
                        border-top: 1px solid #dee2e6;
                    }}
                    
                    .footer-content {{
                        font-size: 14px;
                        color: #6c757d;
                        margin-bottom: 15px;
                    }}
                    
                    .company-info {{
                        font-size: 13px;
                        color: #868e96;
                        font-style: italic;
                    }}
                    
                    /* Responsive Design */
                    @media (max-width: 600px) {{
                        .email-wrapper {{
                            margin: 10px;
                            border-radius: 15px;
                        }}
                        
                        .content {{
                            padding: 30px 20px;
                        }}
                        
                        .header {{
                            padding: 30px 15px;
                        }}
                        
                        .title {{
                            font-size: 26px;
                        }}
                        
                        .logo img {{
                            max-width: 180px;
                        }}
                        
                        .reset-button {{
                            padding: 16px 30px;
                            font-size: 15px;
                        }}
                    }}
                </style>
            </head>
            <body>
                <div class="email-wrapper">
                    <div class="header">
                        <div class="logo">
                            <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjgwIiBoZWlnaHQ9IjcwIiB2aWV3Qm94PSIwIDAgMjgwIDcwIiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPjx0ZXh0IHg9IjE0IiB5PSIzMCIgZm9udC1mYW1pbHk9IkFyaWFsLCBzYW5zLXNlcmlmIiBmb250LXNpemU9IjIyIiBmb250LXdlaWdodD0iYm9sZCIgZmlsbD0id2hpdGUiPkJ1aWxkZXJzIFdhcmVob3VzZTwvdGV4dD48dGV4dCB4PSIxNCIgeT0iNTAiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxNiIgZmlsbD0id2hpdGUiIG9wYWNpdHk9IjAuOSI+QXVzdHJhbGlhPC90ZXh0Pjwvc3ZnPg==" alt="Builders Warehouse Australia" />
                        </div>
                        <h1 class="title">Password Reset Request</h1>
                        <p class="subtitle">Secure access to your account</p>
                    </div>
                    
                    <div class="content">
                        <div class="greeting">Hello {user_name},</div>
                        
                        <div class="message">
                            We received a request to reset your password for your <strong>Builders Warehouse</strong> account. 
                            If you made this request, please click the button below to create a new password and regain access to your account.
                        </div>
                        
                        <div class="button-container">
                            <a href="{reset_link}" class="reset-button">Reset Your Password</a>
                        </div>
                        
                        <div class="warning">
                            <strong>⏰ Important:</strong> This reset link will expire in <strong>1 hour</strong> for security reasons. 
                            If you don't reset your password within this time, you'll need to request a new reset link.
                        </div>
                        
                        <div class="security-note">
                            <strong>🔒 Security Note:</strong> If you didn't request a password reset, please ignore this email. 
                            Your password will remain unchanged and your account stays secure.
                        </div>
                        
                        <div class="link-section">
                            <p><strong>Having trouble with the button?</strong> Copy and paste this link into your browser:</p>
                            <div class="backup-link">{reset_link}</div>
                        </div>
                    </div>
                    
                    <div class="footer">
                        <div class="footer-content">
                            <strong>This is an automated email from Builders Warehouse Australia.</strong><br>
                            Please do not reply to this message.
                        </div>
                        <div class="company-info">
                            © 2024 Builders Warehouse Australia. All rights reserved.
                        </div>
                    </div>
                </div>
            </body>
            </html>
            """
            
            # Prepare email data
            email_data = {
                "From": cls.FROM_EMAIL,
                "To": to_email,
                "Subject": "🔐 Password Reset Request - Builders Warehouse",
                "HtmlBody": html_body
            }
            
            # Send email via Postmark API
            headers = {
                "Accept": "application/json",
                "Content-Type": "application/json",
                "X-Postmark-Server-Token": cls.POSTMARK_SERVER_TOKEN
            }
            
            response = requests.post(
                cls.POSTMARK_API_URL,
                json=email_data,
                headers=headers,
                timeout=30
            )
            
            if response.status_code == 200:
                logger.info(f"Password reset email sent successfully to {to_email}")
                return True
            else:
                logger.error(f"Failed to send password reset email to {to_email}. Status: {response.status_code}, Response: {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"Error sending password reset email to {to_email}: {str(e)}")
            return False 

    @classmethod
    async def send_purchase_order_email(
        cls,
        to_email: str,
        supplier_name: str,
        po_data: dict,
        frontend_url: str = None
    ) -> bool:
        """
        Send purchase order details email to supplier using Postmark API
        
        Args:
            to_email: Supplier email address
            supplier_name: Supplier's name for personalization
            po_data: Purchase order data containing all details
            frontend_url: Frontend URL for links
            
        Returns:
            bool: True if email sent successfully, False otherwise
        """
        if frontend_url is None:
            frontend_url = os.getenv("FRONTEND_URL", "http://localhost:3000")
            
        try:
            # Extract PO details
            po_number = po_data.get('po_number', f"PO-{po_data.get('id', 'N/A')}")
            po_date = po_data.get('date', po_data.get('order_date', 'N/A'))
            total_amount = po_data.get('total_amount', 0)
            items = po_data.get('items', po_data.get('details', []))
            payment_terms = po_data.get('payment_terms', 'Net 30')
            
            # Create items table HTML
            items_html = ""
            total_quantity = 0
            
            for i, item in enumerate(items):
                quantity = item.get('quantity', 0)
                unit_price = item.get('unit_price', 0)
                total_price = quantity * unit_price
                total_quantity += quantity
                
                items_html += f"""
                <tr style="border-bottom: 1px solid #e9ecef;">
                    <td style="padding: 12px 8px; text-align: center; font-weight: 500;">{i + 1}</td>
                    <td style="padding: 12px 8px; font-weight: 500;">{item.get('sku', 'N/A')}</td>
                    <td style="padding: 12px 8px;">{item.get('description', 'N/A')}</td>
                    <td style="padding: 12px 8px; text-align: center;">{quantity}</td>
                    <td style="padding: 12px 8px; text-align: right;">${unit_price:.2f}</td>
                    <td style="padding: 12px 8px; text-align: right; font-weight: 600;">${total_price:.2f}</td>
                </tr>
                """
            
            # Use absolute URL for logo to ensure it loads properly
            logo_url = "https://raw.githubusercontent.com/yourusername/builders-warehouse/main/frontend/builders-warehouse-ui/public/Builder-Logo.png"
            
            # Email content with improved PO details styling
            html_body = f"""
            <!DOCTYPE html>
            <html>
            <head>
                <meta charset="utf-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>Purchase Order - {po_number}</title>
                <style>
                    * {{
                        margin: 0;
                        padding: 0;
                        box-sizing: border-box;
                    }}
                    
                    body {{
                        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                        line-height: 1.6;
                        color: #333;
                        background-color: #f8f9fa;
                        margin: 0;
                        padding: 20px;
                    }}
                    
                    .email-wrapper {{
                        max-width: 700px;
                        margin: 0 auto;
                        background-color: #ffffff;
                        border-radius: 8px;
                        overflow: hidden;
                        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
                    }}
                    
                    .header {{
                        background-color: #042B41;
                        color: white;
                        text-align: center;
                        padding: 30px 20px;
                    }}
                    
                    .logo {{
                        margin-bottom: 15px;
                    }}
                    
                    .logo img {{
                        max-width: 200px;
                        height: auto;
                    }}
                    
                    .title {{
                        font-size: 24px;
                        font-weight: 600;
                        margin-bottom: 5px;
                    }}
                    
                    .subtitle {{
                        font-size: 16px;
                        opacity: 0.9;
                        font-weight: 400;
                    }}
                    
                    .content {{
                        padding: 30px;
                    }}
                    
                    .greeting {{
                        font-size: 18px;
                        color: #042B41;
                        margin-bottom: 20px;
                        font-weight: 500;
                    }}
                    
                    .po-header {{
                        background-color: #f8f9fa;
                        padding: 20px;
                        border-radius: 6px;
                        margin-bottom: 25px;
                        border-left: 4px solid #042B41;
                    }}
                    
                    .po-details {{
                        display: grid;
                        grid-template-columns: repeat(2, 1fr);
                        gap: 15px;
                    }}
                    
                    .po-detail-item {{
                        padding: 0;
                    }}
                    
                    .po-detail-label {{
                        font-weight: 600;
                        color: #666;
                        font-size: 12px;
                        text-transform: uppercase;
                        letter-spacing: 0.5px;
                        margin-bottom: 4px;
                    }}
                    
                    .po-detail-value {{
                        font-size: 16px;
                        color: #042B41;
                        font-weight: 600;
                    }}
                    
                    .message {{
                        background-color: #e3f2fd;
                        border-left: 4px solid #2196f3;
                        padding: 15px;
                        margin: 20px 0;
                        border-radius: 4px;
                        font-size: 14px;
                    }}
                    
                    .items-table {{
                        width: 100%;
                        border-collapse: collapse;
                        margin: 25px 0;
                        border-radius: 6px;
                        overflow: hidden;
                        border: 1px solid #e0e0e0;
                    }}
                    
                    .items-table thead {{
                        background-color: #042B41;
                        color: white;
                    }}
                    
                    .items-table th {{
                        padding: 12px 10px;
                        text-align: left;
                        font-weight: 600;
                        font-size: 13px;
                    }}
                    
                    .items-table th:first-child,
                    .items-table th:nth-child(4) {{
                        text-align: center;
                    }}
                    
                    .items-table th:nth-child(5),
                    .items-table th:nth-child(6) {{
                        text-align: right;
                    }}
                    
                    .items-table td {{
                        padding: 12px 10px;
                        border-bottom: 1px solid #e0e0e0;
                        font-size: 14px;
                    }}
                    
                    .items-table tbody tr:nth-child(even) {{
                        background-color: #f9f9f9;
                    }}
                    
                    .total-section {{
                        background-color: #f8f9fa;
                        padding: 20px;
                        border-radius: 6px;
                        margin-top: 20px;
                        border-left: 4px solid #042B41;
                    }}
                    
                    .total-row {{
                        display: flex;
                        justify-content: space-between;
                        align-items: center;
                        margin-bottom: 8px;
                        font-size: 14px;
                    }}
                    
                    .total-row:last-child {{
                        margin-bottom: 0;
                        padding-top: 10px;
                        border-top: 2px solid #042B41;
                        font-size: 16px;
                        font-weight: 600;
                    }}
                    
                    .total-label {{
                        color: #666;
                    }}
                    
                    .total-value {{
                        color: #042B41;
                        font-weight: 600;
                    }}
                    
                    .footer {{
                        background-color: #f8f9fa;
                        padding: 20px;
                        text-align: center;
                        border-top: 1px solid #e0e0e0;
                    }}
                    
                    .footer-content {{
                        font-size: 13px;
                        color: #666;
                        margin-bottom: 8px;
                    }}
                    
                    .company-info {{
                        font-size: 12px;
                        color: #999;
                    }}
                    
                    @media (max-width: 600px) {{
                        .email-wrapper {{
                            margin: 10px;
                        }}
                        
                        .content {{
                            padding: 20px;
                        }}
                        
                        .po-details {{
                            grid-template-columns: 1fr;
                            gap: 12px;
                        }}
                        
                        .items-table {{
                            font-size: 12px;
                        }}
                        
                        .items-table th,
                        .items-table td {{
                            padding: 8px 6px;
                        }}
                        
                        .title {{
                            font-size: 20px;
                        }}
                    }}
                </style>
            </head>
            <body>
                <div class="email-wrapper">
                    <div class="header">
                        <div class="logo">
                            <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjgwIiBoZWlnaHQ9IjcwIiB2aWV3Qm94PSIwIDAgMjgwIDcwIiBmaWxsPSJub25lIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciPjx0ZXh0IHg9IjE0IiB5PSIzMCIgZm9udC1mYW1pbHk9IkFyaWFsLCBzYW5zLXNlcmlmIiBmb250LXNpemU9IjIyIiBmb250LXdlaWdodD0iYm9sZCIgZmlsbD0id2hpdGUiPkJ1aWxkZXJzIFdhcmVob3VzZTwvdGV4dD48dGV4dCB4PSIxNCIgeT0iNTAiIGZvbnQtZmFtaWx5PSJBcmlhbCwgc2Fucy1zZXJpZiIgZm9udC1zaXplPSIxNiIgZmlsbD0id2hpdGUiIG9wYWNpdHk9IjAuOSI+QXVzdHJhbGlhPC90ZXh0Pjwvc3ZnPg==" alt="Builders Warehouse Australia" />
                        </div>
                        <h1 class="title">Purchase Order</h1>
                        <p class="subtitle">{po_number}</p>
                    </div>
                    
                    <div class="content">
                        <div class="greeting">Dear {supplier_name},</div>
                        
                        <div class="po-header">
                            <div class="po-details">
                                <div class="po-detail-item">
                                    <div class="po-detail-label">Purchase Order #</div>
                                    <div class="po-detail-value">{po_number}</div>
                                </div>
                                <div class="po-detail-item">
                                    <div class="po-detail-label">Order Date</div>
                                    <div class="po-detail-value">{po_date}</div>
                                </div>
                                <div class="po-detail-item">
                                    <div class="po-detail-label">Payment Terms</div>
                                    <div class="po-detail-value">{payment_terms}</div>
                                </div>
                                <div class="po-detail-item">
                                    <div class="po-detail-label">Total Items</div>
                                    <div class="po-detail-value">{total_quantity}</div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="message">
                            <strong>Please supply the following items:</strong><br>
                            Review the order details and confirm availability and delivery timeline.
                        </div>
                        
                        <table class="items-table">
                            <thead>
                                <tr>
                                    <th>#</th>
                                    <th>SKU</th>
                                    <th>Description</th>
                                    <th>Qty</th>
                                    <th>Unit Price</th>
                                    <th>Total</th>
                                </tr>
                            </thead>
                            <tbody>
                                {items_html}
                            </tbody>
                        </table>
                        
                        <div class="total-section">
                            <div class="total-row">
                                <span class="total-label">Total Quantity:</span>
                                <span class="total-value">{total_quantity} items</span>
                            </div>
                            <div class="total-row">
                                <span class="total-label">Total Amount:</span>
                                <span class="total-value">${total_amount:.2f}</span>
                            </div>
                        </div>
                        
                        <div class="message">
                            <strong>Next Steps:</strong><br>
                            • Please confirm receipt of this purchase order<br>
                            • Provide delivery timeline for the items<br>
                            • Contact us if you have any questions
                        </div>
                    </div>
                    
                    <div class="footer">
                        <div class="footer-content">
                            <strong>Builders Warehouse Australia</strong><br>
                            For queries, please contact us directly.
                        </div>
                        <div class="company-info">
                            © 2024 Builders Warehouse Australia. All rights reserved.
                        </div>
                    </div>
                </div>
            </body>
            </html>
            """
            
            # Prepare email data
            email_data = {
                "From": cls.FROM_EMAIL,
                "To": to_email,
                "Subject": f"📋 Purchase Order {po_number} - Builders Warehouse",
                "HtmlBody": html_body
            }
            
            # Send email via Postmark API
            headers = {
                "Accept": "application/json",
                "Content-Type": "application/json",
                "X-Postmark-Server-Token": cls.POSTMARK_SERVER_TOKEN
            }
            
            response = requests.post(
                cls.POSTMARK_API_URL,
                json=email_data,
                headers=headers,
                timeout=30
            )
            
            if response.status_code == 200:
                logger.info(f"Purchase order email sent successfully to {to_email} for PO {po_number}")
                return True
            else:
                logger.error(f"Failed to send purchase order email to {to_email} for PO {po_number}. Status: {response.status_code}, Response: {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"Error sending purchase order email to {to_email}: {str(e)}")
            return False 