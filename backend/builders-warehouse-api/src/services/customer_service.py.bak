"""
Unified Customer Service
This module provides customer service functions that work with either Prisma or SQLAlchemy repositories
"""
from typing import List, Optional, Dict, Any, Tuple, Union
from sqlalchemy.ext.asyncio import AsyncSession
from src.repository.factory import RepositoryFactory
from src.schemas.customer import CustomerCreate, CustomerUpdate, InvoiceListParams
from src.utils import serialize_uuids
import logging

logger = logging.getLogger(__name__)

async def create_customer(db: AsyncSession, obj_in: CustomerCreate) -> Dict[str, Any]:
    """
    Create a new customer
    
    Args:
        db: Database session
        obj_in: Customer data from API
        
    Returns:
        Created customer data
    """
    try:
        # Convert Pydantic model to dict
        customer_data = obj_in.model_dump(exclude_unset=True)
        
        # Serialize any UUID objects
        customer_data = serialize_uuids(customer_data)
        
        # Get the repository class
        from src.repository.customer import SQLAlchemyCustomerRepository
        
        # Create customer using repository class method directly
        return await SQLAlchemyCustomerRepository.create_customer(db, customer_data)
    except Exception as e:
        logger.error(f"Error creating customer: {e}")
        raise

async def get_customer(db: AsyncSession, customer_id: int) -> Optional[Dict[str, Any]]:
    """
    Get a customer by ID
    
    Args:
        db: Database session
        customer_id: Customer ID
        
    Returns:
        Customer data or None if not found
    """
    try:
        # Log the customer_id being requested
        logger.info(f"Getting customer with ID: {customer_id} (type: {type(customer_id)})")
        
        # Get the repository implementation
        repository = RepositoryFactory.get_customer_repository()
        
        # Get customer using repository
        if hasattr(repository, 'get_customer_by_id'):
            if 'db' in repository.get_customer_by_id.__code__.co_varnames:
                customer = await repository.get_customer_by_id(db, customer_id)
                logger.info(f"Customer lookup result: {'Found' if customer else 'Not found'}")
                return customer
            else:
                customer = await repository.get_customer_by_id(customer_id)
                logger.info(f"Customer lookup result: {'Found' if customer else 'Not found'}")
                return customer
        else:
            logger.error("Repository missing get_customer_by_id method")
            return None
    except Exception as e:
        logger.error(f"Error getting customer with ID {customer_id}: {str(e)}")
        # Return None instead of raising to avoid breaking the API
        return None

async def get_customer_by_email(db: AsyncSession, email: str) -> Optional[Dict[str, Any]]:
    """
    Get a customer by email
    
    Args:
        db: Database session
        email: Customer email
        
    Returns:
        Customer data or None if not found
    """
    try:
        # Get the repository implementation
        repository = RepositoryFactory.get_customer_repository()
        
        # Get customer using repository
        if hasattr(repository, 'get_customer_by_email'):
            if 'db' in repository.get_customer_by_email.__code__.co_varnames:
                return await repository.get_customer_by_email(db, email)
            else:
                return await repository.get_customer_by_email(email)
    except Exception as e:
        logger.error(f"Error getting customer by email: {e}")
        raise

async def list_customers(
    db: AsyncSession, 
    skip: int = 0, 
    limit: int = 100, 
    search: Optional[str] = None
) -> Tuple[List[Dict[str, Any]], int]:
    """
    List customers with optional search and pagination
    
    Args:
        db: Database session
        skip: Number of records to skip
        limit: Maximum number of records to return
        search: Optional search term
        
    Returns:
        Tuple of (list of customers, total count)
    """
    try:
        # Get the repository implementation
        repository = RepositoryFactory.get_customer_repository()
        
        # List customers using repository
        if hasattr(repository, 'list_customers'):
            if 'db' in repository.list_customers.__code__.co_varnames:
                return await repository.list_customers(db, skip, limit, search)
            else:
                return await repository.list_customers(skip, limit, search)
    except Exception as e:
        logger.error(f"Error listing customers: {e}")
        raise

async def update_customer(db: AsyncSession, customer_id: int, obj_in: CustomerUpdate) -> Dict[str, Any]:
    """
    Update a customer
    
    Args:
        db: Database session
        customer_id: Customer ID
        obj_in: Customer data to update
        
    Returns:
        Updated customer data
    """
    try:
        # Convert Pydantic model to dict
        customer_data = obj_in.model_dump(exclude_unset=True)
        
        # Serialize any UUID objects
        customer_data = serialize_uuids(customer_data)
        
        # Get the repository implementation
        repository = RepositoryFactory.get_customer_repository()
        
        # Update customer using repository
        if hasattr(repository, 'update_customer'):
            if 'db' in repository.update_customer.__code__.co_varnames:
                return await repository.update_customer(db, customer_id, customer_data)
            else:
                return await repository.update_customer(customer_id, customer_data)
    except Exception as e:
        logger.error(f"Error updating customer: {e}")
        raise

async def delete_customer(db: AsyncSession, customer_id: int) -> Dict[str, Any]:
    """
    Delete a customer
    
    Args:
        db: Database session
        customer_id: Customer ID
        
    Returns:
        Deleted customer data
    """
    try:
        # Get the repository implementation
        repository = RepositoryFactory.get_customer_repository()
        
        # Delete customer using repository
        if hasattr(repository, 'delete_customer'):
            if 'db' in repository.delete_customer.__code__.co_varnames:
                return await repository.delete_customer(db, customer_id)
            else:
                return await repository.delete_customer(customer_id)
    except Exception as e:
        logger.error(f"Error deleting customer: {e}")
        raise

async def get_customer_invoice_summary(db: AsyncSession, customer_id: int) -> Dict[str, Any]:
    """
    Get a summary of invoices for a customer
    
    Args:
        db: Database session
        customer_id: Customer ID
        
    Returns:
        Invoice summary data
    """
    try:
        # Get the repository implementation
        repository = RepositoryFactory.get_customer_repository()
        
        # Get invoice summary using repository
        if hasattr(repository, 'get_customer_invoice_summary'):
            if 'db' in repository.get_customer_invoice_summary.__code__.co_varnames:
                return await repository.get_customer_invoice_summary(db, customer_id)
            else:
                return await repository.get_customer_invoice_summary(customer_id)
    except Exception as e:
        logger.error(f"Error getting customer invoice summary: {e}")
        raise

async def list_customer_invoices(
    db: AsyncSession,
    customer_id: int,
    params: InvoiceListParams
) -> Tuple[List[Dict[str, Any]], int]:
    """
    List invoices for a customer with filtering and pagination
    
    Args:
        db: Database session
        customer_id: Customer ID
        params: Invoice list parameters (date range, type, pagination)
        
    Returns:
        Tuple of (list of invoices, total count)
    """
    try:
        # Get the repository implementation
        repository = RepositoryFactory.get_customer_repository()
        
        # For temporary solution, if no implementation exists, return empty list
        if not hasattr(repository, 'list_customer_invoices'):
            logger.warning("Repository has no list_customer_invoices method. Returning empty list.")
            return [], 0
            
        # List customer invoices using repository
        if 'db' in repository.list_customer_invoices.__code__.co_varnames:
            return await repository.list_customer_invoices(db, customer_id, params)
        else:
            return await repository.list_customer_invoices(customer_id, params)
    except Exception as e:
        logger.error(f"Error listing customer invoices: {e}")
        # Return empty list instead of raising to avoid breaking the UI
        return [], 0 