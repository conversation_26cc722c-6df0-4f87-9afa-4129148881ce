from fastapi import HTTPException, status, Request
from sqlalchemy.orm import Session
from sqlalchemy import desc
from typing import List, Optional, Dict, Any
import logging
from datetime import datetime, date
import uuid

from src.models.invoice import Invoice, PaymentMode
from src.models.customer import Customer
from src.models.company import Company
from src.models.quote import Quote
from src.schemas.invoice import InvoiceCreate, InvoiceItemCreate
from src.services.auditlog import AuditLogger

logger = logging.getLogger(__name__)

class InvoiceService:
    """Service for invoice-related operations"""
    
    @staticmethod
    async def generate_invoice_number(db: Session) -> str:
        """Generate a unique sequential invoice number"""
        # Find the highest invoice number and increment
        last_invoice = db.query(Invoice).order_by(desc(Invoice.id)).first()
        
        if last_invoice and last_invoice.invoice_number and last_invoice.invoice_number.startswith("INV-"):
            try:
                last_number = int(last_invoice.invoice_number.split("-")[1])
                new_number = last_number + 1
            except (ValueError, IndexError):
                # If the last invoice_number doesn't follow the format, start from 1
                new_number = 1
        else:
            new_number = 1
            
        return f"INV-{new_number:03d}"
    
    @staticmethod
    async def create_invoice_from_quote(
        db: Session,
        quote: Quote,
        request: Optional[Request] = None
    ) -> Invoice:
        """Create an invoice from a quote"""
        try:
            # Generate invoice number
            invoice_number = await InvoiceService.generate_invoice_number(db)
            logger.info(f"Generated invoice number: {invoice_number}")
            
            # Process quote items for invoice
            items_data = {"items": []}
            if quote.items:
                if isinstance(quote.items, dict) and 'items' in quote.items:
                    items_data = quote.items
                elif isinstance(quote.items, list):
                    items_data = {"items": quote.items}
            
            # Create invoice with quote data
            db_invoice = Invoice(
                invoice_number=invoice_number,
                company_id=quote.company_id or 1,  # Use quote's company_id or default
                customer_id=quote.customer_id,
                linked_quote_id=str(quote.id),  # Link to the original quote
                invoice_date=quote.quote_date or datetime.now().date(),  # Use quote date
                due_date=quote.quote_date or datetime.now().date(),  # Set due date same as quote date
                status="draft",
                invoice_type=quote.store_type or "Cranbourne",  # Use quote's store_type
                store_type_name=quote.store_type or "Cranbourne",
                notes=f"Generated from Quote {quote.quote_number}. {quote.notes or ''}".strip(),
                items=items_data,
                grand_total=float(quote.grand_total) if quote.grand_total else 0.0,
                total_gst=float(quote.total_gst) if quote.total_gst else 0.0,
                total_order=float(quote.grand_total) if quote.grand_total else 0.0,
                shipping=0.0,
                credit_card_surcharge=0.0,
                mode_of_payment=PaymentMode.CASH,  # Default payment mode
                dont_send_po=False
            )
            
            # Add invoice to session and commit
            db.add(db_invoice)
            db.commit()
            db.refresh(db_invoice)
            
            # Log invoice creation in audit log
            await AuditLogger.log_create(
                db=db,
                entity_type="invoices",
                entity_id=db_invoice.id,
                entity=db_invoice,
                request=request
            )
            
            logger.info(f"Invoice created from quote: {invoice_number} (from quote {quote.quote_number})")
            return db_invoice
            
        except Exception as e:
            db.rollback()
            logger.error(f"Error creating invoice from quote: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error creating invoice from quote: {str(e)}"
            )
    
    @staticmethod
    async def delete_quote_after_conversion(
        db: Session,
        quote_id: int,
        request: Optional[Request] = None
    ) -> bool:
        """Delete a quote after successful conversion to invoice"""
        try:
            # Get quote
            db_quote = db.query(Quote).filter(Quote.id == quote_id).first()
            if not db_quote:
                logger.warning(f"Quote with ID {quote_id} not found for deletion")
                return False
            
            # Store quote info for audit log
            quote_number = db_quote.quote_number
            
            # Delete the quote from database
            db.delete(db_quote)
            db.commit()
            
            # Log quote deletion in audit log
            await AuditLogger.log_delete(
                db=db,
                entity_type="quotes",
                entity_id=quote_id,
                entity={"quote_number": quote_number, "reason": "converted_to_invoice"},
                request=request
            )
            
            logger.info(f"Quote {quote_number} deleted after conversion to invoice")
            return True
            
        except Exception as e:
            db.rollback()
            logger.error(f"Error deleting quote after conversion: {str(e)}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error deleting quote after conversion: {str(e)}"
            ) 