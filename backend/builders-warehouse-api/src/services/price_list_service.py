from typing import List, Optional, Dict, Any
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.future import select
from sqlalchemy import insert
from uuid import UUID
import datetime

from src.schemas.customer import PriceListCreate, PriceList
from src.models.customer import PriceList as PriceListModel

class PriceListService:
    async def list_price_lists(self, db: AsyncSession) -> List[PriceList]:
        """
        List all price lists using SQLAlchemy.
        """
        try:
            # Create a select statement
            stmt = select(PriceListModel)
            
            # Execute the query
            result = await db.execute(stmt)
            price_list_models = result.scalars().all()
            
            # Convert SQLAlchemy models to Pydantic models
            price_lists = []
            for pl in price_list_models:
                price_lists.append(
                    PriceList(
                        id=str(pl.id),
                        name=pl.name,
                        currency=pl.currency
                    )
                )
            
            return price_lists
        except Exception as e:
            print(f"Error listing price lists: {str(e)}")
            raise
    
    async def create_price_list(self, db: AsyncSession, price_list_data: PriceListCreate) -> PriceList:
        """
        Create a new price list using SQLAlchemy.
        """
        try:
            # Create a new price list model instance
            new_price_list = PriceListModel(
                name=price_list_data.name,
                currency=price_list_data.currency,
                created_at=datetime.datetime.utcnow(),
                updated_at=datetime.datetime.utcnow()
            )
            
            # Add to session and commit
            db.add(new_price_list)
            await db.commit()
            await db.refresh(new_price_list)
            
            # Convert SQLAlchemy model to Pydantic model
            return PriceList(
                id=str(new_price_list.id),
                name=new_price_list.name,
                currency=new_price_list.currency
            )
        except Exception as e:
            await db.rollback()
            print(f"Error creating price list: {str(e)}")
            raise
    
    async def get_price_list_by_id(self, db: AsyncSession, price_list_id: UUID) -> Optional[PriceList]:
        """
        Get a price list by ID using SQLAlchemy.
        """
        try:
            # Create a select statement with a filter
            stmt = select(PriceListModel).where(PriceListModel.id == price_list_id)
            
            # Execute the query
            result = await db.execute(stmt)
            price_list = result.scalar_one_or_none()
            
            if not price_list:
                return None
            
            # Convert SQLAlchemy model to Pydantic model
            return PriceList(
                id=str(price_list.id),
                name=price_list.name,
                currency=price_list.currency
            )
        except Exception as e:
            print(f"Error getting price list by ID: {str(e)}")
            raise 