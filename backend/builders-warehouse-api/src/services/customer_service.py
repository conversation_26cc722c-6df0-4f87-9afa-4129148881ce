"""
Unified Customer Service
This module provides customer service functions that work with either Prisma or SQLAlchemy repositories
"""
from typing import List, Optional, Dict, Any, Tuple, Union
from sqlalchemy.ext.asyncio import AsyncSession
from src.repository.customer import SQLAlchemyCustomerRepository
from src.schemas.customer import CustomerCreate, CustomerUpdate, InvoiceListParams
from src.utils import serialize_uuids
import logging

logger = logging.getLogger(__name__)

async def create_customer(db: AsyncSession, obj_in: CustomerCreate) -> Dict[str, Any]:
    """
    Create a new customer
    
    Args:
        db: Database session
        obj_in: Customer data from API
        
    Returns:
        Created customer data
    """
    try:
        # Convert Pydantic model to dict
        customer_data = obj_in.model_dump(exclude_unset=True)
        
        # Serialize any UUID objects
        customer_data = serialize_uuids(customer_data)
        
        # Create customer using repository class method directly
        return await SQLAlchemyCustomerRepository.create_customer(db, customer_data)
    except Exception as e:
        logger.error(f"Error creating customer: {e}")
        raise

async def get_customer(db: AsyncSession, customer_id: int) -> Optional[Dict[str, Any]]:
    """
    Get a customer by ID
    
    Args:
        db: Database session
        customer_id: Customer ID
        
    Returns:
        Customer data or None if not found
    """
    try:
        # Log the customer_id being requested
        logger.info(f"Getting customer with ID: {customer_id} (type: {type(customer_id)})")
        
        # Get customer using repository class method directly
        customer = await SQLAlchemyCustomerRepository.get_customer_by_id(db, customer_id)
        logger.info(f"Customer lookup result: {'Found' if customer else 'Not found'}")
        return customer
    except Exception as e:
        logger.error(f"Error getting customer with ID {customer_id}: {str(e)}")
        # Return None instead of raising to avoid breaking the API
        return None

async def get_customer_by_email(db: AsyncSession, email: str) -> Optional[Dict[str, Any]]:
    """
    Get a customer by email
    
    Args:
        db: Database session
        email: Customer email
        
    Returns:
        Customer data or None if not found
    """
    try:
        # Get customer using repository class method directly
        return await SQLAlchemyCustomerRepository.get_customer_by_email(db, email)
    except Exception as e:
        logger.error(f"Error getting customer by email: {e}")
        raise

async def list_customers(
    db: AsyncSession, 
    skip: int = 0, 
    limit: int = 100, 
    search: Optional[str] = None
) -> Tuple[List[Dict[str, Any]], int]:
    """
    List customers with optional search and pagination
    
    Args:
        db: Database session
        skip: Number of records to skip
        limit: Maximum number of records to return
        search: Optional search term
        
    Returns:
        Tuple of (list of customers, total count)
    """
    try:
        # List customers using repository class method directly
        return await SQLAlchemyCustomerRepository.list_customers(db, skip, limit, search)
    except Exception as e:
        logger.error(f"Error listing customers: {e}")
        raise

async def update_customer(db: AsyncSession, customer_id: int, obj_in: CustomerUpdate) -> Dict[str, Any]:
    """
    Update a customer
    
    Args:
        db: Database session
        customer_id: Customer ID
        obj_in: Customer data to update
        
    Returns:
        Updated customer data
    """
    try:
        # Convert Pydantic model to dict
        customer_data = obj_in.model_dump(exclude_unset=True)
        
        # Serialize any UUID objects
        customer_data = serialize_uuids(customer_data)
        
        # Update customer using repository class method directly
        return await SQLAlchemyCustomerRepository.update_customer(db, customer_id, customer_data)
    except Exception as e:
        logger.error(f"Error updating customer: {e}")
        raise

async def delete_customer(db: AsyncSession, customer_id: int) -> Dict[str, Any]:
    """
    Delete a customer
    
    Args:
        db: Database session
        customer_id: Customer ID
        
    Returns:
        Deleted customer data
    """
    try:
        # Delete customer using repository class method directly
        return await SQLAlchemyCustomerRepository.delete_customer(db, customer_id)
    except Exception as e:
        logger.error(f"Error deleting customer: {e}")
        raise

async def get_customer_invoice_summary(db: AsyncSession, customer_id: int) -> Dict[str, Any]:
    """
    Get a summary of invoices for a customer
    
    Args:
        db: Database session
        customer_id: Customer ID
        
    Returns:
        Invoice summary data
    """
    try:
        # Get invoice summary using repository class method directly
        return await SQLAlchemyCustomerRepository.get_customer_invoice_summary(db, customer_id)
    except Exception as e:
        logger.error(f"Error getting customer invoice summary: {e}")
        raise

async def list_customer_invoices(
    db: AsyncSession,
    customer_id: int,
    params: InvoiceListParams
) -> Tuple[List[Dict[str, Any]], int]:
    """
    List invoices for a customer with filtering and pagination
    
    Args:
        db: Database session
        customer_id: Customer ID
        params: Invoice list parameters (date range, type, pagination)
        
    Returns:
        Tuple of (list of invoices, total count)
    """
    try:
        # List customer invoices using repository class method directly
        return await SQLAlchemyCustomerRepository.list_customer_invoices(db, customer_id, params)
    except Exception as e:
        logger.error(f"Error listing customer invoices: {e}")
        raise 