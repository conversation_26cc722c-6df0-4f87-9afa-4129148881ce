from typing import Dict, List, Optional, Tuple
from sqlalchemy.orm import Session
from src.models.company import Company
from src.models.invoice import Invoice
from src.models.quote import Quote
from src.schemas.company import CompanyHistoryItem, CompanyHistoryItemType


class CompanyService:
    """Service for company operations"""

    @staticmethod
    def get_company_history(
        db: Session, 
        company_id: str, 
        page: int = 1, 
        limit: int = 10
    ) -> Dict:
        """
        Get history of invoices and quotes for a specific company
        
        Args:
            db: Database session
            company_id: UUID of company
            page: Page number
            limit: Number of items per page
            
        Returns:
            Dictionary with invoices and quotes pagination data
        """
        # Calculate offset for pagination
        offset = (page - 1) * limit
        
        # Get invoices for the company
        invoice_query = db.query(Invoice).filter(
            Invoice.company_id == company_id
        ).order_by(Invoice.date.desc())
        
        total_invoices = invoice_query.count()
        
        invoices = invoice_query.offset(offset).limit(limit).all()
        
        # Get quotes for the company
        quote_query = db.query(Quote).filter(
            Quote.company_id == company_id,
            Quote.is_deleted == False
        ).order_by(Quote.date.desc())
        
        total_quotes = quote_query.count()
        
        quotes = quote_query.offset(offset).limit(limit).all()
        
        # Format the invoice results
        invoice_items = []
        for invoice in invoices:
            invoice_items.append(
                CompanyHistoryItem(
                    id=invoice.id,
                    store_type=invoice.store_type_name,
                    date=invoice.date,
                    grand_total=float(invoice.grand_total),
                    notes=invoice.notes,
                    type=CompanyHistoryItemType.INVOICE
                )
            )
            
        # Format the quote results
        quote_items = []
        for quote in quotes:
            quote_items.append(
                CompanyHistoryItem(
                    id=quote.id,
                    store_type=quote.store_type_name,
                    date=quote.date,
                    grand_total=float(quote.grand_total),
                    notes=quote.notes,
                    type=CompanyHistoryItemType.QUOTE
                )
            )
            
        return {
            "invoices": {
                "total": total_invoices,
                "page": page,
                "limit": limit,
                "items": invoice_items
            },
            "quotes": {
                "total": total_quotes,
                "page": page,
                "limit": limit,
                "items": quote_items
            }
        } 