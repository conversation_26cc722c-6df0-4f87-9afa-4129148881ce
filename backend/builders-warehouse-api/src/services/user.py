from fastapi import HTTPException, status, Request
from sqlalchemy.orm import Session
from typing import List, Optional
import logging
import uuid

# ======================================================================
# TEMPORARY FIX: Store type and manager validations have been disabled
# to allow more flexibility in user creation and updates. This is a
# temporary measure and should be reverted once the frontend is updated
# to properly handle these requirements.
#
# Changes:
# 1. Commented out validation requiring store_type_id for Manager and Staff
# 2. Commented out validation requiring manager_id for Staff when created by Admin
# ======================================================================

# ======================================================================
# TEMPORARY FIX: UUID handling has been updated to handle both string and
# UUID objects for IDs. This is a temporary fix and should be refactored
# for a more consistent approach to ID handling.
#
# Changes:
# 1. Added UUID handling for created_by_id, manager_id, user_id, etc.
# 2. Added explicit checks for UUID objects vs string IDs
# ======================================================================

from src.models.user import User, UserRole
from src.models.store_type import StoreType
from src.schemas.user import UserCreate, UserUpdate, ManagerListItem, StoreTypeInfo
from src.utils.auth import get_password_hash
from src.services.auditlog import AuditLogger
from src.controllers.store_type_controller import StoreTypeController

logger = logging.getLogger(__name__)

class UserService:
    """
    Service for user-related operations
    """
    
    @staticmethod
    async def create_user(
        db: Session, 
        user_data: UserCreate, 
        created_by_id: Optional[str] = None,
        request: Optional[Request] = None
    ) -> User:
        """
        Create a new user
        
        Args:
            db: Database session
            user_data: User creation data
            created_by_id: ID of the user creating this user
            request: FastAPI request object (for audit logging)
            
        Returns:
            The created user object
            
        Raises:
            HTTPException: If email is already registered, user_name is already taken,
                          mobile_number is already registered, or store_type_id is invalid
        """
        # Check if email is already registered
        db_user_email = db.query(User).filter(User.email == user_data.email).first()
        if db_user_email:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, 
                detail="Email already registered"
            )
        
        # Check if user_name is already taken
        db_user_name = db.query(User).filter(User.user_name == user_data.user_name).first()
        if db_user_name:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, 
                detail="Username already taken"
            )
            
        # Check if mobile_number is already registered
        db_user_mobile = db.query(User).filter(User.mobile_number == user_data.mobile_number).first()
        if db_user_mobile:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, 
                detail="Mobile number already registered"
            )
        
        # Rule 1 & 2: Admin users must not have a store_type
        if user_data.role == UserRole.ADMIN:
            user_data.store_type_id = None
        # TEMPORARY FIX: Make store_type_id optional for all roles
        # Rule 3: Non-admin users must have a store_type
        # elif user_data.store_type_id is None:
        #     raise HTTPException(
        #         status_code=status.HTTP_400_BAD_REQUEST,
        #         detail="store_type_id is required for non-admin users"
        #     )
        
        # If a store_type_id is provided and the user is not an admin, validate it
        if user_data.store_type_id is not None:
            # Check if store_type_id exists
            store_type = StoreTypeController.get_store_type_by_id(db, user_data.store_type_id)
            if not store_type:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Store type with id {user_data.store_type_id} does not exist"
                )
        
        # TEMPORARY FIX: Make manager_id optional for all roles
        # Rule 4: Staff created by Admin must have a manager
        # if user_data.role == UserRole.STAFF and created_by_id:
        #     creator = db.query(User).filter(User.id == created_by_id).first()
        #     if creator and creator.role == UserRole.ADMIN:
        #         if not user_data.manager_id:
        #             raise HTTPException(
        #                 status_code=status.HTTP_400_BAD_REQUEST,
        #                 detail="manager_id is required when creating a Staff user as an Admin"
        #             )
        
        # Convert manager_id to UUID if provided
        manager_id_uuid = None
        if user_data.manager_id:
            try:
                # Check if manager_id is already a UUID object
                if isinstance(user_data.manager_id, uuid.UUID):
                    manager_id_uuid = user_data.manager_id
                else:
                    manager_id_uuid = uuid.UUID(user_data.manager_id)
            except ValueError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Invalid UUID format for manager_id: {user_data.manager_id}"
                )
                
            # Validate manager_id exists
            manager = db.query(User).filter(User.id == manager_id_uuid).first()
            if not manager:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Manager with id {user_data.manager_id} does not exist"
                )
            if manager.role != UserRole.MANAGER:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"User with id {user_data.manager_id} is not a manager"
                )
        
        # Convert created_by_id to UUID if provided
        created_by_id_uuid = None
        if created_by_id:
            try:
                # Check if created_by_id is already a UUID object
                if isinstance(created_by_id, uuid.UUID):
                    created_by_id_uuid = created_by_id
                else:
                    created_by_id_uuid = uuid.UUID(created_by_id)
            except ValueError:
                logger.warning(f"Invalid UUID for created_by_id: {created_by_id}, using None instead")
        
        # Create new user
        hashed_password = get_password_hash(user_data.password)
        db_user = User(
            user_name=user_data.user_name,
            mobile_number=user_data.mobile_number,
            email=user_data.email, 
            hashed_password=hashed_password,
            role=user_data.role,
            store_type_id=user_data.store_type_id,
            manager_id=manager_id_uuid,
            created_by_id=created_by_id_uuid
        )
        
        # Save to database
        db.add(db_user)
        db.commit()
        db.refresh(db_user)
        
        # Log user creation in audit log
        await AuditLogger.log_create(
            db=db,
            entity_type="users",
            entity_id=db_user.id,
            entity=db_user,
            request=request
        )
        
        logger.info(f"User created: {db_user.user_name}, role: {db_user.role}, store_type_id: {db_user.store_type_id}")
        return db_user
    
    @staticmethod
    def get_users(
        db: Session, 
        skip: int = 0, 
        limit: int = 100,
        search: Optional[str] = None,
        role: Optional[UserRole] = None,
        store_type_id: Optional[int] = None
    ) -> List[User]:
        """
        Get all users with optional pagination, search and filtering
        
        Args:
            db: Database session
            skip: Number of records to skip
            limit: Maximum number of records to return
            search: Optional search term for user_name, email, or mobile_number
            role: Optional filter by user role
            store_type_id: Optional filter by store type ID
            
        Returns:
            List of user objects
        """
        query = db.query(User).filter(User.is_deleted == False)
        
        # Apply search filter if provided
        if search:
            search_term = f"%{search}%"
            query = query.filter(
                (User.user_name.ilike(search_term)) | 
                (User.email.ilike(search_term)) |
                (User.mobile_number.ilike(search_term))
            )
        
        # Apply role filter if provided
        if role:
            query = query.filter(User.role == role)
            
        # Apply store_type_id filter if provided
        if store_type_id:
            query = query.filter(User.store_type_id == store_type_id)
        
        # Order by user_name alphabetically using case-insensitive sort
        # Use func.lower() to ensure proper alphabetical ordering regardless of case
        from sqlalchemy import func
        users = query.order_by(func.lower(User.user_name)).offset(skip).limit(limit).all()
        return users
    
    @staticmethod
    def get_users_count(
        db: Session,
        search: Optional[str] = None,
        role: Optional[UserRole] = None,
        store_type_id: Optional[int] = None
    ) -> int:
        """
        Get total count of users matching filter criteria
        
        Args:
            db: Database session
            search: Optional search term for user_name, email, or mobile_number
            role: Optional filter by user role
            store_type_id: Optional filter by store type ID
            
        Returns:
            Integer count of matching users
        """
        query = db.query(User).filter(User.is_deleted == False)
        
        # Apply search filter if provided
        if search:
            search_term = f"%{search}%"
            query = query.filter(
                (User.user_name.ilike(search_term)) | 
                (User.email.ilike(search_term)) |
                (User.mobile_number.ilike(search_term))
            )
        
        # Apply role filter if provided
        if role:
            query = query.filter(User.role == role)
            
        # Apply store_type_id filter if provided
        if store_type_id:
            query = query.filter(User.store_type_id == store_type_id)
        
        return query.count()
    
    @staticmethod
    def get_managers(db: Session) -> List[ManagerListItem]:
        """
        Get all users with manager role for dropdown
        
        Args:
            db: Database session
            
        Returns:
            List of manager users with id, user_name, and store_type info
        """
        # Use func.lower for case-insensitive ordering
        from sqlalchemy import func
        
        # Update the query to join with store_type table and only return active managers
        # Order by user_name alphabetically using case-insensitive sort
        managers = db.query(User).filter(
            User.role == UserRole.MANAGER,
            User.is_deleted == False,
            User.is_active == True
        ).order_by(func.lower(User.user_name)).all()
        
        result = []
        for manager in managers:
            # Create manager item with store type info
            manager_item = ManagerListItem(
                id=manager.id, 
                user_name=manager.user_name
            )
            
            # Add store_type info if available
            if manager.store_type:
                manager_item.store_type = StoreTypeInfo(
                    id=manager.store_type.id,
                    name=manager.store_type.name
                )
            
            result.append(manager_item)
            
        return result
    
    @staticmethod
    def get_user_by_id(db: Session, user_id: int) -> Optional[User]:
        """
        Get a user by ID
        
        Args:
            db: Database session
            user_id: ID of the user to find
            
        Returns:
            User object if found, None otherwise
        """
        # Convert user_id to UUID if needed
        user_id_uuid = None
        try:
            if isinstance(user_id, uuid.UUID):
                user_id_uuid = user_id
            elif isinstance(user_id, str):
                user_id_uuid = uuid.UUID(user_id)
            else:
                # Handle integer IDs or other types
                user_id_uuid = user_id
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid UUID format for user_id: {user_id}"
            )
            
        user = db.query(User).filter(User.id == user_id_uuid, User.is_deleted == False).first()
        if not user:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="User not found"
            )
        return user
    
    @staticmethod
    def get_user_by_email(db: Session, email: str) -> Optional[User]:
        """
        Get a user by email
        
        Args:
            db: Database session
            email: Email of the user to find
            
        Returns:
            User object if found, None otherwise
        """
        return db.query(User).filter(User.email == email, User.is_deleted == False).first()
    
    @staticmethod
    def get_user_by_username(db: Session, username: str) -> Optional[User]:
        """
        Get a user by username
        
        Args:
            db: Database session
            username: Username of the user to find
            
        Returns:
            User object if found, None otherwise
        """
        return db.query(User).filter(User.user_name == username, User.is_deleted == False).first()
    
    @staticmethod
    def get_user_by_mobile(db: Session, mobile_number: str) -> Optional[User]:
        """
        Get a user by mobile number
        
        Args:
            db: Database session
            mobile_number: Mobile number of the user to find
            
        Returns:
            User object if found, None otherwise
        """
        return db.query(User).filter(User.mobile_number == mobile_number, User.is_deleted == False).first()
    
    @staticmethod
    async def update_user(
        db: Session, 
        user_id: int, 
        user_update: UserUpdate,
        modified_by_id: Optional[int] = None,
        request: Optional[Request] = None
    ) -> User:
        """
        Update a user by ID
        
        Args:
            db: Database session
            user_id: ID of the user to update
            user_update: User update data
            modified_by_id: ID of the user performing the update (for audit log)
            request: FastAPI request object (for audit logging)
            
        Returns:
            Updated user object
            
        Raises:
            HTTPException: If user not found, user_name taken, email already registered,
                          or mobile number already registered
        """
        # Convert user_id to UUID if needed
        user_id_uuid = None
        try:
            if isinstance(user_id, uuid.UUID):
                user_id_uuid = user_id
            elif isinstance(user_id, str):
                user_id_uuid = uuid.UUID(user_id)
            else:
                # Handle integer IDs or other types
                user_id_uuid = user_id
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid UUID format for user_id: {user_id}"
            )
            
        db_user = db.query(User).filter(User.id == user_id_uuid, User.is_deleted == False).first()
        if db_user is None:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, 
                detail="User not found"
            )
        
        # Store original values for audit log
        old_values = {
            "user_name": db_user.user_name,
            "email": db_user.email,
            "mobile_number": db_user.mobile_number,
            "role": db_user.role,
            "is_active": db_user.is_active,
            "store_type_id": db_user.store_type_id,
            "manager_id": db_user.manager_id
        }
        
        # Check if user_name is being updated and if it's already taken
        if user_update.user_name and user_update.user_name != db_user.user_name:
            username_exists = db.query(User).filter(
                User.user_name == user_update.user_name,
                User.id != user_id_uuid,
                User.is_deleted == False
            ).first()
            if username_exists:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST, 
                    detail="Username already taken"
                )
        
        # Check if email is being updated and if it's already registered
        if user_update.email and user_update.email != db_user.email:
            email_exists = db.query(User).filter(
                User.email == user_update.email,
                User.id != user_id_uuid,
                User.is_deleted == False
            ).first()
            if email_exists:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST, 
                    detail="Email already registered"
                )
                
        # Check if mobile_number is being updated and if it's already registered
        if user_update.mobile_number and user_update.mobile_number != db_user.mobile_number:
            mobile_exists = db.query(User).filter(
                User.mobile_number == user_update.mobile_number,
                User.id != user_id_uuid,
                User.is_deleted == False
            ).first()
            if mobile_exists:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST, 
                    detail="Mobile number already registered"
                )
                
        # Role-based validations
        target_role = user_update.role if user_update.role is not None else db_user.role
        
        # Rule 1 & 2: Admin users must not have a store_type
        if target_role == UserRole.ADMIN:
            user_update.store_type_id = None
        # TEMPORARY FIX: Make store_type_id optional for all roles
        # Rule 3: For non-admin users, ensure they have a store_type
        elif user_update.role is not None:  # Only check if role is being changed
            if user_update.store_type_id is None and db_user.store_type_id is None:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="store_type_id is required for non-admin users"
                )
        
        # If store_type_id is being updated and not set to None, validate it
        if user_update.store_type_id is not None:
            # Check if store_type_id exists
            store_type = StoreTypeController.get_store_type_by_id(db, user_update.store_type_id)
            if not store_type:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Store type with id {user_update.store_type_id} does not exist"
                )
        
        # TEMPORARY FIX: Make manager_id optional for all roles
        # Rule 4: Staff users updated by an Admin to Staff role must have a manager
        # if target_role == UserRole.STAFF and modified_by_id:
        #     modifier = db.query(User).filter(User.id == modified_by_id).first()
        #     if modifier and modifier.role == UserRole.ADMIN:
        #         target_manager_id = user_update.manager_id if user_update.manager_id is not None else db_user.manager_id
        #         if target_manager_id is None:
        #             raise HTTPException(
        #                 status_code=status.HTTP_400_BAD_REQUEST,
        #                 detail="manager_id is required for Staff users when updated by an Admin"
        #             )
        
        # Validate manager_id if provided
        if user_update.manager_id and user_update.manager_id != db_user.manager_id:
            try:
                manager_id_uuid = None
                if isinstance(user_update.manager_id, uuid.UUID):
                    manager_id_uuid = user_update.manager_id
                else:
                    manager_id_uuid = uuid.UUID(user_update.manager_id)
                    
                manager = db.query(User).filter(
                    User.id == manager_id_uuid,
                    User.is_deleted == False
                ).first()
                if not manager:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail=f"Manager with id {user_update.manager_id} does not exist"
                    )
                if manager.role != UserRole.MANAGER:
                    raise HTTPException(
                        status_code=status.HTTP_400_BAD_REQUEST,
                        detail=f"User with id {user_update.manager_id} is not a manager"
                    )
                
                # Update the manager_id to be UUID object
                user_update.manager_id = manager_id_uuid
            except ValueError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Invalid UUID format for manager_id: {user_update.manager_id}"
                )
        
        # Update the user fields
        update_data = user_update.model_dump(exclude_unset=True)
        
        # Handle password separately if it's being updated
        if "password" in update_data:
            update_data["hashed_password"] = get_password_hash(update_data.pop("password"))
        
        # Convert modified_by_id to UUID if provided
        modified_by_id_uuid = None
        if modified_by_id:
            try:
                if isinstance(modified_by_id, uuid.UUID):
                    modified_by_id_uuid = modified_by_id
                else:
                    modified_by_id_uuid = uuid.UUID(modified_by_id)
            except ValueError:
                logger.warning(f"Invalid UUID for modified_by_id: {modified_by_id}, using None instead")
        
        # Add modified_by_id to update data
        update_data["modified_by_id"] = modified_by_id_uuid
        
        for key, value in update_data.items():
            setattr(db_user, key, value)
        
        # Save changes to database
        db.commit()
        db.refresh(db_user)
        
        # Prepare new values for audit log
        new_values = {
            "user_name": db_user.user_name,
            "email": db_user.email,
            "mobile_number": db_user.mobile_number,
            "role": db_user.role,
            "is_active": db_user.is_active,
            "store_type_id": db_user.store_type_id,
            "manager_id": db_user.manager_id
        }
        
        # Log user update in audit log
        await AuditLogger.log_update(
            db=db,
            entity_type="users",
            entity_id=db_user.id,
            old_values=old_values,
            new_values=new_values,
            request=request
        )
        
        logger.info(f"User updated: {db_user.user_name}")
        return db_user
    
    @staticmethod
    async def delete_user(
        db: Session, 
        user_id: int,
        deleted_by_id: Optional[int] = None,
        request: Optional[Request] = None
    ) -> None:
        """
        Soft-delete a user by ID
        
        Args:
            db: Database session
            user_id: ID of the user to delete
            deleted_by_id: ID of the user performing the delete (for audit log)
            request: FastAPI request object (for audit logging)
            
        Raises:
            HTTPException: If user not found or attempting to delete own account
        """
        # Convert user_id to UUID if needed
        user_id_uuid = None
        try:
            if isinstance(user_id, uuid.UUID):
                user_id_uuid = user_id
            elif isinstance(user_id, str):
                user_id_uuid = uuid.UUID(user_id)
            else:
                # Handle integer IDs or other types
                user_id_uuid = user_id
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid UUID format for user_id: {user_id}"
            )
            
        # Convert deleted_by_id to UUID if provided
        deleted_by_id_uuid = None
        if deleted_by_id:
            try:
                if isinstance(deleted_by_id, uuid.UUID):
                    deleted_by_id_uuid = deleted_by_id
                else:
                    deleted_by_id_uuid = uuid.UUID(deleted_by_id)
            except ValueError:
                logger.warning(f"Invalid UUID for deleted_by_id: {deleted_by_id}, using None instead")
            
        db_user = db.query(User).filter(User.id == user_id_uuid, User.is_deleted == False).first()
        if db_user is None:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, 
                detail="User not found"
            )
        
        # Prevent self-deletion
        if deleted_by_id_uuid and db_user.id == deleted_by_id_uuid:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST, 
                detail="Cannot delete your own account"
            )
        
        # Soft-delete the user by setting is_deleted flag
        db_user.is_deleted = True
        db_user.is_active = False
        db_user.modified_by_id = deleted_by_id_uuid
        
        # Save changes to database
        db.commit()
        
        # Log user deletion in audit log
        await AuditLogger.log_delete(
            db=db,
            entity_type="users",
            entity_id=db_user.id,
            request=request
        )
        
        logger.info(f"User deleted: {db_user.user_name}")
        return None 