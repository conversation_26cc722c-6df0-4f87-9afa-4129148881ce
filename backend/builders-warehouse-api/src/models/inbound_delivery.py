from sqlalchemy import Column, Integer, String, DateTime, JSON, Boolean
from datetime import datetime
from src.database import Base

class InboundDelivery(Base):
    """Inbound Delivery model for tracking purchase order deliveries"""
    
    __tablename__ = "InboundDelivery"
    __table_args__ = {'extend_existing': True}
    
    id = Column(Integer, primary_key=True, index=True)
    delivery_number = Column(String(50), nullable=True)
    supplier_name = Column(String(255), index=True, nullable=False)
    delivery_date = Column(DateTime, nullable=False)
    items = Column(JSON, nullable=True)
    status = Column(String(50), default="pending", nullable=False)  # pending, partial, completed
    ship_to_customer = Column(Boolean, default=False, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    def calculate_status(self):
        """Calculate the delivery status based on quantities"""
        if not self.items or not self.items.get('received_items', []):
            return "pending"
        elif any(item.get('balance', 0) > 0 for item in self.items.get('items', [])):
            return "partial"
        else:
            return "completed" 