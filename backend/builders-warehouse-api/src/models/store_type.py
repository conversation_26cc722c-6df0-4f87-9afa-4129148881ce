from sqlalchemy import Column, Integer, String, DateTime, Boolean
from datetime import datetime
from sqlalchemy.orm import relationship

from src.database import Base

class StoreType(Base):
    """Model for store types"""
    
    __tablename__ = "StoreType"
    __table_args__ = {'extend_existing': True}
    
    id = Column(Integer, primary_key=True, autoincrement=True, index=True)
    name = Column(String, nullable=False, unique=True, index=True)
    description = Column(String, nullable=True)
    is_active = Column(Boolean, default=True)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Add the relationship back to User model
    users = relationship("User", back_populates="store_type")
    
    def __repr__(self):
        return f"<StoreType {self.name}>" 