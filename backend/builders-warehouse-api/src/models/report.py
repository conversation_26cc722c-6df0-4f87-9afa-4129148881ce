from sqlalchemy import Column, Integer, String, DateTime, Numeric, Text, Boolean, ForeignKey, Index, Date
from sqlalchemy.orm import relationship
from datetime import datetime
from sqlalchemy.dialects.postgresql import UUID, JSONB
import uuid
from src.database import Base

class SalesReport(Base):
    """Sales Report model for Last 30 Sales reports"""
    
    __tablename__ = "SalesReport"
    __table_args__ = {'extend_existing': True}
    
    id = Column(Integer, primary_key=True, index=True)
    date = Column(Date, nullable=False)
    invoice_number = Column(String, nullable=False, index=True)  # Used as ID
    company_name = Column(String, nullable=False, index=True)  # Used as ID
    total_amount = Column(Numeric(10, 2), default=0.0, nullable=False)
    mode_of_payment = Column(String, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)

class POReport(Base):
    """Purchase Order Report model for Last 30 POs reports"""
    
    __tablename__ = "POReport"
    __table_args__ = {'extend_existing': True}
    
    id = Column(Integer, primary_key=True, index=True)
    date = Column(Date, nullable=False)
    po_number = Column(String, nullable=False, index=True)  # Used as ID
    supplier_name = Column(String, nullable=False, index=True)  # Used as ID
    linked_invoice_number = Column(String, nullable=True, index=True)  # Used as ID if applicable
    total_amount = Column(Numeric(10, 2), default=0.0, nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False) 