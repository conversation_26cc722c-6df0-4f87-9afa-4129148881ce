from sqlalchemy import Column, String, Date, DateTime, Text
from datetime import datetime
from src.database import Base

class OutboundDelivery(Base):
    __tablename__ = "outbound_deliveries"
    __table_args__ = {'extend_existing': True}
    

    invoice_no = Column(String(50), primary_key=True, index=True)
    invoice_date = Column(Date, nullable=False)
    linked_po = Column(String(50), nullable=False)
    sku = Column(String(100), nullable=False)
    description = Column(String(255), nullable=False)
    status = Column(String(100), nullable=False, default="New")
    # notes = Column(Text, nullable=True)  # Temporarily commented out until database migration is applied

class DeliveredOrder(Base):
    __tablename__ = "delivered_orders"
    __table_args__ = {'extend_existing': True}

    invoice_no = Column(String(50), primary_key=True, index=True)
    invoice_date = Column(Date, nullable=False)
    linked_po = Column(String(50), nullable=False)
    sku = Column(String(100), nullable=False)
    description = Column(String(255), nullable=False)
    delivered_date = Column(DateTime, nullable=False, default=datetime.utcnow)
    # notes = Column(Text, nullable=True)  # Temporarily commented out until database migration is applied 