import uuid
from sqlalchemy import Column, String, DateTime, Boolean, Text, ForeignKey, Numeric, Date, Float, Enum as SQLAlchemyEnum, Integer, Table, MetaData
from sqlalchemy.dialects.postgresql import UUID, JSONB
from sqlalchemy.orm import relationship, mapper
from datetime import datetime, date
from src.database import Base
import enum
from decimal import Decimal

class SaleType(str, enum.Enum):
    """Type of sale for invoices"""
    TRADE = "trade"
    RETAIL = "retail"
    STORE = "store"

class PaymentMode(str, enum.Enum):
    """Payment modes for invoices"""
    CASH = "cash"
    BANK_TRANSFER = "bank_transfer"
    CREDIT_CARD = "credit_card"
    CHEQUE = "cheque"

class Invoice(Base):
    """Enhanced Invoice model with additional fields"""
    
    __tablename__ = "Invoice"
    __table_args__ = {'extend_existing': True}
    
    # Base fields
    id = Column(Integer, primary_key=True, autoincrement=True, index=True)
    invoice_number = Column(String, nullable=False, unique=True, index=True)
    company_id = Column(Integer, ForeignKey("Company.id"), nullable=False)
    customer_id = Column(Integer, ForeignKey("Customer.id"), nullable=False)
    
    # Add link to quote for converted quotes
    linked_quote_id = Column(String, nullable=True, index=True)
    
    # Add PO reference
    po_id = Column(Integer, ForeignKey("PurchaseOrder.id"), nullable=True)
    
    # Date fields
    invoice_date = Column(DateTime, nullable=False)
    due_date = Column(DateTime, nullable=False)
    
    # Status and type
    status = Column(String, nullable=False, default="draft")
    invoice_type = Column(String, nullable=True)
    
    # Store type and payment information
    store_type_id = Column(Integer, ForeignKey("StoreType.id"), nullable=True)
    store_type_name = Column(String, nullable=True)
    mode_of_payment = Column(SQLAlchemyEnum(PaymentMode), nullable=True, default=PaymentMode.CASH)
    
    # Purchase order information
    po_number = Column(String, nullable=True)
    dont_send_po = Column(Boolean, nullable=False, default=False)
    
    # Financial fields
    total_order = Column(Float, nullable=False, default=0.0)
    credit_card_surcharge = Column(Float, nullable=False, default=0.0)
    shipping = Column(Float, nullable=False, default=0.0)
    grand_total = Column(Float, nullable=False, default=0.0)
    total_gst = Column(Float, nullable=False, default=0.0)
    notes = Column(Text, nullable=True)
    
    # Store items as JSON
    items = Column(JSONB, nullable=False, default={})
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    # Relationships
    customer = relationship("Customer", back_populates="invoices", primaryjoin="Invoice.customer_id == Customer.id")
    company = relationship("Company", back_populates="invoices", primaryjoin="Invoice.company_id == Company.id")
    purchase_order = relationship("PurchaseOrder", backref="invoices")
    store_type = relationship("StoreType", foreign_keys=[store_type_id])
    
    # Add property for date to make it compatible with existing code
    @property
    def date(self):
        """Alias for invoice_date to maintain compatibility with existing code"""
        return self.invoice_date
    
    # Add property for invoice_no to make it compatible with existing code
    @property
    def invoice_no(self):
        """Alias for invoice_number to maintain compatibility with existing code"""
        return self.invoice_number
    
    # Add property for store_type to maintain backward compatibility
    @property
    def store_type(self):
        """Alias for invoice_type to maintain compatibility with existing code"""
        return self.invoice_type
    
    # Add property for purchase_order_number 
    @property
    def purchase_order_number(self):
        """Return the purchase order number if available"""
        if hasattr(self, 'purchase_order') and self.purchase_order:
            return self.purchase_order.po_number
        return None
    
    def calculate_totals(self):
        """Calculate all financial totals based on items and settings"""
        # Calculate from items JSON
        total = 0.0
        if self.items and isinstance(self.items, dict) and 'items' in self.items:
            for item in self.items['items']:
                if 'totalPrice' in item:
                    total += float(item['totalPrice'])
        
        self.grand_total = total
        self.total_gst = total * 0.1  # 10% GST
        
        return self.grand_total

    @property
    def payment_status(self):
        return "Outstanding"

class InvoiceItem(Base):
    """Model for items included in an invoice"""
    
    __tablename__ = "InvoiceItem"
    __table_args__ = {'extend_existing': True}
    
    id = Column(Integer, primary_key=True, autoincrement=True, index=True)
    sku = Column(String(50), nullable=False)
    description = Column(Text, nullable=False)
    units = Column(Integer, default=0)
    boxes = Column(Integer, default=0)
    pieces = Column(Integer, default=0)
    m2 = Column(Numeric(10, 2), default=0.0)
    unit_price = Column(Numeric(10, 2), nullable=False)
    total_price = Column(Numeric(10, 2), nullable=False)
    
    # Foreign key
    invoice_id = Column(Integer, ForeignKey("Invoice.id"), nullable=False)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    invoice = relationship("Invoice")
    
    def calculate_total_price(self):
        """Calculate total price based on quantities and unit price"""
        # Convert all values to Decimal to ensure consistent types
        units = Decimal(self.units or 0)
        boxes = Decimal(self.boxes or 0)
        pieces = Decimal(self.pieces or 0)
        m2 = self.m2 or Decimal('0')
        
        # Calculate total quantity as Decimal
        total_quantity = units + boxes + pieces + m2
        
        # Ensure unit_price is Decimal
        unit_price = self.unit_price or Decimal('0')
        
        # Calculate and store total price
        self.total_price = unit_price * total_quantity
        
        return self.total_price

# Register event listeners to automatically calculate totals
from sqlalchemy import event

@event.listens_for(InvoiceItem, 'before_insert')
@event.listens_for(InvoiceItem, 'before_update')
def invoice_item_before_save(mapper, connection, target):
    """Calculate total price before saving"""
    target.calculate_total_price()

@event.listens_for(InvoiceItem, 'after_insert')
@event.listens_for(InvoiceItem, 'after_update')
@event.listens_for(InvoiceItem, 'after_delete')
def invoice_item_after_change(mapper, connection, target):
    """Update invoice totals after item changes"""
    from sqlalchemy.orm import Session
    session = Session.object_session(target)
    if session is not None and target.invoice_id is not None:
        invoice = session.query(Invoice).filter_by(id=target.invoice_id).first()
        if invoice:
            invoice.calculate_totals() 