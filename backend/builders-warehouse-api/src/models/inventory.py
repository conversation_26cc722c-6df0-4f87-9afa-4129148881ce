from sqlalchemy import Column, Integer, String, Float, ForeignKey, DateTime, Text
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID
import uuid

from src.database import Base

class Inventory(Base):
    """
    SQLAlchemy model for inventory items
    """
    __tablename__ = "Inventory"
    __table_args__ = {'extend_existing': True}

    id = Column(Integer, primary_key=True, index=True)
    sku_code = Column(String, unique=True, index=True, nullable=False)
    style_code = Column(String, index=True, nullable=False)
    supplier_id = Column(Integer, ForeignKey("Supplier.id", ondelete="SET NULL"), nullable=True, index=True)
    carton = Column(Integer, default=1)
    units_per_carton = Column(Integer, nullable=False)
    carton_dimensions = Column(String, nullable=True)
    weight_per_unit = Column(Float, nullable=False)
    weight_per_carton = Column(Float, nullable=False)
    units_per_pallet = Column(Integer, nullable=True)
    pallet_weight = Column(Float, nullable=True)
    notes = Column(Text, nullable=True)
    image_path = Column(String, nullable=True)  # Store relative path to SKU image
    
    # Audit fields
    created_at = Column(DateTime(timezone=True), default=func.now())
    updated_at = Column(DateTime(timezone=True), default=func.now(), onupdate=func.now())
    created_by = Column(UUID(as_uuid=True), ForeignKey("User.id", ondelete="SET NULL"), nullable=True)
    updated_by = Column(UUID(as_uuid=True), ForeignKey("User.id", ondelete="SET NULL"), nullable=True)
    
    # Relationships
    creator = relationship("User", foreign_keys=[created_by], lazy="joined", primaryjoin="Inventory.created_by == User.id")
    updater = relationship("User", foreign_keys=[updated_by], lazy="joined", primaryjoin="Inventory.updated_by == User.id")
    supplier = relationship("Supplier", foreign_keys=[supplier_id], lazy="joined") 