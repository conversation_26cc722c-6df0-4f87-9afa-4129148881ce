from sqlalchemy import Column, Integer, String, DateTime, Numeric, Text, Boolean, ForeignKey, Index, func
from sqlalchemy.orm import relationship
from datetime import datetime
from sqlalchemy.dialects.postgresql import UUID, JSONB
import uuid
from src.database import Base

class Quote(Base):
    """Quote model for quote management system"""
    
    __tablename__ = "Quote"
    
    id = Column(Integer, primary_key=True, autoincrement=True, index=True)
    quote_number = Column(String, nullable=False, unique=True, index=True)
    company_id = Column(Integer, ForeignKey("Company.id"), nullable=False)
    customer_id = Column(Integer, ForeignKey("Customer.id"), nullable=False)
    
    # Match database column names
    quote_date = Column(DateTime, nullable=False)
    valid_until = Column(DateTime, nullable=False)
    status = Column(String, nullable=False, default="draft")
    store_type = Column(String, nullable=True)
    grand_total = Column(Numeric(10, 2), default=0.0, nullable=False)
    total_gst = Column(Numeric(10, 2), default=0.0, nullable=False)
    notes = Column(Text, nullable=True)
    deliver_to_address = Column(Text, nullable=True)
    items = Column(JSONB, nullable=False, default={})
    
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)

    # Relationships
    customer = relationship("Customer", back_populates="quotes", primaryjoin="Quote.customer_id == Customer.id")
    company = relationship("Company", back_populates="quotes", primaryjoin="Quote.company_id == Company.id")
    
    # Create an index on quote_date and set extend_existing
    __table_args__ = (
        Index("ix_quotes_date", quote_date),
        {'extend_existing': True}
    )
    
    def update_grand_total(self):
        """Update the grand total based on quote items"""
        # Calculate from items JSON
        total = 0.0
        if self.items and isinstance(self.items, dict) and 'items' in self.items:
            for item in self.items['items']:
                if 'totalPrice' in item:
                    total += float(item['totalPrice'])
        self.grand_total = total
        self.total_gst = total * 0.15  # Assuming 15% GST
        return self.grand_total 