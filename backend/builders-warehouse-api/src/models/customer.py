import uuid
from sqlalchemy import Column, String, DateTime, <PERSON>olean, Text, ForeignKey, Numeric, Date, Integer
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from sqlalchemy.schema import Column, ForeignKey
from sqlalchemy.types import Integer, String, Text, Numeric, Date, DateTime
from src.database import Base
import datetime


class PriceList(Base):
    """Price List model for different customer pricing tiers"""
    
    __tablename__ = "PriceList"
    __table_args__ = {'extend_existing': True}
    
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String(255), nullable=False, unique=True)
    currency = Column(String(3), default="AUD")
    created_at = Column(DateTime, default=datetime.datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow)
    
    # Relationships
    customers = relationship("Customer", back_populates="price_list")


class Customer(Base):
    """Customer model for the Builder's Warehouse system"""
    
    __tablename__ = "Customer"
    __table_args__ = {'extend_existing': True}
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    company_name = Column(String, nullable=False, unique=True)
    contact_person = Column(String, nullable=True)
    email = Column(String, nullable=True, unique=True)
    phone = Column(String, nullable=True)
    is_account = Column(Boolean, default=False, nullable=False)
    billing_address = Column(Text, nullable=True)
    billing_suburb = Column(String, nullable=True)
    billing_postcode = Column(String, nullable=True)
    
    # Foreign keys
    price_list_id = Column(UUID(as_uuid=True), ForeignKey("PriceList.id"), nullable=True)
    
    created_at = Column(DateTime, default=datetime.datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.datetime.utcnow, onupdate=datetime.datetime.utcnow, nullable=False)
    
    # Relationships
    price_list = relationship("PriceList", back_populates="customers")
    invoices = relationship("Invoice", back_populates="customer")
    quotes = relationship("Quote", back_populates="customer") 