from sqlalchemy import Column, String, DateTime, Foreign<PERSON><PERSON>, Boolean
from sqlalchemy.sql import func
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
import uuid
from datetime import datetime, timedelta, timezone

from src.database import Base

class PasswordResetToken(Base):
    """
    SQLAlchemy model for password reset tokens
    """
    __tablename__ = "PasswordResetToken"
    __table_args__ = {'extend_existing': True}

    id = Column(UUID(as_uuid=True), primary_key=True, index=True, default=uuid.uuid4)
    token = Column(String, unique=True, index=True, nullable=False)
    user_id = Column(UUID(as_uuid=True), ForeignKey("User.id"), nullable=False)
    is_used = Column(Boolean, default=False)
    expires_at = Column(DateTime(timezone=True), nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    
    # Relationship
    user = relationship("User", back_populates="password_reset_tokens")
    
    def is_expired(self) -> bool:
        """Check if the token has expired"""
        now = datetime.now(timezone.utc)
        # Convert expires_at to UTC if it has timezone info, otherwise assume UTC
        if self.expires_at.tzinfo is not None:
            expires_utc = self.expires_at.astimezone(timezone.utc)
        else:
            expires_utc = self.expires_at.replace(tzinfo=timezone.utc)
        return now > expires_utc
    
    def is_valid(self) -> bool:
        """Check if the token is valid (not used and not expired)"""
        return not self.is_used and not self.is_expired() 