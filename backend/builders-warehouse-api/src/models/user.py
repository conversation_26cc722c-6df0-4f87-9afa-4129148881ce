from sqlalchemy import <PERSON>um<PERSON>, Inte<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, DateT<PERSON>, ForeignKey, Enum
from sqlalchemy.sql import func
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
import uuid
import enum

from src.database import Base

class UserRole(str, enum.Enum):
    ADMIN = "admin"
    MANAGER = "manager"
    STAFF = "staff"

class User(Base):
    """
    SQLAlchemy model for users in the system
    """
    __tablename__ = "User"
    __table_args__ = {'extend_existing': True}

    id = Column(UUID(as_uuid=True), primary_key=True, index=True, default=uuid.uuid4)
    user_name = Column(String, unique=True, index=True)
    mobile_number = Column(String, unique=True, index=True, nullable=True)
    email = Column(String, unique=True, index=True)
    hashed_password = Column(String)
    is_active = Column(Boolean, default=True)
    is_deleted = Column(Boolean, default=False)
    
    # Role fields
    role = Column(String, nullable=True)  # Keep for backward compatibility
    role_id = Column(Inte<PERSON>, ForeignKey("Role.id"), nullable=True)  # Changed to match database table name
    role_relation = relationship("Role", back_populates="users", foreign_keys=[role_id])
    
    # Fix the table name to match the actual database table "StoreType" instead of "store_types"
    store_type_id = Column(Integer, ForeignKey("StoreType.id"), nullable=True)
    
    # Self-referencing relationship for manager
    manager_id = Column(UUID(as_uuid=True), ForeignKey("User.id"), nullable=True)  # Changed to match database table name
    manager = relationship("User", remote_side=[id], backref="team_members", foreign_keys=[manager_id])
    
    # Audit fields
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    created_by_id = Column(UUID(as_uuid=True), ForeignKey("User.id"), nullable=True)  # Changed to match database table name
    modified_by_id = Column(UUID(as_uuid=True), ForeignKey("User.id"), nullable=True)  # Changed to match database table name
    
    # Relationships
    store_type = relationship("StoreType", back_populates="users", foreign_keys=[store_type_id])
    created_by = relationship("User", foreign_keys=[created_by_id], remote_side=[id])
    modified_by = relationship("User", foreign_keys=[modified_by_id], remote_side=[id]) 
    password_reset_tokens = relationship("PasswordResetToken", back_populates="user", cascade="all, delete-orphan") 