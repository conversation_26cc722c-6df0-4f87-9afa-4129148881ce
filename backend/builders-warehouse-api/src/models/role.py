from sqlalchemy import Column, Integer, String, DateTime
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID
import uuid

from src.database import Base

class Role(Base):
    """
    SQLAlchemy model for user roles in the system
    """
    __tablename__ = "Role"
    __table_args__ = {'extend_existing': True}

    # Use Integer for compatibility with existing database
    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, unique=True, index=True, nullable=False)
    
    # Audit fields
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())
    
    # Relationship with users
    users = relationship("User", back_populates="role_relation", foreign_keys="[User.role_id]") 