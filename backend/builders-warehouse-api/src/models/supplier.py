from sqlalchemy import Column, <PERSON>te<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, DateTime, JSON, Text
from sqlalchemy.sql import func
from sqlalchemy.orm import column_property

from src.database import Base

class Supplier(Base):
    """
    SQLAlchemy model for suppliers
    """
    __tablename__ = "Supplier"
    __table_args__ = {'extend_existing': True}

    id = Column(Integer, primary_key=True, index=True)
    name = Column(Text, nullable=False)
    phone = Column(Text, nullable=True)
    email = Column(Text, nullable=True)
    address = Column(Text, nullable=True)
    
    # Keep price_list field to store uploaded price list data
    price_list = Column(JSON, nullable=True)
    
    # Active status field
    is_active = Column(Boolean, nullable=False, default=True)
    
    # Audit fields
    created_at = Column(DateTime, nullable=False, default=func.now())
    updated_at = Column(DateTime, nullable=False, default=func.now(), onupdate=func.now()) 
    
    # Virtual property for supplier_name (compatibility with frontend)
    @property
    def supplier_name(self):
        return self.name
        
    @supplier_name.setter
    def supplier_name(self, value):
        self.name = value
        
    # Virtual property for phone_no (compatibility with frontend)
    @property
    def phone_no(self):
        return self.phone
        
    @phone_no.setter
    def phone_no(self, value):
        self.phone = value 