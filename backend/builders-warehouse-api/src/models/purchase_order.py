from sqlalchemy import Column, Integer, String, DateTime, Numeric, Text, Boolean, ForeignKey, Index, func, event
from sqlalchemy.orm import relationship
from datetime import datetime
from sqlalchemy.dialects.postgresql import UUID, JSONB
import uuid
from src.database import Base

class PurchaseOrder(Base):
    """Purchase Order model for tracking vendor orders"""
    
    __tablename__ = "PurchaseOrder"
    __table_args__ = {'extend_existing': True}
    
    id = Column(Integer, primary_key=True, index=True)
    po_number = Column(String(50), unique=True, index=True, nullable=False)
    company_id = Column(Integer, ForeignKey("Company.id"), nullable=False)
    supplier_id = Column(Integer, ForeignKey("Supplier.id"), nullable=False)
    order_date = Column(DateTime, nullable=False)
    expected_date = Column(DateTime, nullable=True)
    items = Column(JSONB, nullable=True)
    total_amount = Column(Numeric(10, 2), default=0.0, nullable=False)
    status = Column(String(50), default="ordered", nullable=False)  # ordered, partial, delivered, cancelled
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    def calculate_status(self):
        """Calculate the order status based on quantities"""
        if not self.items or not self.items.get('items', []):
            return "ordered"
        elif any(
            item.get('quantity_received', 0) < item.get('quantity', 0) 
            for item in self.items.get('items', [])
        ):
            return "partial"
        else:
            return "delivered"

    # Add ship_to_customer as a property that uses the items JSON field
    @property
    def ship_to_customer(self):
        """Get ship_to_customer value from metadata in items JSON"""
        if self.items and isinstance(self.items, dict) and 'metadata' in self.items:
            return self.items['metadata'].get('ship_to_customer', False)
        return False
    
    @ship_to_customer.setter
    def ship_to_customer(self, value):
        """Store ship_to_customer in metadata in items JSON"""
        if not self.items:
            self.items = {}
        
        if 'metadata' not in self.items:
            self.items['metadata'] = {}
            
        self.items['metadata']['ship_to_customer'] = bool(value)

    # Relationships
    company = relationship("Company", backref="purchase_orders")
    supplier = relationship("Supplier", backref="purchase_orders")
    
    # Compatibility properties
    @property
    def issue_date(self):
        """Alias for order_date for compatibility"""
        return self.order_date.date() if self.order_date else None
    
    @property
    def date_issued(self):
        """Alias for order_date for compatibility"""
        return self.order_date.date() if self.order_date else None
    
    @property
    def is_deleted(self):
        """For compatibility - we'll use status="cancelled" to represent deleted"""
        return self.status == "cancelled"
    
    @is_deleted.setter
    def is_deleted(self, value):
        """Setter for is_deleted property"""
        if value:
            self.status = "cancelled"
    
    # Properties for fields stored in JSON due to database schema limitations
    @property
    def store_type(self):
        """Get store_type from items JSON field"""
        if self.items and isinstance(self.items, dict):
            if 'metadata' in self.items:
                return self.items['metadata'].get('store_type')
            return self.items.get('store_type')
        return None
    
    @property
    def invoice_id(self):
        """Get invoice_id from items JSON field"""
        if self.items and isinstance(self.items, dict):
            if 'metadata' in self.items:
                return self.items['metadata'].get('invoice_id')
            return self.items.get('invoice_id')
        return None
    
    @property
    def payment_terms(self):
        """Get payment_terms from items JSON field"""
        if self.items and isinstance(self.items, dict):
            if 'metadata' in self.items:
                return self.items['metadata'].get('payment_terms')
            return self.items.get('payment_terms')
        return None
    
    def update_total(self):
        """Update the total based on items"""
        total = 0
        if self.items and isinstance(self.items, dict) and 'items' in self.items:
            for item in self.items['items']:
                total += float(item.get('total', 0))
        self.total_amount = total
        return total

    # Define details as a property to maintain compatibility with existing code
    @property
    def details(self):
        """Get details from items JSON field"""
        if self.items and isinstance(self.items, dict) and 'items' in self.items:
            # Make sure each item has an id field
            # If id doesn't exist, use index as a string id
            result = []
            for i, item in enumerate(self.items['items']):
                if 'id' not in item:
                    # Create a copy so we don't modify the original
                    item_copy = dict(item)
                    item_copy['id'] = str(i + 1)  # 1-based ID
                    result.append(item_copy)
                else:
                    result.append(item)
            return result
        return []

    @property
    def email_sent(self):
        """Get email_sent from items JSON field"""
        if self.items and isinstance(self.items, dict):
            return self.items.get('email_sent', False)
        return False

    @property
    def email_sent_message(self):
        """Get email_sent_message from items JSON field"""
        if self.items and isinstance(self.items, dict):
            return self.items.get('email_sent_message', None)
        return None

# Define a custom class for handling purchase order details
class PurchaseOrderDetailItem:
    """Helper class for working with purchase order detail items stored in JSON"""
    
    def __init__(self, **kwargs):
        for key, value in kwargs.items():
            setattr(self, key, value)
    
    @property
    def id(self):
        return getattr(self, '_id', None)
    
    @property
    def purchase_order_id(self):
        return getattr(self, 'purchase_order_id', None)
    
    @property
    def sku(self):
        return getattr(self, 'sku', None)
    
    @property
    def description(self):
        return getattr(self, 'description', None)
    
    @property
    def quantity_ordered(self):
        return getattr(self, 'quantity_ordered', 0)
    
    @property
    def quantity_received(self):
        return getattr(self, 'quantity_received', 0)
    
    @property
    def expected_delivery_date(self):
        return getattr(self, 'expected_delivery_date', None)
    
    @property
    def total(self):
        return getattr(self, 'total', 0)
    
    @property
    def notes(self):
        return getattr(self, 'notes', None)

# Update __init__.py to include these models in imports 