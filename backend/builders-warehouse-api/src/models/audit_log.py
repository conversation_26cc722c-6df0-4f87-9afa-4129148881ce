from sqlalchemy import Column, Integer, String, DateTime, Text, ForeignKey
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import JSONB, UUID
import uuid

from src.database import Base

class AuditLog(Base):
    """
    SQLAlchemy model for audit logs that track database changes
    """
    __tablename__ = "AuditLog"
    __table_args__ = {'extend_existing': True}

    id = Column(Integer, primary_key=True, index=True)
    timestamp = Column(DateTime(timezone=True), default=func.now(), nullable=False)
    event_type = Column(String, index=True)  # LOGIN, CREATE, UPDATE, DELETE, etc.
    email = Column(String, index=True)  # Email of the user
    status = Column(String, index=True)  # success, failure, etc.
    ip_address = Column(String)
    additional_info = Column(JSONB)  # JSON with additional details
    
    # For tracking operations on specific entities
    table_name = Column(String, index=True)
    operation = Column(String, index=True)  # CREATE, UPDATE, DELETE
    entity_id = Column(UUID(as_uuid=True))
    user_id = Column(UUID(as_uuid=True))  # UUID of the user performing the action
    
    # No foreign key relationship since we're using email instead of user_id 