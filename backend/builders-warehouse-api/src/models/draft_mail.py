from sqlalchemy import Column, Integer, String, DateTime, Text, ForeignKey, Boolean
from sqlalchemy.orm import relationship
from datetime import datetime
from sqlalchemy.dialects.postgresql import UUID
import uuid

from src.database import Base

class DraftMail(Base):
    """Model for draft email messages for purchase orders"""
    
    __tablename__ = "DraftMail"
    __table_args__ = {'extend_existing': True}
    
    id = Column(Integer, primary_key=True, index=True)
    invoice_id = Column(Integer, ForeignKey("Invoice.id"), nullable=False)
    po_id = Column(Integer, ForeignKey("PurchaseOrder.id"), nullable=False)
    subject = Column(String, nullable=False)
    body = Column(Text, nullable=False)
    ready_to_send = Column(Boolean, default=False)
    sent = Column(Boolean, default=False)
    sent_at = Column(DateTime, nullable=True)
    created_by = Column(UUID(as_uuid=True), Foreign<PERSON>ey("User.id"), nullable=False)
    created_at = Column(DateTime, default=datetime.utcnow, nullable=False)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow, nullable=False)
    
    # Relationships
    invoice = relationship("Invoice", backref="draft_mails")
    purchase_order = relationship("PurchaseOrder", backref="draft_mails")
    user = relationship("User", backref="draft_mails") 