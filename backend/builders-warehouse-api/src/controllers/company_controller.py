from fastapi import Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import Dict

from src.database import get_db
from src.services.company import CompanyService


class CompanyController:
    """Controller for company operations"""
    
    @staticmethod
    def get_company_history(
        company_id: str,
        page: int = 1,
        limit: int = 10,
        db: Session = Depends(get_db)
    ) -> Dict:
        """
        Get history of invoices and quotes for a specific company
        
        Args:
            company_id: UUID of company
            page: Page number
            limit: Number of items per page
            db: Database session
            
        Returns:
            Dictionary with invoices and quotes pagination data
        """
        try:
            return CompanyService.get_company_history(
                db=db,
                company_id=company_id,
                page=page,
                limit=limit
            )
        except Exception as e:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Error retrieving company history: {str(e)}"
            ) 