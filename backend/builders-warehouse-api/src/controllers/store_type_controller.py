from sqlalchemy.orm import Session
from typing import List, Optional

from src.models.store_type import StoreType

class StoreTypeController:
    """
    Controller for store type-related API endpoints
    Handles the request/response cycle and delegates business logic to services
    """
    
    @staticmethod
    def get_store_types(db: Session) -> List[StoreType]:
        """Get all store types"""
        return db.query(StoreType).all()
    
    @staticmethod
    def get_store_type_by_id(db: Session, store_type_id: int) -> Optional[StoreType]:
        """Get a store type by ID"""
        return db.query(StoreType).filter(StoreType.id == store_type_id).first()
    
    @staticmethod
    def create_store_type(db: Session, name: str) -> StoreType:
        """Create a new store type"""
        store_type = StoreType(name=name)
        db.add(store_type)
        db.commit()
        db.refresh(store_type)
        return store_type 