from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
from fastapi import Request, HTTPException, status
from datetime import date, datetime
import uuid

from src.models.quote import Quote
from src.schemas.quote import QuoteCreate, QuoteUpdate, QuoteWithItems, QuotePagination, QuoteResponse, StoreTypeInfo, CompanyInfo, QuoteNumberInfo
from src.services.quote import QuoteService
from src.models.customer import Customer

class QuoteController:
    """
    Controller for quote-related API endpoints
    Handles the request/response cycle and delegates business logic to services
    """
    
    @staticmethod
    def _is_quote_archived(quote) -> bool:
        """Determine if a quote is archived based on date (older than 30 days)"""
        from datetime import datetime, timedelta
        thirty_days_ago = datetime.now().date() - timedelta(days=30)
        quote_date = quote.quote_date.date() if hasattr(quote.quote_date, 'date') else quote.quote_date
        return quote_date < thirty_days_ago and quote.status not in ["deleted", "converted"]
    
    @staticmethod
    def _convert_quote_model_to_schema(db_quote: Quote, db: Session = None) -> Dict[str, Any]:
        """Manually convert a Quote model to a dictionary compatible with the schema
        This is a temporary solution to handle the validation errors"""
        # Extract store_type_id from store_type name if possible
        store_type_id = None
        if db_quote.store_type:
            try:
                # Try to extract store_type_id if it's a number
                if db_quote.store_type.isdigit():
                    store_type_id = int(db_quote.store_type)
            except (ValueError, AttributeError):
                pass
                
        # Create store type info
        store_type_info = {"id": store_type_id, "name": db_quote.store_type or ""}
        
        # Create company info
        company_info = {"id": None, "name": ""}
        
        # Create quote number info
        quote_number_info = {"id": None, "formatted_number": db_quote.quote_number}
        
        # Process items to ensure they're in the correct format
        items_list = []
        if db_quote.items:
            if isinstance(db_quote.items, dict) and 'items' in db_quote.items:
                items_list = db_quote.items['items']
            elif isinstance(db_quote.items, list):
                items_list = db_quote.items
        
        # Try to fetch customer info if not loaded with relationship
        customer_name = None
        if hasattr(db_quote, 'customer') and db_quote.customer:
            if hasattr(db_quote.customer, 'company_name'):
                customer_name = db_quote.customer.company_name
        elif db and db_quote.customer_id:
            # If db session provided and customer_id exists but relationship not loaded
            try:
                customer = db.query(Customer).filter(Customer.id == db_quote.customer_id).first()
                if customer:
                    customer_name = customer.company_name
            except Exception as e:
                print(f"Error fetching customer for quote: {str(e)}")
        
        # Convert the model to a dictionary
        result = {
            "id": db_quote.id,
            "company_id": None,
            "store_type_id": store_type_info["id"],  # Use the ID from store_type_info
            "customer_id": db_quote.customer_id,
            "customer_name": customer_name,  # Include customer name if available
            "date": db_quote.quote_date.date() if hasattr(db_quote.quote_date, 'date') else db_quote.quote_date,
            "notes": db_quote.notes,
            "deliver_to_address": db_quote.deliver_to_address,
            "status": db_quote.status,
            "created_at": db_quote.created_at,
            "updated_at": db_quote.updated_at,
            "quote_number": quote_number_info,
            "company": company_info,
            "store_type": store_type_info,
            "items": items_list
        }
        return result
    
    @staticmethod
    async def create_quote(
        db: Session,
        quote_data: QuoteCreate,
        request: Optional[Request] = None
    ) -> Dict[str, Any]:
        """Create a new quote"""
        db_quote = await QuoteService.create_quote(
            db=db,
            quote_data=quote_data,
            request=request
        )
        
        # Return a simple dictionary response instead of validating with QuoteWithItems
        # to avoid UUID validation issues
        try:
            # Get customer name if available
            customer_name = ""
            if hasattr(db_quote, 'customer') and db_quote.customer:
                customer_name = db_quote.customer.company_name
            elif db_quote.customer_id:
                # Fetch customer if not loaded
                from src.models.customer import Customer
                customer = db.query(Customer).filter(Customer.id == db_quote.customer_id).first()
                if customer:
                    customer_name = customer.company_name
            
            # Get company name if available
            company_name = ""
            if hasattr(db_quote, 'company') and db_quote.company:
                company_name = db_quote.company.name
            elif db_quote.company_id:
                # Fetch company if not loaded
                from src.models.company import Company
                company = db.query(Company).filter(Company.id == db_quote.company_id).first()
                if company:
                    company_name = company.name
            
            # Process items
            items_list = []
            if db_quote.items:
                if isinstance(db_quote.items, dict) and 'items' in db_quote.items:
                    items_list = db_quote.items['items']
                elif isinstance(db_quote.items, list):
                    items_list = db_quote.items
            
            # Create response dictionary
            response = {
                "id": db_quote.id,
                "quote_number": db_quote.quote_number,
                "customer_id": db_quote.customer_id,
                "customer_name": customer_name,
                "company_id": db_quote.company_id,
                "company_name": company_name,
                "store_type": db_quote.store_type,
                "date": db_quote.quote_date.date() if hasattr(db_quote.quote_date, 'date') else db_quote.quote_date,
                "grand_total": float(db_quote.grand_total) if db_quote.grand_total else 0.0,
                "total_gst": float(db_quote.total_gst) if db_quote.total_gst else 0.0,
                "notes": db_quote.notes,
                "deliver_to_address": db_quote.deliver_to_address,
                "status": db_quote.status,
                "items": items_list,
                "created_at": db_quote.created_at.isoformat() if db_quote.created_at else None,
                "updated_at": db_quote.updated_at.isoformat() if db_quote.updated_at else None
            }
            
            return response
            
        except Exception as e:
            print(f"Error creating quote response: {str(e)}")
            # Return minimal response if there's an error
            return {
                "id": db_quote.id,
                "quote_number": db_quote.quote_number,
                "customer_id": db_quote.customer_id,
                "company_id": db_quote.company_id,
                "store_type": db_quote.store_type,
                "date": db_quote.quote_date,
                "grand_total": float(db_quote.grand_total) if db_quote.grand_total else 0.0,
                "status": db_quote.status,
                "items": [],
                "notes": db_quote.notes
            }
    
    @staticmethod
    def get_quotes(
        db: Session,
        search: Optional[str] = None,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
        is_archived: bool = False,
        page: int = 1,
        limit: int = 100,
        current_user = None
    ) -> QuotePagination:
        """Get quotes with filtering and pagination"""
        print(f"Controller - get_quotes called with params: search={search}, start_date={start_date}, end_date={end_date}, is_archived={is_archived}")
        
        # Call service method
        result = QuoteService.get_quotes(
            db, search=search, start_date=start_date, end_date=end_date,
            is_archived=is_archived, page=page, limit=limit, current_user=current_user
        )
        
        print(f"Service returned {len(result['data'])} quotes")
        
        # TEMP DEBUG MODE - If no quotes returned, use hardcoded sample data
        if not result['data']:
            print("DEBUG MODE: Using sample data since no quotes returned")
            # return QuoteController._get_debug_response(page, limit)
        
        # Convert quotes using our temporary method
        quotes_response = []
        
        for quote in result["data"]:
            try:
                # Get customer and company names safely
                customer_name = ""
                if hasattr(quote, 'customer') and quote.customer:
                    if hasattr(quote.customer, 'company_name'):
                        customer_name = quote.customer.company_name
                    elif hasattr(quote.customer, 'name'):
                        customer_name = quote.customer.name 
                    else:
                        print(f"Customer object exists but has no 'name' or 'company_name' attribute. Available attributes: {dir(quote.customer)}")
                        # Try other possible attribute names
                        if hasattr(quote.customer, 'customer_name'):
                            customer_name = quote.customer.customer_name
                else:
                    # If customer relationship not loaded, try to fetch customer by ID
                    try:
                        print(f"Customer relationship not loaded, fetching customer with ID: {quote.customer_id}")
                        customer = db.query(Customer).filter(Customer.id == quote.customer_id).first()
                        if customer:
                            customer_name = customer.company_name
                            print(f"Fetched customer name: {customer_name}")
                            # Attach customer to quote for future use
                            quote.customer = customer
                    except Exception as e:
                        print(f"Error fetching customer: {str(e)}")
                
                company_name = ""
                if hasattr(quote, 'company') and quote.company:
                    if hasattr(quote.company, 'name'):
                        company_name = quote.company.name
                    else:
                        print(f"Company object exists but has no 'name' attribute.")
                        # Try other possible attribute names
                        if hasattr(quote.company, 'company_name'):
                            company_name = quote.company.company_name
                
                # Ensure date is properly handled
                quote_date = quote.quote_date
                if hasattr(quote_date, 'date'):
                    quote_date = quote_date.date()
                
                # Ensure grand_total is a float
                grand_total = 0.0
                if quote.grand_total is not None:
                    try:
                        grand_total = float(quote.grand_total)
                    except (ValueError, TypeError):
                        grand_total = 0.0
                
                # Create a simplified response object with required format
                quote_response = {
                    "id": str(quote.id),  # Convert ID to string for consistency
                    "quote_no": quote.quote_number,  # Use quote_no for frontend compatibility
                    "quote_number": quote.quote_number,  # Keep original field as backup
                    "customer_id": str(quote.customer_id),
                    "customer_name": customer_name,
                    "company_id": "",
                    "company_name": company_name,
                    "store_type_id": int(quote.store_type) if quote.store_type and quote.store_type.isdigit() else None,
                    "store_type": quote.store_type or "",  # Use store_type field directly
                    "store_type_name": quote.store_type or "",
                    "date": quote_date,
                    "grand_total": grand_total,
                    "notes": quote.notes or "",
                    "status": quote.status or "draft",
                    "is_archived": QuoteController._is_quote_archived(quote)
                }
                quotes_response.append(quote_response)
                print(f"Processed quote: {quote.quote_number}, ID: {quote.id}, Response structure: {list(quote_response.keys())}")
            except Exception as e:
                # Even if there's an error, try to create a minimal response
                print(f"Error converting quote: {str(e)}")
                try:
                    # Create a minimal response object
                    minimal_response = {
                        "id": str(getattr(quote, 'id', 0)),
                        "quote_no": getattr(quote, 'quote_number', ""),
                        "quote_number": getattr(quote, 'quote_number', ""),
                        "customer_id": str(getattr(quote, 'customer_id', 0)),
                        "customer_name": "",
                        "company_id": "",
                        "company_name": "",
                        "store_type_id": None,
                        "store_type": getattr(quote, 'store_type', ""),
                        "store_type_name": getattr(quote, 'store_type', ""),
                        "date": getattr(quote, 'quote_date', datetime.now().date()),
                        "grand_total": 0.0,
                        "notes": getattr(quote, 'notes', ""),
                        "status": getattr(quote, 'status', "draft"),
                        "is_archived": QuoteController._is_quote_archived(quote)
                    }
                    quotes_response.append(minimal_response)
                    print(f"Added minimal quote info: {minimal_response['quote_no']}")
                except Exception as inner_e:
                    print(f"Could not create minimal response: {str(inner_e)}")
                    continue
        
        print(f"Returning {len(quotes_response)} quotes in response")
        
        # Create pagination response
        pagination_response = QuotePagination(
            items=quotes_response,
            total=result["total"],
            page=page,
            size=limit
        )
        
        print(f"Response structure: {pagination_response}")
        return pagination_response
    
    @staticmethod
    def _get_debug_response(page: int, limit: int) -> QuotePagination:
        """Create a debug response with sample data when actual quotes can't be retrieved"""
        # Create sample quote data
        sample_quotes = [
            {
                "id": 1,
                "quote_number": "QUO-001",
                "customer_id": 1,
                "customer_name": "Customer 1",
                "company_id": str(uuid.uuid4()),
                "company_name": "Company 1",
                "store_type_id": 1,
                "store_type_name": "Sale",
                "date": datetime.now().date(),
                "grand_total": 1000.0,
                "notes": "Debug sample quote"
            },
            {
                "id": 2,
                "quote_number": "QUO-002",
                "customer_id": 2,
                "customer_name": "Customer 1",
                "company_id": str(uuid.uuid4()),
                "company_name": "Company 1",
                "store_type_id": 1,
                "store_type_name": "Sale",
                "date": datetime.now().date(),
                "grand_total": 2000.0,
                "notes": "debug sample quote"
            }
        ]
        
        # Return pagination with sample data
        return QuotePagination(
            items=sample_quotes,
            total=len(sample_quotes),
            page=page,
            size=limit
        )
    
    @staticmethod
    def get_quote(
        db: Session,
        quote_id: int
    ) -> QuoteWithItems:
        """Get a quote by ID"""
        db_quote = QuoteService.get_quote_by_id(db, quote_id)
        if not db_quote:
            # Use HTTPException for not found error
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Quote with ID {quote_id} not found"
            )
        
        # Use the temporary converter method
        quote_dict = QuoteController._convert_quote_model_to_schema(db_quote, db)
        return QuoteWithItems.model_validate(quote_dict)
    
    @staticmethod
    async def update_quote(
        db: Session,
        quote_id: int,
        quote_update: QuoteUpdate,
        request: Optional[Request] = None
    ) -> Dict[str, Any]:
        """Update a quote"""
        db_quote = await QuoteService.update_quote(
            db=db,
            quote_id=quote_id,
            quote_update=quote_update,
            request=request
        )
        
        # Return a simple dictionary response instead of validating with QuoteWithItems
        # to avoid UUID validation issues
        try:
            # Get customer name if available
            customer_name = ""
            if hasattr(db_quote, 'customer') and db_quote.customer:
                customer_name = db_quote.customer.company_name
            elif db_quote.customer_id:
                # Fetch customer if not loaded
                from src.models.customer import Customer
                customer = db.query(Customer).filter(Customer.id == db_quote.customer_id).first()
                if customer:
                    customer_name = customer.company_name
            
            # Get company name if available
            company_name = ""
            if hasattr(db_quote, 'company') and db_quote.company:
                company_name = db_quote.company.name
            elif db_quote.company_id:
                # Fetch company if not loaded
                from src.models.company import Company
                company = db.query(Company).filter(Company.id == db_quote.company_id).first()
                if company:
                    company_name = company.name
            
            # Process items
            items_list = []
            if db_quote.items:
                if isinstance(db_quote.items, dict) and 'items' in db_quote.items:
                    items_list = db_quote.items['items']
                elif isinstance(db_quote.items, list):
                    items_list = db_quote.items
            
            # Create response dictionary
            response = {
                "id": db_quote.id,
                "quote_number": db_quote.quote_number,
                "customer_id": db_quote.customer_id,
                "customer_name": customer_name,
                "company_id": db_quote.company_id,
                "company_name": company_name,
                "store_type": db_quote.store_type,
                "date": db_quote.quote_date.date() if hasattr(db_quote.quote_date, 'date') else db_quote.quote_date,
                "grand_total": float(db_quote.grand_total) if db_quote.grand_total else 0.0,
                "total_gst": float(db_quote.total_gst) if db_quote.total_gst else 0.0,
                "notes": db_quote.notes,
                "deliver_to_address": db_quote.deliver_to_address,
                "status": db_quote.status,
                "items": items_list,
                "created_at": db_quote.created_at.isoformat() if db_quote.created_at else None,
                "updated_at": db_quote.updated_at.isoformat() if db_quote.updated_at else None,
                "updated": True,  # Add a flag to indicate successful update
                "message": "Quote updated successfully"
            }
            
            return response
            
        except Exception as e:
            print(f"Error creating quote update response: {str(e)}")
            # Return minimal response if there's an error
            return {
                "id": db_quote.id,
                "quote_number": db_quote.quote_number,
                "customer_id": db_quote.customer_id,
                "company_id": db_quote.company_id,
                "store_type": db_quote.store_type,
                "date": db_quote.quote_date,
                "grand_total": float(db_quote.grand_total) if db_quote.grand_total else 0.0,
                "status": db_quote.status,
                "items": [],
                "notes": db_quote.notes,
                "updated": True,
                "message": "Quote updated successfully (minimal response)"
            }
    
    @staticmethod
    async def convert_to_invoice(
        db: Session,
        quote_id: int,
        request: Optional[Request] = None
    ) -> Dict[str, Any]:
        """Convert a quote to invoice"""
        db_quote = await QuoteService.convert_to_invoice(
            db=db,
            quote_id=quote_id,
            request=request
        )
        
        # Return a simple dictionary response instead of validating with QuoteWithItems
        # to avoid UUID validation issues
        try:
            # Get customer name if available
            customer_name = ""
            if hasattr(db_quote, 'customer') and db_quote.customer:
                customer_name = db_quote.customer.company_name
            elif db_quote.customer_id:
                # Fetch customer if not loaded
                from src.models.customer import Customer
                customer = db.query(Customer).filter(Customer.id == db_quote.customer_id).first()
                if customer:
                    customer_name = customer.company_name
            
            # Get company name if available
            company_name = ""
            if hasattr(db_quote, 'company') and db_quote.company:
                company_name = db_quote.company.name
            elif db_quote.company_id:
                # Fetch company if not loaded
                from src.models.company import Company
                company = db.query(Company).filter(Company.id == db_quote.company_id).first()
                if company:
                    company_name = company.name
            
            # Process items
            items_list = []
            if db_quote.items:
                if isinstance(db_quote.items, dict) and 'items' in db_quote.items:
                    items_list = db_quote.items['items']
                elif isinstance(db_quote.items, list):
                    items_list = db_quote.items
            
            # Create response dictionary
            response = {
                "id": db_quote.id,
                "quote_number": db_quote.quote_number,
                "customer_id": db_quote.customer_id,
                "customer_name": customer_name,
                "company_id": db_quote.company_id,
                "company_name": company_name,
                "store_type": db_quote.store_type,
                "date": db_quote.quote_date.date() if hasattr(db_quote.quote_date, 'date') else db_quote.quote_date,
                "grand_total": float(db_quote.grand_total) if db_quote.grand_total else 0.0,
                "total_gst": float(db_quote.total_gst) if db_quote.total_gst else 0.0,
                "notes": db_quote.notes,
                "deliver_to_address": db_quote.deliver_to_address,
                "status": db_quote.status,
                "items": items_list,
                "created_at": db_quote.created_at.isoformat() if db_quote.created_at else None,
                "updated_at": db_quote.updated_at.isoformat() if db_quote.updated_at else None,
                "converted": True,  # Add a flag to indicate successful conversion
                "message": "Quote successfully converted to invoice"
            }
            
            return response
            
        except Exception as e:
            print(f"Error creating quote conversion response: {str(e)}")
            # Return minimal response if there's an error
            return {
                "id": db_quote.id,
                "quote_number": db_quote.quote_number,
                "customer_id": db_quote.customer_id,
                "company_id": db_quote.company_id,
                "store_type": db_quote.store_type,
                "date": db_quote.quote_date,
                "grand_total": float(db_quote.grand_total) if db_quote.grand_total else 0.0,
                "status": db_quote.status,
                "items": [],
                "notes": db_quote.notes,
                "converted": True,
                "message": "Quote converted to invoice (minimal response)"
            }
    
    @staticmethod
    async def renew_quote(
        db: Session,
        quote_id: int,
        request: Optional[Request] = None
    ) -> QuoteWithItems:
        """Renew an archived quote"""
        db_quote = await QuoteService.renew_quote(
            db=db,
            quote_id=quote_id,
            request=request
        )
        
        # Use the temporary converter method
        quote_dict = QuoteController._convert_quote_model_to_schema(db_quote, db)
        return QuoteWithItems.model_validate(quote_dict)
    
    @staticmethod
    async def delete_quote(
        db: Session,
        quote_id: int,
        request: Optional[Request] = None
    ) -> Dict[str, bool]:
        """Soft delete a quote"""
        success = await QuoteService.soft_delete_quote(
            db=db,
            quote_id=quote_id,
            request=request
        )
        
        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to delete quote"
            )
            
        return {"success": True} 