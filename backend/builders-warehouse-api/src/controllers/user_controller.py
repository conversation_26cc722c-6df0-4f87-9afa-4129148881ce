from fastapi import Request
from sqlalchemy.orm import Session
from typing import List, Optional, Any
import uuid

from src.models.user import User
from src.schemas.user import UserCreate, UserUpdate, UserResponse, ManagerListItem
from src.services.user import UserService

class UserController:
    """
    Controller for user-related API endpoints
    Handles the request/response cycle and delegates business logic to services
    """
    
    @staticmethod
    async def create_user(
        db: Session,
        user_data: UserCreate,
        request: Optional[Request] = None,
        created_by_id: Optional[int] = None
    ) -> User:
        """Create a new user"""
        user = await UserService.create_user(
            db=db,
            user_data=user_data,
            created_by_id=created_by_id,
            request=request
        )
        
        # Convert to dictionary to avoid serialization issues
        if user:
            # Format user data for response
            user_dict = {
                "id": str(user.id),
                "user_name": user.user_name,
                "email": user.email,
                "mobile_number": user.mobile_number or "",
                "is_active": user.is_active,
                "role": user.role,
                "created_at": user.created_at,
                "updated_at": user.updated_at,
                "store_type": None,
                "manager": None
            }
            
            # Include store_type if available
            if hasattr(user, 'store_type') and user.store_type:
                user_dict["store_type"] = {
                    "id": user.store_type.id,
                    "name": user.store_type.name
                }
            
            # Include manager if available
            if hasattr(user, 'manager') and user.manager:
                user_dict["manager"] = {
                    "id": str(user.manager.id),
                    "user_name": user.manager.user_name
                }
                
            return user_dict
            
        return user
    
    @staticmethod
    async def get_users(
        db: Session,
        skip: int = 0,
        limit: int = 100,
        search: Optional[str] = None,
        role: Optional[str] = None,
        store_type_id: Optional[int] = None
    ) -> List[User]:
        """Get list of users with pagination, search and filtering"""
        return UserService.get_users(
            db=db,
            skip=skip,
            limit=limit,
            search=search,
            role=role,
            store_type_id=store_type_id
        )
    
    @staticmethod
    async def get_users_count(
        db: Session,
        search: Optional[str] = None,
        role: Optional[str] = None,
        store_type_id: Optional[int] = None
    ) -> int:
        """Get total count of users matching filter criteria"""
        return UserService.get_users_count(
            db=db,
            search=search,
            role=role,
            store_type_id=store_type_id
        )
    
    @staticmethod
    async def get_managers(db: Session) -> List[ManagerListItem]:
        """Get list of users with manager role for dropdown"""
        managers = UserService.get_managers(db=db)
        
        # Convert model objects to dictionaries for serialization
        manager_dicts = []
        for manager in managers:
            # ManagerListItem objects should already be serializable,
            # but let's ensure they're dictionaries
            if isinstance(manager, dict):
                manager_dicts.append(manager)
            else:
                # Create a dictionary with the required fields
                manager_dict = {
                    "id": str(manager.id) if hasattr(manager, 'id') else None,
                    "user_name": manager.user_name if hasattr(manager, 'user_name') else None,
                }
                
                # Add store_type if available
                if hasattr(manager, 'store_type') and manager.store_type:
                    manager_dict["store_type"] = {
                        "id": manager.store_type.id,
                        "name": manager.store_type.name
                    }
                
                manager_dicts.append(manager_dict)
        
        return manager_dicts
    
    @staticmethod
    async def get_user(db: Session, user_id: Any) -> User:
        """Get a user by ID"""
        user = UserService.get_user_by_id(db=db, user_id=user_id)
        
        # Convert to dictionary to avoid serialization issues
        if user:
            # Format user data for response
            user_dict = {
                "id": str(user.id),
                "user_name": user.user_name,
                "email": user.email,
                "mobile_number": user.mobile_number or "",
                "is_active": user.is_active,
                "role": user.role,
                "created_at": user.created_at,
                "updated_at": user.updated_at,
                "store_type": None,
                "manager": None
            }
            
            # Include store_type if available
            if hasattr(user, 'store_type') and user.store_type:
                user_dict["store_type"] = {
                    "id": user.store_type.id,
                    "name": user.store_type.name
                }
            
            # Include manager if available
            if hasattr(user, 'manager') and user.manager:
                user_dict["manager"] = {
                    "id": str(user.manager.id),
                    "user_name": user.manager.user_name
                }
                
            return user_dict
            
        return user
    
    @staticmethod
    async def update_user(
        db: Session,
        user_id: Any,
        user_update: UserUpdate,
        modified_by_id: Optional[Any] = None,
        request: Optional[Request] = None
    ) -> User:
        """Update a user by ID"""
        user = await UserService.update_user(
            db=db,
            user_id=user_id,
            user_update=user_update,
            modified_by_id=modified_by_id,
            request=request
        )
        
        # Convert to dictionary to avoid serialization issues
        if user:
            # Format user data for response
            user_dict = {
                "id": str(user.id),
                "user_name": user.user_name,
                "email": user.email,
                "mobile_number": user.mobile_number or "",
                "is_active": user.is_active,
                "role": user.role,
                "created_at": user.created_at,
                "updated_at": user.updated_at,
                "store_type": None,
                "manager": None
            }
            
            # Include store_type if available
            if hasattr(user, 'store_type') and user.store_type:
                user_dict["store_type"] = {
                    "id": user.store_type.id,
                    "name": user.store_type.name
                }
            
            # Include manager if available
            if hasattr(user, 'manager') and user.manager:
                user_dict["manager"] = {
                    "id": str(user.manager.id),
                    "user_name": user.manager.user_name
                }
                
            return user_dict
            
        return user
    
    @staticmethod
    async def delete_user(
        db: Session,
        user_id: Any,
        deleted_by_id: Optional[Any] = None,
        request: Optional[Request] = None
    ) -> None:
        """Soft-delete a user by ID"""
        return await UserService.delete_user(
            db=db,
            user_id=user_id,
            deleted_by_id=deleted_by_id,
            request=request
        ) 