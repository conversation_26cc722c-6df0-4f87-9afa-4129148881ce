from fastapi import Request
from sqlalchemy.orm import Session
from typing import List, Optional

from src.schemas.user import UserLogin, Token
from src.schemas.audit_log import AuditLogFilter, AuditLogResponse
from src.services.auth import AuthService
from src.models.audit_log import AuditLog

class AuthController:
    """
    Controller for authentication-related API endpoints
    Handles the request/response cycle and delegates business logic to services
    """
    
    @staticmethod
    async def email_login(
        db: Session,
        login_data: UserLogin,
        request: Optional[Request] = None
    ) -> Token:
        """Process login using email/password"""
        return await AuthService.login_by_email(
            db=db,
            login_data=login_data,
            request=request
        )
    
    @staticmethod
    async def get_audit_logs(
        db: Session,
        filter_params: AuditLogFilter,
        skip: int = 0,
        limit: int = 100
    ) -> List[AuditLogResponse]:
        """Get audit logs with filtering"""
        query = db.query(AuditLog)
        
        # Apply filters if provided
        if filter_params.table_name:
            query = query.filter(AuditLog.table_name == filter_params.table_name)
            
        if filter_params.operation:
            query = query.filter(AuditLog.operation == filter_params.operation)
            
        if filter_params.entity_id:
            query = query.filter(AuditLog.entity_id == filter_params.entity_id)
            
        if filter_params.user_id:
            query = query.filter(AuditLog.user_id == filter_params.user_id)
            
        if filter_params.from_date:
            query = query.filter(AuditLog.timestamp >= filter_params.from_date)
            
        if filter_params.to_date:
            query = query.filter(AuditLog.timestamp <= filter_params.to_date)
        
        # Order by timestamp descending (newest first)
        query = query.order_by(AuditLog.timestamp.desc())
        
        # Apply pagination
        logs = query.offset(skip).limit(limit).all()
        
        return logs 