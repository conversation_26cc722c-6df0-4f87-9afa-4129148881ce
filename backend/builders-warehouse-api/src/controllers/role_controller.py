from sqlalchemy.orm import Session
from typing import List, Optional

from src.models.role import Role
from src.schemas.role import RoleCreate, RoleUpdate
from src.services.role import RoleService

class RoleController:
    """
    Controller for role-related API endpoints
    Handles the request/response cycle and delegates business logic to services
    """
    
    @staticmethod
    def create_role(db: Session, role_data: RoleCreate) -> Role:
        """Create a new role"""
        return RoleService.create_role(db=db, role_data=role_data)
    
    @staticmethod
    def get_roles(db: Session, skip: int = 0, limit: int = 100) -> List[Role]:
        """Get list of roles with pagination"""
        return RoleService.get_roles(db=db, skip=skip, limit=limit)
    
    @staticmethod
    def get_roles_count(db: Session) -> int:
        """Get total count of roles"""
        return RoleService.get_roles_count(db=db)
    
    @staticmethod
    def get_role(db: Session, role_id: int) -> Role:
        """Get a role by ID"""
        return RoleService.get_role_by_id(db=db, role_id=role_id)
    
    @staticmethod
    def update_role(db: Session, role_id: int, role_update: RoleUpdate) -> Role:
        """Update a role by ID"""
        return RoleService.update_role(db=db, role_id=role_id, role_update=role_update)
    
    @staticmethod
    def delete_role(db: Session, role_id: int) -> None:
        """Delete a role by ID"""
        return RoleService.delete_role(db=db, role_id=role_id)
    
    @staticmethod
    def seed_default_roles(db: Session) -> None:
        """Seed default roles if they don't exist"""
        return RoleService.seed_default_roles(db=db) 