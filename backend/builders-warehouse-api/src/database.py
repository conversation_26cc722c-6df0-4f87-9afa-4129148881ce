from sqlalchemy import create_engine, MetaData, inspect
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, registry, configure_mappers
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
import logging
from typing import AsyncGenerator
import os
from urllib.parse import urlparse

from src.config import settings

# Set up logging
logger = logging.getLogger(__name__)

# Get database URL from environment variables
DATABASE_URL = os.getenv(
    "DATABASE_URL", 
    "postgresql://warehouse_user:Password#123@localhost:5432/builders_warehouse"
)

# For async operations, we need to use the asyncpg driver
ASYNC_DATABASE_URL = DATABASE_URL.replace('postgresql://', 'postgresql+asyncpg://')

# Log the database connection (without password)
try:
    parsed_url = urlparse(DATABASE_URL)
    safe_url = f"{parsed_url.scheme}://{parsed_url.username}:****@{parsed_url.hostname}:{parsed_url.port}{parsed_url.path}"
    logger.info(f"Connected to database: {safe_url}")
except Exception:
    logger.info(f"Connected to database: {DATABASE_URL.split('@')[0]}:****@{DATABASE_URL.split('@')[1]}")

# Enable SQLAlchemy echo for debugging in development
DEBUG = os.environ.get("DEBUG", "").lower() in ["true", "1", "yes"]

# Create SQLAlchemy engines
engine = create_engine(
    DATABASE_URL, 
    echo=DEBUG,
    pool_pre_ping=True,  # Enable connection pool pre-ping feature
    connect_args={}
)

# Create async engine
async_engine = create_async_engine(
    ASYNC_DATABASE_URL,
    echo=DEBUG,
    pool_pre_ping=True,
)

# Create session factories
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
AsyncSessionLocal = sessionmaker(
    autocommit=False, 
    autoflush=False, 
    bind=async_engine, 
    class_=AsyncSession
)

# Create base class for models
Base = declarative_base()

# Configure mappers to ensure all relationships are properly set up
def configure_db_mappers():
    try:
        configure_mappers()
        logger.debug("SQLAlchemy mappers configured successfully")
    except Exception as e:
        logger.error(f"Error configuring SQLAlchemy mappers: {e}")
        # Don't raise the exception as the application might still work

def refresh_metadata():
    """Refresh SQLAlchemy metadata to match the current database schema"""
    try:
        inspector = inspect(engine)
        store_type_columns = inspector.get_columns("StoreType")
        logger.info(f"StoreType table columns: {[col['name'] for col in store_type_columns]}")
        
        # Force metadata refresh
        Base.metadata.reflect(bind=engine)
        logger.info("Database metadata refreshed successfully")
    except Exception as e:
        logger.error(f"Error refreshing metadata: {e}")

# Dependency to get DB session (sync)
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# Dependency to get Async DB session
async def get_async_db() -> AsyncGenerator[AsyncSession, None]:
    async with AsyncSessionLocal() as session:
        try:
            yield session
        finally:
            await session.close()

# Run metadata refresh at module import time
refresh_metadata()

# Export the engine objects
__all__ = ['engine', 'async_engine', 'SessionLocal', 'AsyncSessionLocal', 'Base', 'get_db', 'get_async_db'] 