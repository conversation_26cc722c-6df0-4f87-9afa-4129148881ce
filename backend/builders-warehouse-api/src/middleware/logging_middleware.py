import time
import uuid
import logging
import json
from fastapi import Request
from starlette.middleware.base import BaseHTTPMiddleware, RequestResponseEndpoint
from starlette.responses import Response

logger = logging.getLogger(__name__)

class RequestLoggingMiddleware(BaseHTTPMiddleware):
    """
    Middleware for logging request/response information
    Adds a unique request ID and logs request method, path, processing time, and status code
    """
    
    async def dispatch(self, request: Request, call_next: RequestResponseEndpoint) -> Response:
        request_id = str(uuid.uuid4())
        request.state.request_id = request_id
        
        # Record start time
        start_time = time.time()
        
        # Extract additional request information
        client_ip = request.client.host if request.client else 'unknown'
        user_agent = request.headers.get('user-agent', 'unknown')
        query_params = dict(request.query_params) if request.query_params else {}
        
        # Get user information if available from headers
        auth_header = request.headers.get('authorization', '')
        user_info = "anonymous"
        if auth_header:
            user_info = "authenticated"
        
        # Log request details with enhanced information
        logger.info(
            f"API Request: {request.method} {request.url.path} "
            f"(ID: {request_id}, Client: {client_ip}, User: {user_info})"
        )
        
        # Log query parameters if present
        if query_params:
            logger.info(f"Query params: {json.dumps(query_params)} (ID: {request_id})")
        
        # Log content type and size for POST/PUT requests
        if request.method in ['POST', 'PUT', 'PATCH']:
            content_type = request.headers.get('content-type', 'unknown')
            content_length = request.headers.get('content-length', 'unknown')
            logger.info(f"Request body: Content-Type={content_type}, Size={content_length} bytes (ID: {request_id})")
        
        try:
            # Process the request
            response = await call_next(request)
            
            # Calculate processing time
            process_time = time.time() - start_time
            
            # Log response details
            logger.info(
                f"API Response: {request.method} {request.url.path} "
                f"-> Status: {response.status_code} in {process_time:.3f}s "
                f"(ID: {request_id})"
            )
            
            # Add request ID to response headers for tracking
            response.headers["X-Request-ID"] = request_id
            
            return response
        except Exception as e:
            # Log exceptions
            process_time = time.time() - start_time
            logger.error(
                f"API Error: {request.method} {request.url.path} "
                f"-> Error: {str(e)} in {process_time:.3f}s "
                f"(ID: {request_id})"
            )
            raise 