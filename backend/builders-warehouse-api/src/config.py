"""
Configuration module for the application.
Loads settings from environment variables.
"""
import os
from typing import List, Union, Optional
from pydantic_settings import BaseSettings
from pydantic import validator
from functools import lru_cache
from dotenv import load_dotenv

# Load environment variables from .env file if it exists
load_dotenv()

class Settings(BaseSettings):
    """Application settings"""
    
    # API configuration
    API_V1_STR: str = "/api/v1"
    PROJECT_NAME: str = "Builders Warehouse API"
    
    # Authentication
    SECRET_KEY: str = os.getenv("SECRET_KEY", "your-secret-key")
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60 * 24 * 8  # 8 days
    ALGORITHM: str = os.getenv("ALGORITHM", "HS256")
    
    # CORS configuration
    CORS_ORIGINS: Union[str, List[str]] = ["https://tadashie-demo.tudip.com", "http://localhost:3000", "http://localhost:4200", "http://localhost:4201"]
    
    @validator("CORS_ORIGINS", pre=True)
    def assemble_cors_origins(cls, v: Union[str, List[str]]) -> Union[List[str], str]:
        if isinstance(v, str):
            return [i.strip() for i in v.split(",")]
        return v
    
    # Database configuration
    DATABASE_URL: str = os.getenv("DATABASE_URL", "postgresql://postgres:postgres@localhost:5432/builders_warehouse")
    USE_PRISMA: bool = False  # Default to False since we're not using Prisma
    
    # Validator to ensure DATABASE_URL is properly formatted
    @validator("DATABASE_URL")
    def validate_database_url(cls, v: str) -> str:
        """Validate the database URL format"""
        # Ensure it's a valid database URL
        if not v.startswith(("postgresql://", "postgres://", "sqlite:///")):
            raise ValueError("DATABASE_URL must be a PostgreSQL connection string")
        return v
    
    # Email Service Configuration
    POSTMARK_SERVER_TOKEN: str = os.getenv("POSTMARK_SERVER_TOKEN", "************************************")
    POSTMARK_FROM_EMAIL: str = os.getenv("POSTMARK_FROM_EMAIL", "<EMAIL>")
    FRONTEND_URL: str = os.getenv("FRONTEND_URL", "http://localhost:4200")
    
    # Logging settings
    LOG_LEVEL: str = os.getenv("LOG_LEVEL", "INFO")
    LOG_RETENTION_DAYS: int = int(os.getenv("LOG_RETENTION_DAYS", "30"))
    LOGS_DIR: str = os.getenv("LOGS_DIR", "logs")
    APP_LOG_FILENAME: str = os.getenv("APP_LOG_FILENAME", "app.log")
    
    # Audit logging settings
    AUDIT_LOG_PATH: str = os.getenv("AUDIT_LOG_PATH", "audit.log")
    
    class Config:
        """Pydantic config"""
        case_sensitive = True
        env_file = ".env"


@lru_cache()
def get_settings() -> Settings:
    """Get cached application settings"""
    return Settings()

# Export settings instance
settings = get_settings() 