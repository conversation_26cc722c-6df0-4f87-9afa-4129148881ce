"""
Configuration module for the application.
Loads settings from environment variables.
"""
import sys
import os
import logging
from fastapi import FastAPI, Depends, Request, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse, RedirectResponse
from fastapi.staticfiles import StaticFiles
from contextlib import asynccontextmanager
from starlette.middleware.base import BaseHTTPMiddleware
from pathlib import Path

# Setup basic logging immediately - will be reconfigured
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Load config
from src.config import settings, get_settings

# Import logging setup
from src.logging_setup import setup_logging

# Import logging middleware
from src.middleware.logging_middleware import RequestLoggingMiddleware

# Import the API router
from src.api.v1.routes import router as api_router

# Import individual routers for explicit registration if needed
from src.api.v1.routes.outbound_delivery import router as outbound_router
from src.api.v1.routes.inventory import router as inventory_router
from src.api.v1.routes.suppliers import router as suppliers_router
from src.api.v1.routes.purchase_orders import router as purchase_orders_router
from src.api.v1.routes.store_type import router as store_types_router

# Import database migration utilities
from src.db.migrations import run_all_migrations

# Import core app initializer
from src.core.initializer import initialize_database

@asynccontextmanager
async def lifespan(app: FastAPI):
    """
    Async context manager for the application lifespan.
    This runs on startup and shutdown of the application.
    """
    # --- Startup ---
    # Setup logging
    setup_logging()
    
    # Initialize the database
    initialize_database()
    
    logging.info("Using SQLAlchemy for database operations")
    
    yield
    
    # --- Shutdown ---
    logging.info("Application shutting down")

# Create FastAPI application
app = FastAPI(
    title=settings.PROJECT_NAME,
    openapi_url=f"{settings.API_V1_STR}/openapi.json",
    lifespan=lifespan,
    redirect_slashes=False  # Disable automatic trailing slash redirects
)

# Add request logging middleware
app.add_middleware(RequestLoggingMiddleware)

# Set up CORS - Allow all origins for development (disable CORS restrictions)
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Allow all origins
    allow_credentials=False,  # Must be False when allow_origins=["*"]
    allow_methods=["*"],  # Allow all HTTP methods
    allow_headers=["*"],  # Allow all headers
)

# Ensure the public directory exists
public_dir = Path("public")
public_dir.mkdir(exist_ok=True)

# Mount static files directory for serving images
app.mount("/images", StaticFiles(directory="public/images"), name="images")

# Include API router
app.include_router(api_router, prefix=settings.API_V1_STR)

# Explicitly register individual routers to ensure they're available
# These were previously defined as direct routes in main.py
# COMMENTED OUT TO AVOID ROUTE CONFLICTS - All routers are already included via api_router
# app.include_router(outbound_router, prefix=f"{settings.API_V1_STR}")
# app.include_router(inventory_router, prefix=f"{settings.API_V1_STR}")
# app.include_router(suppliers_router, prefix=f"{settings.API_V1_STR}")
# purchase_orders_router is already included via api_router - DO NOT include again to avoid conflicts
# app.include_router(store_types_router, prefix=f"{settings.API_V1_STR}")

# Add health check endpoint
@app.get("/health")
async def health_check():
    """Simple health check endpoint"""
    return {"status": "ok"} 