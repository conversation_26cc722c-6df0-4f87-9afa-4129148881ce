from passlib.context import <PERSON>pt<PERSON>ontext
from jose import <PERSON><PERSON><PERSON><PERSON>r, jwt
from datetime import datetime, timedelta
from fastapi import Depends, HTTPException, status
from fastapi.security import OAuth2Password<PERSON>earer
from sqlalchemy.orm import Session
import json
import uuid

from src.core.config import settings
from src.schemas.user import TokenData
from src.database import get_db
from src.models.user import User
from src.models.role import Role

# Password hashing context
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# OAuth2 password bearer for token handling
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="api/v1/auth/login")

# Custom JSON encoder to handle UUID serialization
class CustomJSONEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, uuid.UUID):
            return str(obj)
        return super().default(obj)

def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify a password against a hash"""
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password: str) -> str:
    """Generate a password hash"""
    return pwd_context.hash(password)

def create_access_token(data: dict, expires_delta: timedelta = None) -> str:
    """
    Create a JWT access token
    
    Args:
        data: Data to encode in the token
        expires_delta: Optional expiration time override
        
    Returns:
        JWT token string
    """
    to_encode = data.copy()
    
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(minutes=settings.access_token_expire_minutes)
        
    to_encode.update({"exp": expire})
    
    # Convert UUID objects to strings in the data
    for key, value in to_encode.items():
        if isinstance(value, uuid.UUID):
            to_encode[key] = str(value)
    
    encoded_jwt = jwt.encode(to_encode, settings.secret_key, algorithm=settings.algorithm)
    return encoded_jwt

def get_current_user(token: str = Depends(oauth2_scheme), db: Session = Depends(get_db)) -> User:
    """
    Dependency to get the current authenticated user from a JWT token
    
    Args:
        token: JWT token from Authorization header
        db: Database session
        
    Returns:
        User object for the authenticated user
        
    Raises:
        HTTPException: If token is invalid or user doesn't exist
    """
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        # Decode token
        payload = jwt.decode(token, settings.secret_key, algorithms=[settings.algorithm])
        
        # Extract data from payload - supporting both new and old token formats
        user_id_str = payload.get("user_id")
        email = payload.get("email")
        username = payload.get("username") or payload.get("sub")  # Backward compatibility
        role = payload.get("role") or payload.get("role_name")  # Backward compatibility
        
        if not user_id_str:
            raise credentials_exception
        
        # Convert user_id to UUID if it's a string
        user_id = None
        if isinstance(user_id_str, str):
            try:
                user_id = uuid.UUID(user_id_str)
            except ValueError:
                try:
                    user_id = int(user_id_str)
                except ValueError:
                    user_id = user_id_str
        else:
            user_id = user_id_str
            
        # Create token data with available information
        token_data = TokenData(
            user_id=str(user_id) if user_id is not None else None,
            email=email,
            username=username,
            role=role
        )
    except (JWTError, ValueError) as e:
        # ValueError can be raised if UUID parsing fails
        print(f"Error parsing token: {e}")
        raise credentials_exception
        
    # Get user from database based on ID (most reliable)
    user = db.query(User).filter(
        User.id == token_data.user_id,
        User.is_deleted == False
    ).first()
    
    # If not found by ID, try email as fallback
    if user is None and token_data.email:
        user = db.query(User).filter(
            User.email == token_data.email,
            User.is_deleted == False
        ).first()
    
    # If still not found, try username as fallback (legacy support)
    if user is None and token_data.username:
        user = db.query(User).filter(
            User.user_name == token_data.username,
            User.is_deleted == False
        ).first()
    
    if user is None:
        raise credentials_exception
        
    # Check if user is active
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="User account is inactive"
        )
        
    return user

def get_current_active_admin(current_user: User = Depends(get_current_user), db: Session = Depends(get_db)) -> User:
    """
    Dependency to get the current user and verify they are an admin
    
    Args:
        current_user: Current authenticated user
        db: Database session
        
    Returns:
        User object if the user is an admin
        
    Raises:
        HTTPException: If user is not an admin
    """
    # Get the role name for the current user, trying both new and legacy role fields
    is_admin = False
    
    # First, try using role_id relationship if it exists
    if current_user.role_id is not None:
        role = db.query(Role).filter(Role.id == current_user.role_id).first()
        if role and role.name == "admin":
            is_admin = True
    
    # If not found by role_id, try the legacy role field
    if not is_admin and current_user.role == "admin":
        is_admin = True
    
    if not is_admin:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Not authorized to perform this action"
        )
    
    return current_user 