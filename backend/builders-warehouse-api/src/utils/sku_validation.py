from typing import List, Dict, Tuple, Any
import logging

logger = logging.getLogger(__name__)

class SkuValidationResult:
    """Class to hold SKU validation results"""
    def __init__(self):
        self.matching_skus: List[str] = []
        self.inventory_only_skus: List[str] = []
        self.price_list_only_skus: List[str] = []
        self.total_matches: int = 0
        self.total_inventory_skus: int = 0
        self.total_price_list_skus: int = 0
        self.validation_passed: bool = False
        self.message: str = ""

def validate_sku_cross_reference(
    inventory_data: List[Dict[str, Any]], 
    price_list_data: List[Dict[str, Any]]
) -> SkuValidationResult:
    """
    Validate SKU codes between inventory and price list data
    
    Args:
        inventory_data: List of inventory items with SKU codes
        price_list_data: List of price list items with SKU codes
        
    Returns:
        SkuValidationResult object with validation details
    """
    result = SkuValidationResult()
    
    try:
        # Extract SKU codes from both lists
        inventory_skus = [
            item.get('sku_code', '') for item in inventory_data 
            if item.get('sku_code', '').strip()
        ]
        
        price_list_skus = [
            item.get('code', '') for item in price_list_data 
            if item.get('code', '').strip()
        ]
        
        # Remove duplicates and convert to sets for comparison
        inventory_sku_set = set(inventory_skus)
        price_list_sku_set = set(price_list_skus)
        
        # Find matches and mismatches
        result.matching_skus = list(inventory_sku_set.intersection(price_list_sku_set))
        result.inventory_only_skus = list(inventory_sku_set - price_list_sku_set)
        result.price_list_only_skus = list(price_list_sku_set - inventory_sku_set)
        
        # Set counts
        result.total_matches = len(result.matching_skus)
        result.total_inventory_skus = len(inventory_sku_set)
        result.total_price_list_skus = len(price_list_sku_set)
        
        # Determine if validation passed
        result.validation_passed = result.total_matches > 0
        
        # Create message
        if result.total_matches == 0:
            result.message = "No matching SKU codes found between inventory and price list"
        else:
            message_parts = [f"{result.total_matches} SKU codes matched successfully"]
            
            if result.inventory_only_skus:
                message_parts.append(f"{len(result.inventory_only_skus)} SKU codes in inventory not found in price list")
            
            if result.price_list_only_skus:
                message_parts.append(f"{len(result.price_list_only_skus)} SKU codes in price list not found in inventory")
            
            result.message = ". ".join(message_parts) + "."
        
        logger.info(f"SKU validation completed: {result.message}")
        
    except Exception as e:
        logger.error(f"Error during SKU validation: {str(e)}")
        result.validation_passed = False
        result.message = f"Error during validation: {str(e)}"
    
    return result

def filter_matching_items(
    data_list: List[Dict[str, Any]], 
    matching_skus: List[str], 
    sku_field: str = 'sku_code'
) -> List[Dict[str, Any]]:
    """
    Filter a list of items to only include those with matching SKU codes
    
    Args:
        data_list: List of items to filter
        matching_skus: List of SKU codes to keep
        sku_field: Field name that contains the SKU code
        
    Returns:
        Filtered list containing only items with matching SKU codes
    """
    try:
        return [
            item for item in data_list 
            if item.get(sku_field, '') in matching_skus
        ]
    except Exception as e:
        logger.error(f"Error filtering matching items: {str(e)}")
        return []

def get_validation_summary(result: SkuValidationResult) -> Dict[str, Any]:
    """
    Get a summary dictionary of validation results
    
    Args:
        result: SkuValidationResult object
        
    Returns:
        Dictionary with validation summary
    """
    return {
        "validation_passed": result.validation_passed,
        "total_matches": result.total_matches,
        "total_inventory_skus": result.total_inventory_skus,
        "total_price_list_skus": result.total_price_list_skus,
        "matching_skus": result.matching_skus,
        "inventory_only_skus": result.inventory_only_skus,
        "price_list_only_skus": result.price_list_only_skus,
        "message": result.message
    } 