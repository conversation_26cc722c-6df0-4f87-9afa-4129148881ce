import pandas as pd
from typing import List, Tuple, Dict, Any
import logging
import io
import re
import os
from fastapi import UploadFile
from sqlalchemy.orm import Session

from src.schemas.inventory import InventoryCreate
from src.models.supplier import Supplier

logger = logging.getLogger(__name__)

# Column mapping from excel/csv headers to model fields
INVENTORY_COLUMN_MAPPING = {
    'SKU Code': 'sku_code',
    'Description': 'description',
    'Style Code': 'style_code',
    'Supplier Name': 'supplier_name',  # Keep for backward compatibility in imports
    'Supplier ID': 'supplier_id',
    'Carton': 'carton',
    'Units per carton': 'units_per_carton',
    'Cart Dimensions': 'carton_dimensions',
    'Carton Dimensions': 'carton_dimensions',
    'Cart Dimensions ': 'carton_dimensions',
    'Weight per unit': 'weight_per_unit',
    'Weight per carton': 'weight_per_carton',
    'No. of units per pallet': 'units_per_pallet',
    'Units per pallet': 'units_per_pallet',
    'Pallet weight': 'pallet_weight'
}

def clean_data(data: str) -> str:
    """Clean data from CSV/Excel import"""
    if not data:
        return data
    
    if isinstance(data, str):
        # Remove extra whitespace
        return re.sub(r'\s+', ' ', data).strip()
    
    return data

def process_pandas_df(df: pd.DataFrame, column_mapping: Dict[str, str], db: Session = None) -> Tuple[List[Dict[str, Any]], List[str]]:
    """Process a pandas DataFrame into properly formatted data"""
    result_items = []
    errors = []
    
    # Check for required columns
    required_columns = ['SKU Code', 'Style Code', 'Units per carton', 
                       'Weight per unit', 'Weight per carton']
    # Either Supplier Name or Supplier ID must be present
    if 'Supplier Name' not in df.columns and 'Supplier ID' not in df.columns:
        required_columns.append('Supplier Name or Supplier ID')
    
    missing_columns = [col for col in required_columns if col not in df.columns]
    
    if missing_columns:
        errors.append(f"Missing required columns: {', '.join(missing_columns)}")
        return [], errors
    
    # Cache supplier data if db is provided to avoid repeated lookups
    supplier_cache = {}
    
    # Process each row
    for index, row in df.iterrows():
        # Skip rows with no SKU code (might be header or footer)
        if pd.isna(row['SKU Code']) or not str(row['SKU Code']).strip():
            continue
            
        try:
            item_data = {}
            
            # Map and clean each column
            for excel_col, model_field in column_mapping.items():
                if excel_col in df.columns and not pd.isna(row[excel_col]):
                    item_data[model_field] = clean_data(row[excel_col])
            
            # Ensure required fields are present and have correct types
            if not item_data.get('sku_code'):
                errors.append(f"Row {index+2}: Missing SKU Code")
                continue
                
            if not item_data.get('style_code'):
                errors.append(f"Row {index+2}: Missing Style Code")
                continue
            
            # Handle supplier information - need either supplier_id or supplier_name
            # If supplier_name is provided but not supplier_id, we need to look up the ID
            if db and item_data.get('supplier_name') and not item_data.get('supplier_id'):
                supplier_name = item_data['supplier_name']
                
                # Check cache first
                if supplier_name in supplier_cache:
                    item_data['supplier_id'] = supplier_cache[supplier_name]
                else:
                    # Look up supplier by name
                    supplier = db.query(Supplier).filter(Supplier.name == supplier_name).first()
                    if supplier:
                        item_data['supplier_id'] = supplier.id
                        supplier_cache[supplier_name] = supplier.id
                    else:
                        errors.append(f"Row {index+2}: Supplier '{supplier_name}' not found in the database")
                        continue
            elif not item_data.get('supplier_id') and not item_data.get('supplier_name'):
                errors.append(f"Row {index+2}: Missing Supplier information (either name or ID)")
                continue
            
            # Remove supplier_name from item_data since it's not in the model anymore
            if 'supplier_name' in item_data:
                del item_data['supplier_name']
            
            # Convert supplier_id to integer if it exists
            if 'supplier_id' in item_data and isinstance(item_data['supplier_id'], str):
                try:
                    item_data['supplier_id'] = int(item_data['supplier_id'])
                except ValueError:
                    errors.append(f"Row {index+2}: Invalid Supplier ID format (must be an integer)")
                    continue
            
            # Convert numeric fields
            try:
                if 'units_per_carton' in item_data:
                    item_data['units_per_carton'] = int(float(item_data['units_per_carton']))
                else:
                    errors.append(f"Row {index+2}: Missing Units per carton")
                    continue
                    
                if 'weight_per_unit' in item_data:
                    item_data['weight_per_unit'] = float(item_data['weight_per_unit'])
                else:
                    errors.append(f"Row {index+2}: Missing Weight per unit")
                    continue
                    
                if 'weight_per_carton' in item_data:
                    item_data['weight_per_carton'] = float(item_data['weight_per_carton'])
                else:
                    errors.append(f"Row {index+2}: Missing Weight per carton")
                    continue
                
                if 'carton' in item_data:
                    item_data['carton'] = int(float(item_data['carton']))
                else:
                    item_data['carton'] = 1  # Default value
                
                if 'units_per_pallet' in item_data:
                    item_data['units_per_pallet'] = int(float(item_data['units_per_pallet']))
                
                if 'pallet_weight' in item_data:
                    item_data['pallet_weight'] = float(item_data['pallet_weight'])
                    
            except ValueError as e:
                errors.append(f"Row {index+2}: Error converting numeric values: {str(e)}")
                continue
            
            # Validate weight_per_carton against calculated value
            if abs(item_data['weight_per_carton'] - (item_data['weight_per_unit'] * item_data['units_per_carton'])) > 0.1:
                errors.append(f"Row {index+2}: Weight per carton ({item_data['weight_per_carton']}) doesn't match " +
                             f"weight_per_unit * units_per_carton ({item_data['weight_per_unit'] * item_data['units_per_carton']})")
            
            # Create model instance
            result_items.append(item_data)
            
        except Exception as e:
            errors.append(f"Row {index+2}: Error processing row: {str(e)}")
    
    return result_items, errors

async def process_inventory_file(file: UploadFile, db: Session = None) -> Tuple[List[InventoryCreate], List[str]]:
    """Process an uploaded inventory file (CSV or Excel)"""
    filename = file.filename.lower()
    content = await file.read()
    
    try:
        # Process based on file type
        if filename.endswith('.csv'):
            df = pd.read_csv(io.BytesIO(content))
        elif filename.endswith('.xlsx') or filename.endswith('.xls'):
            df = pd.read_excel(io.BytesIO(content))
        else:
            return [], ["Unsupported file format. Please upload a CSV or Excel file."]
        
        # Process the dataframe
        items_data, errors = process_pandas_df(df, INVENTORY_COLUMN_MAPPING, db)
        
        # Convert dictionary data to Pydantic models
        inventory_items = []
        for item_data in items_data:
            try:
                inventory_item = InventoryCreate(**item_data)
                inventory_items.append(inventory_item)
            except Exception as e:
                errors.append(f"Error creating inventory from data: {str(e)}")
        
        return inventory_items, errors
        
    except Exception as e:
        logger.error(f"Error processing file {filename}: {str(e)}")
        return [], [f"Error processing file: {str(e)}"] 