from src.utils.auth import (
    verify_password, get_password_hash, create_access_token,
    get_current_user, get_current_active_admin
)

__all__ = [
    "verify_password", "get_password_hash", "create_access_token",
    "get_current_user", "get_current_active_admin"
]

"""
Utility functions for the API.
This module provides various helper functions that are used throughout the API.
"""
import json
from typing import Any
from uuid import UUID

class UUIDEncoder(json.JSONEncoder):
    """
    JSON encoder that handles UUID objects by converting them to strings.
    """
    def default(self, obj: Any) -> Any:
        if isinstance(obj, UUID):
            return str(obj)
        return json.JSONEncoder.default(self, obj)

def serialize_uuids(data: Any) -> Any:
    """
    Recursively convert UUID objects to strings to make data JSON serializable.
    
    Args:
        data: The data to serialize
        
    Returns:
        Serialized data with UUIDs as strings
    """
    if isinstance(data, dict):
        return {k: serialize_uuids(v) for k, v in data.items()}
    elif isinstance(data, list):
        return [serialize_uuids(item) for item in data]
    elif isinstance(data, UUID):
        return str(data)
    else:
        return data 