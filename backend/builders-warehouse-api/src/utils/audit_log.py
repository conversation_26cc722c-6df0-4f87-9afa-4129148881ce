import logging
from typing import Any, Dict, Optional, Union
from sqlalchemy.orm import Session

from src.services.auditlog import log_action

logger = logging.getLogger(__name__)

def create_audit_log(
    db: Session,
    user_id: Optional[str] = None,
    action: str = "",
    resource_type: str = "",
    resource_id: Optional[str] = None,
    details: Optional[Dict[str, Any]] = None
) -> None:
    """
    Create an audit log entry wrapper function
    
    Args:
        db: Database session
        user_id: ID of the user performing the action (can be None for system actions)
        action: Action being performed (e.g., "CREATE", "UPDATE", "DELETE", "SYNC", "DOWNLOAD")
        resource_type: Type of resource being acted on (e.g., "REPORTS", "SALES_REPORTS")
        resource_id: ID of the resource being acted on (can be None for bulk actions)
        details: Additional details about the action
    """
    try:
        # Call the existing log_action function from the auditlog service
        log_action(
            db=db, 
            email=user_id,  # Use the user_id as email since that's what the function expects
            event_type=action,
            status="success",
            details={
                "resource_type": resource_type,
                "resource_id": resource_id,
                **(details or {})
            }
        )
        
        logger.info(f"Audit log created: {action} on {resource_type} by user {user_id or 'system'}")
        
    except Exception as e:
        logger.error(f"Error creating audit log: {str(e)}")
        # Don't raise exception - audit logging should not break the main application flow 