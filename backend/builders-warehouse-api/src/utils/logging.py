import os
import logging
import shutil
from datetime import datetime, timedelta
from pathlib import Path
from logging.handlers import TimedRotatingFileHandler
import glob

from src.config import settings

def get_date_folder():
    """Get the folder name for today's date in MM-DD-YYYY format"""
    today = datetime.now()
    return today.strftime("%m-%d-%Y")

def ensure_logs_directory():
    """Ensure the logs directory structure exists"""
    # Create main logs directory if it doesn't exist
    logs_dir = Path(settings.LOGS_DIR)
    logs_dir.mkdir(exist_ok=True)
    
    # Create today's date directory
    date_folder = get_date_folder()
    date_dir = logs_dir / date_folder
    date_dir.mkdir(exist_ok=True)
    
    return date_dir

def clean_old_logs():
    """Delete log folders older than the retention period"""
    logs_dir = Path(settings.LOGS_DIR)
    retention_days = settings.LOG_RETENTION_DAYS
    
    if not logs_dir.exists():
        return
    
    cutoff_date = datetime.now() - timedelta(days=retention_days)
    
    # Loop through date folders
    for date_dir in logs_dir.iterdir():
        if not date_dir.is_dir():
            continue
        
        try:
            # Parse the date from folder name (MM-DD-YYYY)
            folder_date = datetime.strptime(date_dir.name, "%m-%d-%Y")
            
            # Delete if older than retention period
            if folder_date < cutoff_date:
                shutil.rmtree(date_dir)
                logging.info(f"Removed old log directory: {date_dir}")
        except ValueError:
            # Skip folders that don't match our date format
            continue

def setup_logging():
    """Configure application logging with daily folders and rotation"""
    # Clean old logs first
    clean_old_logs()
    
    # Ensure log directory exists
    date_dir = ensure_logs_directory()
    
    # Configure root logger
    log_level = getattr(logging, settings.LOG_LEVEL.upper(), logging.INFO)
    
    # Create logger format
    log_format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
    formatter = logging.Formatter(log_format)
    
    # Create handlers
    # Console handler
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    
    # Application log file
    app_log_path = date_dir / settings.APP_LOG_FILENAME
    file_handler = logging.FileHandler(app_log_path)
    file_handler.setFormatter(formatter)
    
    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(log_level)
    
    # Remove existing handlers to avoid duplicates on reloads
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
        
    # Add handlers
    root_logger.addHandler(console_handler)
    root_logger.addHandler(file_handler)
    
    logging.info(f"Logging configured with level {settings.LOG_LEVEL}")
    logging.info(f"Application logs stored in {app_log_path}")
    
    # Create symlink to latest app log in the root logs directory
    latest_link = Path(settings.LOGS_DIR) / settings.APP_LOG_FILENAME
    if latest_link.exists():
        if latest_link.is_symlink():
            latest_link.unlink()
        else:
            # If it's a regular file (old setup), move it to today's folder
            shutil.move(latest_link, date_dir / f"old_{settings.APP_LOG_FILENAME}")
    
    # Create relative symlink from logs/app.log to logs/MM-DD-YYYY/app.log
    os.symlink(
        os.path.join(get_date_folder(), settings.APP_LOG_FILENAME),
        latest_link
    )

def setup_audit_logging():
    """Configure audit logging with daily folders"""
    # Ensure log directory exists
    date_dir = ensure_logs_directory()
    
    # Set up audit log file in the date directory
    audit_log_filename = os.path.basename(settings.AUDIT_LOG_PATH)
    audit_log_path = date_dir / audit_log_filename
    
    # Create symlink to latest audit log in the root logs directory
    latest_link = Path(settings.LOGS_DIR) / audit_log_filename
    if latest_link.exists():
        if latest_link.is_symlink():
            latest_link.unlink()
        else:
            # If it's a regular file (old setup), move it to today's folder
            shutil.move(latest_link, date_dir / f"old_{audit_log_filename}")
    
    # Create relative symlink from logs/audit.log to logs/MM-DD-YYYY/audit.log
    try:
        os.symlink(
            os.path.join(get_date_folder(), audit_log_filename),
            latest_link
        )
    except FileExistsError:
        logging.debug(f"Symlink {latest_link} already exists.") # Log if link exists
        pass # Ignore error if link already exists
    
    # Update settings to use the new path (assuming mutable settings)
    try:
        settings.AUDIT_LOG_PATH = str(audit_log_path)
    except AttributeError:
        # If settings are immutable, just log the new path
        pass
    
    logging.info(f"Audit logs stored in {audit_log_path}")

# Function to move existing logs to the new structure
def migrate_existing_logs():
    """Migrate existing log files to the new folder structure"""
    # Check for app.log in the root directory
    app_log = Path("app.log")
    audit_log = Path("audit.log")
    
    date_dir = ensure_logs_directory()
    
    if app_log.exists() and app_log.is_file():
        # Move to today's date folder
        shutil.move(app_log, date_dir / settings.APP_LOG_FILENAME)
        logging.info(f"Migrated existing app.log to {date_dir / settings.APP_LOG_FILENAME}")
    
    if audit_log.exists() and audit_log.is_file():
        # Move to today's date folder
        shutil.move(audit_log, date_dir / os.path.basename(settings.AUDIT_LOG_PATH))
        logging.info(f"Migrated existing audit.log to {date_dir / os.path.basename(settings.AUDIT_LOG_PATH)}") 