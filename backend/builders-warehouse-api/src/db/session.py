from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy_utils import database_exists, create_database
import psycopg2
from urllib.parse import urlparse
import logging

from src.core.config import settings

logger = logging.getLogger(__name__)

SQLALCHEMY_DATABASE_URL = settings.database_url

def create_db_if_not_exists(url):
    """Create the database if it doesn't exist."""
    try:
        if not database_exists(url):
            logger.info(f"Database does not exist. Creating database...")
            create_database(url)
            logger.info(f"Database created successfully!")
        else:
            logger.info(f"Database already exists.")
    except Exception as e:
        logger.error(f"Error checking/creating database: {e}")
        # If we can't create the database, we'll attempt to connect to postgres
        # and create it manually
        try:
            parsed_url = urlparse(url)
            username = parsed_url.username
            password = parsed_url.password
            host = parsed_url.hostname
            port = parsed_url.port or 5432
            db_name = parsed_url.path[1:]  # Remove leading slash
            
            # Connect to default postgres database
            conn = psycopg2.connect(
                user=username,
                password=password,
                host=host,
                port=port,
                database="postgres"
            )
            conn.autocommit = True
            cursor = conn.cursor()
            
            # Check if database exists
            cursor.execute(f"SELECT 1 FROM pg_catalog.pg_database WHERE datname = '{db_name}'")
            exists = cursor.fetchone()
            
            if not exists:
                cursor.execute(f"CREATE DATABASE {db_name}")
                logger.info(f"Database {db_name} created using direct PostgreSQL connection")
            
            cursor.close()
            conn.close()
        except Exception as inner_e:
            logger.error(f"Failed to create database directly: {inner_e}")
            raise

# Configure database connection
if SQLALCHEMY_DATABASE_URL.startswith("sqlite"):
    # SQLite specific configuration
    engine = create_engine(
        SQLALCHEMY_DATABASE_URL, 
        connect_args={"check_same_thread": False}  # Only needed for SQLite
    )
else:
    # PostgreSQL configuration
    try:
        # First check if database exists and create it if needed
        create_db_if_not_exists(SQLALCHEMY_DATABASE_URL)
        
        # Then create the engine
        engine = create_engine(SQLALCHEMY_DATABASE_URL)
        logger.info("PostgreSQL database connection established successfully")
    except Exception as e:
        logger.error(f"Error connecting to PostgreSQL database: {e}")
        # Fallback to SQLite if PostgreSQL fails
        logger.warning("Falling back to SQLite database")
        SQLALCHEMY_DATABASE_URL = "sqlite:///./fallback.db"
        engine = create_engine(
            SQLALCHEMY_DATABASE_URL, 
            connect_args={"check_same_thread": False}
        )

# Create session factory
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def get_db():
    """
    Dependency function that yields a SQLAlchemy session
    To be used in FastAPI dependency injection
    """
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close() 