import sys
import os
from sqlalchemy import text, inspect, Column, Integer, String, <PERSON><PERSON><PERSON>, DateTime, Table, MetaData, ARRAY, Text
from sqlalchemy.exc import OperationalError, ProgrammingError
import logging
from sqlalchemy.dialects.postgresql import UUID, JSONB
import uuid
from sqlalchemy.sql import func
from sqlalchemy.orm import Session
from datetime import datetime

from src.db.session import engine

logger = logging.getLogger(__name__)

"""
Database Migration System
=========================

This module provides a code-first approach to database migrations, allowing the application
to automatically create and update database schema when needed.

All migrations are run sequentially in the order specified in run_all_migrations():
1. migrate_add_is_admin_column: Add is_admin column to users table
2. migrate_create_audit_logs_table: Create audit_logs table for tracking changes
3. migrate_create_store_types_table: Create store_types table with default types
4. migrate_create_customers_tables: Create customers module tables (price_lists, customers, invoices)
5. migrate_create_suppliers_table: Create suppliers table with pricing support
6. migrate_create_roles_table: Create roles table and update user table schema
7. migrate_update_inventory_table: Update Inventory table schema to match SQLAlchemy model
8. migrate_create_draft_mails_table: Create the DraftMail table for storing draft emails for purchase orders

Each migration function:
- Checks if the migration needs to be applied
- Creates or alters tables as needed
- Handles both PostgreSQL and SQLite syntax differences
- Reports success or failure through logging

This system complements traditional Alembic migrations and provides more control
for complex schema changes.
"""

def migrate_add_is_admin_column():
    """
    Add is_admin column to the users table
    
    This migration:
    - Checks if the users table exists
    - Checks if the is_admin column already exists
    - Adds the is_admin column with appropriate default based on database type
    """
    logger.info("Running migration for adding is_admin column")
    
    inspector = inspect(engine)
    
    try:
        # Check if the users table exists
        if 'users' not in inspector.get_table_names():
            logger.info("Users table doesn't exist yet. Skipping is_admin column migration.")
            return
            
        # Check if is_admin column exists
        columns = [column['name'] for column in inspector.get_columns('users')]
        if 'is_admin' in columns:
            logger.info("Column 'is_admin' already exists in users table. Skipping migration.")
            return
            
        # Column doesn't exist, add it
        logger.info("Adding is_admin column to users table...")
        
        # Connect to the database
        with engine.connect() as conn:
            with conn.begin():
                # Use the appropriate SQL syntax based on database type
                database_url = str(engine.url)
                if 'sqlite' in database_url:
                    conn.execute(text("ALTER TABLE users ADD COLUMN is_admin BOOLEAN DEFAULT 0"))
                else:  # PostgreSQL
                    conn.execute(text("ALTER TABLE users ADD COLUMN is_admin BOOLEAN DEFAULT FALSE"))
            
            logger.info("Column 'is_admin' added successfully.")
    except Exception as e:
        logger.error(f"Error during migration: {e}")
        raise

def migrate_create_audit_logs_table():
    """
    Create audit_logs table if it doesn't exist
    
    This migration:
    - Checks if audit_logs table exists
    - Creates the table with appropriate schema for the database type
    - Creates indexes for efficient querying
    
    The audit_logs table is used to track all changes to important database records,
    storing the table name, operation type, entity ID, user ID, changes made, and IP address.
    """
    logger.info("Running migration for audit_logs table")
    
    inspector = inspect(engine)
    
    try:
        # Check if audit_logs table exists
        if 'audit_logs' in inspector.get_table_names():
            logger.info("audit_logs table already exists. Skipping creation.")
            return
            
        logger.info("Creating audit_logs table...")
        
        # Connect to the database
        with engine.connect() as conn:
            with conn.begin():
                # Define the audit_logs table
                database_url = str(engine.url)
                if 'sqlite' in database_url:
                    # SQLite specific syntax
                    conn.execute(text("""
                    CREATE TABLE audit_logs (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        table_name VARCHAR(255),
                        operation VARCHAR(50),
                        entity_id VARCHAR(255),
                        user_id VARCHAR(36),
                        changes TEXT,
                        ip_address VARCHAR(50)
                    )
                    """))
                    
                    # Create indexes
                    conn.execute(text("CREATE INDEX ix_audit_logs_table_name ON audit_logs (table_name)"))
                    conn.execute(text("CREATE INDEX ix_audit_logs_operation ON audit_logs (operation)"))
                    conn.execute(text("CREATE INDEX ix_audit_logs_user_id ON audit_logs (user_id)"))
                else:
                    # PostgreSQL syntax
                    conn.execute(text("""
                    CREATE TABLE audit_logs (
                        id SERIAL PRIMARY KEY,
                        timestamp TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                        table_name VARCHAR(255),
                        operation VARCHAR(50),
                        entity_id VARCHAR(255),
                        user_id UUID,
                        changes JSONB,
                        ip_address VARCHAR(50)
                    )
                    """))
                    
                    # Create indexes
                    conn.execute(text("CREATE INDEX ix_audit_logs_table_name ON audit_logs (table_name)"))
                    conn.execute(text("CREATE INDEX ix_audit_logs_operation ON audit_logs (operation)"))
                    conn.execute(text("CREATE INDEX ix_audit_logs_user_id ON audit_logs (user_id)"))
            
            logger.info("audit_logs table created successfully.")
    except Exception as e:
        logger.error(f"Error during audit_logs table creation: {e}")
        raise

def migrate_create_customers_tables():
    """
    Create customers module tables:
    - price_lists: Stores pricing tiers for customers
    - customers: Stores customer information with relation to price_lists
    - invoices: Stores invoice records linked to customers
    
    This migration:
    - Checks if tables already exist
    - Creates missing tables with appropriate schema
    - Creates indexes for efficient querying
    - Attempts to migrate data if existing tables need schema changes
    """
    logger.info("Running migration for customers module tables")
    
    inspector = inspect(engine)
    database_url = str(engine.url)
    is_sqlite = 'sqlite' in database_url
    
    try:
        # Check if tables exist
        existing_tables = inspector.get_table_names()
        tables_to_create = []
        
        if 'price_lists' not in existing_tables:
            tables_to_create.append('price_lists')
        if 'customers' in existing_tables:
            # Check if we need to update the existing customers table
            columns = {column['name']: column for column in inspector.get_columns('customers')}
            if 'company_name' not in columns or 'price_list_id' not in columns:
                # Need to drop and recreate
                tables_to_create.append('customers')
                logger.info("Existing customers table needs to be updated. Will attempt to migrate data.")
        else:
            tables_to_create.append('customers')
        if 'invoices' not in existing_tables:
            tables_to_create.append('invoices')
            
        if not tables_to_create:
            logger.info("All customers module tables already exist. Skipping creation.")
            return
            
        logger.info(f"Creating tables: {', '.join(tables_to_create)}")
        
        # Connect to the database
        with engine.connect() as conn:
            with conn.begin():
                # 1. Create price_lists table if needed
                if 'price_lists' in tables_to_create:
                    if is_sqlite:
                        conn.execute(text("""
                        CREATE TABLE price_lists (
                            id TEXT PRIMARY KEY,
                            name VARCHAR(255) NOT NULL UNIQUE,
                            currency VARCHAR(3) DEFAULT 'AUD',
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                        )
                        """))
                    else:  # PostgreSQL
                        conn.execute(text("""
                        CREATE TABLE price_lists (
                            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                            name VARCHAR(255) NOT NULL UNIQUE,
                            currency VARCHAR(3) DEFAULT 'AUD',
                            created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                            updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
                        )
                        """))
                    
                    conn.execute(text("CREATE INDEX ix_price_lists_name ON price_lists (name)"))
                    logger.info("price_lists table created successfully")
                
                # 2. Create/Recreate customers table if needed
                if 'customers' in tables_to_create:
                    # If table exists, back up data first
                    if 'customers' in existing_tables:
                        logger.info("Backing up existing customers data")
                        conn.execute(text("CREATE TABLE customers_backup AS SELECT * FROM customers"))
                        conn.execute(text("DROP TABLE customers"))
                    
                    # Create new customers table
                    if is_sqlite:
                        conn.execute(text("""
                        CREATE TABLE customers (
                            id TEXT PRIMARY KEY,
                            company_name VARCHAR(255) NOT NULL UNIQUE,
                            contact_person VARCHAR(255),
                            email VARCHAR(255) NOT NULL UNIQUE,
                            phone VARCHAR(20),
                            is_account BOOLEAN DEFAULT 0,
                            billing_address TEXT,
                            billing_suburb VARCHAR(255),
                            billing_postcode VARCHAR(20),
                            price_list_id TEXT,
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            FOREIGN KEY (price_list_id) REFERENCES price_lists (id)
                        )
                        """))
                    else:  # PostgreSQL
                        conn.execute(text("""
                        CREATE TABLE customers (
                            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                            company_name VARCHAR(255) NOT NULL UNIQUE,
                            contact_person VARCHAR(255),
                            email VARCHAR(255) NOT NULL UNIQUE,
                            phone VARCHAR(20),
                            is_account BOOLEAN DEFAULT FALSE,
                            billing_address TEXT,
                            billing_suburb VARCHAR(255),
                            billing_postcode VARCHAR(20),
                            price_list_id UUID,
                            created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                            updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                            FOREIGN KEY (price_list_id) REFERENCES price_lists (id)
                        )
                        """))
                    
                    conn.execute(text("CREATE INDEX ix_customers_company_name ON customers (company_name)"))
                    conn.execute(text("CREATE INDEX ix_customers_email ON customers (email)"))
                    conn.execute(text("CREATE INDEX ix_customers_phone ON customers (phone)"))
                    logger.info("customers table created successfully")
                    
                    # Attempt to migrate data if backup exists
                    if 'customers' in existing_tables:
                        try:
                            logger.info("Migrating customer data from backup")
                            # This is simplified - in a real scenario, you'd need to map fields correctly
                            conn.execute(text("""
                            INSERT INTO customers (id, company_name, contact_person, email, phone, billing_address)
                            SELECT id, name, NULL, email, phone, address
                            FROM customers_backup
                            """))
                            logger.info("Customer data migration completed")
                        except Exception as e:
                            logger.error(f"Error migrating customer data: {e}")
                            logger.info("Backup table 'customers_backup' preserved for manual data recovery")
                
                # 3. Create invoices table if needed
                if 'invoices' in tables_to_create:
                    if is_sqlite:
                        conn.execute(text("""
                        CREATE TABLE invoices (
                            id TEXT PRIMARY KEY,
                            invoice_no VARCHAR(50) NOT NULL UNIQUE,
                            store_type VARCHAR(50) NOT NULL,
                            date DATE NOT NULL,
                            total_gst DECIMAL(10, 2) NOT NULL,
                            grand_total DECIMAL(10, 2) NOT NULL,
                            notes TEXT,
                            customer_id TEXT NOT NULL,
                            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                            FOREIGN KEY (customer_id) REFERENCES customers (id)
                        )
                        """))
                    else:  # PostgreSQL
                        conn.execute(text("""
                        CREATE TABLE invoices (
                            id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
                            invoice_no VARCHAR(50) NOT NULL UNIQUE,
                            store_type VARCHAR(50) NOT NULL,
                            date DATE NOT NULL,
                            total_gst DECIMAL(10, 2) NOT NULL,
                            grand_total DECIMAL(10, 2) NOT NULL,
                            notes TEXT,
                            customer_id UUID NOT NULL,
                            created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                            updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                            FOREIGN KEY (customer_id) REFERENCES customers (id)
                        )
                        """))
                    
                    conn.execute(text("CREATE INDEX ix_invoices_invoice_no ON invoices (invoice_no)"))
                    conn.execute(text("CREATE INDEX ix_invoices_date ON invoices (date)"))
                    conn.execute(text("CREATE INDEX ix_invoices_customer_id ON invoices (customer_id)"))
                    logger.info("invoices table created successfully")
            
            logger.info("All customers module tables created successfully")
    except Exception as e:
        logger.error(f"Error during customers tables creation: {e}")
        raise

def migrate_create_suppliers_table():
    """
    Create suppliers table if it doesn't exist
    
    This migration:
    - Checks if suppliers table exists
    - Adds any missing columns to existing table
    - Creates the table with appropriate schema if it doesn't exist
    - Creates indexes for efficient querying
    
    The suppliers table stores information about suppliers including their
    price lists (as JSON/JSONB).
    """
    logger.info("Running migration for suppliers table")
    
    inspector = inspect(engine)
    database_url = str(engine.url)
    is_sqlite = 'sqlite' in database_url
    
    try:
        # Check if suppliers table exists
        if 'Supplier' in inspector.get_table_names():
            logger.info("Supplier table already exists. Checking for column updates...")
            
            # Check existing columns
            columns = {column['name']: column for column in inspector.get_columns('Supplier')}
            missing_columns = []
            
            if 'price_list' not in columns:
                missing_columns.append('price_list')
                
            if not missing_columns:
                logger.info("Supplier table is up to date. No changes needed.")
                return
                
            logger.info(f"Adding missing columns to Supplier table: {', '.join(missing_columns)}")
            
            # Connect to the database to add missing columns
            with engine.connect() as conn:
                with conn.begin():
                    for column in missing_columns:
                        if column == 'price_list':
                            if is_sqlite:
                                conn.execute(text("ALTER TABLE Supplier ADD COLUMN price_list TEXT"))
                            else:  # PostgreSQL
                                conn.execute(text("ALTER TABLE Supplier ADD COLUMN price_list JSONB"))
                
            logger.info("Successfully updated Supplier table.")
            return
            
        logger.info("Creating Supplier table...")
        
        # Connect to the database
        with engine.connect() as conn:
            with conn.begin():
                # Define the suppliers table based on database type
                if is_sqlite:
                    conn.execute(text("""
                    CREATE TABLE Supplier (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        supplier_name VARCHAR(255) NOT NULL UNIQUE,
                        phone_no VARCHAR(50) NOT NULL UNIQUE,
                        email VARCHAR(255) NOT NULL UNIQUE,
                        address TEXT,
                        price_list TEXT,
                        is_active BOOLEAN DEFAULT 1,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        created_by INTEGER,
                        updated_by INTEGER
                    )
                    """))
                    
                    # Create indexes
                    conn.execute(text("CREATE INDEX ix_suppliers_supplier_name ON Supplier (supplier_name)"))
                    conn.execute(text("CREATE INDEX ix_suppliers_email ON Supplier (email)"))
                    conn.execute(text("CREATE INDEX ix_suppliers_phone_no ON Supplier (phone_no)"))
                else:  # PostgreSQL
                    conn.execute(text("""
                    CREATE TABLE "Supplier" (
                        id SERIAL PRIMARY KEY,
                        supplier_name VARCHAR(255) NOT NULL UNIQUE,
                        phone_no VARCHAR(50) NOT NULL UNIQUE,
                        email VARCHAR(255) NOT NULL UNIQUE,
                        address TEXT,
                        price_list JSONB,
                        is_active BOOLEAN DEFAULT TRUE,
                        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                        created_by INTEGER,
                        updated_by INTEGER
                    )
                    """))
                    
                    # Create indexes
                    conn.execute(text("CREATE INDEX ix_suppliers_supplier_name ON \"Supplier\" (supplier_name)"))
                    conn.execute(text("CREATE INDEX ix_suppliers_email ON \"Supplier\" (email)"))
                    conn.execute(text("CREATE INDEX ix_suppliers_phone_no ON \"Supplier\" (phone_no)"))
            
            logger.info("Supplier table created successfully.")
    except Exception as e:
        logger.error(f"Error during Supplier table creation: {e}")
        raise

def migrate_create_store_types_table():
    """
    Create store_types table if it doesn't exist
    
    This migration:
    - Checks if store_types table exists
    - Creates the table with appropriate schema
    - Creates indexes for efficient querying
    - Inserts default store types (Cranbourne, Sale)
    
    Store types are used to categorize users by their store location.
    """
    logger.info("Running migration for store_types table")
    
    inspector = inspect(engine)
    database_url = str(engine.url)
    is_sqlite = 'sqlite' in database_url
    
    try:
        # Check if store_types table exists
        if 'store_types' in inspector.get_table_names():
            logger.info("store_types table already exists. Skipping creation.")
            return
            
        logger.info("Creating store_types table...")
        
        # Connect to the database
        with engine.connect() as conn:
            with conn.begin():
                # Define the store_types table based on database type
                if is_sqlite:
                    conn.execute(text("""
                    CREATE TABLE store_types (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        name VARCHAR(255) NOT NULL UNIQUE,
                        description TEXT,
                        is_active BOOLEAN DEFAULT 1,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                    """))
                    
                    # Create indexes
                    conn.execute(text("CREATE INDEX ix_store_types_name ON store_types (name)"))
                else:  # PostgreSQL
                    conn.execute(text("""
                    CREATE TABLE store_types (
                        id SERIAL PRIMARY KEY,
                        name VARCHAR(255) NOT NULL UNIQUE,
                        description TEXT,
                        is_active BOOLEAN DEFAULT TRUE,
                        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
                    )
                    """))
                    
                    # Create indexes
                    conn.execute(text("CREATE INDEX ix_store_types_name ON store_types (name)"))
                
                # Insert default store types
                conn.execute(text("""
                INSERT INTO store_types (name, description) VALUES
                ('Cranbourne', 'Cranbourne Store'),
                ('Sale', 'Sale Store')
                """))
            
            logger.info("store_types table created successfully.")
    except Exception as e:
        logger.error(f"Error during store_types table creation: {e}")
        raise

def migrate_create_roles_table():
    """
    Create roles table and update users table to use role_id
    
    This migration:
    - Checks if roles table exists
    - Creates the roles table with appropriate schema
    - Inserts default roles (admin, manager, staff) 
    - Adds role_id column to users table
    - Creates foreign key constraint between users and roles
    - Migrates existing role enum values to role_id references
    - Makes role_id not nullable
    - Drops the old role column in PostgreSQL (SQLite has limitations for this)
    
    This migration updates the system to use a proper role table instead
    of a string enum, allowing for more flexible role management.
    
    Note: For more complex scenarios or if this migration fails, use the 
    dedicated migration script in scripts/migrate_roles.py
    """
    logger.info("Running migration for roles table")
    
    inspector = inspect(engine)
    database_url = str(engine.url)
    is_sqlite = 'sqlite' in database_url
    
    try:
        # Check if roles table exists
        if 'roles' in inspector.get_table_names():
            logger.info("roles table already exists. Skipping creation.")
            return
            
        # Check if users table has role_id column
        if 'users' in inspector.get_table_names():
            columns = [column['name'] for column in inspector.get_columns('users')]
            if 'role_id' in columns and 'role' not in columns:
                logger.info("User table already has role_id column. Skipping migration.")
                return
        
        logger.info("Creating roles table and updating users table...")
        
        # Connect to the database
        with engine.connect() as conn:
            with conn.begin():
                # 1. Create roles table
                if is_sqlite:
                    conn.execute(text("""
                    CREATE TABLE roles (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        name VARCHAR(255) NOT NULL UNIQUE,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                    """))
                else:  # PostgreSQL
                    conn.execute(text("""
                    CREATE TABLE roles (
                        id SERIAL PRIMARY KEY,
                        name VARCHAR(255) NOT NULL UNIQUE,
                        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP WITH TIME ZONE
                    )
                    """))
                
                # Create indexes
                conn.execute(text("CREATE INDEX ix_roles_id ON roles (id)"))
                conn.execute(text("CREATE INDEX ix_roles_name ON roles (name)"))
                
                # 2. Insert default roles
                conn.execute(text("INSERT INTO roles (name) VALUES ('admin')"))
                conn.execute(text("INSERT INTO roles (name) VALUES ('manager')"))
                conn.execute(text("INSERT INTO roles (name) VALUES ('staff')"))
                
                # 3. Add role_id column to users table
                if is_sqlite:
                    conn.execute(text("ALTER TABLE users ADD COLUMN role_id INTEGER"))
                else:
                    conn.execute(text("ALTER TABLE users ADD COLUMN role_id INTEGER"))
                
                # 4. Create foreign key reference
                if not is_sqlite:  # SQLite doesn't support ALTER TABLE ADD CONSTRAINT
                    conn.execute(text("""
                    ALTER TABLE users 
                    ADD CONSTRAINT fk_users_role_id_roles 
                    FOREIGN KEY (role_id) REFERENCES roles(id)
                    """))
                
                # 5. Migrate existing role strings to role_id references
                conn.execute(text("""
                UPDATE users SET role_id = 
                    CASE 
                        WHEN role = 'admin' THEN 1
                        WHEN role = 'manager' THEN 2
                        WHEN role = 'staff' THEN 3
                        ELSE 3
                    END
                WHERE role IS NOT NULL
                """))
                
                # 6. Make role_id not nullable
                if is_sqlite:
                    # SQLite doesn't support ALTER COLUMN, so we would need a more complex migration
                    # For simplicity, we'll skip this step for SQLite
                    pass
                else:
                    conn.execute(text("ALTER TABLE users ALTER COLUMN role_id SET NOT NULL"))
                
                # 7. Drop the old role column
                if is_sqlite:
                    # SQLite doesn't support DROP COLUMN in older versions
                    # We'd need to create a new table and migrate data
                    # For simplicity, we'll skip this step for SQLite
                    pass
                else:
                    conn.execute(text("ALTER TABLE users DROP COLUMN role"))
        
        logger.info("Roles migration completed successfully.")
    except Exception as e:
        logger.error(f"Error during roles migration: {e}")
        raise

def migrate_update_inventory_table():
    """
    Update Inventory table schema to match SQLAlchemy model
    
    This migration:
    - Checks if Inventory table exists
    - Adds any missing columns to match the SQLAlchemy model
    - Creates indexes for common query fields
    
    This ensures that the Inventory table structure matches the expected
    model definition in SQLAlchemy, avoiding errors from column mismatches.
    """
    logger.info("Running migration for Inventory table")
    
    inspector = inspect(engine)
    database_url = str(engine.url)
    is_sqlite = 'sqlite' in database_url
    
    try:
        # Check if Inventory table exists
        if 'Inventory' in inspector.get_table_names():
            logger.info("Inventory table exists. Checking for column updates...")
            
            # Get existing columns
            existing_columns = [column['name'].lower() for column in inspector.get_columns('Inventory')]
            
            # Columns required by the model
            required_columns = {
                'sku_code': 'VARCHAR(255) UNIQUE',
                'style_code': 'VARCHAR(255)',
                'supplier_name': 'VARCHAR(255)',
                'carton': 'INTEGER DEFAULT 1',
                'units_per_carton': 'INTEGER',
                'carton_dimensions': 'VARCHAR(255)',
                'weight_per_unit': 'FLOAT',
                'weight_per_carton': 'FLOAT',
                'units_per_pallet': 'INTEGER',
                'pallet_weight': 'FLOAT',
                'created_by': 'UUID',
                'updated_by': 'UUID'
            }
            
            # Find missing columns
            missing_columns = []
            for col_name in required_columns:
                if col_name.lower() not in existing_columns:
                    missing_columns.append(col_name)
            
            if not missing_columns:
                logger.info("Inventory table is up to date. No changes needed.")
                return
            
            logger.info(f"Adding missing columns to Inventory table: {', '.join(missing_columns)}")
            
            # Connect to the database to add missing columns
            with engine.connect() as conn:
                with conn.begin():
                    for column in missing_columns:
                        col_type = required_columns[column]
                        # SQLite doesn't support adding NOT NULL columns without a default
                        if is_sqlite:
                            if 'FLOAT' in col_type:
                                conn.execute(text(f"ALTER TABLE Inventory ADD COLUMN {column} REAL"))
                            elif 'INTEGER' in col_type:
                                conn.execute(text(f"ALTER TABLE Inventory ADD COLUMN {column} INTEGER"))
                            else:
                                conn.execute(text(f"ALTER TABLE Inventory ADD COLUMN {column} TEXT"))
                        else:  # PostgreSQL
                            # If it might be NOT NULL, add without constraint first
                            try:
                                conn.execute(text(f'ALTER TABLE "Inventory" ADD COLUMN {column} {col_type}'))
                            except Exception as e:
                                logger.warning(f"Error adding column with type: {e}")
                                # Try without UNIQUE constraint
                                simple_type = col_type.replace(' UNIQUE', '')
                                try:
                                    conn.execute(text(f'ALTER TABLE "Inventory" ADD COLUMN {column} {simple_type}'))
                                except Exception as e2:
                                    logger.error(f"Failed to add column {column}: {e2}")
                
                    # Add indexes if needed
                    index_columns = ['sku_code', 'style_code', 'supplier_name']
                    for idx_col in index_columns:
                        if idx_col.lower() in existing_columns or idx_col in missing_columns:
                            try:
                                idx_name = f"idx_inventory_{idx_col}"
                                conn.execute(text(f'CREATE INDEX IF NOT EXISTS {idx_name} ON "Inventory" ({idx_col})'))
                            except Exception as e:
                                logger.warning(f"Could not create index on {idx_col}: {e}")
            
            logger.info("Successfully updated Inventory table.")
            return
        
        # Inventory table doesn't exist, create it
        logger.info("Inventory table doesn't exist. Creating it...")
        
        # Connect to the database
        with engine.connect() as conn:
            with conn.begin():
                # Define the Inventory table based on database type
                if is_sqlite:
                    conn.execute(text("""
                    CREATE TABLE Inventory (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        sku_code TEXT UNIQUE NOT NULL,
                        style_code TEXT NOT NULL,
                        supplier_name TEXT NOT NULL,
                        carton INTEGER DEFAULT 1,
                        units_per_carton INTEGER NOT NULL,
                        carton_dimensions TEXT,
                        weight_per_unit REAL NOT NULL,
                        weight_per_carton REAL NOT NULL,
                        units_per_pallet INTEGER,
                        pallet_weight REAL,
                        item_name TEXT,
                        description TEXT,
                        quantity INTEGER DEFAULT 0,
                        cost_price REAL DEFAULT 0,
                        sell_price REAL DEFAULT 0,
                        created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                        created_by TEXT,
                        updated_by TEXT
                    )
                    """))
                    
                    # Create indexes
                    conn.execute(text("CREATE INDEX idx_inventory_sku_code ON Inventory (sku_code)"))
                    conn.execute(text("CREATE INDEX idx_inventory_style_code ON Inventory (style_code)"))
                    conn.execute(text("CREATE INDEX idx_inventory_supplier_name ON Inventory (supplier_name)"))
                else:  # PostgreSQL
                    conn.execute(text("""
                    CREATE TABLE "Inventory" (
                        id SERIAL PRIMARY KEY,
                        sku_code VARCHAR(255) UNIQUE NOT NULL,
                        style_code VARCHAR(255) NOT NULL,
                        supplier_name VARCHAR(255) NOT NULL,
                        carton INTEGER DEFAULT 1,
                        units_per_carton INTEGER NOT NULL,
                        carton_dimensions VARCHAR(255),
                        weight_per_unit FLOAT NOT NULL,
                        weight_per_carton FLOAT NOT NULL,
                        units_per_pallet INTEGER,
                        pallet_weight FLOAT,
                        item_name VARCHAR(255),
                        description TEXT,
                        quantity INTEGER DEFAULT 0,
                        cost_price FLOAT DEFAULT 0,
                        sell_price FLOAT DEFAULT 0,
                        created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
                        created_by UUID,
                        updated_by UUID
                    )
                    """))
                    
                    # Create indexes
                    conn.execute(text('CREATE INDEX idx_inventory_sku_code ON "Inventory" (sku_code)'))
                    conn.execute(text('CREATE INDEX idx_inventory_style_code ON "Inventory" (style_code)'))
                    conn.execute(text('CREATE INDEX idx_inventory_supplier_name ON "Inventory" (supplier_name)'))
            
            logger.info("Inventory table created successfully.")
    except Exception as e:
        logger.error(f"Error during Inventory table creation: {e}")
        raise

def migrate_create_draft_mails_table():
    """
    Create the DraftMail table for storing draft emails for purchase orders.
    
    This migration creates the table if it doesn't exist, otherwise ensures
    all required columns are present.
    """
    
    logger.info("Starting DraftMail table migration...")
    
    try:
        # Determine database type
        from src.database import engine
        is_sqlite = engine.url.drivername == 'sqlite'
        
        # Check if the table already exists
        inspector = inspect(engine)
        exists = "DraftMail" in inspector.get_table_names()
        
        if exists:
            logger.info("DraftMail table already exists. Checking for any missing columns...")
            
            # Get existing columns
            existing_columns = [col['name'] for col in inspector.get_columns("DraftMail")]
            
            # Define required columns with their types
            required_columns = {
                "id": "SERIAL PRIMARY KEY" if not is_sqlite else "INTEGER PRIMARY KEY AUTOINCREMENT",
                "invoice_id": "INTEGER NOT NULL REFERENCES \"Invoice\"(id)",
                "po_id": "INTEGER NOT NULL REFERENCES \"PurchaseOrder\"(id)",
                "subject": "VARCHAR(255) NOT NULL", 
                "body": "TEXT NOT NULL",
                "ready_to_send": "BOOLEAN NOT NULL DEFAULT FALSE",
                "sent": "BOOLEAN NOT NULL DEFAULT FALSE",
                "sent_at": "TIMESTAMP WITH TIME ZONE" if not is_sqlite else "TIMESTAMP",
                "created_by": "UUID NOT NULL REFERENCES \"User\"(id)",
                "created_at": "TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP" if not is_sqlite else "TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP",
                "updated_at": "TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP" if not is_sqlite else "TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP"
            }
            
            # Identify missing columns
            missing_columns = {col: col_type for col, col_type in required_columns.items() 
                             if col.lower() not in [col.lower() for col in existing_columns]}
            
            if missing_columns:
                logger.info(f"Found missing columns in DraftMail table: {', '.join(missing_columns.keys())}")
                
                # Add missing columns
                with engine.connect() as conn:
                    for column, col_type in missing_columns.items():
                        try:
                            conn.execute(text(f'ALTER TABLE "DraftMail" ADD COLUMN {column} {col_type}'))
                            logger.info(f"Added column {column} to DraftMail table")
                        except Exception as e:
                            logger.warning(f"Error adding column {column}: {e}")
                            # If foreign key constraint is the issue, try without it
                            if "REFERENCES" in col_type:
                                simple_type = col_type.split("REFERENCES")[0].strip()
                                try:
                                    conn.execute(text(f'ALTER TABLE "DraftMail" ADD COLUMN {column} {simple_type}'))
                                    logger.info(f"Added column {column} without foreign key constraint")
                                except Exception as e2:
                                    logger.error(f"Failed to add column {column}: {e2}")
                
                logger.info("DraftMail table update completed.")
            else:
                logger.info("DraftMail table has all required columns.")
            
            return
        
        # DraftMail table doesn't exist, create it
        logger.info("DraftMail table doesn't exist. Creating it...")
        
        # Connect to the database
        with engine.connect() as conn:
            with conn.begin():
                # Define the DraftMail table based on database type
                if is_sqlite:
                    conn.execute(text("""
                    CREATE TABLE DraftMail (
                        id INTEGER PRIMARY KEY AUTOINCREMENT,
                        invoice_id INTEGER NOT NULL,
                        po_id INTEGER NOT NULL,
                        subject TEXT NOT NULL,
                        body TEXT NOT NULL,
                        ready_to_send BOOLEAN NOT NULL DEFAULT 0,
                        sent BOOLEAN NOT NULL DEFAULT 0,
                        sent_at TIMESTAMP,
                        created_by TEXT NOT NULL,
                        created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
                        FOREIGN KEY (invoice_id) REFERENCES Invoice(id),
                        FOREIGN KEY (po_id) REFERENCES PurchaseOrder(id),
                        FOREIGN KEY (created_by) REFERENCES User(id)
                    )
                    """))
                else:  # PostgreSQL
                    conn.execute(text("""
                    CREATE TABLE "DraftMail" (
                        id SERIAL PRIMARY KEY,
                        invoice_id INTEGER NOT NULL REFERENCES "Invoice"(id),
                        po_id INTEGER NOT NULL REFERENCES "PurchaseOrder"(id),
                        subject VARCHAR(255) NOT NULL,
                        body TEXT NOT NULL,
                        ready_to_send BOOLEAN NOT NULL DEFAULT FALSE,
                        sent BOOLEAN NOT NULL DEFAULT FALSE,
                        sent_at TIMESTAMP WITH TIME ZONE,
                        created_by UUID NOT NULL REFERENCES "User"(id),
                        created_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP,
                        updated_at TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT CURRENT_TIMESTAMP
                    )
                    """))
                    
                    # Create indexes
                    conn.execute(text('CREATE INDEX idx_draft_mail_invoice_id ON "DraftMail" (invoice_id)'))
                    conn.execute(text('CREATE INDEX idx_draft_mail_po_id ON "DraftMail" (po_id)'))
            
            logger.info("DraftMail table created successfully.")
    except Exception as e:
        logger.error(f"Error during DraftMail table creation: {e}")
        raise

def run_all_migrations():
    """
    Run all database migrations in the correct order
    
    Migrations are executed sequentially to ensure dependencies are met.
    Each migration handles its own error cases and reports success or failure.
    
    This function is called during application startup to ensure the database 
    schema is up to date before the application starts handling requests.
    """
    logger.info("Starting database migrations...")
    
    try:
        # Run migrations in order of dependencies
        migrate_add_is_admin_column()
        migrate_create_audit_logs_table()
        migrate_create_store_types_table()
        migrate_create_customers_tables() 
        migrate_create_suppliers_table()
        migrate_create_roles_table()
        migrate_update_inventory_table()
        migrate_create_draft_mails_table()  # Add draft_mails migration
        
        logger.info("All migrations completed successfully")
    except Exception as e:
        logger.error(f"Error running migrations: {e}")
        raise

def run_migrations(db: Session):
    """Run all database migrations"""
    try:
        # Run migrations in sequence
        add_is_admin_column(db)
        create_audit_logs_table(db)
        create_store_types_table(db)
        create_customers_module_tables(db)
        create_suppliers_table(db)
        update_suppliers_table(db)
        create_roles_table(db)
        add_linked_quote_id_to_invoice(db)  # Add the new migration
        
        logger.info("All migrations completed successfully")
    except Exception as e:
        logger.error(f"Error running migrations: {str(e)}")
        raise e

def add_is_admin_column(db: Session):
    """Add is_admin column to users table if it doesn't exist"""
    try:
        logger.info("Running migration for adding is_admin column")
        
        # Check if users table exists
        inspector = inspect(db.bind)
        if not inspector.has_table("users"):
            logger.info("Users table doesn't exist yet. Skipping is_admin column migration.")
            return
        
        # Check if the column already exists
        columns = inspector.get_columns("users")
        column_names = [column["name"] for column in columns]
        
        if "is_admin" not in column_names:
            # Add the column
            db.execute(text("ALTER TABLE users ADD COLUMN is_admin BOOLEAN DEFAULT FALSE"))
            db.commit()
            logger.info("Successfully added is_admin column to users table")
        else:
            logger.info("is_admin column already exists in users table. Skipping.")
            
    except Exception as e:
        db.rollback()
        logger.error(f"Error during is_admin column migration: {str(e)}")
        # Continue with other migrations

def create_audit_logs_table(db: Session):
    """Create audit_logs table if it doesn't exist"""
    try:
        logger.info("Running migration for audit_logs table")
        
        # Check if the table already exists
        inspector = inspect(db.bind)
        if inspector.has_table("audit_logs"):
            logger.info("audit_logs table already exists. Skipping creation.")
            return
            
        # Create the table
        db.execute(text("""
        CREATE TABLE audit_logs (
            id SERIAL PRIMARY KEY,
            action VARCHAR(50) NOT NULL,
            entity_type VARCHAR(50) NOT NULL,
            entity_id VARCHAR(255) NOT NULL,
            user_id VARCHAR(255),
            username VARCHAR(255),
            before_state JSONB,
            after_state JSONB,
            ip_address VARCHAR(50),
            timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            success BOOLEAN DEFAULT TRUE,
            details TEXT
        )
        """))
        
        # Create indexes
        db.execute(text("CREATE INDEX idx_audit_logs_entity ON audit_logs(entity_type, entity_id)"))
        db.execute(text("CREATE INDEX idx_audit_logs_user ON audit_logs(user_id)"))
        db.execute(text("CREATE INDEX idx_audit_logs_timestamp ON audit_logs(timestamp)"))
        
        db.commit()
        logger.info("Successfully created audit_logs table")
        
    except Exception as e:
        db.rollback()
        logger.error(f"Error during audit_logs table creation: {str(e)}")
        # Continue with other migrations

def create_store_types_table(db: Session):
    """Create store_types table if it doesn't exist"""
    try:
        logger.info("Running migration for store_types table")
        
        # Check if the table already exists
        inspector = inspect(db.bind)
        if inspector.has_table("store_types"):
            logger.info("store_types table already exists. Skipping creation.")
            return
            
        # Create the table
        db.execute(text("""
        CREATE TABLE store_types (
            id SERIAL PRIMARY KEY,
            name VARCHAR(50) NOT NULL UNIQUE,
            description TEXT,
            is_active BOOLEAN DEFAULT TRUE,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """))
        
        # Insert default store types
        db.execute(text("""
        INSERT INTO store_types (name, description) 
        VALUES 
            ('retail', 'Retail store type for selling directly to consumers'),
            ('trade', 'Trade store type for selling to businesses and contractors')
        """))
        
        db.commit()
        logger.info("Successfully created store_types table with default values")
        
    except Exception as e:
        db.rollback()
        logger.error(f"Error during store_types table creation: {str(e)}")
        # Continue with other migrations

def create_customers_module_tables(db: Session):
    """Create all tables related to the customers module"""
    try:
        logger.info("Running migration for customers module tables")
        
        # Check if the Customer table already exists
        inspector = inspect(db.bind)
        if inspector.has_table("Customer") and inspector.has_table("Address") and inspector.has_table("ContactInfo"):
            logger.info("All customers module tables already exist. Skipping creation.")
            return
        
        # Implement customer module table creation if needed
        # This is just a placeholder - implement the actual table creation based on your schema
        
        logger.info("Customers module tables created")
        
    except Exception as e:
        db.rollback()
        logger.error(f"Error during customers module tables creation: {str(e)}")
        # Continue with other migrations

def create_suppliers_table(db: Session):
    """Create suppliers table if it doesn't exist"""
    try:
        logger.info("Running migration for suppliers table")
        
        # Check if the table already exists (case-insensitive)
        inspector = inspect(db.bind)
        if inspector.has_table("Supplier"):
            logger.info("Supplier table already exists. Checking for column updates...")
            return
            
        # Create the table if it doesn't exist
        db.execute(text("""
        CREATE TABLE "Supplier" (
            id SERIAL PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            contact_person VARCHAR(255),
            email VARCHAR(255),
            phone VARCHAR(50),
            address TEXT,
            city VARCHAR(100),
            state VARCHAR(100),
            postal_code VARCHAR(20),
            country VARCHAR(100),
            tax_id VARCHAR(50),
            payment_terms VARCHAR(100),
            notes TEXT,
            website VARCHAR(255),
            price_list JSONB,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """))
        
        # Create indexes
        db.execute(text('CREATE INDEX idx_supplier_name ON "Supplier"(name)'))
        
        db.commit()
        logger.info("Successfully created Supplier table")
        
    except Exception as e:
        db.rollback()
        logger.error(f"Error during Supplier table creation: {str(e)}")
        # Continue with other migrations

def update_suppliers_table(db: Session):
    """Update suppliers table structure if needed"""
    try:
        logger.info("Running migration to update suppliers table")
        
        # Check if the table exists
        inspector = inspect(db.bind)
        if not inspector.has_table("Supplier"):
            logger.info("Supplier table doesn't exist yet. Skipping updates.")
            return
            
        # Check if price_list column exists
        columns = inspector.get_columns("Supplier")
        column_names = [column["name"] for column in columns]
        
        if "price_list" not in column_names:
            # Add price_list column
            db.execute(text('ALTER TABLE "Supplier" ADD COLUMN price_list JSONB'))
            db.commit()
            logger.info("Added price_list column to Supplier table")
            
        # Check if index exists (cannot easily check with SQLAlchemy inspect)
        try:
            db.execute(text('CREATE INDEX idx_supplier_name ON "Supplier"(name)'))
            db.commit()
            logger.info("Created index on supplier name")
        except Exception:
            # Index probably already exists
            db.rollback()
            logger.info("Index on supplier name already exists")
        
    except Exception as e:
        db.rollback()
        logger.error(f"Error during suppliers table update: {str(e)}")
        # Continue with other migrations

def create_roles_table(db: Session):
    """Create roles table and add role_id to users table"""
    try:
        logger.info("Running migration for roles table")
        
        # Check if the table already exists
        inspector = inspect(db.bind)
        if inspector.has_table("roles"):
            logger.info("roles table already exists. Skipping creation.")
            return
            
        # Create roles table
        db.execute(text("""
        CREATE TABLE roles (
            id SERIAL PRIMARY KEY,
            name VARCHAR(50) NOT NULL UNIQUE,
            permissions JSONB,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
        )
        """))
        
        # Insert default roles
        db.execute(text("""
        INSERT INTO roles (name, permissions) 
        VALUES 
            ('admin', '{"all": true}'),
            ('manager', '{"users": {"read": true, "create": true, "update": true}, "inventory": {"read": true, "create": true, "update": true}}'),
            ('staff', '{"inventory": {"read": true, "update": true}}'),
            ('readonly', '{"all": {"read": true}}')
        """))
        
        # Add role_id to users table
        logger.info("Creating roles table and updating users table...")
        try:
            if inspector.has_table("users"):
                db.execute(text("ALTER TABLE users ADD COLUMN role_id INTEGER"))
                db.execute(text("ALTER TABLE users ADD CONSTRAINT fk_users_roles FOREIGN KEY (role_id) REFERENCES roles(id)"))
            else:
                logger.info("users table doesn't exist yet. It will be created with role_id column.")
        except Exception as e:
            logger.error(f"Error during roles migration: {str(e)}")
        
        db.commit()
        logger.info("Successfully created roles table and updated users table")
        
    except Exception as e:
        db.rollback()
        logger.error(f"Error during roles table creation: {str(e)}")
        # Continue with other migrations

def add_linked_quote_id_to_invoice(db: Session):
    """Add linked_quote_id column to the Invoice table"""
    try:
        logger.info("Running migration to add linked_quote_id column to Invoice table")
        
        # Check if Invoice table exists
        inspector = inspect(db.bind)
        if not inspector.has_table("Invoice"):
            logger.info("Invoice table doesn't exist yet. Skipping linked_quote_id column migration.")
            return
        
        # Check if the column already exists
        columns = inspector.get_columns("Invoice")
        column_names = [column["name"] for column in columns]
        
        if "linked_quote_id" not in column_names:
            # Add the column
            db.execute(text('ALTER TABLE "Invoice" ADD COLUMN linked_quote_id VARCHAR(255)'))
            db.execute(text('CREATE INDEX idx_invoice_linked_quote_id ON "Invoice" (linked_quote_id)'))
            db.commit()
            logger.info("Successfully added linked_quote_id column to Invoice table")
        else:
            logger.info("linked_quote_id column already exists in Invoice table. Skipping.")
            
    except Exception as e:
        db.rollback()
        logger.error(f"Error during linked_quote_id migration: {str(e)}")
        # Continue with other migrations 