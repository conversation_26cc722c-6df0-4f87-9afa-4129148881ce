FROM python:3.11-slim

# Install ALL required WeasyPrint and GObject dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    libcairo2 \
    libpango-1.0-0 \
    libpangocairo-1.0-0 \
    libgdk-pixbuf2.0-0 \
    libffi-dev \
    shared-mime-info \
    libgirepository1.0-dev \
    libglib2.0-0 \
    libxml2 \
    librsvg2-2 \
    libglib2.0-dev \
    gobject-introspection \
    python3-gi \
    python3-gi-cairo \
    gir1.2-pango-1.0 \
    gir1.2-gtk-3.0 \
    libpq-dev \
    curl \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Environment variables to fix Python GI
ENV GI_TYPELIB_PATH=/usr/lib/x86_64-linux-gnu/girepository-1.0
ENV PYTHONPATH=${PYTHONPATH}:/usr/lib/python3/dist-packages

WORKDIR /app

# Copy requirements first for better caching
COPY requirements.txt .

# Install dependencies
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY . .

# Create log directory
RUN mkdir -p logs

# Expose port
EXPOSE 8000

# Start the application
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "8000"]
