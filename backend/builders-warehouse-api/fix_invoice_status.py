#!/usr/bin/env python3
import os
import psycopg2
from src.database import DATABASE_URL

def fix_invoice_status():
    """Fix invoice status based on notes content"""
    
    # Parse database URL
    db_url = DATABASE_URL
    if db_url.startswith('postgresql://'):
        db_url = db_url.replace('postgresql://', 'postgres://')

    # Read SQL script
    with open('fix_invoice_status.sql', 'r') as f:
        sql_script = f.read()

    print('Connecting to database...')
    conn = psycopg2.connect(db_url)
    cur = conn.cursor()

    try:
        # Split the script into individual statements
        statements = [stmt.strip() for stmt in sql_script.split(';') if stmt.strip()]
        
        for i, statement in enumerate(statements):
            if statement.lower().startswith('select'):
                print(f'Executing query {i+1}...')
                cur.execute(statement)
                results = cur.fetchall()
                
                print('Status distribution after fix:')
                for row in results:
                    print(f'Status: {row[0]}, Count: {row[1]}, Draft Notes Count: {row[2]}')
            else:
                print(f'Executing update {i+1}...')
                cur.execute(statement)
                print(f'Affected rows: {cur.rowcount}')
        
        conn.commit()
        print('Database updated successfully!')
        
    except Exception as e:
        print(f'Error: {e}')
        conn.rollback()
    finally:
        cur.close()
        conn.close()

if __name__ == "__main__":
    fix_invoice_status() 