#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create the PasswordResetToken table
"""
import sys
import os

# Add the src directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.database import engine
from sqlalchemy import text

def run_migration():
    """Run the password reset token table migration"""
    try:
        # Read the SQL migration file
        with open('create_password_reset_table.sql', 'r') as f:
            sql_script = f.read()

        # Execute the migration
        with engine.connect() as conn:
            conn.execute(text(sql_script))
            conn.commit()
        
        print('✅ Password reset token table created successfully!')
        return True
        
    except Exception as e:
        print(f'❌ Error creating table: {e}')
        return False

if __name__ == "__main__":
    success = run_migration()
    sys.exit(0 if success else 1) 