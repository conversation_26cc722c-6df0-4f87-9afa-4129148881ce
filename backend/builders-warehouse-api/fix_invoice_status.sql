-- Fix invoice status based on notes content
-- This script updates invoice status to match the business logic:
-- - If notes start with [DRAFT], status should be 'draft'
-- - Otherwise, status should be 'saved'

-- Update invoices that have notes starting with [DRAFT] to draft status
UPDATE "Invoice" 
SET status = 'draft'
WHERE (notes IS NOT NULL AND (notes LIKE '[DRAFT]%' OR notes LIKE '[draft]%'))
   OR status = 'draft';

-- Update all other invoices to saved status
UPDATE "Invoice" 
SET status = 'saved'
WHERE (notes IS NULL OR (notes NOT LIKE '[DRAFT]%' AND notes NOT LIKE '[draft]%'))
   AND status != 'saved';

-- Ensure all invoices have a proper status (default to saved)
UPDATE "Invoice" 
SET status = 'saved'
WHERE status IS NULL OR status = '';

-- Display results
SELECT 
    status,
    COUNT(*) as count,
    COUNT(CASE WHEN notes LIKE '[DRAFT]%' OR notes LIKE '[draft]%' THEN 1 END) as draft_notes_count
FROM "Invoice" 
GROUP BY status
ORDER BY status; 