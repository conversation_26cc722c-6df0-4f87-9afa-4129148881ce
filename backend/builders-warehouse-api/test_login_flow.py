#!/usr/bin/env python3
"""
Test script to verify login flow works correctly
"""
import requests
import json

def test_login_flow():
    """Test the complete login flow"""
    
    # Test credentials for <PERSON> (manager)
    username = "ram<PERSON>.<EMAIL>"
    password = "password123"  # Assuming this is the password
    
    print("Testing login flow...")
    print(f"Username: {username}")
    
    # Prepare login data
    login_data = {
        'username': username,
        'password': password,
        'grant_type': 'password'
    }
    
    try:
        # Make login request
        response = requests.post(
            'http://localhost:8000/api/v1/auth/login',
            data=login_data,
            headers={
                'Content-Type': 'application/x-www-form-urlencoded',
                'Accept': 'application/json'
            }
        )
        
        print(f"Login response status: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print("✅ Login successful!")
            print(f"Access token received: {data.get('access_token', 'N/A')[:20]}...")
            print(f"User data: {json.dumps(data.get('user', {}), indent=2)}")
            
            # Test authenticated request
            token = data.get('access_token')
            if token:
                print("\nTesting authenticated request...")
                auth_response = requests.get(
                    'http://localhost:8000/api/v1/users/me',
                    headers={
                        'Authorization': f'Bearer {token}',
                        'Accept': 'application/json'
                    }
                )
                
                print(f"Auth request status: {auth_response.status_code}")
                if auth_response.status_code == 200:
                    user_data = auth_response.json()
                    print("✅ Authenticated request successful!")
                    print(f"User profile: {json.dumps(user_data, indent=2)}")
                else:
                    print(f"❌ Authenticated request failed: {auth_response.text}")
            
        else:
            print(f"❌ Login failed: {response.status_code}")
            print(f"Error: {response.text}")
            
    except Exception as e:
        print(f"❌ Error during login test: {e}")

if __name__ == "__main__":
    test_login_flow() 