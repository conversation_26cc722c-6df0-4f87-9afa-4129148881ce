<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice #{{ invoice.invoice_no }}</title>
    <style>
        body {
            font-family: Arial, Helvetica, sans-serif;
            margin: 0;
            padding: 0;
            color: #333;
            font-size: 12px;
        }
        .container {
            width: 100%;
            margin: 0 auto;
            padding: 20px;
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        .company-logo {
            max-height: 100px;
            margin-bottom: 10px;
        }
        .invoice-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .invoice-number {
            font-size: 16px;
            margin-bottom: 20px;
        }
        .invoice-meta {
            display: flex;
            justify-content: space-between;
            margin-bottom: 30px;
        }
        .company-details, .customer-details, .invoice-details {
            width: 30%;
        }
        h2 {
            font-size: 16px;
            border-bottom: 1px solid #ccc;
            padding-bottom: 5px;
            margin-bottom: 10px;
        }
        .label {
            font-weight: bold;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin-bottom: 30px;
        }
        thead {
            background-color: #f5f5f5;
        }
        th, td {
            padding: 10px;
            border: 1px solid #ddd;
            text-align: left;
        }
        th {
            font-weight: bold;
        }
        .amount-column {
            text-align: right;
        }
        .totals {
            width: 40%;
            margin-left: auto;
        }
        .total-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
        }
        .grand-total {
            font-weight: bold;
            font-size: 16px;
            margin-top: 5px;
            padding-top: 5px;
            border-top: 2px solid #333;
        }
        .notes {
            margin-top: 30px;
            padding: 15px;
            border: 1px solid #ddd;
            background-color: #f9f9f9;
        }
        .footer {
            margin-top: 50px;
            text-align: center;
            font-size: 10px;
            color: #777;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <img src="logo.png" alt="Builder's Warehouse Logo" class="company-logo">
            <div class="invoice-title">TAX INVOICE</div>
            <div class="invoice-number">{{ invoice.invoice_no }}</div>
        </div>

        <div class="invoice-meta">
            <div class="company-details">
                <h2>From</h2>
                <p>
                    <strong>Builder's Warehouse</strong><br>
                    123 Construction Ave<br>
                    Industrial District<br>
                    Sydney, NSW 2000<br>
                    Australia<br>
                    <br>
                    ABN: 12 ***********<br>
                    Phone: (02) 1234 5678<br>
                    Email: <EMAIL>
                </p>
            </div>

            <div class="customer-details">
                <h2>Bill To</h2>
                <p>
                    <strong>{{ customer.company_name }}</strong><br>
                    {% if customer.contact_person %}Attn: {{ customer.contact_person }}<br>{% endif %}
                    {% if customer.billing_address %}{{ customer.billing_address }}<br>{% endif %}
                    {% if customer.billing_suburb %}{{ customer.billing_suburb }}{% endif %}
                    {% if customer.billing_postcode %}, {{ customer.billing_postcode }}{% endif %}<br>
                    <br>
                    Phone: {{ customer.phone }}<br>
                    Email: {{ customer.email }}
                </p>
            </div>

            <div class="invoice-details">
                <h2>Invoice Details</h2>
                <p>
                    <span class="label">Invoice Date:</span> {{ invoice.date }}<br>
                    <span class="label">Store Type:</span> {{ invoice.store_type }}<br>
                    <span class="label">Account:</span> {% if customer.is_account %}Yes{% else %}No{% endif %}<br>
                    {% if customer.price_list %}
                    <span class="label">Price List:</span> {{ customer.price_list.name }}<br>
                    {% endif %}
                </p>
            </div>
        </div>

        <table>
            <thead>
                <tr>
                    <th>Description</th>
                    <th>Quantity</th>
                    <th>Unit Price</th>
                    <th>GST</th>
                    <th>Amount</th>
                </tr>
            </thead>
            <tbody>
                <!-- This is a placeholder - in a real system, you would loop through invoice items -->
                <tr>
                    <td>Placeholder Item (Invoice Items would be dynamically generated)</td>
                    <td>1</td>
                    <td class="amount-column">${{ (invoice.grand_total - invoice.total_gst)|float|round(2) }}</td>
                    <td class="amount-column">${{ invoice.total_gst|float|round(2) }}</td>
                    <td class="amount-column">${{ invoice.grand_total|float|round(2) }}</td>
                </tr>
            </tbody>
        </table>

        <div class="totals">
            <div class="total-row">
                <span class="label">Subtotal:</span>
                <span>${{ (invoice.grand_total - invoice.total_gst)|float|round(2) }}</span>
            </div>
            <div class="total-row">
                <span class="label">GST:</span>
                <span>${{ invoice.total_gst|float|round(2) }}</span>
            </div>
            <div class="total-row grand-total">
                <span class="label">TOTAL:</span>
                <span>${{ invoice.grand_total|float|round(2) }}</span>
            </div>
        </div>

        {% if invoice.notes %}
        <div class="notes">
            <h2>Notes</h2>
            <p>{{ invoice.notes }}</p>
        </div>
        {% endif %}

        <div class="footer">
            <p>Thank you for your business!</p>
            <p>This invoice was generated automatically. Please contact us if you have any questions.</p>
        </div>
    </div>
</body>
</html> 