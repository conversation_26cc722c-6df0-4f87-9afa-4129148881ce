# PostgreSQL connection (recommended for production)
DATABASE_URL=postgresql://warehouse_user:Password#123@localhost:5432/builders_warehouse

# SQLite connection (for testing only)
# DATABASE_URL=sqlite:///./test.db

# Authentication settings
SECRET_KEY=09d25e094faa6ca2556c818166b7a9563b93f7099f6f0f4caa6cf63b88e8d3e7
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# CORS settings (comma-separated origins)
CORS_ORIGINS=http://localhost:3000,https://yourdomain.com

# Logging settings
LOG_LEVEL=INFO
LOG_RETENTION_DAYS=30
LOGS_DIR=logs
APP_LOG_FILENAME=app.log

# Audit logging settings
AUDIT_LOG_PATH=audit.log 

# Email Service Configuration (Postmark)
POSTMARK_SERVER_TOKEN=************************************
POSTMARK_FROM_EMAIL=<EMAIL>
FRONTEND_URL=http://localhost:4200 