-- Migration to create PasswordResetToken table
-- Run this script to add password reset functionality

CREATE TABLE IF NOT EXISTS "PasswordResetToken" (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    token VARCHAR NOT NULL UNIQUE,
    user_id UUID NOT NULL,
    is_used BOOLEAN DEFAULT FALSE,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    
    -- Foreign key constraint
    CONSTRAINT fk_password_reset_token_user 
        FOREIGN KEY (user_id) 
        REFERENCES "User"(id) 
        ON DELETE CASCADE
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_password_reset_token_token ON "PasswordResetToken"(token);
CREATE INDEX IF NOT EXISTS idx_password_reset_token_user_id ON "PasswordResetToken"(user_id);
CREATE INDEX IF NOT EXISTS idx_password_reset_token_expires_at ON "PasswordResetToken"(expires_at);
CREATE INDEX IF NOT EXISTS idx_password_reset_token_is_used ON "PasswordResetToken"(is_used);

-- Add comment to the table
COMMENT ON TABLE "PasswordResetToken" IS 'Stores password reset tokens for user password recovery';
COMMENT ON COLUMN "PasswordResetToken".token IS 'Unique token for password reset';
COMMENT ON COLUMN "PasswordResetToken".user_id IS 'Reference to the user requesting password reset';
COMMENT ON COLUMN "PasswordResetToken".is_used IS 'Flag to track if token has been used';
COMMENT ON COLUMN "PasswordResetToken".expires_at IS 'Token expiration timestamp';
COMMENT ON COLUMN "PasswordResetToken".created_at IS 'Token creation timestamp'; 