"""
Builder's Warehouse API
Main entry point for the application
"""
import uvicorn
import logging
from src.main import app

logger = logging.getLogger(__name__)

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    
    logger.info("Starting Builders Warehouse API server...")
    uvicorn.run(
        "src.main:app", 
        host="0.0.0.0", 
        port=8000, 
        reload=True,
        forwarded_allow_ips="*",
        proxy_headers=True
    ) 