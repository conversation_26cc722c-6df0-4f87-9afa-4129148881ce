#!/usr/bin/env python3
"""
Simple test for password reset
"""
import sys
import os
from datetime import datetime, timezone

# Add the src directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.database import SessionLocal
from src.models.password_reset_token import PasswordResetToken
from src.models.user import User
from src.utils.auth import get_password_hash

def test_password_reset():
    """Test password reset confirmation directly"""
    db = SessionLocal()
    try:
        token_str = "5u0WTHVXk6L-BkE2Fzlv7gC8QTP47_6FpMpc_aJC6lU"
        new_password = "Password123!"
        
        print("Testing password reset confirmation...")
        print(f"Token: {token_str}")
        print(f"New password: {new_password}")
        print(f"Current time (UTC): {datetime.now(timezone.utc)}")
        
        # Find the token
        token_record = db.query(PasswordResetToken).filter(
            PasswordResetToken.token == token_str
        ).first()
        
        if not token_record:
            print("❌ Token not found")
            return
            
        print(f"Token found: {token_record.token[:20]}...")
        print(f"Token used: {token_record.is_used}")
        print(f"Token expires: {token_record.expires_at}")
        
        # Check expiration manually
        now = datetime.now(timezone.utc)
        if token_record.expires_at.tzinfo is not None:
            expires_utc = token_record.expires_at.astimezone(timezone.utc)
        else:
            expires_utc = token_record.expires_at.replace(tzinfo=timezone.utc)
        
        print(f"Current UTC: {now}")
        print(f"Expires UTC: {expires_utc}")
        print(f"Is expired: {now > expires_utc}")
        print(f"Token valid: {token_record.is_valid()}")
        
        if not token_record.is_valid():
            print("❌ Token is not valid (used or expired)")
            return
            
        # Get the user
        user = db.query(User).filter(User.id == token_record.user_id).first()
        if not user:
            print("❌ User not found")
            return
            
        print(f"User found: {user.user_name} ({user.email})")
        print(f"User active: {user.is_active}")
        print(f"User deleted: {user.is_deleted}")
        
        if not user.is_active or user.is_deleted:
            print("❌ User is not active or is deleted")
            return
            
        # Update user password
        old_password = user.hashed_password
        user.hashed_password = get_password_hash(new_password)
        
        # Mark token as used
        token_record.is_used = True
        
        db.commit()
        
        print("✅ Password reset completed successfully!")
        print(f"Old password hash: {old_password[:20]}...")
        print(f"New password hash: {user.hashed_password[:20]}...")
        
    except Exception as e:
        print(f"❌ Error: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()

if __name__ == "__main__":
    test_password_reset() 