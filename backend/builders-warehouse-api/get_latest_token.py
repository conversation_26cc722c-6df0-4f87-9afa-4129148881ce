#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to get the latest password reset token
"""
import sys
import os

# Add the src directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.database import SessionLocal
from src.models.password_reset_token import PasswordResetToken
from src.models.user import User

def get_latest_token():
    """Get the latest unused token for Ram"""
    db = SessionLocal()
    try:
        # Get the latest unused token for Ram
        user = db.query(User).filter(User.email == '<EMAIL>').first()
        if user:
            token = db.query(PasswordResetToken).filter(
                PasswordResetToken.user_id == user.id,
                PasswordResetToken.is_used == False
            ).order_by(PasswordResetToken.created_at.desc()).first()
            if token:
                print(f'Latest token for {user.user_name}: {token.token}')
                return token.token
            else:
                print('No unused tokens found')
                return None
        else:
            print('User not found')
            return None
    finally:
        db.close()

if __name__ == "__main__":
    get_latest_token() 