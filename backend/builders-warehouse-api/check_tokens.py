#!/usr/bin/env python3
"""
Script to check password reset tokens
"""
import sys
import os

# Add the src directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.database import SessionLocal
from src.models.password_reset_token import PasswordResetToken
from src.models.user import User

def check_tokens():
    """Check password reset tokens"""
    db = SessionLocal()
    try:
        tokens = db.query(PasswordResetToken).all()
        print(f'Found {len(tokens)} password reset tokens:')
        
        for token in tokens:
            user = db.query(User).filter(User.id == token.user_id).first()
            print(f'  - Token: {token.token[:20]}...')
            print(f'    User: {user.user_name if user else "Unknown"} ({user.email if user else "Unknown"})')
            print(f'    Used: {token.is_used}')
            print(f'    Expires: {token.expires_at}')
            print(f'    Created: {token.created_at}')
            print()
            
    finally:
        db.close()

if __name__ == "__main__":
    check_tokens() 