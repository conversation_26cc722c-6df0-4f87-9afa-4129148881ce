#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to check if the test user exists
"""
import sys
import os

# Add the src directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.database import SessionLocal
from src.models.user import User

def check_test_user():
    """Check if the test user exists"""
    db = SessionLocal()
    try:
        user = db.query(User).filter(User.email == '<EMAIL>').first()
        if user:
            print(f'✅ User found: {user.user_name} ({user.email})')
            print(f'   Role: {user.role}')
            print(f'   Active: {user.is_active}')
            print(f'   Mobile: {user.mobile_number}')
            return True
        else:
            print('❌ User not found with email: <EMAIL>')
            
            # Let's see what users exist
            users = db.query(User).limit(5).all()
            print('Available users:')
            for u in users:
                print(f'  - {u.user_name} ({u.email})')
            return False
    finally:
        db.close()

if __name__ == "__main__":
    check_test_user() 