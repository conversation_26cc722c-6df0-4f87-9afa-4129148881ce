# Builders Warehouse API - Code Structure Guide

This document provides an overview of the backend code organization and structure for the Builders Warehouse API.

## Project Structure

```
backend/builders-warehouse-api/
├── src/                 # Main source code directory
│   ├── api/             # API endpoints and routers
│   │   └── v1/          # API version 1
│   │       ├── routes/  # Route handlers organized by feature
│   │       └── dependencies.py # Shared API dependencies (auth, etc.)
│   ├── core/            # Core application components
│   ├── db/              # Database configurations and sessions
│   ├── models/          # Database models (SQLAlchemy)
│   ├── repository/      # Repository pattern for database operations
│   ├── schemas/         # Pydantic models for validation and serialization
│   ├── services/        # Business logic services
│   ├── utils/           # Utility functions and helpers
│   ├── middleware/      # HTTP middleware
│   ├── config.py        # Application configuration
│   ├── database.py      # Database connection setup
│   ├── logging_setup.py # Logging configuration
│   └── main.py          # Application entry point
├── tests/               # Test suite
├── scripts/             # Utility scripts
├── alembic.ini          # Alembic migration configuration
├── run_migrations.py    # Script to run database migrations
└── requirements.txt     # Project dependencies
```

## Key Components

### API Structure

The API follows a modular structure where endpoints are organized by feature in the `src/api/v1/routes/` directory. Each feature has its own router file:

- `inventory.py` - Inventory management endpoints
- `suppliers.py` - Supplier management endpoints 
- `purchase_orders.py` - Purchase order management endpoints
- `outbound_delivery.py` - Outbound delivery management endpoints
- etc.

Each router file defines a FastAPI router with its own routes, which are then included in the main application.

### Repository Pattern

The application uses the Repository pattern to abstract database operations. The repository modules are located in `src/repository/` and provide a clean interface for data access.

**Note:** There is also a deprecated `src/crud/` directory that contains older direct database operation functions. New code should use the repository pattern instead.

### Models and Schemas

- **Models** (`src/models/`): SQLAlchemy ORM models that represent database tables
- **Schemas** (`src/services/`): Pydantic models used for validation and serialization of API requests/responses

## Recent Changes

### API Refactoring

The API structure has been refactored to:

1. Move direct endpoint definitions from `main.py` into their respective feature router files
2. Ensure consistent use of the repository pattern instead of direct database operations
3. Apply consistent error handling and response formats
4. Organize routes by feature to improve maintainability

### Inventory Model Changes

The Inventory model has been updated to:

1. Replace `supplier_name` with `supplier_id` as a foreign key to the Supplier table
2. Use a proper relationship with the Supplier model
3. Automatically migrate existing data using the migration script in `scripts/migrate_supplier_name.py`

### Logging Improvements

The logging system has been enhanced to:

1. Create daily log folders with format MM-DD-YYYY
2. Store separate app.log and error.log files in each daily folder
3. Include detailed error information including file paths and line numbers

### Deprecated Components

The `src/crud/` directory is deprecated and maintained only for backward compatibility. New code should use the repository pattern in `src/repository/` instead.

## Development Guidelines

1. **Add new endpoints** to the appropriate router file in `src/api/v1/routes/`
2. **Use the repository pattern** for database operations
3. **Define Pydantic schemas** for request/response models
4. **Add proper documentation** using docstrings and OpenAPI annotations
5. **Write tests** for new functionality in the `tests/` directory

## Running the Application

To run the application locally:

```bash
# Install dependencies
pip install -r requirements.txt

# Run database migrations
python run_migrations.py

# Start the application
uvicorn src.main:app --reload
```

The API will be available at http://localhost:8000 and the API documentation at http://localhost:8000/docs. 