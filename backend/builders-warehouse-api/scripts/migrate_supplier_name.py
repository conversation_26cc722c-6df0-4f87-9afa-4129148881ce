#!/usr/bin/env python3
"""
Migration script to update the Inventory table to use supplier_id instead of supplier_name.
This script will:
1. Update any inventory items with supplier_name but no supplier_id
2. Remove the supplier_name column from the Inventory table
"""
import os
import sys
import logging
from sqlalchemy import create_engine, text, inspect

# Add the parent directory to sys.path to import from src
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

from src.config import settings

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler()]
)
logger = logging.getLogger(__name__)

def migrate_supplier_name():
    """
    Migrate the Inventory table from using supplier_name to supplier_id
    """
    try:
        # Create connection to database
        engine = create_engine(settings.database_url)
        inspector = inspect(engine)
        
        logger.info("Starting migration from supplier_name to supplier_id...")
        
        # Check if Inventory table exists
        if 'Inventory' not in inspector.get_table_names():
            logger.error("Inventory table not found in database")
            return False
        
        # Check if supplier_name column exists
        columns = [c['name'] for c in inspector.get_columns('Inventory')]
        supplier_name_exists = 'supplier_name' in columns
        
        if not supplier_name_exists:
            logger.info("supplier_name column doesn't exist in Inventory table. Migration not needed.")
            return True
        
        # Check if supplier_id column exists
        supplier_id_exists = 'supplier_id' in columns
        
        if not supplier_id_exists:
            logger.error("supplier_id column doesn't exist in Inventory table. Cannot migrate.")
            return False
        
        # Perform the migration
        with engine.connect() as conn:
            # Begin transaction
            trans = conn.begin()
            
            try:
                # Update inventory items that have supplier_name but no supplier_id
                logger.info("Updating inventory records with supplier_id based on supplier_name...")
                result = conn.execute(text('''
                    UPDATE "Inventory" i
                    SET supplier_id = s.id
                    FROM "Supplier" s
                    WHERE i.supplier_name = s.name
                    AND i.supplier_id IS NULL
                '''))
                
                # Check if any records still have null supplier_id
                result = conn.execute(text('SELECT COUNT(*) FROM "Inventory" WHERE supplier_id IS NULL'))
                null_count = result.scalar()
                
                if null_count > 0:
                    logger.warning(f"Found {null_count} inventory items with no supplier_id after migration.")
                    logger.warning("These records may need manual fixing.")
                
                # Drop the supplier_name column
                logger.info("Removing supplier_name column from Inventory table...")
                conn.execute(text('ALTER TABLE "Inventory" DROP COLUMN supplier_name'))
                
                # Commit transaction
                trans.commit()
                logger.info("Migration completed successfully")
                return True
                
            except Exception as e:
                # Rollback transaction on error
                trans.rollback()
                logger.error(f"Error during migration: {str(e)}")
                return False
                
    except Exception as e:
        logger.error(f"Error setting up migration: {str(e)}")
        return False

if __name__ == "__main__":
    success = migrate_supplier_name()
    if success:
        logger.info("Migration completed successfully")
        sys.exit(0)
    else:
        logger.error("Migration failed")
        sys.exit(1) 